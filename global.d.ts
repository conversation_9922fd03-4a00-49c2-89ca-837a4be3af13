import { ReactThree<PERSON>iber } from '@react-three/fiber'
import * as THREE from 'three'

declare global {
  namespace JSX {
    interface IntrinsicElements {
      // Geometries
      boxGeometry: ReactThreeFiber.Object3DNode<THREE.BoxGeometry, typeof THREE.BoxGeometry>
      sphereGeometry: ReactThreeFiber.Object3DNode<THREE.SphereGeometry, typeof THREE.SphereGeometry>
      cylinderGeometry: ReactThreeFiber.Object3DNode<THREE.CylinderGeometry, typeof THREE.CylinderGeometry>
      planeGeometry: ReactThreeFiber.Object3DNode<THREE.PlaneGeometry, typeof THREE.PlaneGeometry>
      ringGeometry: ReactThreeFiber.Object3DNode<THREE.RingGeometry, typeof THREE.RingGeometry>
      coneGeometry: ReactThreeFiber.Object3DNode<THREE.ConeGeometry, typeof THREE.ConeGeometry>
      torusGeometry: ReactThreeFiber.Object3DNode<THREE.TorusGeometry, typeof THREE.TorusGeometry>
      bufferGeometry: ReactThreeFiber.Object3DNode<THREE.BufferGeometry, typeof THREE.BufferGeometry>
      
      // Materials
      meshStandardMaterial: ReactThreeFiber.MaterialNode<THREE.MeshStandardMaterial, typeof THREE.MeshStandardMaterial>
      meshBasicMaterial: ReactThreeFiber.MaterialNode<THREE.MeshBasicMaterial, typeof THREE.MeshBasicMaterial>
      meshPhongMaterial: ReactThreeFiber.MaterialNode<THREE.MeshPhongMaterial, typeof THREE.MeshPhongMaterial>
      meshLambertMaterial: ReactThreeFiber.MaterialNode<THREE.MeshLambertMaterial, typeof THREE.MeshLambertMaterial>
      pointsMaterial: ReactThreeFiber.MaterialNode<THREE.PointsMaterial, typeof THREE.PointsMaterial>
      shaderMaterial: ReactThreeFiber.MaterialNode<THREE.ShaderMaterial, typeof THREE.ShaderMaterial>
      
      // Lights
      ambientLight: ReactThreeFiber.Object3DNode<THREE.AmbientLight, typeof THREE.AmbientLight>
      directionalLight: ReactThreeFiber.Object3DNode<THREE.DirectionalLight, typeof THREE.DirectionalLight>
      pointLight: ReactThreeFiber.Object3DNode<THREE.PointLight, typeof THREE.PointLight>
      spotLight: ReactThreeFiber.Object3DNode<THREE.SpotLight, typeof THREE.SpotLight>
      
      // Objects
      mesh: ReactThreeFiber.Object3DNode<THREE.Mesh, typeof THREE.Mesh>
      group: ReactThreeFiber.Object3DNode<THREE.Group, typeof THREE.Group>
      points: ReactThreeFiber.Object3DNode<THREE.Points, typeof THREE.Points>
      line: ReactThreeFiber.Object3DNode<THREE.Line, typeof THREE.Line>
      
      // Attributes
      bufferAttribute: ReactThreeFiber.BufferAttributeNode<THREE.BufferAttribute, typeof THREE.BufferAttribute>
    }
  }
}

export {}
