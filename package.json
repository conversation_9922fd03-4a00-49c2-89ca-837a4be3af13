{"name": "nextjs-portfolio", "private": true, "version": "0.1.0", "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@react-spring/three": "^9.7.3", "@react-three/drei": "^9.122.0", "@react-three/fiber": "^8.15.11", "@splinetool/react-spline": "^4.0.0", "@splinetool/runtime": "^1.9.96", "@tanstack/react-query": "^5.56.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.3.0", "framer-motion": "^12.12.1", "framer-motion-3d": "^10.18.0", "gsap": "^3.12.5", "input-otp": "^1.2.4", "lucide-react": "^0.462.0", "next-themes": "^0.3.0", "react": "18.3.1", "react-day-picker": "^8.10.1", "react-dom": "18.3.1", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.53.0", "react-resizable-panels": "^2.1.3", "next": "15.3.3", "recharts": "^2.12.7", "sonner": "^1.5.0", "styled-components": "^6.1.18", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "three": "^0.158.0", "vaul": "^0.9.3", "zod": "^3.23.8"}, "devDependencies": {"@next/eslint-plugin-next": "15.3.3", "@tailwindcss/typography": "^0.5.15", "@types/node": "^22.5.5", "@types/react": "19.1.6", "@types/react-dom": "19.1.5", "@types/styled-components": "^5.1.34", "@types/three": "^0.158.0", "autoprefixer": "^10.4.20", "eslint": "^8.57.0", "eslint-config-next": "15.3.3", "postcss": "^8.4.47", "tailwindcss": "^3.4.11", "typescript": "^5.5.3"}, "overrides": {"@types/react": "19.1.6", "@types/react-dom": "19.1.5"}}