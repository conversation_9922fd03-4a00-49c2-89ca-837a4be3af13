(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@react-three/fiber/dist/react-three-fiber.esm.js [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_react-reconciler_25c8ec02._.js",
  "static/chunks/node_modules_three_build_three_module_7d17ea5c.js",
  "static/chunks/node_modules_@react-three_fiber_dist_711ab369._.js",
  "static/chunks/node_modules_971b5526._.js",
  "static/chunks/node_modules_@react-three_fiber_dist_react-three-fiber_esm_7c89245f.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@react-three/fiber/dist/react-three-fiber.esm.js [app-client] (ecmascript, next/dynamic entry)");
    });
});
}}),
"[project]/src/components/3d/InteractiveThreeScene.tsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_three_build_three_module_7d17ea5c.js",
  "static/chunks/node_modules_react-reconciler_25c8ec02._.js",
  "static/chunks/node_modules_@react-three_fiber_dist_events-776716bd_esm_1cf5dfd2.js",
  "static/chunks/node_modules_@react-spring_core_dist_react-spring_core_modern_mjs_65a81099._.js",
  "static/chunks/node_modules_c493175d._.js",
  "static/chunks/src_components_3d_d0823335._.js",
  "static/chunks/src_components_3d_InteractiveThreeScene_tsx_4b877fa2._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/3d/InteractiveThreeScene.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}}),
}]);