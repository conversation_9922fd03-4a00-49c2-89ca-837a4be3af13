{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/%40react-three/fiber/dist/events-776716bd.esm.js"], "sourcesContent": ["import * as THREE from 'three';\nimport * as React from 'react';\nimport { DefaultEventPriority, ContinuousEventPriority, DiscreteEventPriority, ConcurrentRoot } from 'react-reconciler/constants';\nimport create from 'zustand';\nimport { suspend, preload, clear } from 'suspend-react';\nimport { jsx, Fragment } from 'react/jsx-runtime';\nimport Reconciler from 'react-reconciler';\nimport { unstable_scheduleCallback, unstable_IdlePriority } from 'scheduler';\n\nvar threeTypes = /*#__PURE__*/Object.freeze({\n  __proto__: null\n});\n\nconst catalogue = {};\nconst extend = objects => void Object.assign(catalogue, objects);\nfunction createRenderer(_roots, _getEventPriority) {\n  function createInstance(type, {\n    args = [],\n    attach,\n    ...props\n  }, root) {\n    let name = `${type[0].toUpperCase()}${type.slice(1)}`;\n    let instance;\n    if (type === 'primitive') {\n      if (props.object === undefined) throw new Error(\"R3F: Primitives without 'object' are invalid!\");\n      const object = props.object;\n      instance = prepare(object, {\n        type,\n        root,\n        attach,\n        primitive: true\n      });\n    } else {\n      const target = catalogue[name];\n      if (!target) {\n        throw new Error(`R3F: ${name} is not part of the THREE namespace! Did you forget to extend? See: https://docs.pmnd.rs/react-three-fiber/api/objects#using-3rd-party-objects-declaratively`);\n      }\n\n      // Throw if an object or literal was passed for args\n      if (!Array.isArray(args)) throw new Error('R3F: The args prop must be an array!');\n\n      // Instanciate new object, link it to the root\n      // Append memoized props with args so it's not forgotten\n      instance = prepare(new target(...args), {\n        type,\n        root,\n        attach,\n        // Save args in case we need to reconstruct later for HMR\n        memoizedProps: {\n          args\n        }\n      });\n    }\n\n    // Auto-attach geometries and materials\n    if (instance.__r3f.attach === undefined) {\n      if (instance.isBufferGeometry) instance.__r3f.attach = 'geometry';else if (instance.isMaterial) instance.__r3f.attach = 'material';\n    }\n\n    // It should NOT call onUpdate on object instanciation, because it hasn't been added to the\n    // view yet. If the callback relies on references for instance, they won't be ready yet, this is\n    // why it passes \"true\" here\n    // There is no reason to apply props to injects\n    if (name !== 'inject') applyProps$1(instance, props);\n    return instance;\n  }\n  function appendChild(parentInstance, child) {\n    let added = false;\n    if (child) {\n      var _child$__r3f, _parentInstance$__r3f;\n      // The attach attribute implies that the object attaches itself on the parent\n      if ((_child$__r3f = child.__r3f) != null && _child$__r3f.attach) {\n        attach(parentInstance, child, child.__r3f.attach);\n      } else if (child.isObject3D && parentInstance.isObject3D) {\n        // add in the usual parent-child way\n        parentInstance.add(child);\n        added = true;\n      }\n      // This is for anything that used attach, and for non-Object3Ds that don't get attached to props;\n      // that is, anything that's a child in React but not a child in the scenegraph.\n      if (!added) (_parentInstance$__r3f = parentInstance.__r3f) == null ? void 0 : _parentInstance$__r3f.objects.push(child);\n      if (!child.__r3f) prepare(child, {});\n      child.__r3f.parent = parentInstance;\n      updateInstance(child);\n      invalidateInstance(child);\n    }\n  }\n  function insertBefore(parentInstance, child, beforeChild) {\n    let added = false;\n    if (child) {\n      var _child$__r3f2, _parentInstance$__r3f2;\n      if ((_child$__r3f2 = child.__r3f) != null && _child$__r3f2.attach) {\n        attach(parentInstance, child, child.__r3f.attach);\n      } else if (child.isObject3D && parentInstance.isObject3D) {\n        child.parent = parentInstance;\n        child.dispatchEvent({\n          type: 'added'\n        });\n        parentInstance.dispatchEvent({\n          type: 'childadded',\n          child\n        });\n        const restSiblings = parentInstance.children.filter(sibling => sibling !== child);\n        const index = restSiblings.indexOf(beforeChild);\n        parentInstance.children = [...restSiblings.slice(0, index), child, ...restSiblings.slice(index)];\n        added = true;\n      }\n      if (!added) (_parentInstance$__r3f2 = parentInstance.__r3f) == null ? void 0 : _parentInstance$__r3f2.objects.push(child);\n      if (!child.__r3f) prepare(child, {});\n      child.__r3f.parent = parentInstance;\n      updateInstance(child);\n      invalidateInstance(child);\n    }\n  }\n  function removeRecursive(array, parent, dispose = false) {\n    if (array) [...array].forEach(child => removeChild(parent, child, dispose));\n  }\n  function removeChild(parentInstance, child, dispose) {\n    if (child) {\n      var _parentInstance$__r3f3, _child$__r3f3, _child$__r3f5;\n      // Clear the parent reference\n      if (child.__r3f) child.__r3f.parent = null;\n      // Remove child from the parents objects\n      if ((_parentInstance$__r3f3 = parentInstance.__r3f) != null && _parentInstance$__r3f3.objects) parentInstance.__r3f.objects = parentInstance.__r3f.objects.filter(x => x !== child);\n      // Remove attachment\n      if ((_child$__r3f3 = child.__r3f) != null && _child$__r3f3.attach) {\n        detach(parentInstance, child, child.__r3f.attach);\n      } else if (child.isObject3D && parentInstance.isObject3D) {\n        var _child$__r3f4;\n        parentInstance.remove(child);\n        // @ts-expect-error\n        // Remove interactivity on the initial root\n        if ((_child$__r3f4 = child.__r3f) != null && _child$__r3f4.root) {\n          removeInteractivity(findInitialRoot(child), child);\n        }\n      }\n\n      // Allow objects to bail out of recursive dispose altogether by passing dispose={null}\n      // Never dispose of primitives because their state may be kept outside of React!\n      // In order for an object to be able to dispose it has to have\n      //   - a dispose method,\n      //   - it cannot be a <primitive object={...} />\n      //   - it cannot be a THREE.Scene, because three has broken it's own api\n      //\n      // Since disposal is recursive, we can check the optional dispose arg, which will be undefined\n      // when the reconciler calls it, but then carry our own check recursively\n      const isPrimitive = (_child$__r3f5 = child.__r3f) == null ? void 0 : _child$__r3f5.primitive;\n      const shouldDispose = !isPrimitive && (dispose === undefined ? child.dispose !== null : dispose);\n\n      // Remove nested child objects. Primitives should not have objects and children that are\n      // attached to them declaratively ...\n      if (!isPrimitive) {\n        var _child$__r3f6;\n        removeRecursive((_child$__r3f6 = child.__r3f) == null ? void 0 : _child$__r3f6.objects, child, shouldDispose);\n        removeRecursive(child.children, child, shouldDispose);\n      }\n\n      // Remove references\n      delete child.__r3f;\n\n      // Dispose item whenever the reconciler feels like it\n      if (shouldDispose && child.dispose && child.type !== 'Scene') {\n        const callback = () => {\n          try {\n            child.dispose();\n          } catch (e) {\n            /* ... */\n          }\n        };\n\n        // Schedule async at runtime, flush sync in testing\n        if (typeof IS_REACT_ACT_ENVIRONMENT === 'undefined') {\n          unstable_scheduleCallback(unstable_IdlePriority, callback);\n        } else {\n          callback();\n        }\n      }\n      invalidateInstance(parentInstance);\n    }\n  }\n  function switchInstance(instance, type, newProps, fiber) {\n    var _instance$__r3f;\n    const parent = (_instance$__r3f = instance.__r3f) == null ? void 0 : _instance$__r3f.parent;\n    if (!parent) return;\n    const newInstance = createInstance(type, newProps, instance.__r3f.root);\n\n    // https://github.com/pmndrs/react-three-fiber/issues/1348\n    // When args change the instance has to be re-constructed, which then\n    // forces r3f to re-parent the children and non-scene objects\n    if (instance.children) {\n      for (const child of instance.children) {\n        if (child.__r3f) appendChild(newInstance, child);\n      }\n      instance.children = instance.children.filter(child => !child.__r3f);\n    }\n    instance.__r3f.objects.forEach(child => appendChild(newInstance, child));\n    instance.__r3f.objects = [];\n    if (!instance.__r3f.autoRemovedBeforeAppend) {\n      removeChild(parent, instance);\n    }\n    if (newInstance.parent) {\n      newInstance.__r3f.autoRemovedBeforeAppend = true;\n    }\n    appendChild(parent, newInstance);\n\n    // Re-bind event handlers on the initial root\n    if (newInstance.raycast && newInstance.__r3f.eventCount) {\n      const rootState = findInitialRoot(newInstance).getState();\n      rootState.internal.interaction.push(newInstance);\n    }\n    [fiber, fiber.alternate].forEach(fiber => {\n      if (fiber !== null) {\n        fiber.stateNode = newInstance;\n        if (fiber.ref) {\n          if (typeof fiber.ref === 'function') fiber.ref(newInstance);else fiber.ref.current = newInstance;\n        }\n      }\n    });\n  }\n\n  // Don't handle text instances, make it no-op\n  const handleTextInstance = () => {};\n  const reconciler = Reconciler({\n    createInstance,\n    removeChild,\n    appendChild,\n    appendInitialChild: appendChild,\n    insertBefore,\n    supportsMutation: true,\n    isPrimaryRenderer: false,\n    supportsPersistence: false,\n    supportsHydration: false,\n    noTimeout: -1,\n    appendChildToContainer: (container, child) => {\n      if (!child) return;\n\n      // Don't append to unmounted container\n      const scene = container.getState().scene;\n      if (!scene.__r3f) return;\n\n      // Link current root to the default scene\n      scene.__r3f.root = container;\n      appendChild(scene, child);\n    },\n    removeChildFromContainer: (container, child) => {\n      if (!child) return;\n      removeChild(container.getState().scene, child);\n    },\n    insertInContainerBefore: (container, child, beforeChild) => {\n      if (!child || !beforeChild) return;\n\n      // Don't append to unmounted container\n      const scene = container.getState().scene;\n      if (!scene.__r3f) return;\n      insertBefore(scene, child, beforeChild);\n    },\n    getRootHostContext: () => null,\n    getChildHostContext: parentHostContext => parentHostContext,\n    finalizeInitialChildren(instance) {\n      var _instance$__r3f2;\n      const localState = (_instance$__r3f2 = instance == null ? void 0 : instance.__r3f) != null ? _instance$__r3f2 : {};\n      // https://github.com/facebook/react/issues/20271\n      // Returning true will trigger commitMount\n      return Boolean(localState.handlers);\n    },\n    prepareUpdate(instance, _type, oldProps, newProps) {\n      var _instance$__r3f3;\n      const localState = (_instance$__r3f3 = instance == null ? void 0 : instance.__r3f) != null ? _instance$__r3f3 : {};\n\n      // Create diff-sets\n      if (localState.primitive && newProps.object && newProps.object !== instance) {\n        return [true];\n      } else {\n        // This is a data object, let's extract critical information about it\n        const {\n          args: argsNew = [],\n          children: cN,\n          ...restNew\n        } = newProps;\n        const {\n          args: argsOld = [],\n          children: cO,\n          ...restOld\n        } = oldProps;\n\n        // Throw if an object or literal was passed for args\n        if (!Array.isArray(argsNew)) throw new Error('R3F: the args prop must be an array!');\n\n        // If it has new props or arguments, then it needs to be re-instantiated\n        if (argsNew.some((value, index) => value !== argsOld[index])) return [true];\n        // Create a diff-set, flag if there are any changes\n        const diff = diffProps(instance, restNew, restOld, true);\n        if (diff.changes.length) return [false, diff];\n\n        // Otherwise do not touch the instance\n        return null;\n      }\n    },\n    commitUpdate(instance, [reconstruct, diff], type, _oldProps, newProps, fiber) {\n      // Reconstruct when args or <primitive object={...} have changes\n      if (reconstruct) switchInstance(instance, type, newProps, fiber);\n      // Otherwise just overwrite props\n      else applyProps$1(instance, diff);\n    },\n    commitMount(instance, _type, _props, _int) {\n      var _instance$__r3f4;\n      // https://github.com/facebook/react/issues/20271\n      // This will make sure events are only added once to the central container on the initial root\n      const localState = (_instance$__r3f4 = instance.__r3f) != null ? _instance$__r3f4 : {};\n      if (instance.raycast && localState.handlers && localState.eventCount) {\n        findInitialRoot(instance).getState().internal.interaction.push(instance);\n      }\n    },\n    getPublicInstance: instance => instance,\n    prepareForCommit: () => null,\n    preparePortalMount: container => prepare(container.getState().scene),\n    resetAfterCommit: () => {},\n    shouldSetTextContent: () => false,\n    clearContainer: () => false,\n    hideInstance(instance) {\n      var _instance$__r3f5;\n      // Detach while the instance is hidden\n      const {\n        attach: type,\n        parent\n      } = (_instance$__r3f5 = instance.__r3f) != null ? _instance$__r3f5 : {};\n      if (type && parent) detach(parent, instance, type);\n      if (instance.isObject3D) instance.visible = false;\n      invalidateInstance(instance);\n    },\n    unhideInstance(instance, props) {\n      var _instance$__r3f6;\n      // Re-attach when the instance is unhidden\n      const {\n        attach: type,\n        parent\n      } = (_instance$__r3f6 = instance.__r3f) != null ? _instance$__r3f6 : {};\n      if (type && parent) attach(parent, instance, type);\n      if (instance.isObject3D && props.visible == null || props.visible) instance.visible = true;\n      invalidateInstance(instance);\n    },\n    createTextInstance: handleTextInstance,\n    hideTextInstance: handleTextInstance,\n    unhideTextInstance: handleTextInstance,\n    // https://github.com/pmndrs/react-three-fiber/pull/2360#discussion_r916356874\n    // @ts-expect-error\n    getCurrentEventPriority: () => _getEventPriority ? _getEventPriority() : DefaultEventPriority,\n    beforeActiveInstanceBlur: () => {},\n    afterActiveInstanceBlur: () => {},\n    detachDeletedInstance: () => {},\n    now: typeof performance !== 'undefined' && is.fun(performance.now) ? performance.now : is.fun(Date.now) ? Date.now : () => 0,\n    // https://github.com/pmndrs/react-three-fiber/pull/2360#discussion_r920883503\n    scheduleTimeout: is.fun(setTimeout) ? setTimeout : undefined,\n    cancelTimeout: is.fun(clearTimeout) ? clearTimeout : undefined\n  });\n  return {\n    reconciler,\n    applyProps: applyProps$1\n  };\n}\n\nvar _window$document, _window$navigator;\n/**\r\n * Returns `true` with correct TS type inference if an object has a configurable color space (since r152).\r\n */\nconst hasColorSpace = object => 'colorSpace' in object || 'outputColorSpace' in object;\n/**\r\n * The current THREE.ColorManagement instance, if present.\r\n */\nconst getColorManagement = () => {\n  var _ColorManagement;\n  return (_ColorManagement = catalogue.ColorManagement) != null ? _ColorManagement : null;\n};\nconst isOrthographicCamera = def => def && def.isOrthographicCamera;\nconst isRef = obj => obj && obj.hasOwnProperty('current');\n\n/**\r\n * An SSR-friendly useLayoutEffect.\r\n *\r\n * React currently throws a warning when using useLayoutEffect on the server.\r\n * To get around it, we can conditionally useEffect on the server (no-op) and\r\n * useLayoutEffect elsewhere.\r\n *\r\n * @see https://github.com/facebook/react/issues/14927\r\n */\nconst useIsomorphicLayoutEffect = typeof window !== 'undefined' && ((_window$document = window.document) != null && _window$document.createElement || ((_window$navigator = window.navigator) == null ? void 0 : _window$navigator.product) === 'ReactNative') ? React.useLayoutEffect : React.useEffect;\nfunction useMutableCallback(fn) {\n  const ref = React.useRef(fn);\n  useIsomorphicLayoutEffect(() => void (ref.current = fn), [fn]);\n  return ref;\n}\nfunction Block({\n  set\n}) {\n  useIsomorphicLayoutEffect(() => {\n    set(new Promise(() => null));\n    return () => set(false);\n  }, [set]);\n  return null;\n}\nclass ErrorBoundary extends React.Component {\n  constructor(...args) {\n    super(...args);\n    this.state = {\n      error: false\n    };\n  }\n  componentDidCatch(err) {\n    this.props.set(err);\n  }\n  render() {\n    return this.state.error ? null : this.props.children;\n  }\n}\nErrorBoundary.getDerivedStateFromError = () => ({\n  error: true\n});\nconst DEFAULT = '__default';\nconst DEFAULTS = new Map();\nconst isDiffSet = def => def && !!def.memoized && !!def.changes;\nfunction calculateDpr(dpr) {\n  var _window$devicePixelRa;\n  // Err on the side of progress by assuming 2x dpr if we can't detect it\n  // This will happen in workers where window is defined but dpr isn't.\n  const target = typeof window !== 'undefined' ? (_window$devicePixelRa = window.devicePixelRatio) != null ? _window$devicePixelRa : 2 : 1;\n  return Array.isArray(dpr) ? Math.min(Math.max(dpr[0], target), dpr[1]) : dpr;\n}\n\n/**\r\n * Returns instance root state\r\n */\nconst getRootState = obj => {\n  var _r3f;\n  return (_r3f = obj.__r3f) == null ? void 0 : _r3f.root.getState();\n};\n\n/**\r\n * Returns the instances initial (outmost) root\r\n */\nfunction findInitialRoot(child) {\n  let root = child.__r3f.root;\n  while (root.getState().previousRoot) root = root.getState().previousRoot;\n  return root;\n}\n// A collection of compare functions\nconst is = {\n  obj: a => a === Object(a) && !is.arr(a) && typeof a !== 'function',\n  fun: a => typeof a === 'function',\n  str: a => typeof a === 'string',\n  num: a => typeof a === 'number',\n  boo: a => typeof a === 'boolean',\n  und: a => a === void 0,\n  arr: a => Array.isArray(a),\n  equ(a, b, {\n    arrays = 'shallow',\n    objects = 'reference',\n    strict = true\n  } = {}) {\n    // Wrong type or one of the two undefined, doesn't match\n    if (typeof a !== typeof b || !!a !== !!b) return false;\n    // Atomic, just compare a against b\n    if (is.str(a) || is.num(a) || is.boo(a)) return a === b;\n    const isObj = is.obj(a);\n    if (isObj && objects === 'reference') return a === b;\n    const isArr = is.arr(a);\n    if (isArr && arrays === 'reference') return a === b;\n    // Array or Object, shallow compare first to see if it's a match\n    if ((isArr || isObj) && a === b) return true;\n    // Last resort, go through keys\n    let i;\n    // Check if a has all the keys of b\n    for (i in a) if (!(i in b)) return false;\n    // Check if values between keys match\n    if (isObj && arrays === 'shallow' && objects === 'shallow') {\n      for (i in strict ? b : a) if (!is.equ(a[i], b[i], {\n        strict,\n        objects: 'reference'\n      })) return false;\n    } else {\n      for (i in strict ? b : a) if (a[i] !== b[i]) return false;\n    }\n    // If i is undefined\n    if (is.und(i)) {\n      // If both arrays are empty we consider them equal\n      if (isArr && a.length === 0 && b.length === 0) return true;\n      // If both objects are empty we consider them equal\n      if (isObj && Object.keys(a).length === 0 && Object.keys(b).length === 0) return true;\n      // Otherwise match them by value\n      if (a !== b) return false;\n    }\n    return true;\n  }\n};\n\n/**\r\n * Collects nodes and materials from a THREE.Object3D.\r\n */\nfunction buildGraph(object) {\n  const data = {\n    nodes: {},\n    materials: {}\n  };\n  if (object) {\n    object.traverse(obj => {\n      if (obj.name) data.nodes[obj.name] = obj;\n      if (obj.material && !data.materials[obj.material.name]) data.materials[obj.material.name] = obj.material;\n    });\n  }\n  return data;\n}\n\n// Disposes an object and all its properties\nfunction dispose(obj) {\n  if (obj.dispose && obj.type !== 'Scene') obj.dispose();\n  for (const p in obj) {\n    p.dispose == null ? void 0 : p.dispose();\n    delete obj[p];\n  }\n}\n\n// Each object in the scene carries a small LocalState descriptor\nfunction prepare(object, state) {\n  const instance = object;\n  instance.__r3f = {\n    type: '',\n    root: null,\n    previousAttach: null,\n    memoizedProps: {},\n    eventCount: 0,\n    handlers: {},\n    objects: [],\n    parent: null,\n    ...state\n  };\n  return object;\n}\nfunction resolve(instance, key) {\n  let target = instance;\n  if (key.includes('-')) {\n    const entries = key.split('-');\n    const last = entries.pop();\n    target = entries.reduce((acc, key) => acc[key], instance);\n    return {\n      target,\n      key: last\n    };\n  } else return {\n    target,\n    key\n  };\n}\n\n// Checks if a dash-cased string ends with an integer\nconst INDEX_REGEX = /-\\d+$/;\nfunction attach(parent, child, type) {\n  if (is.str(type)) {\n    // If attaching into an array (foo-0), create one\n    if (INDEX_REGEX.test(type)) {\n      const root = type.replace(INDEX_REGEX, '');\n      const {\n        target,\n        key\n      } = resolve(parent, root);\n      if (!Array.isArray(target[key])) target[key] = [];\n    }\n    const {\n      target,\n      key\n    } = resolve(parent, type);\n    child.__r3f.previousAttach = target[key];\n    target[key] = child;\n  } else child.__r3f.previousAttach = type(parent, child);\n}\nfunction detach(parent, child, type) {\n  var _child$__r3f, _child$__r3f2;\n  if (is.str(type)) {\n    const {\n      target,\n      key\n    } = resolve(parent, type);\n    const previous = child.__r3f.previousAttach;\n    // When the previous value was undefined, it means the value was never set to begin with\n    if (previous === undefined) delete target[key];\n    // Otherwise set the previous value\n    else target[key] = previous;\n  } else (_child$__r3f = child.__r3f) == null ? void 0 : _child$__r3f.previousAttach == null ? void 0 : _child$__r3f.previousAttach(parent, child);\n  (_child$__r3f2 = child.__r3f) == null ? true : delete _child$__r3f2.previousAttach;\n}\n// This function prepares a set of changes to be applied to the instance\nfunction diffProps(instance, {\n  children: cN,\n  key: kN,\n  ref: rN,\n  ...props\n}, {\n  children: cP,\n  key: kP,\n  ref: rP,\n  ...previous\n} = {}, remove = false) {\n  const localState = instance.__r3f;\n  const entries = Object.entries(props);\n  const changes = [];\n\n  // Catch removed props, prepend them so they can be reset or removed\n  if (remove) {\n    const previousKeys = Object.keys(previous);\n    for (let i = 0; i < previousKeys.length; i++) {\n      if (!props.hasOwnProperty(previousKeys[i])) entries.unshift([previousKeys[i], DEFAULT + 'remove']);\n    }\n  }\n  entries.forEach(([key, value]) => {\n    var _instance$__r3f;\n    // Bail out on primitive object\n    if ((_instance$__r3f = instance.__r3f) != null && _instance$__r3f.primitive && key === 'object') return;\n    // When props match bail out\n    if (is.equ(value, previous[key])) return;\n    // Collect handlers and bail out\n    if (/^on(Pointer|Click|DoubleClick|ContextMenu|Wheel)/.test(key)) return changes.push([key, value, true, []]);\n    // Split dashed props\n    let entries = [];\n    if (key.includes('-')) entries = key.split('-');\n    changes.push([key, value, false, entries]);\n\n    // Reset pierced props\n    for (const prop in props) {\n      const value = props[prop];\n      if (prop.startsWith(`${key}-`)) changes.push([prop, value, false, prop.split('-')]);\n    }\n  });\n  const memoized = {\n    ...props\n  };\n  if (localState != null && localState.memoizedProps && localState != null && localState.memoizedProps.args) memoized.args = localState.memoizedProps.args;\n  if (localState != null && localState.memoizedProps && localState != null && localState.memoizedProps.attach) memoized.attach = localState.memoizedProps.attach;\n  return {\n    memoized,\n    changes\n  };\n}\nconst __DEV__ = typeof process !== 'undefined' && process.env.NODE_ENV !== 'production';\n\n// This function applies a set of changes to the instance\nfunction applyProps$1(instance, data) {\n  var _instance$__r3f2;\n  // Filter equals, events and reserved props\n  const localState = instance.__r3f;\n  const root = localState == null ? void 0 : localState.root;\n  const rootState = root == null ? void 0 : root.getState == null ? void 0 : root.getState();\n  const {\n    memoized,\n    changes\n  } = isDiffSet(data) ? data : diffProps(instance, data);\n  const prevHandlers = localState == null ? void 0 : localState.eventCount;\n\n  // Prepare memoized props\n  if (instance.__r3f) instance.__r3f.memoizedProps = memoized;\n  for (let i = 0; i < changes.length; i++) {\n    let [key, value, isEvent, keys] = changes[i];\n\n    // Alias (output)encoding => (output)colorSpace (since r152)\n    // https://github.com/pmndrs/react-three-fiber/pull/2829\n    if (hasColorSpace(instance)) {\n      const sRGBEncoding = 3001;\n      const SRGBColorSpace = 'srgb';\n      const LinearSRGBColorSpace = 'srgb-linear';\n      if (key === 'encoding') {\n        key = 'colorSpace';\n        value = value === sRGBEncoding ? SRGBColorSpace : LinearSRGBColorSpace;\n      } else if (key === 'outputEncoding') {\n        key = 'outputColorSpace';\n        value = value === sRGBEncoding ? SRGBColorSpace : LinearSRGBColorSpace;\n      }\n    }\n    let currentInstance = instance;\n    let targetProp = currentInstance[key];\n\n    // Revolve dashed props\n    if (keys.length) {\n      targetProp = keys.reduce((acc, key) => acc[key], instance);\n      // If the target is atomic, it forces us to switch the root\n      if (!(targetProp && targetProp.set)) {\n        const [name, ...reverseEntries] = keys.reverse();\n        currentInstance = reverseEntries.reverse().reduce((acc, key) => acc[key], instance);\n        key = name;\n      }\n    }\n\n    // https://github.com/mrdoob/three.js/issues/21209\n    // HMR/fast-refresh relies on the ability to cancel out props, but threejs\n    // has no means to do this. Hence we curate a small collection of value-classes\n    // with their respective constructor/set arguments\n    // For removed props, try to set default values, if possible\n    if (value === DEFAULT + 'remove') {\n      if (currentInstance.constructor) {\n        // create a blank slate of the instance and copy the particular parameter.\n        let ctor = DEFAULTS.get(currentInstance.constructor);\n        if (!ctor) {\n          // @ts-expect-error\n          ctor = new currentInstance.constructor();\n          DEFAULTS.set(currentInstance.constructor, ctor);\n        }\n        value = ctor[key];\n      } else {\n        // instance does not have constructor, just set it to 0\n        value = 0;\n      }\n    }\n\n    // Deal with pointer events ...\n    if (isEvent && localState) {\n      if (value) localState.handlers[key] = value;else delete localState.handlers[key];\n      localState.eventCount = Object.keys(localState.handlers).length;\n    }\n    // Special treatment for objects with support for set/copy, and layers\n    else if (targetProp && targetProp.set && (targetProp.copy || targetProp instanceof THREE.Layers)) {\n      // If value is an array\n      if (Array.isArray(value)) {\n        if (targetProp.fromArray) targetProp.fromArray(value);else targetProp.set(...value);\n      }\n      // Test again target.copy(class) next ...\n      else if (targetProp.copy && value && value.constructor && (\n      // Some environments may break strict identity checks by duplicating versions of three.js.\n      // Loosen to unminified names, ignoring descendents.\n      // https://github.com/pmndrs/react-three-fiber/issues/2856\n      // TODO: fix upstream and remove in v9\n      __DEV__ ? targetProp.constructor.name === value.constructor.name : targetProp.constructor === value.constructor)) {\n        targetProp.copy(value);\n      }\n      // If nothing else fits, just set the single value, ignore undefined\n      // https://github.com/pmndrs/react-three-fiber/issues/274\n      else if (value !== undefined) {\n        var _targetProp;\n        const isColor = (_targetProp = targetProp) == null ? void 0 : _targetProp.isColor;\n        // Allow setting array scalars\n        if (!isColor && targetProp.setScalar) targetProp.setScalar(value);\n        // Layers have no copy function, we must therefore copy the mask property\n        else if (targetProp instanceof THREE.Layers && value instanceof THREE.Layers) targetProp.mask = value.mask;\n        // Otherwise just set ...\n        else targetProp.set(value);\n        // For versions of three which don't support THREE.ColorManagement,\n        // Auto-convert sRGB colors\n        // https://github.com/pmndrs/react-three-fiber/issues/344\n        if (!getColorManagement() && rootState && !rootState.linear && isColor) targetProp.convertSRGBToLinear();\n      }\n      // Else, just overwrite the value\n    } else {\n      var _currentInstance$key;\n      currentInstance[key] = value;\n\n      // Auto-convert sRGB textures, for now ...\n      // https://github.com/pmndrs/react-three-fiber/issues/344\n      if ((_currentInstance$key = currentInstance[key]) != null && _currentInstance$key.isTexture &&\n      // sRGB textures must be RGBA8 since r137 https://github.com/mrdoob/three.js/pull/23129\n      currentInstance[key].format === THREE.RGBAFormat && currentInstance[key].type === THREE.UnsignedByteType && rootState) {\n        const texture = currentInstance[key];\n        if (hasColorSpace(texture) && hasColorSpace(rootState.gl)) texture.colorSpace = rootState.gl.outputColorSpace;else texture.encoding = rootState.gl.outputEncoding;\n      }\n    }\n    invalidateInstance(instance);\n  }\n  if (localState && localState.parent && instance.raycast && prevHandlers !== localState.eventCount) {\n    // Get the initial root state's internals\n    const internal = findInitialRoot(instance).getState().internal;\n    // Pre-emptively remove the instance from the interaction manager\n    const index = internal.interaction.indexOf(instance);\n    if (index > -1) internal.interaction.splice(index, 1);\n    // Add the instance to the interaction manager only when it has handlers\n    if (localState.eventCount) internal.interaction.push(instance);\n  }\n\n  // Call the update lifecycle when it is being updated, but only when it is part of the scene.\n  // Skip updates to the `onUpdate` prop itself\n  const isCircular = changes.length === 1 && changes[0][0] === 'onUpdate';\n  if (!isCircular && changes.length && (_instance$__r3f2 = instance.__r3f) != null && _instance$__r3f2.parent) updateInstance(instance);\n  return instance;\n}\nfunction invalidateInstance(instance) {\n  var _instance$__r3f3, _instance$__r3f3$root;\n  const state = (_instance$__r3f3 = instance.__r3f) == null ? void 0 : (_instance$__r3f3$root = _instance$__r3f3.root) == null ? void 0 : _instance$__r3f3$root.getState == null ? void 0 : _instance$__r3f3$root.getState();\n  if (state && state.internal.frames === 0) state.invalidate();\n}\nfunction updateInstance(instance) {\n  instance.onUpdate == null ? void 0 : instance.onUpdate(instance);\n}\nfunction updateCamera(camera, size) {\n  // https://github.com/pmndrs/react-three-fiber/issues/92\n  // Do not mess with the camera if it belongs to the user\n  if (!camera.manual) {\n    if (isOrthographicCamera(camera)) {\n      camera.left = size.width / -2;\n      camera.right = size.width / 2;\n      camera.top = size.height / 2;\n      camera.bottom = size.height / -2;\n    } else {\n      camera.aspect = size.width / size.height;\n    }\n    camera.updateProjectionMatrix();\n    // https://github.com/pmndrs/react-three-fiber/issues/178\n    // Update matrix world since the renderer is a frame late\n    camera.updateMatrixWorld();\n  }\n}\n\nfunction makeId(event) {\n  return (event.eventObject || event.object).uuid + '/' + event.index + event.instanceId;\n}\n\n// https://github.com/facebook/react/tree/main/packages/react-reconciler#getcurrenteventpriority\n// Gives React a clue as to how import the current interaction is\nfunction getEventPriority() {\n  var _globalScope$event;\n  // Get a handle to the current global scope in window and worker contexts if able\n  // https://github.com/pmndrs/react-three-fiber/pull/2493\n  const globalScope = typeof self !== 'undefined' && self || typeof window !== 'undefined' && window;\n  if (!globalScope) return DefaultEventPriority;\n  const name = (_globalScope$event = globalScope.event) == null ? void 0 : _globalScope$event.type;\n  switch (name) {\n    case 'click':\n    case 'contextmenu':\n    case 'dblclick':\n    case 'pointercancel':\n    case 'pointerdown':\n    case 'pointerup':\n      return DiscreteEventPriority;\n    case 'pointermove':\n    case 'pointerout':\n    case 'pointerover':\n    case 'pointerenter':\n    case 'pointerleave':\n    case 'wheel':\n      return ContinuousEventPriority;\n    default:\n      return DefaultEventPriority;\n  }\n}\n\n/**\r\n * Release pointer captures.\r\n * This is called by releasePointerCapture in the API, and when an object is removed.\r\n */\nfunction releaseInternalPointerCapture(capturedMap, obj, captures, pointerId) {\n  const captureData = captures.get(obj);\n  if (captureData) {\n    captures.delete(obj);\n    // If this was the last capturing object for this pointer\n    if (captures.size === 0) {\n      capturedMap.delete(pointerId);\n      captureData.target.releasePointerCapture(pointerId);\n    }\n  }\n}\nfunction removeInteractivity(store, object) {\n  const {\n    internal\n  } = store.getState();\n  // Removes every trace of an object from the data store\n  internal.interaction = internal.interaction.filter(o => o !== object);\n  internal.initialHits = internal.initialHits.filter(o => o !== object);\n  internal.hovered.forEach((value, key) => {\n    if (value.eventObject === object || value.object === object) {\n      // Clear out intersects, they are outdated by now\n      internal.hovered.delete(key);\n    }\n  });\n  internal.capturedMap.forEach((captures, pointerId) => {\n    releaseInternalPointerCapture(internal.capturedMap, object, captures, pointerId);\n  });\n}\nfunction createEvents(store) {\n  /** Calculates delta */\n  function calculateDistance(event) {\n    const {\n      internal\n    } = store.getState();\n    const dx = event.offsetX - internal.initialClick[0];\n    const dy = event.offsetY - internal.initialClick[1];\n    return Math.round(Math.sqrt(dx * dx + dy * dy));\n  }\n\n  /** Returns true if an instance has a valid pointer-event registered, this excludes scroll, clicks etc */\n  function filterPointerEvents(objects) {\n    return objects.filter(obj => ['Move', 'Over', 'Enter', 'Out', 'Leave'].some(name => {\n      var _r3f;\n      return (_r3f = obj.__r3f) == null ? void 0 : _r3f.handlers['onPointer' + name];\n    }));\n  }\n  function intersect(event, filter) {\n    const state = store.getState();\n    const duplicates = new Set();\n    const intersections = [];\n    // Allow callers to eliminate event objects\n    const eventsObjects = filter ? filter(state.internal.interaction) : state.internal.interaction;\n    // Reset all raycaster cameras to undefined\n    for (let i = 0; i < eventsObjects.length; i++) {\n      const state = getRootState(eventsObjects[i]);\n      if (state) {\n        state.raycaster.camera = undefined;\n      }\n    }\n    if (!state.previousRoot) {\n      // Make sure root-level pointer and ray are set up\n      state.events.compute == null ? void 0 : state.events.compute(event, state);\n    }\n    function handleRaycast(obj) {\n      const state = getRootState(obj);\n      // Skip event handling when noEvents is set, or when the raycasters camera is null\n      if (!state || !state.events.enabled || state.raycaster.camera === null) return [];\n\n      // When the camera is undefined we have to call the event layers update function\n      if (state.raycaster.camera === undefined) {\n        var _state$previousRoot;\n        state.events.compute == null ? void 0 : state.events.compute(event, state, (_state$previousRoot = state.previousRoot) == null ? void 0 : _state$previousRoot.getState());\n        // If the camera is still undefined we have to skip this layer entirely\n        if (state.raycaster.camera === undefined) state.raycaster.camera = null;\n      }\n\n      // Intersect object by object\n      return state.raycaster.camera ? state.raycaster.intersectObject(obj, true) : [];\n    }\n\n    // Collect events\n    let hits = eventsObjects\n    // Intersect objects\n    .flatMap(handleRaycast)\n    // Sort by event priority and distance\n    .sort((a, b) => {\n      const aState = getRootState(a.object);\n      const bState = getRootState(b.object);\n      if (!aState || !bState) return a.distance - b.distance;\n      return bState.events.priority - aState.events.priority || a.distance - b.distance;\n    })\n    // Filter out duplicates\n    .filter(item => {\n      const id = makeId(item);\n      if (duplicates.has(id)) return false;\n      duplicates.add(id);\n      return true;\n    });\n\n    // https://github.com/mrdoob/three.js/issues/16031\n    // Allow custom userland intersect sort order, this likely only makes sense on the root filter\n    if (state.events.filter) hits = state.events.filter(hits, state);\n\n    // Bubble up the events, find the event source (eventObject)\n    for (const hit of hits) {\n      let eventObject = hit.object;\n      // Bubble event up\n      while (eventObject) {\n        var _r3f2;\n        if ((_r3f2 = eventObject.__r3f) != null && _r3f2.eventCount) intersections.push({\n          ...hit,\n          eventObject\n        });\n        eventObject = eventObject.parent;\n      }\n    }\n\n    // If the interaction is captured, make all capturing targets part of the intersect.\n    if ('pointerId' in event && state.internal.capturedMap.has(event.pointerId)) {\n      for (let captureData of state.internal.capturedMap.get(event.pointerId).values()) {\n        if (!duplicates.has(makeId(captureData.intersection))) intersections.push(captureData.intersection);\n      }\n    }\n    return intersections;\n  }\n\n  /**  Handles intersections by forwarding them to handlers */\n  function handleIntersects(intersections, event, delta, callback) {\n    const rootState = store.getState();\n\n    // If anything has been found, forward it to the event listeners\n    if (intersections.length) {\n      const localState = {\n        stopped: false\n      };\n      for (const hit of intersections) {\n        const state = getRootState(hit.object) || rootState;\n        const {\n          raycaster,\n          pointer,\n          camera,\n          internal\n        } = state;\n        const unprojectedPoint = new THREE.Vector3(pointer.x, pointer.y, 0).unproject(camera);\n        const hasPointerCapture = id => {\n          var _internal$capturedMap, _internal$capturedMap2;\n          return (_internal$capturedMap = (_internal$capturedMap2 = internal.capturedMap.get(id)) == null ? void 0 : _internal$capturedMap2.has(hit.eventObject)) != null ? _internal$capturedMap : false;\n        };\n        const setPointerCapture = id => {\n          const captureData = {\n            intersection: hit,\n            target: event.target\n          };\n          if (internal.capturedMap.has(id)) {\n            // if the pointerId was previously captured, we add the hit to the\n            // event capturedMap.\n            internal.capturedMap.get(id).set(hit.eventObject, captureData);\n          } else {\n            // if the pointerId was not previously captured, we create a map\n            // containing the hitObject, and the hit. hitObject is used for\n            // faster access.\n            internal.capturedMap.set(id, new Map([[hit.eventObject, captureData]]));\n          }\n          event.target.setPointerCapture(id);\n        };\n        const releasePointerCapture = id => {\n          const captures = internal.capturedMap.get(id);\n          if (captures) {\n            releaseInternalPointerCapture(internal.capturedMap, hit.eventObject, captures, id);\n          }\n        };\n\n        // Add native event props\n        let extractEventProps = {};\n        // This iterates over the event's properties including the inherited ones. Native PointerEvents have most of their props as getters which are inherited, but polyfilled PointerEvents have them all as their own properties (i.e. not inherited). We can't use Object.keys() or Object.entries() as they only return \"own\" properties; nor Object.getPrototypeOf(event) as that *doesn't* return \"own\" properties, only inherited ones.\n        for (let prop in event) {\n          let property = event[prop];\n          // Only copy over atomics, leave functions alone as these should be\n          // called as event.nativeEvent.fn()\n          if (typeof property !== 'function') extractEventProps[prop] = property;\n        }\n        let raycastEvent = {\n          ...hit,\n          ...extractEventProps,\n          pointer,\n          intersections,\n          stopped: localState.stopped,\n          delta,\n          unprojectedPoint,\n          ray: raycaster.ray,\n          camera: camera,\n          // Hijack stopPropagation, which just sets a flag\n          stopPropagation() {\n            // https://github.com/pmndrs/react-three-fiber/issues/596\n            // Events are not allowed to stop propagation if the pointer has been captured\n            const capturesForPointer = 'pointerId' in event && internal.capturedMap.get(event.pointerId);\n\n            // We only authorize stopPropagation...\n            if (\n            // ...if this pointer hasn't been captured\n            !capturesForPointer ||\n            // ... or if the hit object is capturing the pointer\n            capturesForPointer.has(hit.eventObject)) {\n              raycastEvent.stopped = localState.stopped = true;\n              // Propagation is stopped, remove all other hover records\n              // An event handler is only allowed to flush other handlers if it is hovered itself\n              if (internal.hovered.size && Array.from(internal.hovered.values()).find(i => i.eventObject === hit.eventObject)) {\n                // Objects cannot flush out higher up objects that have already caught the event\n                const higher = intersections.slice(0, intersections.indexOf(hit));\n                cancelPointer([...higher, hit]);\n              }\n            }\n          },\n          // there should be a distinction between target and currentTarget\n          target: {\n            hasPointerCapture,\n            setPointerCapture,\n            releasePointerCapture\n          },\n          currentTarget: {\n            hasPointerCapture,\n            setPointerCapture,\n            releasePointerCapture\n          },\n          nativeEvent: event\n        };\n\n        // Call subscribers\n        callback(raycastEvent);\n        // Event bubbling may be interrupted by stopPropagation\n        if (localState.stopped === true) break;\n      }\n    }\n    return intersections;\n  }\n  function cancelPointer(intersections) {\n    const {\n      internal\n    } = store.getState();\n    for (const hoveredObj of internal.hovered.values()) {\n      // When no objects were hit or the the hovered object wasn't found underneath the cursor\n      // we call onPointerOut and delete the object from the hovered-elements map\n      if (!intersections.length || !intersections.find(hit => hit.object === hoveredObj.object && hit.index === hoveredObj.index && hit.instanceId === hoveredObj.instanceId)) {\n        const eventObject = hoveredObj.eventObject;\n        const instance = eventObject.__r3f;\n        const handlers = instance == null ? void 0 : instance.handlers;\n        internal.hovered.delete(makeId(hoveredObj));\n        if (instance != null && instance.eventCount) {\n          // Clear out intersects, they are outdated by now\n          const data = {\n            ...hoveredObj,\n            intersections\n          };\n          handlers.onPointerOut == null ? void 0 : handlers.onPointerOut(data);\n          handlers.onPointerLeave == null ? void 0 : handlers.onPointerLeave(data);\n        }\n      }\n    }\n  }\n  function pointerMissed(event, objects) {\n    for (let i = 0; i < objects.length; i++) {\n      const instance = objects[i].__r3f;\n      instance == null ? void 0 : instance.handlers.onPointerMissed == null ? void 0 : instance.handlers.onPointerMissed(event);\n    }\n  }\n  function handlePointer(name) {\n    // Deal with cancelation\n    switch (name) {\n      case 'onPointerLeave':\n      case 'onPointerCancel':\n        return () => cancelPointer([]);\n      case 'onLostPointerCapture':\n        return event => {\n          const {\n            internal\n          } = store.getState();\n          if ('pointerId' in event && internal.capturedMap.has(event.pointerId)) {\n            // If the object event interface had onLostPointerCapture, we'd call it here on every\n            // object that's getting removed. We call it on the next frame because onLostPointerCapture\n            // fires before onPointerUp. Otherwise pointerUp would never be called if the event didn't\n            // happen in the object it originated from, leaving components in a in-between state.\n            requestAnimationFrame(() => {\n              // Only release if pointer-up didn't do it already\n              if (internal.capturedMap.has(event.pointerId)) {\n                internal.capturedMap.delete(event.pointerId);\n                cancelPointer([]);\n              }\n            });\n          }\n        };\n    }\n\n    // Any other pointer goes here ...\n    return function handleEvent(event) {\n      const {\n        onPointerMissed,\n        internal\n      } = store.getState();\n\n      // prepareRay(event)\n      internal.lastEvent.current = event;\n\n      // Get fresh intersects\n      const isPointerMove = name === 'onPointerMove';\n      const isClickEvent = name === 'onClick' || name === 'onContextMenu' || name === 'onDoubleClick';\n      const filter = isPointerMove ? filterPointerEvents : undefined;\n      const hits = intersect(event, filter);\n      const delta = isClickEvent ? calculateDistance(event) : 0;\n\n      // Save initial coordinates on pointer-down\n      if (name === 'onPointerDown') {\n        internal.initialClick = [event.offsetX, event.offsetY];\n        internal.initialHits = hits.map(hit => hit.eventObject);\n      }\n\n      // If a click yields no results, pass it back to the user as a miss\n      // Missed events have to come first in order to establish user-land side-effect clean up\n      if (isClickEvent && !hits.length) {\n        if (delta <= 2) {\n          pointerMissed(event, internal.interaction);\n          if (onPointerMissed) onPointerMissed(event);\n        }\n      }\n      // Take care of unhover\n      if (isPointerMove) cancelPointer(hits);\n      function onIntersect(data) {\n        const eventObject = data.eventObject;\n        const instance = eventObject.__r3f;\n        const handlers = instance == null ? void 0 : instance.handlers;\n\n        // Check presence of handlers\n        if (!(instance != null && instance.eventCount)) return;\n\n        /*\r\n        MAYBE TODO, DELETE IF NOT: \r\n          Check if the object is captured, captured events should not have intersects running in parallel\r\n          But wouldn't it be better to just replace capturedMap with a single entry?\r\n          Also, are we OK with straight up making picking up multiple objects impossible?\r\n          \r\n        const pointerId = (data as ThreeEvent<PointerEvent>).pointerId        \r\n        if (pointerId !== undefined) {\r\n          const capturedMeshSet = internal.capturedMap.get(pointerId)\r\n          if (capturedMeshSet) {\r\n            const captured = capturedMeshSet.get(eventObject)\r\n            if (captured && captured.localState.stopped) return\r\n          }\r\n        }*/\n\n        if (isPointerMove) {\n          // Move event ...\n          if (handlers.onPointerOver || handlers.onPointerEnter || handlers.onPointerOut || handlers.onPointerLeave) {\n            // When enter or out is present take care of hover-state\n            const id = makeId(data);\n            const hoveredItem = internal.hovered.get(id);\n            if (!hoveredItem) {\n              // If the object wasn't previously hovered, book it and call its handler\n              internal.hovered.set(id, data);\n              handlers.onPointerOver == null ? void 0 : handlers.onPointerOver(data);\n              handlers.onPointerEnter == null ? void 0 : handlers.onPointerEnter(data);\n            } else if (hoveredItem.stopped) {\n              // If the object was previously hovered and stopped, we shouldn't allow other items to proceed\n              data.stopPropagation();\n            }\n          }\n          // Call mouse move\n          handlers.onPointerMove == null ? void 0 : handlers.onPointerMove(data);\n        } else {\n          // All other events ...\n          const handler = handlers[name];\n          if (handler) {\n            // Forward all events back to their respective handlers with the exception of click events,\n            // which must use the initial target\n            if (!isClickEvent || internal.initialHits.includes(eventObject)) {\n              // Missed events have to come first\n              pointerMissed(event, internal.interaction.filter(object => !internal.initialHits.includes(object)));\n              // Now call the handler\n              handler(data);\n            }\n          } else {\n            // Trigger onPointerMissed on all elements that have pointer over/out handlers, but not click and weren't hit\n            if (isClickEvent && internal.initialHits.includes(eventObject)) {\n              pointerMissed(event, internal.interaction.filter(object => !internal.initialHits.includes(object)));\n            }\n          }\n        }\n      }\n      handleIntersects(hits, event, delta, onIntersect);\n    };\n  }\n  return {\n    handlePointer\n  };\n}\n\n// Keys that shouldn't be copied between R3F stores\nconst privateKeys = ['set', 'get', 'setSize', 'setFrameloop', 'setDpr', 'events', 'invalidate', 'advance', 'size', 'viewport'];\nconst isRenderer = def => !!(def != null && def.render);\nconst context = /*#__PURE__*/React.createContext(null);\nconst createStore = (invalidate, advance) => {\n  const rootState = create((set, get) => {\n    const position = new THREE.Vector3();\n    const defaultTarget = new THREE.Vector3();\n    const tempTarget = new THREE.Vector3();\n    function getCurrentViewport(camera = get().camera, target = defaultTarget, size = get().size) {\n      const {\n        width,\n        height,\n        top,\n        left\n      } = size;\n      const aspect = width / height;\n      if (target.isVector3) tempTarget.copy(target);else tempTarget.set(...target);\n      const distance = camera.getWorldPosition(position).distanceTo(tempTarget);\n      if (isOrthographicCamera(camera)) {\n        return {\n          width: width / camera.zoom,\n          height: height / camera.zoom,\n          top,\n          left,\n          factor: 1,\n          distance,\n          aspect\n        };\n      } else {\n        const fov = camera.fov * Math.PI / 180; // convert vertical fov to radians\n        const h = 2 * Math.tan(fov / 2) * distance; // visible height\n        const w = h * (width / height);\n        return {\n          width: w,\n          height: h,\n          top,\n          left,\n          factor: width / w,\n          distance,\n          aspect\n        };\n      }\n    }\n    let performanceTimeout = undefined;\n    const setPerformanceCurrent = current => set(state => ({\n      performance: {\n        ...state.performance,\n        current\n      }\n    }));\n    const pointer = new THREE.Vector2();\n    const rootState = {\n      set,\n      get,\n      // Mock objects that have to be configured\n      gl: null,\n      camera: null,\n      raycaster: null,\n      events: {\n        priority: 1,\n        enabled: true,\n        connected: false\n      },\n      xr: null,\n      scene: null,\n      invalidate: (frames = 1) => invalidate(get(), frames),\n      advance: (timestamp, runGlobalEffects) => advance(timestamp, runGlobalEffects, get()),\n      legacy: false,\n      linear: false,\n      flat: false,\n      controls: null,\n      clock: new THREE.Clock(),\n      pointer,\n      mouse: pointer,\n      frameloop: 'always',\n      onPointerMissed: undefined,\n      performance: {\n        current: 1,\n        min: 0.5,\n        max: 1,\n        debounce: 200,\n        regress: () => {\n          const state = get();\n          // Clear timeout\n          if (performanceTimeout) clearTimeout(performanceTimeout);\n          // Set lower bound performance\n          if (state.performance.current !== state.performance.min) setPerformanceCurrent(state.performance.min);\n          // Go back to upper bound performance after a while unless something regresses meanwhile\n          performanceTimeout = setTimeout(() => setPerformanceCurrent(get().performance.max), state.performance.debounce);\n        }\n      },\n      size: {\n        width: 0,\n        height: 0,\n        top: 0,\n        left: 0,\n        updateStyle: false\n      },\n      viewport: {\n        initialDpr: 0,\n        dpr: 0,\n        width: 0,\n        height: 0,\n        top: 0,\n        left: 0,\n        aspect: 0,\n        distance: 0,\n        factor: 0,\n        getCurrentViewport\n      },\n      setEvents: events => set(state => ({\n        ...state,\n        events: {\n          ...state.events,\n          ...events\n        }\n      })),\n      setSize: (width, height, updateStyle, top, left) => {\n        const camera = get().camera;\n        const size = {\n          width,\n          height,\n          top: top || 0,\n          left: left || 0,\n          updateStyle\n        };\n        set(state => ({\n          size,\n          viewport: {\n            ...state.viewport,\n            ...getCurrentViewport(camera, defaultTarget, size)\n          }\n        }));\n      },\n      setDpr: dpr => set(state => {\n        const resolved = calculateDpr(dpr);\n        return {\n          viewport: {\n            ...state.viewport,\n            dpr: resolved,\n            initialDpr: state.viewport.initialDpr || resolved\n          }\n        };\n      }),\n      setFrameloop: (frameloop = 'always') => {\n        const clock = get().clock;\n\n        // if frameloop === \"never\" clock.elapsedTime is updated using advance(timestamp)\n        clock.stop();\n        clock.elapsedTime = 0;\n        if (frameloop !== 'never') {\n          clock.start();\n          clock.elapsedTime = 0;\n        }\n        set(() => ({\n          frameloop\n        }));\n      },\n      previousRoot: undefined,\n      internal: {\n        active: false,\n        priority: 0,\n        frames: 0,\n        lastEvent: /*#__PURE__*/React.createRef(),\n        interaction: [],\n        hovered: new Map(),\n        subscribers: [],\n        initialClick: [0, 0],\n        initialHits: [],\n        capturedMap: new Map(),\n        subscribe: (ref, priority, store) => {\n          const internal = get().internal;\n          // If this subscription was given a priority, it takes rendering into its own hands\n          // For that reason we switch off automatic rendering and increase the manual flag\n          // As long as this flag is positive there can be no internal rendering at all\n          // because there could be multiple render subscriptions\n          internal.priority = internal.priority + (priority > 0 ? 1 : 0);\n          internal.subscribers.push({\n            ref,\n            priority,\n            store\n          });\n          // Register subscriber and sort layers from lowest to highest, meaning,\n          // highest priority renders last (on top of the other frames)\n          internal.subscribers = internal.subscribers.sort((a, b) => a.priority - b.priority);\n          return () => {\n            const internal = get().internal;\n            if (internal != null && internal.subscribers) {\n              // Decrease manual flag if this subscription had a priority\n              internal.priority = internal.priority - (priority > 0 ? 1 : 0);\n              // Remove subscriber from list\n              internal.subscribers = internal.subscribers.filter(s => s.ref !== ref);\n            }\n          };\n        }\n      }\n    };\n    return rootState;\n  });\n  const state = rootState.getState();\n  let oldSize = state.size;\n  let oldDpr = state.viewport.dpr;\n  let oldCamera = state.camera;\n  rootState.subscribe(() => {\n    const {\n      camera,\n      size,\n      viewport,\n      gl,\n      set\n    } = rootState.getState();\n\n    // Resize camera and renderer on changes to size and pixelratio\n    if (size.width !== oldSize.width || size.height !== oldSize.height || viewport.dpr !== oldDpr) {\n      var _size$updateStyle;\n      oldSize = size;\n      oldDpr = viewport.dpr;\n      // Update camera & renderer\n      updateCamera(camera, size);\n      gl.setPixelRatio(viewport.dpr);\n      const updateStyle = (_size$updateStyle = size.updateStyle) != null ? _size$updateStyle : typeof HTMLCanvasElement !== 'undefined' && gl.domElement instanceof HTMLCanvasElement;\n      gl.setSize(size.width, size.height, updateStyle);\n    }\n\n    // Update viewport once the camera changes\n    if (camera !== oldCamera) {\n      oldCamera = camera;\n      // Update viewport\n      set(state => ({\n        viewport: {\n          ...state.viewport,\n          ...state.viewport.getCurrentViewport(camera)\n        }\n      }));\n    }\n  });\n\n  // Invalidate on any change\n  rootState.subscribe(state => invalidate(state));\n\n  // Return root state\n  return rootState;\n};\n\nfunction createSubs(callback, subs) {\n  const sub = {\n    callback\n  };\n  subs.add(sub);\n  return () => void subs.delete(sub);\n}\nlet i;\nlet globalEffects = new Set();\nlet globalAfterEffects = new Set();\nlet globalTailEffects = new Set();\n\n/**\r\n * Adds a global render callback which is called each frame.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addEffect\r\n */\nconst addEffect = callback => createSubs(callback, globalEffects);\n\n/**\r\n * Adds a global after-render callback which is called each frame.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addAfterEffect\r\n */\nconst addAfterEffect = callback => createSubs(callback, globalAfterEffects);\n\n/**\r\n * Adds a global callback which is called when rendering stops.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#addTail\r\n */\nconst addTail = callback => createSubs(callback, globalTailEffects);\nfunction run(effects, timestamp) {\n  if (!effects.size) return;\n  for (const {\n    callback\n  } of effects.values()) {\n    callback(timestamp);\n  }\n}\nfunction flushGlobalEffects(type, timestamp) {\n  switch (type) {\n    case 'before':\n      return run(globalEffects, timestamp);\n    case 'after':\n      return run(globalAfterEffects, timestamp);\n    case 'tail':\n      return run(globalTailEffects, timestamp);\n  }\n}\nlet subscribers;\nlet subscription;\nfunction render$1(timestamp, state, frame) {\n  // Run local effects\n  let delta = state.clock.getDelta();\n  // In frameloop='never' mode, clock times are updated using the provided timestamp\n  if (state.frameloop === 'never' && typeof timestamp === 'number') {\n    delta = timestamp - state.clock.elapsedTime;\n    state.clock.oldTime = state.clock.elapsedTime;\n    state.clock.elapsedTime = timestamp;\n  }\n  // Call subscribers (useFrame)\n  subscribers = state.internal.subscribers;\n  for (i = 0; i < subscribers.length; i++) {\n    subscription = subscribers[i];\n    subscription.ref.current(subscription.store.getState(), delta, frame);\n  }\n  // Render content\n  if (!state.internal.priority && state.gl.render) state.gl.render(state.scene, state.camera);\n  // Decrease frame count\n  state.internal.frames = Math.max(0, state.internal.frames - 1);\n  return state.frameloop === 'always' ? 1 : state.internal.frames;\n}\nfunction createLoop(roots) {\n  let running = false;\n  let useFrameInProgress = false;\n  let repeat;\n  let frame;\n  let state;\n  function loop(timestamp) {\n    frame = requestAnimationFrame(loop);\n    running = true;\n    repeat = 0;\n\n    // Run effects\n    flushGlobalEffects('before', timestamp);\n\n    // Render all roots\n    useFrameInProgress = true;\n    for (const root of roots.values()) {\n      var _state$gl$xr;\n      state = root.store.getState();\n      // If the frameloop is invalidated, do not run another frame\n      if (state.internal.active && (state.frameloop === 'always' || state.internal.frames > 0) && !((_state$gl$xr = state.gl.xr) != null && _state$gl$xr.isPresenting)) {\n        repeat += render$1(timestamp, state);\n      }\n    }\n    useFrameInProgress = false;\n\n    // Run after-effects\n    flushGlobalEffects('after', timestamp);\n\n    // Stop the loop if nothing invalidates it\n    if (repeat === 0) {\n      // Tail call effects, they are called when rendering stops\n      flushGlobalEffects('tail', timestamp);\n\n      // Flag end of operation\n      running = false;\n      return cancelAnimationFrame(frame);\n    }\n  }\n  function invalidate(state, frames = 1) {\n    var _state$gl$xr2;\n    if (!state) return roots.forEach(root => invalidate(root.store.getState(), frames));\n    if ((_state$gl$xr2 = state.gl.xr) != null && _state$gl$xr2.isPresenting || !state.internal.active || state.frameloop === 'never') return;\n    if (frames > 1) {\n      // legacy support for people using frames parameters\n      // Increase frames, do not go higher than 60\n      state.internal.frames = Math.min(60, state.internal.frames + frames);\n    } else {\n      if (useFrameInProgress) {\n        //called from within a useFrame, it means the user wants an additional frame\n        state.internal.frames = 2;\n      } else {\n        //the user need a new frame, no need to increment further than 1\n        state.internal.frames = 1;\n      }\n    }\n\n    // If the render-loop isn't active, start it\n    if (!running) {\n      running = true;\n      requestAnimationFrame(loop);\n    }\n  }\n  function advance(timestamp, runGlobalEffects = true, state, frame) {\n    if (runGlobalEffects) flushGlobalEffects('before', timestamp);\n    if (!state) for (const root of roots.values()) render$1(timestamp, root.store.getState());else render$1(timestamp, state, frame);\n    if (runGlobalEffects) flushGlobalEffects('after', timestamp);\n  }\n  return {\n    loop,\n    invalidate,\n    advance\n  };\n}\n\n/**\r\n * Exposes an object's {@link LocalState}.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/additional-exports#useInstanceHandle\r\n *\r\n * **Note**: this is an escape hatch to react-internal fields. Expect this to change significantly between versions.\r\n */\nfunction useInstanceHandle(ref) {\n  const instance = React.useRef(null);\n  useIsomorphicLayoutEffect(() => void (instance.current = ref.current.__r3f), [ref]);\n  return instance;\n}\nfunction useStore() {\n  const store = React.useContext(context);\n  if (!store) throw new Error('R3F: Hooks can only be used within the Canvas component!');\n  return store;\n}\n\n/**\r\n * Accesses R3F's internal state, containing renderer, canvas, scene, etc.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#usethree\r\n */\nfunction useThree(selector = state => state, equalityFn) {\n  return useStore()(selector, equalityFn);\n}\n\n/**\r\n * Executes a callback before render in a shared frame loop.\r\n * Can order effects with render priority or manually render with a positive priority.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#useframe\r\n */\nfunction useFrame(callback, renderPriority = 0) {\n  const store = useStore();\n  const subscribe = store.getState().internal.subscribe;\n  // Memoize ref\n  const ref = useMutableCallback(callback);\n  // Subscribe on mount, unsubscribe on unmount\n  useIsomorphicLayoutEffect(() => subscribe(ref, renderPriority, store), [renderPriority, subscribe, store]);\n  return null;\n}\n\n/**\r\n * Returns a node graph of an object with named nodes & materials.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#usegraph\r\n */\nfunction useGraph(object) {\n  return React.useMemo(() => buildGraph(object), [object]);\n}\nconst memoizedLoaders = new WeakMap();\nfunction loadingFn(extensions, onProgress) {\n  return function (Proto, ...input) {\n    // Construct new loader and run extensions\n    let loader = memoizedLoaders.get(Proto);\n    if (!loader) {\n      loader = new Proto();\n      memoizedLoaders.set(Proto, loader);\n    }\n    if (extensions) extensions(loader);\n    // Go through the urls and load them\n    return Promise.all(input.map(input => new Promise((res, reject) => loader.load(input, data => {\n      if (data.scene) Object.assign(data, buildGraph(data.scene));\n      res(data);\n    }, onProgress, error => reject(new Error(`Could not load ${input}: ${error == null ? void 0 : error.message}`))))));\n  };\n}\n/**\r\n * Synchronously loads and caches assets with a three loader.\r\n *\r\n * Note: this hook's caller must be wrapped with `React.Suspense`\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/hooks#useloader\r\n */\nfunction useLoader(Proto, input, extensions, onProgress) {\n  // Use suspense to load async assets\n  const keys = Array.isArray(input) ? input : [input];\n  const results = suspend(loadingFn(extensions, onProgress), [Proto, ...keys], {\n    equal: is.equ\n  });\n  // Return the object/s\n  return Array.isArray(input) ? results : results[0];\n}\n\n/**\r\n * Preloads an asset into cache as a side-effect.\r\n */\nuseLoader.preload = function (Proto, input, extensions) {\n  const keys = Array.isArray(input) ? input : [input];\n  return preload(loadingFn(extensions), [Proto, ...keys]);\n};\n\n/**\r\n * Removes a loaded asset from cache.\r\n */\nuseLoader.clear = function (Proto, input) {\n  const keys = Array.isArray(input) ? input : [input];\n  return clear([Proto, ...keys]);\n};\n\nconst roots = new Map();\nconst {\n  invalidate,\n  advance\n} = createLoop(roots);\nconst {\n  reconciler,\n  applyProps\n} = createRenderer(roots, getEventPriority);\nconst shallowLoose = {\n  objects: 'shallow',\n  strict: false\n};\nconst createRendererInstance = (gl, canvas) => {\n  const customRenderer = typeof gl === 'function' ? gl(canvas) : gl;\n  if (isRenderer(customRenderer)) return customRenderer;else return new THREE.WebGLRenderer({\n    powerPreference: 'high-performance',\n    canvas: canvas,\n    antialias: true,\n    alpha: true,\n    ...gl\n  });\n};\nfunction computeInitialSize(canvas, defaultSize) {\n  const defaultStyle = typeof HTMLCanvasElement !== 'undefined' && canvas instanceof HTMLCanvasElement;\n  if (defaultSize) {\n    const {\n      width,\n      height,\n      top,\n      left,\n      updateStyle = defaultStyle\n    } = defaultSize;\n    return {\n      width,\n      height,\n      top,\n      left,\n      updateStyle\n    };\n  } else if (typeof HTMLCanvasElement !== 'undefined' && canvas instanceof HTMLCanvasElement && canvas.parentElement) {\n    const {\n      width,\n      height,\n      top,\n      left\n    } = canvas.parentElement.getBoundingClientRect();\n    return {\n      width,\n      height,\n      top,\n      left,\n      updateStyle: defaultStyle\n    };\n  } else if (typeof OffscreenCanvas !== 'undefined' && canvas instanceof OffscreenCanvas) {\n    return {\n      width: canvas.width,\n      height: canvas.height,\n      top: 0,\n      left: 0,\n      updateStyle: defaultStyle\n    };\n  }\n  return {\n    width: 0,\n    height: 0,\n    top: 0,\n    left: 0\n  };\n}\nfunction createRoot(canvas) {\n  // Check against mistaken use of createRoot\n  const prevRoot = roots.get(canvas);\n  const prevFiber = prevRoot == null ? void 0 : prevRoot.fiber;\n  const prevStore = prevRoot == null ? void 0 : prevRoot.store;\n  if (prevRoot) console.warn('R3F.createRoot should only be called once!');\n\n  // Report when an error was detected in a previous render\n  // https://github.com/pmndrs/react-three-fiber/pull/2261\n  const logRecoverableError = typeof reportError === 'function' ?\n  // In modern browsers, reportError will dispatch an error event,\n  // emulating an uncaught JavaScript error.\n  reportError :\n  // In older browsers and test environments, fallback to console.error.\n  console.error;\n\n  // Create store\n  const store = prevStore || createStore(invalidate, advance);\n  // Create renderer\n  const fiber = prevFiber || reconciler.createContainer(store, ConcurrentRoot, null, false, null, '', logRecoverableError, null);\n  // Map it\n  if (!prevRoot) roots.set(canvas, {\n    fiber,\n    store\n  });\n\n  // Locals\n  let onCreated;\n  let configured = false;\n  let lastCamera;\n  return {\n    configure(props = {}) {\n      let {\n        gl: glConfig,\n        size: propsSize,\n        scene: sceneOptions,\n        events,\n        onCreated: onCreatedCallback,\n        shadows = false,\n        linear = false,\n        flat = false,\n        legacy = false,\n        orthographic = false,\n        frameloop = 'always',\n        dpr = [1, 2],\n        performance,\n        raycaster: raycastOptions,\n        camera: cameraOptions,\n        onPointerMissed\n      } = props;\n      let state = store.getState();\n\n      // Set up renderer (one time only!)\n      let gl = state.gl;\n      if (!state.gl) state.set({\n        gl: gl = createRendererInstance(glConfig, canvas)\n      });\n\n      // Set up raycaster (one time only!)\n      let raycaster = state.raycaster;\n      if (!raycaster) state.set({\n        raycaster: raycaster = new THREE.Raycaster()\n      });\n\n      // Set raycaster options\n      const {\n        params,\n        ...options\n      } = raycastOptions || {};\n      if (!is.equ(options, raycaster, shallowLoose)) applyProps(raycaster, {\n        ...options\n      });\n      if (!is.equ(params, raycaster.params, shallowLoose)) applyProps(raycaster, {\n        params: {\n          ...raycaster.params,\n          ...params\n        }\n      });\n\n      // Create default camera, don't overwrite any user-set state\n      if (!state.camera || state.camera === lastCamera && !is.equ(lastCamera, cameraOptions, shallowLoose)) {\n        lastCamera = cameraOptions;\n        const isCamera = cameraOptions instanceof THREE.Camera;\n        const camera = isCamera ? cameraOptions : orthographic ? new THREE.OrthographicCamera(0, 0, 0, 0, 0.1, 1000) : new THREE.PerspectiveCamera(75, 0, 0.1, 1000);\n        if (!isCamera) {\n          camera.position.z = 5;\n          if (cameraOptions) {\n            applyProps(camera, cameraOptions);\n            // Preserve user-defined frustum if possible\n            // https://github.com/pmndrs/react-three-fiber/issues/3160\n            if ('aspect' in cameraOptions || 'left' in cameraOptions || 'right' in cameraOptions || 'bottom' in cameraOptions || 'top' in cameraOptions) {\n              camera.manual = true;\n              camera.updateProjectionMatrix();\n            }\n          }\n          // Always look at center by default\n          if (!state.camera && !(cameraOptions != null && cameraOptions.rotation)) camera.lookAt(0, 0, 0);\n        }\n        state.set({\n          camera\n        });\n\n        // Configure raycaster\n        // https://github.com/pmndrs/react-xr/issues/300\n        raycaster.camera = camera;\n      }\n\n      // Set up scene (one time only!)\n      if (!state.scene) {\n        let scene;\n        if (sceneOptions != null && sceneOptions.isScene) {\n          scene = sceneOptions;\n        } else {\n          scene = new THREE.Scene();\n          if (sceneOptions) applyProps(scene, sceneOptions);\n        }\n        state.set({\n          scene: prepare(scene)\n        });\n      }\n\n      // Set up XR (one time only!)\n      if (!state.xr) {\n        var _gl$xr;\n        // Handle frame behavior in WebXR\n        const handleXRFrame = (timestamp, frame) => {\n          const state = store.getState();\n          if (state.frameloop === 'never') return;\n          advance(timestamp, true, state, frame);\n        };\n\n        // Toggle render switching on session\n        const handleSessionChange = () => {\n          const state = store.getState();\n          state.gl.xr.enabled = state.gl.xr.isPresenting;\n          state.gl.xr.setAnimationLoop(state.gl.xr.isPresenting ? handleXRFrame : null);\n          if (!state.gl.xr.isPresenting) invalidate(state);\n        };\n\n        // WebXR session manager\n        const xr = {\n          connect() {\n            const gl = store.getState().gl;\n            gl.xr.addEventListener('sessionstart', handleSessionChange);\n            gl.xr.addEventListener('sessionend', handleSessionChange);\n          },\n          disconnect() {\n            const gl = store.getState().gl;\n            gl.xr.removeEventListener('sessionstart', handleSessionChange);\n            gl.xr.removeEventListener('sessionend', handleSessionChange);\n          }\n        };\n\n        // Subscribe to WebXR session events\n        if (typeof ((_gl$xr = gl.xr) == null ? void 0 : _gl$xr.addEventListener) === 'function') xr.connect();\n        state.set({\n          xr\n        });\n      }\n\n      // Set shadowmap\n      if (gl.shadowMap) {\n        const oldEnabled = gl.shadowMap.enabled;\n        const oldType = gl.shadowMap.type;\n        gl.shadowMap.enabled = !!shadows;\n        if (is.boo(shadows)) {\n          gl.shadowMap.type = THREE.PCFSoftShadowMap;\n        } else if (is.str(shadows)) {\n          var _types$shadows;\n          const types = {\n            basic: THREE.BasicShadowMap,\n            percentage: THREE.PCFShadowMap,\n            soft: THREE.PCFSoftShadowMap,\n            variance: THREE.VSMShadowMap\n          };\n          gl.shadowMap.type = (_types$shadows = types[shadows]) != null ? _types$shadows : THREE.PCFSoftShadowMap;\n        } else if (is.obj(shadows)) {\n          Object.assign(gl.shadowMap, shadows);\n        }\n        if (oldEnabled !== gl.shadowMap.enabled || oldType !== gl.shadowMap.type) gl.shadowMap.needsUpdate = true;\n      }\n\n      // Safely set color management if available.\n      // Avoid accessing THREE.ColorManagement to play nice with older versions\n      const ColorManagement = getColorManagement();\n      if (ColorManagement) {\n        if ('enabled' in ColorManagement) ColorManagement.enabled = !legacy;else if ('legacyMode' in ColorManagement) ColorManagement.legacyMode = legacy;\n      }\n      if (!configured) {\n        // Set color space and tonemapping preferences, once\n        const LinearEncoding = 3000;\n        const sRGBEncoding = 3001;\n        applyProps(gl, {\n          outputEncoding: linear ? LinearEncoding : sRGBEncoding,\n          toneMapping: flat ? THREE.NoToneMapping : THREE.ACESFilmicToneMapping\n        });\n      }\n\n      // Update color management state\n      if (state.legacy !== legacy) state.set(() => ({\n        legacy\n      }));\n      if (state.linear !== linear) state.set(() => ({\n        linear\n      }));\n      if (state.flat !== flat) state.set(() => ({\n        flat\n      }));\n\n      // Set gl props\n      if (glConfig && !is.fun(glConfig) && !isRenderer(glConfig) && !is.equ(glConfig, gl, shallowLoose)) applyProps(gl, glConfig);\n      // Store events internally\n      if (events && !state.events.handlers) state.set({\n        events: events(store)\n      });\n      // Check size, allow it to take on container bounds initially\n      const size = computeInitialSize(canvas, propsSize);\n      if (!is.equ(size, state.size, shallowLoose)) {\n        state.setSize(size.width, size.height, size.updateStyle, size.top, size.left);\n      }\n      // Check pixelratio\n      if (dpr && state.viewport.dpr !== calculateDpr(dpr)) state.setDpr(dpr);\n      // Check frameloop\n      if (state.frameloop !== frameloop) state.setFrameloop(frameloop);\n      // Check pointer missed\n      if (!state.onPointerMissed) state.set({\n        onPointerMissed\n      });\n      // Check performance\n      if (performance && !is.equ(performance, state.performance, shallowLoose)) state.set(state => ({\n        performance: {\n          ...state.performance,\n          ...performance\n        }\n      }));\n\n      // Set locals\n      onCreated = onCreatedCallback;\n      configured = true;\n      return this;\n    },\n    render(children) {\n      // The root has to be configured before it can be rendered\n      if (!configured) this.configure();\n      reconciler.updateContainer( /*#__PURE__*/jsx(Provider, {\n        store: store,\n        children: children,\n        onCreated: onCreated,\n        rootElement: canvas\n      }), fiber, null, () => undefined);\n      return store;\n    },\n    unmount() {\n      unmountComponentAtNode(canvas);\n    }\n  };\n}\nfunction render(children, canvas, config) {\n  console.warn('R3F.render is no longer supported in React 18. Use createRoot instead!');\n  const root = createRoot(canvas);\n  root.configure(config);\n  return root.render(children);\n}\nfunction Provider({\n  store,\n  children,\n  onCreated,\n  rootElement\n}) {\n  useIsomorphicLayoutEffect(() => {\n    const state = store.getState();\n    // Flag the canvas active, rendering will now begin\n    state.set(state => ({\n      internal: {\n        ...state.internal,\n        active: true\n      }\n    }));\n    // Notifiy that init is completed, the scene graph exists, but nothing has yet rendered\n    if (onCreated) onCreated(state);\n    // Connect events to the targets parent, this is done to ensure events are registered on\n    // a shared target, and not on the canvas itself\n    if (!store.getState().events.connected) state.events.connect == null ? void 0 : state.events.connect(rootElement);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  return /*#__PURE__*/jsx(context.Provider, {\n    value: store,\n    children: children\n  });\n}\nfunction unmountComponentAtNode(canvas, callback) {\n  const root = roots.get(canvas);\n  const fiber = root == null ? void 0 : root.fiber;\n  if (fiber) {\n    const state = root == null ? void 0 : root.store.getState();\n    if (state) state.internal.active = false;\n    reconciler.updateContainer(null, fiber, null, () => {\n      if (state) {\n        setTimeout(() => {\n          try {\n            var _state$gl, _state$gl$renderLists, _state$gl2, _state$gl3;\n            state.events.disconnect == null ? void 0 : state.events.disconnect();\n            (_state$gl = state.gl) == null ? void 0 : (_state$gl$renderLists = _state$gl.renderLists) == null ? void 0 : _state$gl$renderLists.dispose == null ? void 0 : _state$gl$renderLists.dispose();\n            (_state$gl2 = state.gl) == null ? void 0 : _state$gl2.forceContextLoss == null ? void 0 : _state$gl2.forceContextLoss();\n            if ((_state$gl3 = state.gl) != null && _state$gl3.xr) state.xr.disconnect();\n            dispose(state);\n            roots.delete(canvas);\n            if (callback) callback(canvas);\n          } catch (e) {\n            /* ... */\n          }\n        }, 500);\n      }\n    });\n  }\n}\nfunction createPortal(children, container, state) {\n  return /*#__PURE__*/jsx(Portal, {\n    children: children,\n    container: container,\n    state: state\n  }, container.uuid);\n}\nfunction Portal({\n  state = {},\n  children,\n  container\n}) {\n  /** This has to be a component because it would not be able to call useThree/useStore otherwise since\r\n   *  if this is our environment, then we are not in r3f's renderer but in react-dom, it would trigger\r\n   *  the \"R3F hooks can only be used within the Canvas component!\" warning:\r\n   *  <Canvas>\r\n   *    {createPortal(...)} */\n  const {\n    events,\n    size,\n    ...rest\n  } = state;\n  const previousRoot = useStore();\n  const [raycaster] = React.useState(() => new THREE.Raycaster());\n  const [pointer] = React.useState(() => new THREE.Vector2());\n  const inject = React.useCallback((rootState, injectState) => {\n    const intersect = {\n      ...rootState\n    }; // all prev state props\n\n    // Only the fields of \"rootState\" that do not differ from injectState\n    // Some props should be off-limits\n    // Otherwise filter out the props that are different and let the inject layer take precedence\n    Object.keys(rootState).forEach(key => {\n      if (\n      // Some props should be off-limits\n      privateKeys.includes(key) ||\n      // Otherwise filter out the props that are different and let the inject layer take precedence\n      // Unless the inject layer props is undefined, then we keep the root layer\n      rootState[key] !== injectState[key] && injectState[key]) {\n        delete intersect[key];\n      }\n    });\n    let viewport = undefined;\n    if (injectState && size) {\n      const camera = injectState.camera;\n      // Calculate the override viewport, if present\n      viewport = rootState.viewport.getCurrentViewport(camera, new THREE.Vector3(), size);\n      // Update the portal camera, if it differs from the previous layer\n      if (camera !== rootState.camera) updateCamera(camera, size);\n    }\n    return {\n      // The intersect consists of the previous root state\n      ...intersect,\n      // Portals have their own scene, which forms the root, a raycaster and a pointer\n      scene: container,\n      raycaster,\n      pointer,\n      mouse: pointer,\n      // Their previous root is the layer before it\n      previousRoot,\n      // Events, size and viewport can be overridden by the inject layer\n      events: {\n        ...rootState.events,\n        ...(injectState == null ? void 0 : injectState.events),\n        ...events\n      },\n      size: {\n        ...rootState.size,\n        ...size\n      },\n      viewport: {\n        ...rootState.viewport,\n        ...viewport\n      },\n      ...rest\n    };\n  },\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [state]);\n  const [usePortalStore] = React.useState(() => {\n    // Create a mirrored store, based on the previous root with a few overrides ...\n    const previousState = previousRoot.getState();\n    const store = create((set, get) => ({\n      ...previousState,\n      scene: container,\n      raycaster,\n      pointer,\n      mouse: pointer,\n      previousRoot,\n      events: {\n        ...previousState.events,\n        ...events\n      },\n      size: {\n        ...previousState.size,\n        ...size\n      },\n      ...rest,\n      // Set and get refer to this root-state\n      set,\n      get,\n      // Layers are allowed to override events\n      setEvents: events => set(state => ({\n        ...state,\n        events: {\n          ...state.events,\n          ...events\n        }\n      }))\n    }));\n    return store;\n  });\n  React.useEffect(() => {\n    // Subscribe to previous root-state and copy changes over to the mirrored portal-state\n    const unsub = previousRoot.subscribe(prev => usePortalStore.setState(state => inject(prev, state)));\n    return () => {\n      unsub();\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [inject]);\n  React.useEffect(() => {\n    usePortalStore.setState(injectState => inject(previousRoot.getState(), injectState));\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [inject]);\n  React.useEffect(() => {\n    return () => {\n      usePortalStore.destroy();\n    };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  return /*#__PURE__*/jsx(Fragment, {\n    children: reconciler.createPortal( /*#__PURE__*/jsx(context.Provider, {\n      value: usePortalStore,\n      children: children\n    }), usePortalStore, null)\n  });\n}\n\n/**\r\n * Force React to flush any updates inside the provided callback synchronously and immediately.\r\n * All the same caveats documented for react-dom's `flushSync` apply here (see https://react.dev/reference/react-dom/flushSync).\r\n * Nevertheless, sometimes one needs to render synchronously, for example to keep DOM and 3D changes in lock-step without\r\n * having to revert to a non-React solution.\r\n */\nfunction flushSync(fn) {\n  // `flushSync` implementation only takes one argument. I don't know what's up with the type declaration for it.\n  return reconciler.flushSync(fn, undefined);\n}\nreconciler.injectIntoDevTools({\n  bundleType: process.env.NODE_ENV === 'production' ? 0 : 1,\n  rendererPackageName: '@react-three/fiber',\n  version: React.version\n});\nconst act = React.unstable_act;\n\nconst DOM_EVENTS = {\n  onClick: ['click', false],\n  onContextMenu: ['contextmenu', false],\n  onDoubleClick: ['dblclick', false],\n  onWheel: ['wheel', true],\n  onPointerDown: ['pointerdown', true],\n  onPointerUp: ['pointerup', true],\n  onPointerLeave: ['pointerleave', true],\n  onPointerMove: ['pointermove', true],\n  onPointerCancel: ['pointercancel', true],\n  onLostPointerCapture: ['lostpointercapture', true]\n};\n\n/** Default R3F event manager for web */\nfunction createPointerEvents(store) {\n  const {\n    handlePointer\n  } = createEvents(store);\n  return {\n    priority: 1,\n    enabled: true,\n    compute(event, state, previous) {\n      // https://github.com/pmndrs/react-three-fiber/pull/782\n      // Events trigger outside of canvas when moved, use offsetX/Y by default and allow overrides\n      state.pointer.set(event.offsetX / state.size.width * 2 - 1, -(event.offsetY / state.size.height) * 2 + 1);\n      state.raycaster.setFromCamera(state.pointer, state.camera);\n    },\n    connected: undefined,\n    handlers: Object.keys(DOM_EVENTS).reduce((acc, key) => ({\n      ...acc,\n      [key]: handlePointer(key)\n    }), {}),\n    update: () => {\n      var _internal$lastEvent;\n      const {\n        events,\n        internal\n      } = store.getState();\n      if ((_internal$lastEvent = internal.lastEvent) != null && _internal$lastEvent.current && events.handlers) events.handlers.onPointerMove(internal.lastEvent.current);\n    },\n    connect: target => {\n      var _events$handlers;\n      const {\n        set,\n        events\n      } = store.getState();\n      events.disconnect == null ? void 0 : events.disconnect();\n      set(state => ({\n        events: {\n          ...state.events,\n          connected: target\n        }\n      }));\n      Object.entries((_events$handlers = events.handlers) != null ? _events$handlers : []).forEach(([name, event]) => {\n        const [eventName, passive] = DOM_EVENTS[name];\n        target.addEventListener(eventName, event, {\n          passive\n        });\n      });\n    },\n    disconnect: () => {\n      const {\n        set,\n        events\n      } = store.getState();\n      if (events.connected) {\n        var _events$handlers2;\n        Object.entries((_events$handlers2 = events.handlers) != null ? _events$handlers2 : []).forEach(([name, event]) => {\n          if (events && events.connected instanceof HTMLElement) {\n            const [eventName] = DOM_EVENTS[name];\n            events.connected.removeEventListener(eventName, event);\n          }\n        });\n        set(state => ({\n          events: {\n            ...state.events,\n            connected: undefined\n          }\n        }));\n      }\n    }\n  };\n}\n\nexport { useInstanceHandle as A, Block as B, useStore as C, useThree as D, ErrorBoundary as E, useFrame as F, useGraph as G, useLoader as H, useIsomorphicLayoutEffect as a, createRoot as b, createPointerEvents as c, unmountComponentAtNode as d, extend as e, createEvents as f, context as g, createPortal as h, isRef as i, reconciler as j, applyProps as k, dispose as l, invalidate as m, advance as n, addEffect as o, addAfterEffect as p, addTail as q, render as r, flushGlobalEffects as s, threeTypes as t, useMutableCallback as u, flushSync as v, getRootState as w, act as x, buildGraph as y, roots as z };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgoBuB;AAhoBvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAEA,IAAI,aAAa,WAAW,GAAE,OAAO,MAAM,CAAC;IAC1C,WAAW;AACb;AAEA,MAAM,YAAY,CAAC;AACnB,MAAM,SAAS,CAAA,UAAW,KAAK,OAAO,MAAM,CAAC,WAAW;AACxD,SAAS,eAAe,MAAM,EAAE,iBAAiB;IAC/C,SAAS,eAAe,IAAI,EAAE,EAC5B,OAAO,EAAE,EACT,MAAM,EACN,GAAG,OACJ,EAAE,IAAI;QACL,IAAI,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC,WAAW,KAAK,KAAK,KAAK,CAAC,IAAI;QACrD,IAAI;QACJ,IAAI,SAAS,aAAa;YACxB,IAAI,MAAM,MAAM,KAAK,WAAW,MAAM,IAAI,MAAM;YAChD,MAAM,SAAS,MAAM,MAAM;YAC3B,WAAW,QAAQ,QAAQ;gBACzB;gBACA;gBACA;gBACA,WAAW;YACb;QACF,OAAO;YACL,MAAM,SAAS,SAAS,CAAC,KAAK;YAC9B,IAAI,CAAC,QAAQ;gBACX,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,KAAK,4JAA4J,CAAC;YAC5L;YAEA,oDAAoD;YACpD,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO,MAAM,IAAI,MAAM;YAE1C,8CAA8C;YAC9C,wDAAwD;YACxD,WAAW,QAAQ,IAAI,UAAU,OAAO;gBACtC;gBACA;gBACA;gBACA,yDAAyD;gBACzD,eAAe;oBACb;gBACF;YACF;QACF;QAEA,uCAAuC;QACvC,IAAI,SAAS,KAAK,CAAC,MAAM,KAAK,WAAW;YACvC,IAAI,SAAS,gBAAgB,EAAE,SAAS,KAAK,CAAC,MAAM,GAAG;iBAAgB,IAAI,SAAS,UAAU,EAAE,SAAS,KAAK,CAAC,MAAM,GAAG;QAC1H;QAEA,2FAA2F;QAC3F,gGAAgG;QAChG,4BAA4B;QAC5B,+CAA+C;QAC/C,IAAI,SAAS,UAAU,aAAa,UAAU;QAC9C,OAAO;IACT;IACA,SAAS,YAAY,cAAc,EAAE,KAAK;QACxC,IAAI,QAAQ;QACZ,IAAI,OAAO;YACT,IAAI,cAAc;YAClB,6EAA6E;YAC7E,IAAI,CAAC,eAAe,MAAM,KAAK,KAAK,QAAQ,aAAa,MAAM,EAAE;gBAC/D,OAAO,gBAAgB,OAAO,MAAM,KAAK,CAAC,MAAM;YAClD,OAAO,IAAI,MAAM,UAAU,IAAI,eAAe,UAAU,EAAE;gBACxD,oCAAoC;gBACpC,eAAe,GAAG,CAAC;gBACnB,QAAQ;YACV;YACA,iGAAiG;YACjG,+EAA+E;YAC/E,IAAI,CAAC,OAAO,CAAC,wBAAwB,eAAe,KAAK,KAAK,OAAO,KAAK,IAAI,sBAAsB,OAAO,CAAC,IAAI,CAAC;YACjH,IAAI,CAAC,MAAM,KAAK,EAAE,QAAQ,OAAO,CAAC;YAClC,MAAM,KAAK,CAAC,MAAM,GAAG;YACrB,eAAe;YACf,mBAAmB;QACrB;IACF;IACA,SAAS,aAAa,cAAc,EAAE,KAAK,EAAE,WAAW;QACtD,IAAI,QAAQ;QACZ,IAAI,OAAO;YACT,IAAI,eAAe;YACnB,IAAI,CAAC,gBAAgB,MAAM,KAAK,KAAK,QAAQ,cAAc,MAAM,EAAE;gBACjE,OAAO,gBAAgB,OAAO,MAAM,KAAK,CAAC,MAAM;YAClD,OAAO,IAAI,MAAM,UAAU,IAAI,eAAe,UAAU,EAAE;gBACxD,MAAM,MAAM,GAAG;gBACf,MAAM,aAAa,CAAC;oBAClB,MAAM;gBACR;gBACA,eAAe,aAAa,CAAC;oBAC3B,MAAM;oBACN;gBACF;gBACA,MAAM,eAAe,eAAe,QAAQ,CAAC,MAAM,CAAC,CAAA,UAAW,YAAY;gBAC3E,MAAM,QAAQ,aAAa,OAAO,CAAC;gBACnC,eAAe,QAAQ,GAAG;uBAAI,aAAa,KAAK,CAAC,GAAG;oBAAQ;uBAAU,aAAa,KAAK,CAAC;iBAAO;gBAChG,QAAQ;YACV;YACA,IAAI,CAAC,OAAO,CAAC,yBAAyB,eAAe,KAAK,KAAK,OAAO,KAAK,IAAI,uBAAuB,OAAO,CAAC,IAAI,CAAC;YACnH,IAAI,CAAC,MAAM,KAAK,EAAE,QAAQ,OAAO,CAAC;YAClC,MAAM,KAAK,CAAC,MAAM,GAAG;YACrB,eAAe;YACf,mBAAmB;QACrB;IACF;IACA,SAAS,gBAAgB,KAAK,EAAE,MAAM,EAAE,UAAU,KAAK;QACrD,IAAI,OAAO;eAAI;SAAM,CAAC,OAAO,CAAC,CAAA,QAAS,YAAY,QAAQ,OAAO;IACpE;IACA,SAAS,YAAY,cAAc,EAAE,KAAK,EAAE,OAAO;QACjD,IAAI,OAAO;YACT,IAAI,wBAAwB,eAAe;YAC3C,6BAA6B;YAC7B,IAAI,MAAM,KAAK,EAAE,MAAM,KAAK,CAAC,MAAM,GAAG;YACtC,wCAAwC;YACxC,IAAI,CAAC,yBAAyB,eAAe,KAAK,KAAK,QAAQ,uBAAuB,OAAO,EAAE,eAAe,KAAK,CAAC,OAAO,GAAG,eAAe,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM;YAC7K,oBAAoB;YACpB,IAAI,CAAC,gBAAgB,MAAM,KAAK,KAAK,QAAQ,cAAc,MAAM,EAAE;gBACjE,OAAO,gBAAgB,OAAO,MAAM,KAAK,CAAC,MAAM;YAClD,OAAO,IAAI,MAAM,UAAU,IAAI,eAAe,UAAU,EAAE;gBACxD,IAAI;gBACJ,eAAe,MAAM,CAAC;gBACtB,mBAAmB;gBACnB,2CAA2C;gBAC3C,IAAI,CAAC,gBAAgB,MAAM,KAAK,KAAK,QAAQ,cAAc,IAAI,EAAE;oBAC/D,oBAAoB,gBAAgB,QAAQ;gBAC9C;YACF;YAEA,sFAAsF;YACtF,gFAAgF;YAChF,8DAA8D;YAC9D,wBAAwB;YACxB,gDAAgD;YAChD,wEAAwE;YACxE,EAAE;YACF,8FAA8F;YAC9F,yEAAyE;YACzE,MAAM,cAAc,CAAC,gBAAgB,MAAM,KAAK,KAAK,OAAO,KAAK,IAAI,cAAc,SAAS;YAC5F,MAAM,gBAAgB,CAAC,eAAe,CAAC,YAAY,YAAY,MAAM,OAAO,KAAK,OAAO,OAAO;YAE/F,wFAAwF;YACxF,qCAAqC;YACrC,IAAI,CAAC,aAAa;gBAChB,IAAI;gBACJ,gBAAgB,CAAC,gBAAgB,MAAM,KAAK,KAAK,OAAO,KAAK,IAAI,cAAc,OAAO,EAAE,OAAO;gBAC/F,gBAAgB,MAAM,QAAQ,EAAE,OAAO;YACzC;YAEA,oBAAoB;YACpB,OAAO,MAAM,KAAK;YAElB,qDAAqD;YACrD,IAAI,iBAAiB,MAAM,OAAO,IAAI,MAAM,IAAI,KAAK,SAAS;gBAC5D,MAAM,WAAW;oBACf,IAAI;wBACF,MAAM,OAAO;oBACf,EAAE,OAAO,GAAG;oBACV,OAAO,GACT;gBACF;gBAEA,mDAAmD;gBACnD,IAAI,OAAO,6BAA6B,aAAa;oBACnD,CAAA,GAAA,qIAAA,CAAA,4BAAyB,AAAD,EAAE,qIAAA,CAAA,wBAAqB,EAAE;gBACnD,OAAO;oBACL;gBACF;YACF;YACA,mBAAmB;QACrB;IACF;IACA,SAAS,eAAe,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK;QACrD,IAAI;QACJ,MAAM,SAAS,CAAC,kBAAkB,SAAS,KAAK,KAAK,OAAO,KAAK,IAAI,gBAAgB,MAAM;QAC3F,IAAI,CAAC,QAAQ;QACb,MAAM,cAAc,eAAe,MAAM,UAAU,SAAS,KAAK,CAAC,IAAI;QAEtE,0DAA0D;QAC1D,qEAAqE;QACrE,6DAA6D;QAC7D,IAAI,SAAS,QAAQ,EAAE;YACrB,KAAK,MAAM,SAAS,SAAS,QAAQ,CAAE;gBACrC,IAAI,MAAM,KAAK,EAAE,YAAY,aAAa;YAC5C;YACA,SAAS,QAAQ,GAAG,SAAS,QAAQ,CAAC,MAAM,CAAC,CAAA,QAAS,CAAC,MAAM,KAAK;QACpE;QACA,SAAS,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAA,QAAS,YAAY,aAAa;QACjE,SAAS,KAAK,CAAC,OAAO,GAAG,EAAE;QAC3B,IAAI,CAAC,SAAS,KAAK,CAAC,uBAAuB,EAAE;YAC3C,YAAY,QAAQ;QACtB;QACA,IAAI,YAAY,MAAM,EAAE;YACtB,YAAY,KAAK,CAAC,uBAAuB,GAAG;QAC9C;QACA,YAAY,QAAQ;QAEpB,6CAA6C;QAC7C,IAAI,YAAY,OAAO,IAAI,YAAY,KAAK,CAAC,UAAU,EAAE;YACvD,MAAM,YAAY,gBAAgB,aAAa,QAAQ;YACvD,UAAU,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC;QACtC;QACA;YAAC;YAAO,MAAM,SAAS;SAAC,CAAC,OAAO,CAAC,CAAA;YAC/B,IAAI,UAAU,MAAM;gBAClB,MAAM,SAAS,GAAG;gBAClB,IAAI,MAAM,GAAG,EAAE;oBACb,IAAI,OAAO,MAAM,GAAG,KAAK,YAAY,MAAM,GAAG,CAAC;yBAAkB,MAAM,GAAG,CAAC,OAAO,GAAG;gBACvF;YACF;QACF;IACF;IAEA,6CAA6C;IAC7C,MAAM,qBAAqB,KAAO;IAClC,MAAM,aAAa,CAAA,GAAA,+IAAA,CAAA,UAAU,AAAD,EAAE;QAC5B;QACA;QACA;QACA,oBAAoB;QACpB;QACA,kBAAkB;QAClB,mBAAmB;QACnB,qBAAqB;QACrB,mBAAmB;QACnB,WAAW,CAAC;QACZ,wBAAwB,CAAC,WAAW;YAClC,IAAI,CAAC,OAAO;YAEZ,sCAAsC;YACtC,MAAM,QAAQ,UAAU,QAAQ,GAAG,KAAK;YACxC,IAAI,CAAC,MAAM,KAAK,EAAE;YAElB,yCAAyC;YACzC,MAAM,KAAK,CAAC,IAAI,GAAG;YACnB,YAAY,OAAO;QACrB;QACA,0BAA0B,CAAC,WAAW;YACpC,IAAI,CAAC,OAAO;YACZ,YAAY,UAAU,QAAQ,GAAG,KAAK,EAAE;QAC1C;QACA,yBAAyB,CAAC,WAAW,OAAO;YAC1C,IAAI,CAAC,SAAS,CAAC,aAAa;YAE5B,sCAAsC;YACtC,MAAM,QAAQ,UAAU,QAAQ,GAAG,KAAK;YACxC,IAAI,CAAC,MAAM,KAAK,EAAE;YAClB,aAAa,OAAO,OAAO;QAC7B;QACA,oBAAoB,IAAM;QAC1B,qBAAqB,CAAA,oBAAqB;QAC1C,yBAAwB,QAAQ;YAC9B,IAAI;YACJ,MAAM,aAAa,CAAC,mBAAmB,YAAY,OAAO,KAAK,IAAI,SAAS,KAAK,KAAK,OAAO,mBAAmB,CAAC;YACjH,iDAAiD;YACjD,0CAA0C;YAC1C,OAAO,QAAQ,WAAW,QAAQ;QACpC;QACA,eAAc,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ;YAC/C,IAAI;YACJ,MAAM,aAAa,CAAC,mBAAmB,YAAY,OAAO,KAAK,IAAI,SAAS,KAAK,KAAK,OAAO,mBAAmB,CAAC;YAEjH,mBAAmB;YACnB,IAAI,WAAW,SAAS,IAAI,SAAS,MAAM,IAAI,SAAS,MAAM,KAAK,UAAU;gBAC3E,OAAO;oBAAC;iBAAK;YACf,OAAO;gBACL,qEAAqE;gBACrE,MAAM,EACJ,MAAM,UAAU,EAAE,EAClB,UAAU,EAAE,EACZ,GAAG,SACJ,GAAG;gBACJ,MAAM,EACJ,MAAM,UAAU,EAAE,EAClB,UAAU,EAAE,EACZ,GAAG,SACJ,GAAG;gBAEJ,oDAAoD;gBACpD,IAAI,CAAC,MAAM,OAAO,CAAC,UAAU,MAAM,IAAI,MAAM;gBAE7C,wEAAwE;gBACxE,IAAI,QAAQ,IAAI,CAAC,CAAC,OAAO,QAAU,UAAU,OAAO,CAAC,MAAM,GAAG,OAAO;oBAAC;iBAAK;gBAC3E,mDAAmD;gBACnD,MAAM,OAAO,UAAU,UAAU,SAAS,SAAS;gBACnD,IAAI,KAAK,OAAO,CAAC,MAAM,EAAE,OAAO;oBAAC;oBAAO;iBAAK;gBAE7C,sCAAsC;gBACtC,OAAO;YACT;QACF;QACA,cAAa,QAAQ,EAAE,CAAC,aAAa,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK;YAC1E,gEAAgE;YAChE,IAAI,aAAa,eAAe,UAAU,MAAM,UAAU;iBAErD,aAAa,UAAU;QAC9B;QACA,aAAY,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,IAAI;YACvC,IAAI;YACJ,iDAAiD;YACjD,8FAA8F;YAC9F,MAAM,aAAa,CAAC,mBAAmB,SAAS,KAAK,KAAK,OAAO,mBAAmB,CAAC;YACrF,IAAI,SAAS,OAAO,IAAI,WAAW,QAAQ,IAAI,WAAW,UAAU,EAAE;gBACpE,gBAAgB,UAAU,QAAQ,GAAG,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC;YACjE;QACF;QACA,mBAAmB,CAAA,WAAY;QAC/B,kBAAkB,IAAM;QACxB,oBAAoB,CAAA,YAAa,QAAQ,UAAU,QAAQ,GAAG,KAAK;QACnE,kBAAkB,KAAO;QACzB,sBAAsB,IAAM;QAC5B,gBAAgB,IAAM;QACtB,cAAa,QAAQ;YACnB,IAAI;YACJ,sCAAsC;YACtC,MAAM,EACJ,QAAQ,IAAI,EACZ,MAAM,EACP,GAAG,CAAC,mBAAmB,SAAS,KAAK,KAAK,OAAO,mBAAmB,CAAC;YACtE,IAAI,QAAQ,QAAQ,OAAO,QAAQ,UAAU;YAC7C,IAAI,SAAS,UAAU,EAAE,SAAS,OAAO,GAAG;YAC5C,mBAAmB;QACrB;QACA,gBAAe,QAAQ,EAAE,KAAK;YAC5B,IAAI;YACJ,0CAA0C;YAC1C,MAAM,EACJ,QAAQ,IAAI,EACZ,MAAM,EACP,GAAG,CAAC,mBAAmB,SAAS,KAAK,KAAK,OAAO,mBAAmB,CAAC;YACtE,IAAI,QAAQ,QAAQ,OAAO,QAAQ,UAAU;YAC7C,IAAI,SAAS,UAAU,IAAI,MAAM,OAAO,IAAI,QAAQ,MAAM,OAAO,EAAE,SAAS,OAAO,GAAG;YACtF,mBAAmB;QACrB;QACA,oBAAoB;QACpB,kBAAkB;QAClB,oBAAoB;QACpB,8EAA8E;QAC9E,mBAAmB;QACnB,yBAAyB,IAAM,oBAAoB,sBAAsB,mJAAA,CAAA,uBAAoB;QAC7F,0BAA0B,KAAO;QACjC,yBAAyB,KAAO;QAChC,uBAAuB,KAAO;QAC9B,KAAK,OAAO,gBAAgB,eAAe,GAAG,GAAG,CAAC,YAAY,GAAG,IAAI,YAAY,GAAG,GAAG,GAAG,GAAG,CAAC,KAAK,GAAG,IAAI,KAAK,GAAG,GAAG,IAAM;QAC3H,8EAA8E;QAC9E,iBAAiB,GAAG,GAAG,CAAC,cAAc,aAAa;QACnD,eAAe,GAAG,GAAG,CAAC,gBAAgB,eAAe;IACvD;IACA,OAAO;QACL;QACA,YAAY;IACd;AACF;AAEA,IAAI,kBAAkB;AACtB;;CAEC,GACD,MAAM,gBAAgB,CAAA,SAAU,gBAAgB,UAAU,sBAAsB;AAChF;;CAEC,GACD,MAAM,qBAAqB;IACzB,IAAI;IACJ,OAAO,CAAC,mBAAmB,UAAU,eAAe,KAAK,OAAO,mBAAmB;AACrF;AACA,MAAM,uBAAuB,CAAA,MAAO,OAAO,IAAI,oBAAoB;AACnE,MAAM,QAAQ,CAAA,MAAO,OAAO,IAAI,cAAc,CAAC;AAE/C;;;;;;;;CAQC,GACD,MAAM,4BAA4B,aAAkB,eAAe,CAAC,CAAC,mBAAmB,OAAO,QAAQ,KAAK,QAAQ,iBAAiB,aAAa,IAAI,CAAC,CAAC,oBAAoB,OAAO,SAAS,KAAK,OAAO,KAAK,IAAI,kBAAkB,OAAO,MAAM,aAAa,IAAI,6JAAA,CAAA,kBAAqB,GAAG,6JAAA,CAAA,YAAe;AACxS,SAAS,mBAAmB,EAAE;;IAC5B,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAY,AAAD,EAAE;IACzB;wDAA0B,IAAM,KAAK,CAAC,IAAI,OAAO,GAAG,EAAE;uDAAG;QAAC;KAAG;IAC7D,OAAO;AACT;GAJS;;QAEP;;;AAGF,SAAS,MAAM,EACb,GAAG,EACJ;;IACC;2CAA0B;YACxB,IAAI,IAAI;mDAAQ,IAAM;;YACtB;mDAAO,IAAM,IAAI;;QACnB;0CAAG;QAAC;KAAI;IACR,OAAO;AACT;IARS;;QAGP;;;KAHO;AAST,MAAM,sBAAsB,6JAAA,CAAA,YAAe;IACzC,YAAY,GAAG,IAAI,CAAE;QACnB,KAAK,IAAI;QACT,IAAI,CAAC,KAAK,GAAG;YACX,OAAO;QACT;IACF;IACA,kBAAkB,GAAG,EAAE;QACrB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;IACjB;IACA,SAAS;QACP,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IACtD;AACF;AACA,cAAc,wBAAwB,GAAG,IAAM,CAAC;QAC9C,OAAO;IACT,CAAC;AACD,MAAM,UAAU;AAChB,MAAM,WAAW,IAAI;AACrB,MAAM,YAAY,CAAA,MAAO,OAAO,CAAC,CAAC,IAAI,QAAQ,IAAI,CAAC,CAAC,IAAI,OAAO;AAC/D,SAAS,aAAa,GAAG;IACvB,IAAI;IACJ,uEAAuE;IACvE,qEAAqE;IACrE,MAAM,SAAS,uCAAgC,CAAC,wBAAwB,OAAO,gBAAgB,KAAK,OAAO,wBAAwB;IACnI,OAAO,MAAM,OAAO,CAAC,OAAO,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,EAAE,EAAE,SAAS,GAAG,CAAC,EAAE,IAAI;AAC3E;AAEA;;CAEC,GACD,MAAM,eAAe,CAAA;IACnB,IAAI;IACJ,OAAO,CAAC,OAAO,IAAI,KAAK,KAAK,OAAO,KAAK,IAAI,KAAK,IAAI,CAAC,QAAQ;AACjE;AAEA;;CAEC,GACD,SAAS,gBAAgB,KAAK;IAC5B,IAAI,OAAO,MAAM,KAAK,CAAC,IAAI;IAC3B,MAAO,KAAK,QAAQ,GAAG,YAAY,CAAE,OAAO,KAAK,QAAQ,GAAG,YAAY;IACxE,OAAO;AACT;AACA,oCAAoC;AACpC,MAAM,KAAK;IACT,KAAK,CAAA,IAAK,MAAM,OAAO,MAAM,CAAC,GAAG,GAAG,CAAC,MAAM,OAAO,MAAM;IACxD,KAAK,CAAA,IAAK,OAAO,MAAM;IACvB,KAAK,CAAA,IAAK,OAAO,MAAM;IACvB,KAAK,CAAA,IAAK,OAAO,MAAM;IACvB,KAAK,CAAA,IAAK,OAAO,MAAM;IACvB,KAAK,CAAA,IAAK,MAAM,KAAK;IACrB,KAAK,CAAA,IAAK,MAAM,OAAO,CAAC;IACxB,KAAI,CAAC,EAAE,CAAC,EAAE,EACR,SAAS,SAAS,EAClB,UAAU,WAAW,EACrB,SAAS,IAAI,EACd,GAAG,CAAC,CAAC;QACJ,wDAAwD;QACxD,IAAI,OAAO,MAAM,OAAO,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,OAAO;QACjD,mCAAmC;QACnC,IAAI,GAAG,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,OAAO,MAAM;QACtD,MAAM,QAAQ,GAAG,GAAG,CAAC;QACrB,IAAI,SAAS,YAAY,aAAa,OAAO,MAAM;QACnD,MAAM,QAAQ,GAAG,GAAG,CAAC;QACrB,IAAI,SAAS,WAAW,aAAa,OAAO,MAAM;QAClD,gEAAgE;QAChE,IAAI,CAAC,SAAS,KAAK,KAAK,MAAM,GAAG,OAAO;QACxC,+BAA+B;QAC/B,IAAI;QACJ,mCAAmC;QACnC,IAAK,KAAK,EAAG,IAAI,CAAC,CAAC,KAAK,CAAC,GAAG,OAAO;QACnC,qCAAqC;QACrC,IAAI,SAAS,WAAW,aAAa,YAAY,WAAW;YAC1D,IAAK,KAAK,SAAS,IAAI,EAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE;gBAChD;gBACA,SAAS;YACX,IAAI,OAAO;QACb,OAAO;YACL,IAAK,KAAK,SAAS,IAAI,EAAG,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,EAAE,OAAO;QACtD;QACA,oBAAoB;QACpB,IAAI,GAAG,GAAG,CAAC,IAAI;YACb,kDAAkD;YAClD,IAAI,SAAS,EAAE,MAAM,KAAK,KAAK,EAAE,MAAM,KAAK,GAAG,OAAO;YACtD,mDAAmD;YACnD,IAAI,SAAS,OAAO,IAAI,CAAC,GAAG,MAAM,KAAK,KAAK,OAAO,IAAI,CAAC,GAAG,MAAM,KAAK,GAAG,OAAO;YAChF,gCAAgC;YAChC,IAAI,MAAM,GAAG,OAAO;QACtB;QACA,OAAO;IACT;AACF;AAEA;;CAEC,GACD,SAAS,WAAW,MAAM;IACxB,MAAM,OAAO;QACX,OAAO,CAAC;QACR,WAAW,CAAC;IACd;IACA,IAAI,QAAQ;QACV,OAAO,QAAQ,CAAC,CAAA;YACd,IAAI,IAAI,IAAI,EAAE,KAAK,KAAK,CAAC,IAAI,IAAI,CAAC,GAAG;YACrC,IAAI,IAAI,QAAQ,IAAI,CAAC,KAAK,SAAS,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE,KAAK,SAAS,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,QAAQ;QAC1G;IACF;IACA,OAAO;AACT;AAEA,4CAA4C;AAC5C,SAAS,QAAQ,GAAG;IAClB,IAAI,IAAI,OAAO,IAAI,IAAI,IAAI,KAAK,SAAS,IAAI,OAAO;IACpD,IAAK,MAAM,KAAK,IAAK;QACnB,EAAE,OAAO,IAAI,OAAO,KAAK,IAAI,EAAE,OAAO;QACtC,OAAO,GAAG,CAAC,EAAE;IACf;AACF;AAEA,iEAAiE;AACjE,SAAS,QAAQ,MAAM,EAAE,KAAK;IAC5B,MAAM,WAAW;IACjB,SAAS,KAAK,GAAG;QACf,MAAM;QACN,MAAM;QACN,gBAAgB;QAChB,eAAe,CAAC;QAChB,YAAY;QACZ,UAAU,CAAC;QACX,SAAS,EAAE;QACX,QAAQ;QACR,GAAG,KAAK;IACV;IACA,OAAO;AACT;AACA,SAAS,QAAQ,QAAQ,EAAE,GAAG;IAC5B,IAAI,SAAS;IACb,IAAI,IAAI,QAAQ,CAAC,MAAM;QACrB,MAAM,UAAU,IAAI,KAAK,CAAC;QAC1B,MAAM,OAAO,QAAQ,GAAG;QACxB,SAAS,QAAQ,MAAM,CAAC,CAAC,KAAK,MAAQ,GAAG,CAAC,IAAI,EAAE;QAChD,OAAO;YACL;YACA,KAAK;QACP;IACF,OAAO,OAAO;QACZ;QACA;IACF;AACF;AAEA,qDAAqD;AACrD,MAAM,cAAc;AACpB,SAAS,OAAO,MAAM,EAAE,KAAK,EAAE,IAAI;IACjC,IAAI,GAAG,GAAG,CAAC,OAAO;QAChB,iDAAiD;QACjD,IAAI,YAAY,IAAI,CAAC,OAAO;YAC1B,MAAM,OAAO,KAAK,OAAO,CAAC,aAAa;YACvC,MAAM,EACJ,MAAM,EACN,GAAG,EACJ,GAAG,QAAQ,QAAQ;YACpB,IAAI,CAAC,MAAM,OAAO,CAAC,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,GAAG,EAAE;QACnD;QACA,MAAM,EACJ,MAAM,EACN,GAAG,EACJ,GAAG,QAAQ,QAAQ;QACpB,MAAM,KAAK,CAAC,cAAc,GAAG,MAAM,CAAC,IAAI;QACxC,MAAM,CAAC,IAAI,GAAG;IAChB,OAAO,MAAM,KAAK,CAAC,cAAc,GAAG,KAAK,QAAQ;AACnD;AACA,SAAS,OAAO,MAAM,EAAE,KAAK,EAAE,IAAI;IACjC,IAAI,cAAc;IAClB,IAAI,GAAG,GAAG,CAAC,OAAO;QAChB,MAAM,EACJ,MAAM,EACN,GAAG,EACJ,GAAG,QAAQ,QAAQ;QACpB,MAAM,WAAW,MAAM,KAAK,CAAC,cAAc;QAC3C,wFAAwF;QACxF,IAAI,aAAa,WAAW,OAAO,MAAM,CAAC,IAAI;aAEzC,MAAM,CAAC,IAAI,GAAG;IACrB,OAAO,CAAC,eAAe,MAAM,KAAK,KAAK,OAAO,KAAK,IAAI,aAAa,cAAc,IAAI,OAAO,KAAK,IAAI,aAAa,cAAc,CAAC,QAAQ;IAC1I,CAAC,gBAAgB,MAAM,KAAK,KAAK,OAAO,OAAO,OAAO,cAAc,cAAc;AACpF;AACA,wEAAwE;AACxE,SAAS,UAAU,QAAQ,EAAE,EAC3B,UAAU,EAAE,EACZ,KAAK,EAAE,EACP,KAAK,EAAE,EACP,GAAG,OACJ,EAAE,EACD,UAAU,EAAE,EACZ,KAAK,EAAE,EACP,KAAK,EAAE,EACP,GAAG,UACJ,GAAG,CAAC,CAAC,EAAE,SAAS,KAAK;IACpB,MAAM,aAAa,SAAS,KAAK;IACjC,MAAM,UAAU,OAAO,OAAO,CAAC;IAC/B,MAAM,UAAU,EAAE;IAElB,oEAAoE;IACpE,IAAI,QAAQ;QACV,MAAM,eAAe,OAAO,IAAI,CAAC;QACjC,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;YAC5C,IAAI,CAAC,MAAM,cAAc,CAAC,YAAY,CAAC,EAAE,GAAG,QAAQ,OAAO,CAAC;gBAAC,YAAY,CAAC,EAAE;gBAAE,UAAU;aAAS;QACnG;IACF;IACA,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;QAC3B,IAAI;QACJ,+BAA+B;QAC/B,IAAI,CAAC,kBAAkB,SAAS,KAAK,KAAK,QAAQ,gBAAgB,SAAS,IAAI,QAAQ,UAAU;QACjG,4BAA4B;QAC5B,IAAI,GAAG,GAAG,CAAC,OAAO,QAAQ,CAAC,IAAI,GAAG;QAClC,gCAAgC;QAChC,IAAI,mDAAmD,IAAI,CAAC,MAAM,OAAO,QAAQ,IAAI,CAAC;YAAC;YAAK;YAAO;YAAM,EAAE;SAAC;QAC5G,qBAAqB;QACrB,IAAI,UAAU,EAAE;QAChB,IAAI,IAAI,QAAQ,CAAC,MAAM,UAAU,IAAI,KAAK,CAAC;QAC3C,QAAQ,IAAI,CAAC;YAAC;YAAK;YAAO;YAAO;SAAQ;QAEzC,sBAAsB;QACtB,IAAK,MAAM,QAAQ,MAAO;YACxB,MAAM,QAAQ,KAAK,CAAC,KAAK;YACzB,IAAI,KAAK,UAAU,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,QAAQ,IAAI,CAAC;gBAAC;gBAAM;gBAAO;gBAAO,KAAK,KAAK,CAAC;aAAK;QACpF;IACF;IACA,MAAM,WAAW;QACf,GAAG,KAAK;IACV;IACA,IAAI,cAAc,QAAQ,WAAW,aAAa,IAAI,cAAc,QAAQ,WAAW,aAAa,CAAC,IAAI,EAAE,SAAS,IAAI,GAAG,WAAW,aAAa,CAAC,IAAI;IACxJ,IAAI,cAAc,QAAQ,WAAW,aAAa,IAAI,cAAc,QAAQ,WAAW,aAAa,CAAC,MAAM,EAAE,SAAS,MAAM,GAAG,WAAW,aAAa,CAAC,MAAM;IAC9J,OAAO;QACL;QACA;IACF;AACF;AACA,MAAM,UAAU,OAAO,gKAAA,CAAA,UAAO,KAAK,eAAe,oDAAyB;AAE3E,yDAAyD;AACzD,SAAS,aAAa,QAAQ,EAAE,IAAI;IAClC,IAAI;IACJ,2CAA2C;IAC3C,MAAM,aAAa,SAAS,KAAK;IACjC,MAAM,OAAO,cAAc,OAAO,KAAK,IAAI,WAAW,IAAI;IAC1D,MAAM,YAAY,QAAQ,OAAO,KAAK,IAAI,KAAK,QAAQ,IAAI,OAAO,KAAK,IAAI,KAAK,QAAQ;IACxF,MAAM,EACJ,QAAQ,EACR,OAAO,EACR,GAAG,UAAU,QAAQ,OAAO,UAAU,UAAU;IACjD,MAAM,eAAe,cAAc,OAAO,KAAK,IAAI,WAAW,UAAU;IAExE,yBAAyB;IACzB,IAAI,SAAS,KAAK,EAAE,SAAS,KAAK,CAAC,aAAa,GAAG;IACnD,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;QACvC,IAAI,CAAC,KAAK,OAAO,SAAS,KAAK,GAAG,OAAO,CAAC,EAAE;QAE5C,4DAA4D;QAC5D,wDAAwD;QACxD,IAAI,cAAc,WAAW;YAC3B,MAAM,eAAe;YACrB,MAAM,iBAAiB;YACvB,MAAM,uBAAuB;YAC7B,IAAI,QAAQ,YAAY;gBACtB,MAAM;gBACN,QAAQ,UAAU,eAAe,iBAAiB;YACpD,OAAO,IAAI,QAAQ,kBAAkB;gBACnC,MAAM;gBACN,QAAQ,UAAU,eAAe,iBAAiB;YACpD;QACF;QACA,IAAI,kBAAkB;QACtB,IAAI,aAAa,eAAe,CAAC,IAAI;QAErC,uBAAuB;QACvB,IAAI,KAAK,MAAM,EAAE;YACf,aAAa,KAAK,MAAM,CAAC,CAAC,KAAK,MAAQ,GAAG,CAAC,IAAI,EAAE;YACjD,2DAA2D;YAC3D,IAAI,CAAC,CAAC,cAAc,WAAW,GAAG,GAAG;gBACnC,MAAM,CAAC,MAAM,GAAG,eAAe,GAAG,KAAK,OAAO;gBAC9C,kBAAkB,eAAe,OAAO,GAAG,MAAM,CAAC,CAAC,KAAK,MAAQ,GAAG,CAAC,IAAI,EAAE;gBAC1E,MAAM;YACR;QACF;QAEA,kDAAkD;QAClD,0EAA0E;QAC1E,+EAA+E;QAC/E,kDAAkD;QAClD,4DAA4D;QAC5D,IAAI,UAAU,UAAU,UAAU;YAChC,IAAI,gBAAgB,WAAW,EAAE;gBAC/B,0EAA0E;gBAC1E,IAAI,OAAO,SAAS,GAAG,CAAC,gBAAgB,WAAW;gBACnD,IAAI,CAAC,MAAM;oBACT,mBAAmB;oBACnB,OAAO,IAAI,gBAAgB,WAAW;oBACtC,SAAS,GAAG,CAAC,gBAAgB,WAAW,EAAE;gBAC5C;gBACA,QAAQ,IAAI,CAAC,IAAI;YACnB,OAAO;gBACL,uDAAuD;gBACvD,QAAQ;YACV;QACF;QAEA,+BAA+B;QAC/B,IAAI,WAAW,YAAY;YACzB,IAAI,OAAO,WAAW,QAAQ,CAAC,IAAI,GAAG;iBAAW,OAAO,WAAW,QAAQ,CAAC,IAAI;YAChF,WAAW,UAAU,GAAG,OAAO,IAAI,CAAC,WAAW,QAAQ,EAAE,MAAM;QACjE,OAEK,IAAI,cAAc,WAAW,GAAG,IAAI,CAAC,WAAW,IAAI,IAAI,sBAAsB,oJAAA,CAAA,SAAY,GAAG;YAChG,uBAAuB;YACvB,IAAI,MAAM,OAAO,CAAC,QAAQ;gBACxB,IAAI,WAAW,SAAS,EAAE,WAAW,SAAS,CAAC;qBAAY,WAAW,GAAG,IAAI;YAC/E,OAEK,IAAI,WAAW,IAAI,IAAI,SAAS,MAAM,WAAW,IAAI,CAC1D,0FAA0F;YAC1F,oDAAoD;YACpD,0DAA0D;YAC1D,sCAAsC;YACtC,UAAU,WAAW,WAAW,CAAC,IAAI,KAAK,MAAM,WAAW,CAAC,IAAI,GAAG,WAAW,WAAW,KAAK,MAAM,WAAW,GAAG;gBAChH,WAAW,IAAI,CAAC;YAClB,OAGK,IAAI,UAAU,WAAW;gBAC5B,IAAI;gBACJ,MAAM,UAAU,CAAC,cAAc,UAAU,KAAK,OAAO,KAAK,IAAI,YAAY,OAAO;gBACjF,8BAA8B;gBAC9B,IAAI,CAAC,WAAW,WAAW,SAAS,EAAE,WAAW,SAAS,CAAC;qBAEtD,IAAI,sBAAsB,oJAAA,CAAA,SAAY,IAAI,iBAAiB,oJAAA,CAAA,SAAY,EAAE,WAAW,IAAI,GAAG,MAAM,IAAI;qBAErG,WAAW,GAAG,CAAC;gBACpB,mEAAmE;gBACnE,2BAA2B;gBAC3B,yDAAyD;gBACzD,IAAI,CAAC,wBAAwB,aAAa,CAAC,UAAU,MAAM,IAAI,SAAS,WAAW,mBAAmB;YACxG;QACA,iCAAiC;QACnC,OAAO;YACL,IAAI;YACJ,eAAe,CAAC,IAAI,GAAG;YAEvB,0CAA0C;YAC1C,yDAAyD;YACzD,IAAI,CAAC,uBAAuB,eAAe,CAAC,IAAI,KAAK,QAAQ,qBAAqB,SAAS,IAC3F,uFAAuF;YACvF,eAAe,CAAC,IAAI,CAAC,MAAM,KAAK,oJAAA,CAAA,aAAgB,IAAI,eAAe,CAAC,IAAI,CAAC,IAAI,KAAK,oJAAA,CAAA,mBAAsB,IAAI,WAAW;gBACrH,MAAM,UAAU,eAAe,CAAC,IAAI;gBACpC,IAAI,cAAc,YAAY,cAAc,UAAU,EAAE,GAAG,QAAQ,UAAU,GAAG,UAAU,EAAE,CAAC,gBAAgB;qBAAM,QAAQ,QAAQ,GAAG,UAAU,EAAE,CAAC,cAAc;YACnK;QACF;QACA,mBAAmB;IACrB;IACA,IAAI,cAAc,WAAW,MAAM,IAAI,SAAS,OAAO,IAAI,iBAAiB,WAAW,UAAU,EAAE;QACjG,yCAAyC;QACzC,MAAM,WAAW,gBAAgB,UAAU,QAAQ,GAAG,QAAQ;QAC9D,iEAAiE;QACjE,MAAM,QAAQ,SAAS,WAAW,CAAC,OAAO,CAAC;QAC3C,IAAI,QAAQ,CAAC,GAAG,SAAS,WAAW,CAAC,MAAM,CAAC,OAAO;QACnD,wEAAwE;QACxE,IAAI,WAAW,UAAU,EAAE,SAAS,WAAW,CAAC,IAAI,CAAC;IACvD;IAEA,6FAA6F;IAC7F,6CAA6C;IAC7C,MAAM,aAAa,QAAQ,MAAM,KAAK,KAAK,OAAO,CAAC,EAAE,CAAC,EAAE,KAAK;IAC7D,IAAI,CAAC,cAAc,QAAQ,MAAM,IAAI,CAAC,mBAAmB,SAAS,KAAK,KAAK,QAAQ,iBAAiB,MAAM,EAAE,eAAe;IAC5H,OAAO;AACT;AACA,SAAS,mBAAmB,QAAQ;IAClC,IAAI,kBAAkB;IACtB,MAAM,QAAQ,CAAC,mBAAmB,SAAS,KAAK,KAAK,OAAO,KAAK,IAAI,CAAC,wBAAwB,iBAAiB,IAAI,KAAK,OAAO,KAAK,IAAI,sBAAsB,QAAQ,IAAI,OAAO,KAAK,IAAI,sBAAsB,QAAQ;IACxN,IAAI,SAAS,MAAM,QAAQ,CAAC,MAAM,KAAK,GAAG,MAAM,UAAU;AAC5D;AACA,SAAS,eAAe,QAAQ;IAC9B,SAAS,QAAQ,IAAI,OAAO,KAAK,IAAI,SAAS,QAAQ,CAAC;AACzD;AACA,SAAS,aAAa,MAAM,EAAE,IAAI;IAChC,wDAAwD;IACxD,wDAAwD;IACxD,IAAI,CAAC,OAAO,MAAM,EAAE;QAClB,IAAI,qBAAqB,SAAS;YAChC,OAAO,IAAI,GAAG,KAAK,KAAK,GAAG,CAAC;YAC5B,OAAO,KAAK,GAAG,KAAK,KAAK,GAAG;YAC5B,OAAO,GAAG,GAAG,KAAK,MAAM,GAAG;YAC3B,OAAO,MAAM,GAAG,KAAK,MAAM,GAAG,CAAC;QACjC,OAAO;YACL,OAAO,MAAM,GAAG,KAAK,KAAK,GAAG,KAAK,MAAM;QAC1C;QACA,OAAO,sBAAsB;QAC7B,yDAAyD;QACzD,yDAAyD;QACzD,OAAO,iBAAiB;IAC1B;AACF;AAEA,SAAS,OAAO,KAAK;IACnB,OAAO,CAAC,MAAM,WAAW,IAAI,MAAM,MAAM,EAAE,IAAI,GAAG,MAAM,MAAM,KAAK,GAAG,MAAM,UAAU;AACxF;AAEA,gGAAgG;AAChG,iEAAiE;AACjE,SAAS;IACP,IAAI;IACJ,iFAAiF;IACjF,wDAAwD;IACxD,MAAM,cAAc,OAAO,SAAS,eAAe,QAAQ,aAAkB,eAAe;IAC5F,IAAI,CAAC,aAAa,OAAO,mJAAA,CAAA,uBAAoB;IAC7C,MAAM,OAAO,CAAC,qBAAqB,YAAY,KAAK,KAAK,OAAO,KAAK,IAAI,mBAAmB,IAAI;IAChG,OAAQ;QACN,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO,mJAAA,CAAA,wBAAqB;QAC9B,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO,mJAAA,CAAA,0BAAuB;QAChC;YACE,OAAO,mJAAA,CAAA,uBAAoB;IAC/B;AACF;AAEA;;;CAGC,GACD,SAAS,8BAA8B,WAAW,EAAE,GAAG,EAAE,QAAQ,EAAE,SAAS;IAC1E,MAAM,cAAc,SAAS,GAAG,CAAC;IACjC,IAAI,aAAa;QACf,SAAS,MAAM,CAAC;QAChB,yDAAyD;QACzD,IAAI,SAAS,IAAI,KAAK,GAAG;YACvB,YAAY,MAAM,CAAC;YACnB,YAAY,MAAM,CAAC,qBAAqB,CAAC;QAC3C;IACF;AACF;AACA,SAAS,oBAAoB,KAAK,EAAE,MAAM;IACxC,MAAM,EACJ,QAAQ,EACT,GAAG,MAAM,QAAQ;IAClB,uDAAuD;IACvD,SAAS,WAAW,GAAG,SAAS,WAAW,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM;IAC9D,SAAS,WAAW,GAAG,SAAS,WAAW,CAAC,MAAM,CAAC,CAAA,IAAK,MAAM;IAC9D,SAAS,OAAO,CAAC,OAAO,CAAC,CAAC,OAAO;QAC/B,IAAI,MAAM,WAAW,KAAK,UAAU,MAAM,MAAM,KAAK,QAAQ;YAC3D,iDAAiD;YACjD,SAAS,OAAO,CAAC,MAAM,CAAC;QAC1B;IACF;IACA,SAAS,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU;QACtC,8BAA8B,SAAS,WAAW,EAAE,QAAQ,UAAU;IACxE;AACF;AACA,SAAS,aAAa,KAAK;IACzB,qBAAqB,GACrB,SAAS,kBAAkB,KAAK;QAC9B,MAAM,EACJ,QAAQ,EACT,GAAG,MAAM,QAAQ;QAClB,MAAM,KAAK,MAAM,OAAO,GAAG,SAAS,YAAY,CAAC,EAAE;QACnD,MAAM,KAAK,MAAM,OAAO,GAAG,SAAS,YAAY,CAAC,EAAE;QACnD,OAAO,KAAK,KAAK,CAAC,KAAK,IAAI,CAAC,KAAK,KAAK,KAAK;IAC7C;IAEA,uGAAuG,GACvG,SAAS,oBAAoB,OAAO;QAClC,OAAO,QAAQ,MAAM,CAAC,CAAA,MAAO;gBAAC;gBAAQ;gBAAQ;gBAAS;gBAAO;aAAQ,CAAC,IAAI,CAAC,CAAA;gBAC1E,IAAI;gBACJ,OAAO,CAAC,OAAO,IAAI,KAAK,KAAK,OAAO,KAAK,IAAI,KAAK,QAAQ,CAAC,cAAc,KAAK;YAChF;IACF;IACA,SAAS,UAAU,KAAK,EAAE,MAAM;QAC9B,MAAM,QAAQ,MAAM,QAAQ;QAC5B,MAAM,aAAa,IAAI;QACvB,MAAM,gBAAgB,EAAE;QACxB,2CAA2C;QAC3C,MAAM,gBAAgB,SAAS,OAAO,MAAM,QAAQ,CAAC,WAAW,IAAI,MAAM,QAAQ,CAAC,WAAW;QAC9F,2CAA2C;QAC3C,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,MAAM,EAAE,IAAK;YAC7C,MAAM,QAAQ,aAAa,aAAa,CAAC,EAAE;YAC3C,IAAI,OAAO;gBACT,MAAM,SAAS,CAAC,MAAM,GAAG;YAC3B;QACF;QACA,IAAI,CAAC,MAAM,YAAY,EAAE;YACvB,kDAAkD;YAClD,MAAM,MAAM,CAAC,OAAO,IAAI,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC,OAAO,CAAC,OAAO;QACtE;QACA,SAAS,cAAc,GAAG;YACxB,MAAM,QAAQ,aAAa;YAC3B,kFAAkF;YAClF,IAAI,CAAC,SAAS,CAAC,MAAM,MAAM,CAAC,OAAO,IAAI,MAAM,SAAS,CAAC,MAAM,KAAK,MAAM,OAAO,EAAE;YAEjF,gFAAgF;YAChF,IAAI,MAAM,SAAS,CAAC,MAAM,KAAK,WAAW;gBACxC,IAAI;gBACJ,MAAM,MAAM,CAAC,OAAO,IAAI,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC,OAAO,CAAC,OAAO,OAAO,CAAC,sBAAsB,MAAM,YAAY,KAAK,OAAO,KAAK,IAAI,oBAAoB,QAAQ;gBACrK,uEAAuE;gBACvE,IAAI,MAAM,SAAS,CAAC,MAAM,KAAK,WAAW,MAAM,SAAS,CAAC,MAAM,GAAG;YACrE;YAEA,6BAA6B;YAC7B,OAAO,MAAM,SAAS,CAAC,MAAM,GAAG,MAAM,SAAS,CAAC,eAAe,CAAC,KAAK,QAAQ,EAAE;QACjF;QAEA,iBAAiB;QACjB,IAAI,OAAO,aACX,oBAAoB;SACnB,OAAO,CAAC,cACT,sCAAsC;SACrC,IAAI,CAAC,CAAC,GAAG;YACR,MAAM,SAAS,aAAa,EAAE,MAAM;YACpC,MAAM,SAAS,aAAa,EAAE,MAAM;YACpC,IAAI,CAAC,UAAU,CAAC,QAAQ,OAAO,EAAE,QAAQ,GAAG,EAAE,QAAQ;YACtD,OAAO,OAAO,MAAM,CAAC,QAAQ,GAAG,OAAO,MAAM,CAAC,QAAQ,IAAI,EAAE,QAAQ,GAAG,EAAE,QAAQ;QACnF,EACA,wBAAwB;SACvB,MAAM,CAAC,CAAA;YACN,MAAM,KAAK,OAAO;YAClB,IAAI,WAAW,GAAG,CAAC,KAAK,OAAO;YAC/B,WAAW,GAAG,CAAC;YACf,OAAO;QACT;QAEA,kDAAkD;QAClD,8FAA8F;QAC9F,IAAI,MAAM,MAAM,CAAC,MAAM,EAAE,OAAO,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM;QAE1D,4DAA4D;QAC5D,KAAK,MAAM,OAAO,KAAM;YACtB,IAAI,cAAc,IAAI,MAAM;YAC5B,kBAAkB;YAClB,MAAO,YAAa;gBAClB,IAAI;gBACJ,IAAI,CAAC,QAAQ,YAAY,KAAK,KAAK,QAAQ,MAAM,UAAU,EAAE,cAAc,IAAI,CAAC;oBAC9E,GAAG,GAAG;oBACN;gBACF;gBACA,cAAc,YAAY,MAAM;YAClC;QACF;QAEA,oFAAoF;QACpF,IAAI,eAAe,SAAS,MAAM,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,SAAS,GAAG;YAC3E,KAAK,IAAI,eAAe,MAAM,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,SAAS,EAAE,MAAM,GAAI;gBAChF,IAAI,CAAC,WAAW,GAAG,CAAC,OAAO,YAAY,YAAY,IAAI,cAAc,IAAI,CAAC,YAAY,YAAY;YACpG;QACF;QACA,OAAO;IACT;IAEA,0DAA0D,GAC1D,SAAS,iBAAiB,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ;QAC7D,MAAM,YAAY,MAAM,QAAQ;QAEhC,gEAAgE;QAChE,IAAI,cAAc,MAAM,EAAE;YACxB,MAAM,aAAa;gBACjB,SAAS;YACX;YACA,KAAK,MAAM,OAAO,cAAe;gBAC/B,MAAM,QAAQ,aAAa,IAAI,MAAM,KAAK;gBAC1C,MAAM,EACJ,SAAS,EACT,OAAO,EACP,MAAM,EACN,QAAQ,EACT,GAAG;gBACJ,MAAM,mBAAmB,IAAI,oJAAA,CAAA,UAAa,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC,EAAE,GAAG,SAAS,CAAC;gBAC9E,MAAM,oBAAoB,CAAA;oBACxB,IAAI,uBAAuB;oBAC3B,OAAO,CAAC,wBAAwB,CAAC,yBAAyB,SAAS,WAAW,CAAC,GAAG,CAAC,GAAG,KAAK,OAAO,KAAK,IAAI,uBAAuB,GAAG,CAAC,IAAI,WAAW,CAAC,KAAK,OAAO,wBAAwB;gBAC5L;gBACA,MAAM,oBAAoB,CAAA;oBACxB,MAAM,cAAc;wBAClB,cAAc;wBACd,QAAQ,MAAM,MAAM;oBACtB;oBACA,IAAI,SAAS,WAAW,CAAC,GAAG,CAAC,KAAK;wBAChC,kEAAkE;wBAClE,qBAAqB;wBACrB,SAAS,WAAW,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,WAAW,EAAE;oBACpD,OAAO;wBACL,gEAAgE;wBAChE,+DAA+D;wBAC/D,iBAAiB;wBACjB,SAAS,WAAW,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI;4BAAC;gCAAC,IAAI,WAAW;gCAAE;6BAAY;yBAAC;oBACvE;oBACA,MAAM,MAAM,CAAC,iBAAiB,CAAC;gBACjC;gBACA,MAAM,wBAAwB,CAAA;oBAC5B,MAAM,WAAW,SAAS,WAAW,CAAC,GAAG,CAAC;oBAC1C,IAAI,UAAU;wBACZ,8BAA8B,SAAS,WAAW,EAAE,IAAI,WAAW,EAAE,UAAU;oBACjF;gBACF;gBAEA,yBAAyB;gBACzB,IAAI,oBAAoB,CAAC;gBACzB,uaAAua;gBACva,IAAK,IAAI,QAAQ,MAAO;oBACtB,IAAI,WAAW,KAAK,CAAC,KAAK;oBAC1B,mEAAmE;oBACnE,mCAAmC;oBACnC,IAAI,OAAO,aAAa,YAAY,iBAAiB,CAAC,KAAK,GAAG;gBAChE;gBACA,IAAI,eAAe;oBACjB,GAAG,GAAG;oBACN,GAAG,iBAAiB;oBACpB;oBACA;oBACA,SAAS,WAAW,OAAO;oBAC3B;oBACA;oBACA,KAAK,UAAU,GAAG;oBAClB,QAAQ;oBACR,iDAAiD;oBACjD;wBACE,yDAAyD;wBACzD,8EAA8E;wBAC9E,MAAM,qBAAqB,eAAe,SAAS,SAAS,WAAW,CAAC,GAAG,CAAC,MAAM,SAAS;wBAE3F,uCAAuC;wBACvC,IACA,0CAA0C;wBAC1C,CAAC,sBACD,oDAAoD;wBACpD,mBAAmB,GAAG,CAAC,IAAI,WAAW,GAAG;4BACvC,aAAa,OAAO,GAAG,WAAW,OAAO,GAAG;4BAC5C,yDAAyD;4BACzD,mFAAmF;4BACnF,IAAI,SAAS,OAAO,CAAC,IAAI,IAAI,MAAM,IAAI,CAAC,SAAS,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,CAAA,IAAK,EAAE,WAAW,KAAK,IAAI,WAAW,GAAG;gCAC/G,gFAAgF;gCAChF,MAAM,SAAS,cAAc,KAAK,CAAC,GAAG,cAAc,OAAO,CAAC;gCAC5D,cAAc;uCAAI;oCAAQ;iCAAI;4BAChC;wBACF;oBACF;oBACA,iEAAiE;oBACjE,QAAQ;wBACN;wBACA;wBACA;oBACF;oBACA,eAAe;wBACb;wBACA;wBACA;oBACF;oBACA,aAAa;gBACf;gBAEA,mBAAmB;gBACnB,SAAS;gBACT,uDAAuD;gBACvD,IAAI,WAAW,OAAO,KAAK,MAAM;YACnC;QACF;QACA,OAAO;IACT;IACA,SAAS,cAAc,aAAa;QAClC,MAAM,EACJ,QAAQ,EACT,GAAG,MAAM,QAAQ;QAClB,KAAK,MAAM,cAAc,SAAS,OAAO,CAAC,MAAM,GAAI;YAClD,wFAAwF;YACxF,2EAA2E;YAC3E,IAAI,CAAC,cAAc,MAAM,IAAI,CAAC,cAAc,IAAI,CAAC,CAAA,MAAO,IAAI,MAAM,KAAK,WAAW,MAAM,IAAI,IAAI,KAAK,KAAK,WAAW,KAAK,IAAI,IAAI,UAAU,KAAK,WAAW,UAAU,GAAG;gBACvK,MAAM,cAAc,WAAW,WAAW;gBAC1C,MAAM,WAAW,YAAY,KAAK;gBAClC,MAAM,WAAW,YAAY,OAAO,KAAK,IAAI,SAAS,QAAQ;gBAC9D,SAAS,OAAO,CAAC,MAAM,CAAC,OAAO;gBAC/B,IAAI,YAAY,QAAQ,SAAS,UAAU,EAAE;oBAC3C,iDAAiD;oBACjD,MAAM,OAAO;wBACX,GAAG,UAAU;wBACb;oBACF;oBACA,SAAS,YAAY,IAAI,OAAO,KAAK,IAAI,SAAS,YAAY,CAAC;oBAC/D,SAAS,cAAc,IAAI,OAAO,KAAK,IAAI,SAAS,cAAc,CAAC;gBACrE;YACF;QACF;IACF;IACA,SAAS,cAAc,KAAK,EAAE,OAAO;QACnC,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACvC,MAAM,WAAW,OAAO,CAAC,EAAE,CAAC,KAAK;YACjC,YAAY,OAAO,KAAK,IAAI,SAAS,QAAQ,CAAC,eAAe,IAAI,OAAO,KAAK,IAAI,SAAS,QAAQ,CAAC,eAAe,CAAC;QACrH;IACF;IACA,SAAS,cAAc,IAAI;QACzB,wBAAwB;QACxB,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,OAAO,IAAM,cAAc,EAAE;YAC/B,KAAK;gBACH,OAAO,CAAA;oBACL,MAAM,EACJ,QAAQ,EACT,GAAG,MAAM,QAAQ;oBAClB,IAAI,eAAe,SAAS,SAAS,WAAW,CAAC,GAAG,CAAC,MAAM,SAAS,GAAG;wBACrE,qFAAqF;wBACrF,2FAA2F;wBAC3F,0FAA0F;wBAC1F,qFAAqF;wBACrF,sBAAsB;4BACpB,kDAAkD;4BAClD,IAAI,SAAS,WAAW,CAAC,GAAG,CAAC,MAAM,SAAS,GAAG;gCAC7C,SAAS,WAAW,CAAC,MAAM,CAAC,MAAM,SAAS;gCAC3C,cAAc,EAAE;4BAClB;wBACF;oBACF;gBACF;QACJ;QAEA,kCAAkC;QAClC,OAAO,SAAS,YAAY,KAAK;YAC/B,MAAM,EACJ,eAAe,EACf,QAAQ,EACT,GAAG,MAAM,QAAQ;YAElB,oBAAoB;YACpB,SAAS,SAAS,CAAC,OAAO,GAAG;YAE7B,uBAAuB;YACvB,MAAM,gBAAgB,SAAS;YAC/B,MAAM,eAAe,SAAS,aAAa,SAAS,mBAAmB,SAAS;YAChF,MAAM,SAAS,gBAAgB,sBAAsB;YACrD,MAAM,OAAO,UAAU,OAAO;YAC9B,MAAM,QAAQ,eAAe,kBAAkB,SAAS;YAExD,2CAA2C;YAC3C,IAAI,SAAS,iBAAiB;gBAC5B,SAAS,YAAY,GAAG;oBAAC,MAAM,OAAO;oBAAE,MAAM,OAAO;iBAAC;gBACtD,SAAS,WAAW,GAAG,KAAK,GAAG,CAAC,CAAA,MAAO,IAAI,WAAW;YACxD;YAEA,mEAAmE;YACnE,wFAAwF;YACxF,IAAI,gBAAgB,CAAC,KAAK,MAAM,EAAE;gBAChC,IAAI,SAAS,GAAG;oBACd,cAAc,OAAO,SAAS,WAAW;oBACzC,IAAI,iBAAiB,gBAAgB;gBACvC;YACF;YACA,uBAAuB;YACvB,IAAI,eAAe,cAAc;YACjC,SAAS,YAAY,IAAI;gBACvB,MAAM,cAAc,KAAK,WAAW;gBACpC,MAAM,WAAW,YAAY,KAAK;gBAClC,MAAM,WAAW,YAAY,OAAO,KAAK,IAAI,SAAS,QAAQ;gBAE9D,6BAA6B;gBAC7B,IAAI,CAAC,CAAC,YAAY,QAAQ,SAAS,UAAU,GAAG;gBAEhD;;;;;;;;;;;;;SAaC,GAED,IAAI,eAAe;oBACjB,iBAAiB;oBACjB,IAAI,SAAS,aAAa,IAAI,SAAS,cAAc,IAAI,SAAS,YAAY,IAAI,SAAS,cAAc,EAAE;wBACzG,wDAAwD;wBACxD,MAAM,KAAK,OAAO;wBAClB,MAAM,cAAc,SAAS,OAAO,CAAC,GAAG,CAAC;wBACzC,IAAI,CAAC,aAAa;4BAChB,wEAAwE;4BACxE,SAAS,OAAO,CAAC,GAAG,CAAC,IAAI;4BACzB,SAAS,aAAa,IAAI,OAAO,KAAK,IAAI,SAAS,aAAa,CAAC;4BACjE,SAAS,cAAc,IAAI,OAAO,KAAK,IAAI,SAAS,cAAc,CAAC;wBACrE,OAAO,IAAI,YAAY,OAAO,EAAE;4BAC9B,8FAA8F;4BAC9F,KAAK,eAAe;wBACtB;oBACF;oBACA,kBAAkB;oBAClB,SAAS,aAAa,IAAI,OAAO,KAAK,IAAI,SAAS,aAAa,CAAC;gBACnE,OAAO;oBACL,uBAAuB;oBACvB,MAAM,UAAU,QAAQ,CAAC,KAAK;oBAC9B,IAAI,SAAS;wBACX,2FAA2F;wBAC3F,oCAAoC;wBACpC,IAAI,CAAC,gBAAgB,SAAS,WAAW,CAAC,QAAQ,CAAC,cAAc;4BAC/D,mCAAmC;4BACnC,cAAc,OAAO,SAAS,WAAW,CAAC,MAAM,CAAC,CAAA,SAAU,CAAC,SAAS,WAAW,CAAC,QAAQ,CAAC;4BAC1F,uBAAuB;4BACvB,QAAQ;wBACV;oBACF,OAAO;wBACL,6GAA6G;wBAC7G,IAAI,gBAAgB,SAAS,WAAW,CAAC,QAAQ,CAAC,cAAc;4BAC9D,cAAc,OAAO,SAAS,WAAW,CAAC,MAAM,CAAC,CAAA,SAAU,CAAC,SAAS,WAAW,CAAC,QAAQ,CAAC;wBAC5F;oBACF;gBACF;YACF;YACA,iBAAiB,MAAM,OAAO,OAAO;QACvC;IACF;IACA,OAAO;QACL;IACF;AACF;AAEA,mDAAmD;AACnD,MAAM,cAAc;IAAC;IAAO;IAAO;IAAW;IAAgB;IAAU;IAAU;IAAc;IAAW;IAAQ;CAAW;AAC9H,MAAM,aAAa,CAAA,MAAO,CAAC,CAAC,CAAC,OAAO,QAAQ,IAAI,MAAM;AACtD,MAAM,UAAU,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE;AACjD,MAAM,cAAc,CAAC,YAAY;IAC/B,MAAM,YAAY,CAAA,GAAA,uLAAA,CAAA,UAAM,AAAD,EAAE,CAAC,KAAK;QAC7B,MAAM,WAAW,IAAI,oJAAA,CAAA,UAAa;QAClC,MAAM,gBAAgB,IAAI,oJAAA,CAAA,UAAa;QACvC,MAAM,aAAa,IAAI,oJAAA,CAAA,UAAa;QACpC,SAAS,mBAAmB,SAAS,MAAM,MAAM,EAAE,SAAS,aAAa,EAAE,OAAO,MAAM,IAAI;YAC1F,MAAM,EACJ,KAAK,EACL,MAAM,EACN,GAAG,EACH,IAAI,EACL,GAAG;YACJ,MAAM,SAAS,QAAQ;YACvB,IAAI,OAAO,SAAS,EAAE,WAAW,IAAI,CAAC;iBAAa,WAAW,GAAG,IAAI;YACrE,MAAM,WAAW,OAAO,gBAAgB,CAAC,UAAU,UAAU,CAAC;YAC9D,IAAI,qBAAqB,SAAS;gBAChC,OAAO;oBACL,OAAO,QAAQ,OAAO,IAAI;oBAC1B,QAAQ,SAAS,OAAO,IAAI;oBAC5B;oBACA;oBACA,QAAQ;oBACR;oBACA;gBACF;YACF,OAAO;gBACL,MAAM,MAAM,OAAO,GAAG,GAAG,KAAK,EAAE,GAAG,KAAK,kCAAkC;gBAC1E,MAAM,IAAI,IAAI,KAAK,GAAG,CAAC,MAAM,KAAK,UAAU,iBAAiB;gBAC7D,MAAM,IAAI,IAAI,CAAC,QAAQ,MAAM;gBAC7B,OAAO;oBACL,OAAO;oBACP,QAAQ;oBACR;oBACA;oBACA,QAAQ,QAAQ;oBAChB;oBACA;gBACF;YACF;QACF;QACA,IAAI,qBAAqB;QACzB,MAAM,wBAAwB,CAAA,UAAW,IAAI,CAAA,QAAS,CAAC;oBACrD,aAAa;wBACX,GAAG,MAAM,WAAW;wBACpB;oBACF;gBACF,CAAC;QACD,MAAM,UAAU,IAAI,oJAAA,CAAA,UAAa;QACjC,MAAM,YAAY;YAChB;YACA;YACA,0CAA0C;YAC1C,IAAI;YACJ,QAAQ;YACR,WAAW;YACX,QAAQ;gBACN,UAAU;gBACV,SAAS;gBACT,WAAW;YACb;YACA,IAAI;YACJ,OAAO;YACP,YAAY,CAAC,SAAS,CAAC,GAAK,WAAW,OAAO;YAC9C,SAAS,CAAC,WAAW,mBAAqB,QAAQ,WAAW,kBAAkB;YAC/E,QAAQ;YACR,QAAQ;YACR,MAAM;YACN,UAAU;YACV,OAAO,IAAI,oJAAA,CAAA,QAAW;YACtB;YACA,OAAO;YACP,WAAW;YACX,iBAAiB;YACjB,aAAa;gBACX,SAAS;gBACT,KAAK;gBACL,KAAK;gBACL,UAAU;gBACV,SAAS;oBACP,MAAM,QAAQ;oBACd,gBAAgB;oBAChB,IAAI,oBAAoB,aAAa;oBACrC,8BAA8B;oBAC9B,IAAI,MAAM,WAAW,CAAC,OAAO,KAAK,MAAM,WAAW,CAAC,GAAG,EAAE,sBAAsB,MAAM,WAAW,CAAC,GAAG;oBACpG,wFAAwF;oBACxF,qBAAqB,WAAW,IAAM,sBAAsB,MAAM,WAAW,CAAC,GAAG,GAAG,MAAM,WAAW,CAAC,QAAQ;gBAChH;YACF;YACA,MAAM;gBACJ,OAAO;gBACP,QAAQ;gBACR,KAAK;gBACL,MAAM;gBACN,aAAa;YACf;YACA,UAAU;gBACR,YAAY;gBACZ,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK;gBACL,MAAM;gBACN,QAAQ;gBACR,UAAU;gBACV,QAAQ;gBACR;YACF;YACA,WAAW,CAAA,SAAU,IAAI,CAAA,QAAS,CAAC;wBACjC,GAAG,KAAK;wBACR,QAAQ;4BACN,GAAG,MAAM,MAAM;4BACf,GAAG,MAAM;wBACX;oBACF,CAAC;YACD,SAAS,CAAC,OAAO,QAAQ,aAAa,KAAK;gBACzC,MAAM,SAAS,MAAM,MAAM;gBAC3B,MAAM,OAAO;oBACX;oBACA;oBACA,KAAK,OAAO;oBACZ,MAAM,QAAQ;oBACd;gBACF;gBACA,IAAI,CAAA,QAAS,CAAC;wBACZ;wBACA,UAAU;4BACR,GAAG,MAAM,QAAQ;4BACjB,GAAG,mBAAmB,QAAQ,eAAe,KAAK;wBACpD;oBACF,CAAC;YACH;YACA,QAAQ,CAAA,MAAO,IAAI,CAAA;oBACjB,MAAM,WAAW,aAAa;oBAC9B,OAAO;wBACL,UAAU;4BACR,GAAG,MAAM,QAAQ;4BACjB,KAAK;4BACL,YAAY,MAAM,QAAQ,CAAC,UAAU,IAAI;wBAC3C;oBACF;gBACF;YACA,cAAc,CAAC,YAAY,QAAQ;gBACjC,MAAM,QAAQ,MAAM,KAAK;gBAEzB,iFAAiF;gBACjF,MAAM,IAAI;gBACV,MAAM,WAAW,GAAG;gBACpB,IAAI,cAAc,SAAS;oBACzB,MAAM,KAAK;oBACX,MAAM,WAAW,GAAG;gBACtB;gBACA,IAAI,IAAM,CAAC;wBACT;oBACF,CAAC;YACH;YACA,cAAc;YACd,UAAU;gBACR,QAAQ;gBACR,UAAU;gBACV,QAAQ;gBACR,WAAW,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;gBACtC,aAAa,EAAE;gBACf,SAAS,IAAI;gBACb,aAAa,EAAE;gBACf,cAAc;oBAAC;oBAAG;iBAAE;gBACpB,aAAa,EAAE;gBACf,aAAa,IAAI;gBACjB,WAAW,CAAC,KAAK,UAAU;oBACzB,MAAM,WAAW,MAAM,QAAQ;oBAC/B,mFAAmF;oBACnF,iFAAiF;oBACjF,6EAA6E;oBAC7E,uDAAuD;oBACvD,SAAS,QAAQ,GAAG,SAAS,QAAQ,GAAG,CAAC,WAAW,IAAI,IAAI,CAAC;oBAC7D,SAAS,WAAW,CAAC,IAAI,CAAC;wBACxB;wBACA;wBACA;oBACF;oBACA,uEAAuE;oBACvE,6DAA6D;oBAC7D,SAAS,WAAW,GAAG,SAAS,WAAW,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;oBAClF,OAAO;wBACL,MAAM,WAAW,MAAM,QAAQ;wBAC/B,IAAI,YAAY,QAAQ,SAAS,WAAW,EAAE;4BAC5C,2DAA2D;4BAC3D,SAAS,QAAQ,GAAG,SAAS,QAAQ,GAAG,CAAC,WAAW,IAAI,IAAI,CAAC;4BAC7D,8BAA8B;4BAC9B,SAAS,WAAW,GAAG,SAAS,WAAW,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,GAAG,KAAK;wBACpE;oBACF;gBACF;YACF;QACF;QACA,OAAO;IACT;IACA,MAAM,QAAQ,UAAU,QAAQ;IAChC,IAAI,UAAU,MAAM,IAAI;IACxB,IAAI,SAAS,MAAM,QAAQ,CAAC,GAAG;IAC/B,IAAI,YAAY,MAAM,MAAM;IAC5B,UAAU,SAAS,CAAC;QAClB,MAAM,EACJ,MAAM,EACN,IAAI,EACJ,QAAQ,EACR,EAAE,EACF,GAAG,EACJ,GAAG,UAAU,QAAQ;QAEtB,+DAA+D;QAC/D,IAAI,KAAK,KAAK,KAAK,QAAQ,KAAK,IAAI,KAAK,MAAM,KAAK,QAAQ,MAAM,IAAI,SAAS,GAAG,KAAK,QAAQ;YAC7F,IAAI;YACJ,UAAU;YACV,SAAS,SAAS,GAAG;YACrB,2BAA2B;YAC3B,aAAa,QAAQ;YACrB,GAAG,aAAa,CAAC,SAAS,GAAG;YAC7B,MAAM,cAAc,CAAC,oBAAoB,KAAK,WAAW,KAAK,OAAO,oBAAoB,OAAO,sBAAsB,eAAe,GAAG,UAAU,YAAY;YAC9J,GAAG,OAAO,CAAC,KAAK,KAAK,EAAE,KAAK,MAAM,EAAE;QACtC;QAEA,0CAA0C;QAC1C,IAAI,WAAW,WAAW;YACxB,YAAY;YACZ,kBAAkB;YAClB,IAAI,CAAA,QAAS,CAAC;oBACZ,UAAU;wBACR,GAAG,MAAM,QAAQ;wBACjB,GAAG,MAAM,QAAQ,CAAC,kBAAkB,CAAC,OAAO;oBAC9C;gBACF,CAAC;QACH;IACF;IAEA,2BAA2B;IAC3B,UAAU,SAAS,CAAC,CAAA,QAAS,WAAW;IAExC,oBAAoB;IACpB,OAAO;AACT;AAEA,SAAS,WAAW,QAAQ,EAAE,IAAI;IAChC,MAAM,MAAM;QACV;IACF;IACA,KAAK,GAAG,CAAC;IACT,OAAO,IAAM,KAAK,KAAK,MAAM,CAAC;AAChC;AACA,IAAI;AACJ,IAAI,gBAAgB,IAAI;AACxB,IAAI,qBAAqB,IAAI;AAC7B,IAAI,oBAAoB,IAAI;AAE5B;;;CAGC,GACD,MAAM,YAAY,CAAA,WAAY,WAAW,UAAU;AAEnD;;;CAGC,GACD,MAAM,iBAAiB,CAAA,WAAY,WAAW,UAAU;AAExD;;;CAGC,GACD,MAAM,UAAU,CAAA,WAAY,WAAW,UAAU;AACjD,SAAS,IAAI,OAAO,EAAE,SAAS;IAC7B,IAAI,CAAC,QAAQ,IAAI,EAAE;IACnB,KAAK,MAAM,EACT,QAAQ,EACT,IAAI,QAAQ,MAAM,GAAI;QACrB,SAAS;IACX;AACF;AACA,SAAS,mBAAmB,IAAI,EAAE,SAAS;IACzC,OAAQ;QACN,KAAK;YACH,OAAO,IAAI,eAAe;QAC5B,KAAK;YACH,OAAO,IAAI,oBAAoB;QACjC,KAAK;YACH,OAAO,IAAI,mBAAmB;IAClC;AACF;AACA,IAAI;AACJ,IAAI;AACJ,SAAS,SAAS,SAAS,EAAE,KAAK,EAAE,KAAK;IACvC,oBAAoB;IACpB,IAAI,QAAQ,MAAM,KAAK,CAAC,QAAQ;IAChC,kFAAkF;IAClF,IAAI,MAAM,SAAS,KAAK,WAAW,OAAO,cAAc,UAAU;QAChE,QAAQ,YAAY,MAAM,KAAK,CAAC,WAAW;QAC3C,MAAM,KAAK,CAAC,OAAO,GAAG,MAAM,KAAK,CAAC,WAAW;QAC7C,MAAM,KAAK,CAAC,WAAW,GAAG;IAC5B;IACA,8BAA8B;IAC9B,cAAc,MAAM,QAAQ,CAAC,WAAW;IACxC,IAAK,IAAI,GAAG,IAAI,YAAY,MAAM,EAAE,IAAK;QACvC,eAAe,WAAW,CAAC,EAAE;QAC7B,aAAa,GAAG,CAAC,OAAO,CAAC,aAAa,KAAK,CAAC,QAAQ,IAAI,OAAO;IACjE;IACA,iBAAiB;IACjB,IAAI,CAAC,MAAM,QAAQ,CAAC,QAAQ,IAAI,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,CAAC,MAAM,CAAC,MAAM,KAAK,EAAE,MAAM,MAAM;IAC1F,uBAAuB;IACvB,MAAM,QAAQ,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,GAAG,MAAM,QAAQ,CAAC,MAAM,GAAG;IAC5D,OAAO,MAAM,SAAS,KAAK,WAAW,IAAI,MAAM,QAAQ,CAAC,MAAM;AACjE;AACA,SAAS,WAAW,KAAK;IACvB,IAAI,UAAU;IACd,IAAI,qBAAqB;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,SAAS,KAAK,SAAS;QACrB,QAAQ,sBAAsB;QAC9B,UAAU;QACV,SAAS;QAET,cAAc;QACd,mBAAmB,UAAU;QAE7B,mBAAmB;QACnB,qBAAqB;QACrB,KAAK,MAAM,QAAQ,MAAM,MAAM,GAAI;YACjC,IAAI;YACJ,QAAQ,KAAK,KAAK,CAAC,QAAQ;YAC3B,4DAA4D;YAC5D,IAAI,MAAM,QAAQ,CAAC,MAAM,IAAI,CAAC,MAAM,SAAS,KAAK,YAAY,MAAM,QAAQ,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,eAAe,MAAM,EAAE,CAAC,EAAE,KAAK,QAAQ,aAAa,YAAY,GAAG;gBAChK,UAAU,SAAS,WAAW;YAChC;QACF;QACA,qBAAqB;QAErB,oBAAoB;QACpB,mBAAmB,SAAS;QAE5B,0CAA0C;QAC1C,IAAI,WAAW,GAAG;YAChB,0DAA0D;YAC1D,mBAAmB,QAAQ;YAE3B,wBAAwB;YACxB,UAAU;YACV,OAAO,qBAAqB;QAC9B;IACF;IACA,SAAS,WAAW,KAAK,EAAE,SAAS,CAAC;QACnC,IAAI;QACJ,IAAI,CAAC,OAAO,OAAO,MAAM,OAAO,CAAC,CAAA,OAAQ,WAAW,KAAK,KAAK,CAAC,QAAQ,IAAI;QAC3E,IAAI,CAAC,gBAAgB,MAAM,EAAE,CAAC,EAAE,KAAK,QAAQ,cAAc,YAAY,IAAI,CAAC,MAAM,QAAQ,CAAC,MAAM,IAAI,MAAM,SAAS,KAAK,SAAS;QAClI,IAAI,SAAS,GAAG;YACd,oDAAoD;YACpD,4CAA4C;YAC5C,MAAM,QAAQ,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,IAAI,MAAM,QAAQ,CAAC,MAAM,GAAG;QAC/D,OAAO;YACL,IAAI,oBAAoB;gBACtB,4EAA4E;gBAC5E,MAAM,QAAQ,CAAC,MAAM,GAAG;YAC1B,OAAO;gBACL,gEAAgE;gBAChE,MAAM,QAAQ,CAAC,MAAM,GAAG;YAC1B;QACF;QAEA,4CAA4C;QAC5C,IAAI,CAAC,SAAS;YACZ,UAAU;YACV,sBAAsB;QACxB;IACF;IACA,SAAS,QAAQ,SAAS,EAAE,mBAAmB,IAAI,EAAE,KAAK,EAAE,KAAK;QAC/D,IAAI,kBAAkB,mBAAmB,UAAU;QACnD,IAAI,CAAC,OAAO,KAAK,MAAM,QAAQ,MAAM,MAAM,GAAI,SAAS,WAAW,KAAK,KAAK,CAAC,QAAQ;aAAS,SAAS,WAAW,OAAO;QAC1H,IAAI,kBAAkB,mBAAmB,SAAS;IACpD;IACA,OAAO;QACL;QACA;QACA;IACF;AACF;AAEA;;;;;CAKC,GACD,SAAS,kBAAkB,GAAG;;IAC5B,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAY,AAAD,EAAE;IAC9B;uDAA0B,IAAM,KAAK,CAAC,SAAS,OAAO,GAAG,IAAI,OAAO,CAAC,KAAK;sDAAG;QAAC;KAAI;IAClF,OAAO;AACT;IAJS;;QAEP;;;AAGF,SAAS;;IACP,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;IAC/B,IAAI,CAAC,OAAO,MAAM,IAAI,MAAM;IAC5B,OAAO;AACT;IAJS;AAMT;;;CAGC,GACD,SAAS,SAAS,WAAW,CAAA,QAAS,KAAK,EAAE,UAAU;;IACrD,OAAO,WAAW,UAAU;AAC9B;IAFS;;QACA;;;AAGT;;;;CAIC,GACD,SAAS,SAAS,QAAQ,EAAE,iBAAiB,CAAC;;IAC5C,MAAM,QAAQ;IACd,MAAM,YAAY,MAAM,QAAQ,GAAG,QAAQ,CAAC,SAAS;IACrD,cAAc;IACd,MAAM,MAAM,mBAAmB;IAC/B,6CAA6C;IAC7C;8CAA0B,IAAM,UAAU,KAAK,gBAAgB;6CAAQ;QAAC;QAAgB;QAAW;KAAM;IACzG,OAAO;AACT;IARS;;QACO;QAGF;QAEZ;;;AAIF;;;CAGC,GACD,SAAS,SAAS,MAAM;;IACtB,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;4BAAE,IAAM,WAAW;2BAAS;QAAC;KAAO;AACzD;IAFS;AAGT,MAAM,kBAAkB,IAAI;AAC5B,SAAS,UAAU,UAAU,EAAE,UAAU;IACvC,OAAO,SAAU,KAAK,EAAE,GAAG,KAAK;QAC9B,0CAA0C;QAC1C,IAAI,SAAS,gBAAgB,GAAG,CAAC;QACjC,IAAI,CAAC,QAAQ;YACX,SAAS,IAAI;YACb,gBAAgB,GAAG,CAAC,OAAO;QAC7B;QACA,IAAI,YAAY,WAAW;QAC3B,oCAAoC;QACpC,OAAO,QAAQ,GAAG,CAAC,MAAM,GAAG,CAAC,CAAA,QAAS,IAAI,QAAQ,CAAC,KAAK,SAAW,OAAO,IAAI,CAAC,OAAO,CAAA;oBACpF,IAAI,KAAK,KAAK,EAAE,OAAO,MAAM,CAAC,MAAM,WAAW,KAAK,KAAK;oBACzD,IAAI;gBACN,GAAG,YAAY,CAAA,QAAS,OAAO,IAAI,MAAM,CAAC,eAAe,EAAE,MAAM,EAAE,EAAE,SAAS,OAAO,KAAK,IAAI,MAAM,OAAO,EAAE;IAC/G;AACF;AACA;;;;;CAKC,GACD,SAAS,UAAU,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,UAAU;IACrD,oCAAoC;IACpC,MAAM,OAAO,MAAM,OAAO,CAAC,SAAS,QAAQ;QAAC;KAAM;IACnD,MAAM,UAAU,CAAA,GAAA,4IAAA,CAAA,UAAO,AAAD,EAAE,UAAU,YAAY,aAAa;QAAC;WAAU;KAAK,EAAE;QAC3E,OAAO,GAAG,GAAG;IACf;IACA,sBAAsB;IACtB,OAAO,MAAM,OAAO,CAAC,SAAS,UAAU,OAAO,CAAC,EAAE;AACpD;AAEA;;CAEC,GACD,UAAU,OAAO,GAAG,SAAU,KAAK,EAAE,KAAK,EAAE,UAAU;IACpD,MAAM,OAAO,MAAM,OAAO,CAAC,SAAS,QAAQ;QAAC;KAAM;IACnD,OAAO,CAAA,GAAA,4IAAA,CAAA,UAAO,AAAD,EAAE,UAAU,aAAa;QAAC;WAAU;KAAK;AACxD;AAEA;;CAEC,GACD,UAAU,KAAK,GAAG,SAAU,KAAK,EAAE,KAAK;IACtC,MAAM,OAAO,MAAM,OAAO,CAAC,SAAS,QAAQ;QAAC;KAAM;IACnD,OAAO,CAAA,GAAA,4IAAA,CAAA,QAAK,AAAD,EAAE;QAAC;WAAU;KAAK;AAC/B;AAEA,MAAM,QAAQ,IAAI;AAClB,MAAM,EACJ,UAAU,EACV,OAAO,EACR,GAAG,WAAW;AACf,MAAM,EACJ,UAAU,EACV,UAAU,EACX,GAAG,eAAe,OAAO;AAC1B,MAAM,eAAe;IACnB,SAAS;IACT,QAAQ;AACV;AACA,MAAM,yBAAyB,CAAC,IAAI;IAClC,MAAM,iBAAiB,OAAO,OAAO,aAAa,GAAG,UAAU;IAC/D,IAAI,WAAW,iBAAiB,OAAO;SAAoB,OAAO,IAAI,oJAAA,CAAA,gBAAmB,CAAC;QACxF,iBAAiB;QACjB,QAAQ;QACR,WAAW;QACX,OAAO;QACP,GAAG,EAAE;IACP;AACF;AACA,SAAS,mBAAmB,MAAM,EAAE,WAAW;IAC7C,MAAM,eAAe,OAAO,sBAAsB,eAAe,kBAAkB;IACnF,IAAI,aAAa;QACf,MAAM,EACJ,KAAK,EACL,MAAM,EACN,GAAG,EACH,IAAI,EACJ,cAAc,YAAY,EAC3B,GAAG;QACJ,OAAO;YACL;YACA;YACA;YACA;YACA;QACF;IACF,OAAO,IAAI,OAAO,sBAAsB,eAAe,kBAAkB,qBAAqB,OAAO,aAAa,EAAE;QAClH,MAAM,EACJ,KAAK,EACL,MAAM,EACN,GAAG,EACH,IAAI,EACL,GAAG,OAAO,aAAa,CAAC,qBAAqB;QAC9C,OAAO;YACL;YACA;YACA;YACA;YACA,aAAa;QACf;IACF,OAAO,IAAI,OAAO,oBAAoB,eAAe,kBAAkB,iBAAiB;QACtF,OAAO;YACL,OAAO,OAAO,KAAK;YACnB,QAAQ,OAAO,MAAM;YACrB,KAAK;YACL,MAAM;YACN,aAAa;QACf;IACF;IACA,OAAO;QACL,OAAO;QACP,QAAQ;QACR,KAAK;QACL,MAAM;IACR;AACF;AACA,SAAS,WAAW,MAAM;IACxB,2CAA2C;IAC3C,MAAM,WAAW,MAAM,GAAG,CAAC;IAC3B,MAAM,YAAY,YAAY,OAAO,KAAK,IAAI,SAAS,KAAK;IAC5D,MAAM,YAAY,YAAY,OAAO,KAAK,IAAI,SAAS,KAAK;IAC5D,IAAI,UAAU,QAAQ,IAAI,CAAC;IAE3B,yDAAyD;IACzD,wDAAwD;IACxD,MAAM,sBAAsB,OAAO,gBAAgB,aACnD,gEAAgE;IAChE,0CAA0C;IAC1C,cACA,sEAAsE;IACtE,QAAQ,KAAK;IAEb,eAAe;IACf,MAAM,QAAQ,aAAa,YAAY,YAAY;IACnD,kBAAkB;IAClB,MAAM,QAAQ,aAAa,WAAW,eAAe,CAAC,OAAO,mJAAA,CAAA,iBAAc,EAAE,MAAM,OAAO,MAAM,IAAI,qBAAqB;IACzH,SAAS;IACT,IAAI,CAAC,UAAU,MAAM,GAAG,CAAC,QAAQ;QAC/B;QACA;IACF;IAEA,SAAS;IACT,IAAI;IACJ,IAAI,aAAa;IACjB,IAAI;IACJ,OAAO;QACL,WAAU,QAAQ,CAAC,CAAC;YAClB,IAAI,EACF,IAAI,QAAQ,EACZ,MAAM,SAAS,EACf,OAAO,YAAY,EACnB,MAAM,EACN,WAAW,iBAAiB,EAC5B,UAAU,KAAK,EACf,SAAS,KAAK,EACd,OAAO,KAAK,EACZ,SAAS,KAAK,EACd,eAAe,KAAK,EACpB,YAAY,QAAQ,EACpB,MAAM;gBAAC;gBAAG;aAAE,EACZ,aAAA,YAAW,EACX,WAAW,cAAc,EACzB,QAAQ,aAAa,EACrB,eAAe,EAChB,GAAG;YACJ,IAAI,QAAQ,MAAM,QAAQ;YAE1B,mCAAmC;YACnC,IAAI,KAAK,MAAM,EAAE;YACjB,IAAI,CAAC,MAAM,EAAE,EAAE,MAAM,GAAG,CAAC;gBACvB,IAAI,KAAK,uBAAuB,UAAU;YAC5C;YAEA,oCAAoC;YACpC,IAAI,YAAY,MAAM,SAAS;YAC/B,IAAI,CAAC,WAAW,MAAM,GAAG,CAAC;gBACxB,WAAW,YAAY,IAAI,oJAAA,CAAA,YAAe;YAC5C;YAEA,wBAAwB;YACxB,MAAM,EACJ,MAAM,EACN,GAAG,SACJ,GAAG,kBAAkB,CAAC;YACvB,IAAI,CAAC,GAAG,GAAG,CAAC,SAAS,WAAW,eAAe,WAAW,WAAW;gBACnE,GAAG,OAAO;YACZ;YACA,IAAI,CAAC,GAAG,GAAG,CAAC,QAAQ,UAAU,MAAM,EAAE,eAAe,WAAW,WAAW;gBACzE,QAAQ;oBACN,GAAG,UAAU,MAAM;oBACnB,GAAG,MAAM;gBACX;YACF;YAEA,4DAA4D;YAC5D,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM,MAAM,KAAK,cAAc,CAAC,GAAG,GAAG,CAAC,YAAY,eAAe,eAAe;gBACpG,aAAa;gBACb,MAAM,WAAW,yBAAyB,oJAAA,CAAA,SAAY;gBACtD,MAAM,SAAS,WAAW,gBAAgB,eAAe,IAAI,oJAAA,CAAA,qBAAwB,CAAC,GAAG,GAAG,GAAG,GAAG,KAAK,QAAQ,IAAI,oJAAA,CAAA,oBAAuB,CAAC,IAAI,GAAG,KAAK;gBACvJ,IAAI,CAAC,UAAU;oBACb,OAAO,QAAQ,CAAC,CAAC,GAAG;oBACpB,IAAI,eAAe;wBACjB,WAAW,QAAQ;wBACnB,4CAA4C;wBAC5C,0DAA0D;wBAC1D,IAAI,YAAY,iBAAiB,UAAU,iBAAiB,WAAW,iBAAiB,YAAY,iBAAiB,SAAS,eAAe;4BAC3I,OAAO,MAAM,GAAG;4BAChB,OAAO,sBAAsB;wBAC/B;oBACF;oBACA,mCAAmC;oBACnC,IAAI,CAAC,MAAM,MAAM,IAAI,CAAC,CAAC,iBAAiB,QAAQ,cAAc,QAAQ,GAAG,OAAO,MAAM,CAAC,GAAG,GAAG;gBAC/F;gBACA,MAAM,GAAG,CAAC;oBACR;gBACF;gBAEA,sBAAsB;gBACtB,gDAAgD;gBAChD,UAAU,MAAM,GAAG;YACrB;YAEA,gCAAgC;YAChC,IAAI,CAAC,MAAM,KAAK,EAAE;gBAChB,IAAI;gBACJ,IAAI,gBAAgB,QAAQ,aAAa,OAAO,EAAE;oBAChD,QAAQ;gBACV,OAAO;oBACL,QAAQ,IAAI,oJAAA,CAAA,QAAW;oBACvB,IAAI,cAAc,WAAW,OAAO;gBACtC;gBACA,MAAM,GAAG,CAAC;oBACR,OAAO,QAAQ;gBACjB;YACF;YAEA,6BAA6B;YAC7B,IAAI,CAAC,MAAM,EAAE,EAAE;gBACb,IAAI;gBACJ,iCAAiC;gBACjC,MAAM,gBAAgB,CAAC,WAAW;oBAChC,MAAM,QAAQ,MAAM,QAAQ;oBAC5B,IAAI,MAAM,SAAS,KAAK,SAAS;oBACjC,QAAQ,WAAW,MAAM,OAAO;gBAClC;gBAEA,qCAAqC;gBACrC,MAAM,sBAAsB;oBAC1B,MAAM,QAAQ,MAAM,QAAQ;oBAC5B,MAAM,EAAE,CAAC,EAAE,CAAC,OAAO,GAAG,MAAM,EAAE,CAAC,EAAE,CAAC,YAAY;oBAC9C,MAAM,EAAE,CAAC,EAAE,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,YAAY,GAAG,gBAAgB;oBACxE,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,YAAY,EAAE,WAAW;gBAC5C;gBAEA,wBAAwB;gBACxB,MAAM,KAAK;oBACT;wBACE,MAAM,KAAK,MAAM,QAAQ,GAAG,EAAE;wBAC9B,GAAG,EAAE,CAAC,gBAAgB,CAAC,gBAAgB;wBACvC,GAAG,EAAE,CAAC,gBAAgB,CAAC,cAAc;oBACvC;oBACA;wBACE,MAAM,KAAK,MAAM,QAAQ,GAAG,EAAE;wBAC9B,GAAG,EAAE,CAAC,mBAAmB,CAAC,gBAAgB;wBAC1C,GAAG,EAAE,CAAC,mBAAmB,CAAC,cAAc;oBAC1C;gBACF;gBAEA,oCAAoC;gBACpC,IAAI,OAAO,CAAC,CAAC,SAAS,GAAG,EAAE,KAAK,OAAO,KAAK,IAAI,OAAO,gBAAgB,MAAM,YAAY,GAAG,OAAO;gBACnG,MAAM,GAAG,CAAC;oBACR;gBACF;YACF;YAEA,gBAAgB;YAChB,IAAI,GAAG,SAAS,EAAE;gBAChB,MAAM,aAAa,GAAG,SAAS,CAAC,OAAO;gBACvC,MAAM,UAAU,GAAG,SAAS,CAAC,IAAI;gBACjC,GAAG,SAAS,CAAC,OAAO,GAAG,CAAC,CAAC;gBACzB,IAAI,GAAG,GAAG,CAAC,UAAU;oBACnB,GAAG,SAAS,CAAC,IAAI,GAAG,oJAAA,CAAA,mBAAsB;gBAC5C,OAAO,IAAI,GAAG,GAAG,CAAC,UAAU;oBAC1B,IAAI;oBACJ,MAAM,QAAQ;wBACZ,OAAO,oJAAA,CAAA,iBAAoB;wBAC3B,YAAY,oJAAA,CAAA,eAAkB;wBAC9B,MAAM,oJAAA,CAAA,mBAAsB;wBAC5B,UAAU,oJAAA,CAAA,eAAkB;oBAC9B;oBACA,GAAG,SAAS,CAAC,IAAI,GAAG,CAAC,iBAAiB,KAAK,CAAC,QAAQ,KAAK,OAAO,iBAAiB,oJAAA,CAAA,mBAAsB;gBACzG,OAAO,IAAI,GAAG,GAAG,CAAC,UAAU;oBAC1B,OAAO,MAAM,CAAC,GAAG,SAAS,EAAE;gBAC9B;gBACA,IAAI,eAAe,GAAG,SAAS,CAAC,OAAO,IAAI,YAAY,GAAG,SAAS,CAAC,IAAI,EAAE,GAAG,SAAS,CAAC,WAAW,GAAG;YACvG;YAEA,4CAA4C;YAC5C,yEAAyE;YACzE,MAAM,kBAAkB;YACxB,IAAI,iBAAiB;gBACnB,IAAI,aAAa,iBAAiB,gBAAgB,OAAO,GAAG,CAAC;qBAAY,IAAI,gBAAgB,iBAAiB,gBAAgB,UAAU,GAAG;YAC7I;YACA,IAAI,CAAC,YAAY;gBACf,oDAAoD;gBACpD,MAAM,iBAAiB;gBACvB,MAAM,eAAe;gBACrB,WAAW,IAAI;oBACb,gBAAgB,SAAS,iBAAiB;oBAC1C,aAAa,OAAO,oJAAA,CAAA,gBAAmB,GAAG,oJAAA,CAAA,wBAA2B;gBACvE;YACF;YAEA,gCAAgC;YAChC,IAAI,MAAM,MAAM,KAAK,QAAQ,MAAM,GAAG,CAAC,IAAM,CAAC;oBAC5C;gBACF,CAAC;YACD,IAAI,MAAM,MAAM,KAAK,QAAQ,MAAM,GAAG,CAAC,IAAM,CAAC;oBAC5C;gBACF,CAAC;YACD,IAAI,MAAM,IAAI,KAAK,MAAM,MAAM,GAAG,CAAC,IAAM,CAAC;oBACxC;gBACF,CAAC;YAED,eAAe;YACf,IAAI,YAAY,CAAC,GAAG,GAAG,CAAC,aAAa,CAAC,WAAW,aAAa,CAAC,GAAG,GAAG,CAAC,UAAU,IAAI,eAAe,WAAW,IAAI;YAClH,0BAA0B;YAC1B,IAAI,UAAU,CAAC,MAAM,MAAM,CAAC,QAAQ,EAAE,MAAM,GAAG,CAAC;gBAC9C,QAAQ,OAAO;YACjB;YACA,6DAA6D;YAC7D,MAAM,OAAO,mBAAmB,QAAQ;YACxC,IAAI,CAAC,GAAG,GAAG,CAAC,MAAM,MAAM,IAAI,EAAE,eAAe;gBAC3C,MAAM,OAAO,CAAC,KAAK,KAAK,EAAE,KAAK,MAAM,EAAE,KAAK,WAAW,EAAE,KAAK,GAAG,EAAE,KAAK,IAAI;YAC9E;YACA,mBAAmB;YACnB,IAAI,OAAO,MAAM,QAAQ,CAAC,GAAG,KAAK,aAAa,MAAM,MAAM,MAAM,CAAC;YAClE,kBAAkB;YAClB,IAAI,MAAM,SAAS,KAAK,WAAW,MAAM,YAAY,CAAC;YACtD,uBAAuB;YACvB,IAAI,CAAC,MAAM,eAAe,EAAE,MAAM,GAAG,CAAC;gBACpC;YACF;YACA,oBAAoB;YACpB,IAAI,gBAAe,CAAC,GAAG,GAAG,CAAC,cAAa,MAAM,WAAW,EAAE,eAAe,MAAM,GAAG,CAAC,CAAA,QAAS,CAAC;oBAC5F,aAAa;wBACX,GAAG,MAAM,WAAW;wBACpB,GAAG,YAAW;oBAChB;gBACF,CAAC;YAED,aAAa;YACb,YAAY;YACZ,aAAa;YACb,OAAO,IAAI;QACb;QACA,QAAO,QAAQ;YACb,0DAA0D;YAC1D,IAAI,CAAC,YAAY,IAAI,CAAC,SAAS;YAC/B,WAAW,eAAe,CAAE,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,UAAU;gBACrD,OAAO;gBACP,UAAU;gBACV,WAAW;gBACX,aAAa;YACf,IAAI,OAAO,MAAM,IAAM;YACvB,OAAO;QACT;QACA;YACE,uBAAuB;QACzB;IACF;AACF;AACA,SAAS,OAAO,QAAQ,EAAE,MAAM,EAAE,MAAM;IACtC,QAAQ,IAAI,CAAC;IACb,MAAM,OAAO,WAAW;IACxB,KAAK,SAAS,CAAC;IACf,OAAO,KAAK,MAAM,CAAC;AACrB;AACA,SAAS,SAAS,EAChB,KAAK,EACL,QAAQ,EACR,SAAS,EACT,WAAW,EACZ;;IACC;8CAA0B;YACxB,MAAM,QAAQ,MAAM,QAAQ;YAC5B,mDAAmD;YACnD,MAAM,GAAG;sDAAC,CAAA,QAAS,CAAC;wBAClB,UAAU;4BACR,GAAG,MAAM,QAAQ;4BACjB,QAAQ;wBACV;oBACF,CAAC;;YACD,uFAAuF;YACvF,IAAI,WAAW,UAAU;YACzB,wFAAwF;YACxF,gDAAgD;YAChD,IAAI,CAAC,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,EAAE,MAAM,MAAM,CAAC,OAAO,IAAI,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC,OAAO,CAAC;QACrG,uDAAuD;QACzD;6CAAG,EAAE;IACL,OAAO,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,QAAQ,QAAQ,EAAE;QACxC,OAAO;QACP,UAAU;IACZ;AACF;IA1BS;;QAMP;;;MANO;AA2BT,SAAS,uBAAuB,MAAM,EAAE,QAAQ;IAC9C,MAAM,OAAO,MAAM,GAAG,CAAC;IACvB,MAAM,QAAQ,QAAQ,OAAO,KAAK,IAAI,KAAK,KAAK;IAChD,IAAI,OAAO;QACT,MAAM,QAAQ,QAAQ,OAAO,KAAK,IAAI,KAAK,KAAK,CAAC,QAAQ;QACzD,IAAI,OAAO,MAAM,QAAQ,CAAC,MAAM,GAAG;QACnC,WAAW,eAAe,CAAC,MAAM,OAAO,MAAM;YAC5C,IAAI,OAAO;gBACT,WAAW;oBACT,IAAI;wBACF,IAAI,WAAW,uBAAuB,YAAY;wBAClD,MAAM,MAAM,CAAC,UAAU,IAAI,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC,UAAU;wBAClE,CAAC,YAAY,MAAM,EAAE,KAAK,OAAO,KAAK,IAAI,CAAC,wBAAwB,UAAU,WAAW,KAAK,OAAO,KAAK,IAAI,sBAAsB,OAAO,IAAI,OAAO,KAAK,IAAI,sBAAsB,OAAO;wBAC3L,CAAC,aAAa,MAAM,EAAE,KAAK,OAAO,KAAK,IAAI,WAAW,gBAAgB,IAAI,OAAO,KAAK,IAAI,WAAW,gBAAgB;wBACrH,IAAI,CAAC,aAAa,MAAM,EAAE,KAAK,QAAQ,WAAW,EAAE,EAAE,MAAM,EAAE,CAAC,UAAU;wBACzE,QAAQ;wBACR,MAAM,MAAM,CAAC;wBACb,IAAI,UAAU,SAAS;oBACzB,EAAE,OAAO,GAAG;oBACV,OAAO,GACT;gBACF,GAAG;YACL;QACF;IACF;AACF;AACA,SAAS,aAAa,QAAQ,EAAE,SAAS,EAAE,KAAK;IAC9C,OAAO,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;QAC9B,UAAU;QACV,WAAW;QACX,OAAO;IACT,GAAG,UAAU,IAAI;AACnB;AACA,SAAS,OAAO,EACd,QAAQ,CAAC,CAAC,EACV,QAAQ,EACR,SAAS,EACV;;IACC;;;;4BAI0B,GAC1B,MAAM,EACJ,MAAM,EACN,IAAI,EACJ,GAAG,MACJ,GAAG;IACJ,MAAM,eAAe;IACrB,MAAM,CAAC,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD;2BAAE,IAAM,IAAI,oJAAA,CAAA,YAAe;;IAC5D,MAAM,CAAC,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD;2BAAE,IAAM,IAAI,oJAAA,CAAA,UAAa;;IACxD,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;sCAAE,CAAC,WAAW;YAC3C,MAAM,YAAY;gBAChB,GAAG,SAAS;YACd,GAAG,uBAAuB;YAE1B,qEAAqE;YACrE,kCAAkC;YAClC,6FAA6F;YAC7F,OAAO,IAAI,CAAC,WAAW,OAAO;8CAAC,CAAA;oBAC7B,IACA,kCAAkC;oBAClC,YAAY,QAAQ,CAAC,QACrB,6FAA6F;oBAC7F,0EAA0E;oBAC1E,SAAS,CAAC,IAAI,KAAK,WAAW,CAAC,IAAI,IAAI,WAAW,CAAC,IAAI,EAAE;wBACvD,OAAO,SAAS,CAAC,IAAI;oBACvB;gBACF;;YACA,IAAI,WAAW;YACf,IAAI,eAAe,MAAM;gBACvB,MAAM,SAAS,YAAY,MAAM;gBACjC,8CAA8C;gBAC9C,WAAW,UAAU,QAAQ,CAAC,kBAAkB,CAAC,QAAQ,IAAI,oJAAA,CAAA,UAAa,IAAI;gBAC9E,kEAAkE;gBAClE,IAAI,WAAW,UAAU,MAAM,EAAE,aAAa,QAAQ;YACxD;YACA,OAAO;gBACL,oDAAoD;gBACpD,GAAG,SAAS;gBACZ,gFAAgF;gBAChF,OAAO;gBACP;gBACA;gBACA,OAAO;gBACP,6CAA6C;gBAC7C;gBACA,kEAAkE;gBAClE,QAAQ;oBACN,GAAG,UAAU,MAAM;oBACnB,GAAI,eAAe,OAAO,KAAK,IAAI,YAAY,MAAM;oBACrD,GAAG,MAAM;gBACX;gBACA,MAAM;oBACJ,GAAG,UAAU,IAAI;oBACjB,GAAG,IAAI;gBACT;gBACA,UAAU;oBACR,GAAG,UAAU,QAAQ;oBACrB,GAAG,QAAQ;gBACb;gBACA,GAAG,IAAI;YACT;QACF;qCACA,uDAAuD;IACvD;QAAC;KAAM;IACP,MAAM,CAAC,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD;2BAAE;YACtC,+EAA+E;YAC/E,MAAM,gBAAgB,aAAa,QAAQ;YAC3C,MAAM,QAAQ,CAAA,GAAA,uLAAA,CAAA,UAAM,AAAD;yCAAE,CAAC,KAAK,MAAQ,CAAC;wBAClC,GAAG,aAAa;wBAChB,OAAO;wBACP;wBACA;wBACA,OAAO;wBACP;wBACA,QAAQ;4BACN,GAAG,cAAc,MAAM;4BACvB,GAAG,MAAM;wBACX;wBACA,MAAM;4BACJ,GAAG,cAAc,IAAI;4BACrB,GAAG,IAAI;wBACT;wBACA,GAAG,IAAI;wBACP,uCAAuC;wBACvC;wBACA;wBACA,wCAAwC;wBACxC,SAAS;qDAAE,CAAA,SAAU;6DAAI,CAAA,QAAS,CAAC;4CACjC,GAAG,KAAK;4CACR,QAAQ;gDACN,GAAG,MAAM,MAAM;gDACf,GAAG,MAAM;4CACX;wCACF,CAAC;;;oBACH,CAAC;;YACD,OAAO;QACT;;IACA,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;4BAAE;YACd,sFAAsF;YACtF,MAAM,QAAQ,aAAa,SAAS;0CAAC,CAAA,OAAQ,eAAe,QAAQ;kDAAC,CAAA,QAAS,OAAO,MAAM;;;YAC3F;oCAAO;oBACL;gBACF;;QACA,uDAAuD;QACzD;2BAAG;QAAC;KAAO;IACX,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;4BAAE;YACd,eAAe,QAAQ;oCAAC,CAAA,cAAe,OAAO,aAAa,QAAQ,IAAI;;QACvE,uDAAuD;QACzD;2BAAG;QAAC;KAAO;IACX,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;4BAAE;YACd;oCAAO;oBACL,eAAe,OAAO;gBACxB;;QACA,uDAAuD;QACzD;2BAAG,EAAE;IACL,OAAO,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,sKAAA,CAAA,WAAQ,EAAE;QAChC,UAAU,WAAW,YAAY,CAAE,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,QAAQ,QAAQ,EAAE;YACpE,OAAO;YACP,UAAU;QACZ,IAAI,gBAAgB;IACtB;AACF;IAlIS;;QAec;;;MAfd;AAoIT;;;;;CAKC,GACD,SAAS,UAAU,EAAE;IACnB,+GAA+G;IAC/G,OAAO,WAAW,SAAS,CAAC,IAAI;AAClC;AACA,WAAW,kBAAkB,CAAC;IAC5B,YAAY,6EAA4C;IACxD,qBAAqB;IACrB,SAAS,6JAAA,CAAA,UAAa;AACxB;AACA,MAAM,MAAM,6JAAA,CAAA,eAAkB;AAE9B,MAAM,aAAa;IACjB,SAAS;QAAC;QAAS;KAAM;IACzB,eAAe;QAAC;QAAe;KAAM;IACrC,eAAe;QAAC;QAAY;KAAM;IAClC,SAAS;QAAC;QAAS;KAAK;IACxB,eAAe;QAAC;QAAe;KAAK;IACpC,aAAa;QAAC;QAAa;KAAK;IAChC,gBAAgB;QAAC;QAAgB;KAAK;IACtC,eAAe;QAAC;QAAe;KAAK;IACpC,iBAAiB;QAAC;QAAiB;KAAK;IACxC,sBAAsB;QAAC;QAAsB;KAAK;AACpD;AAEA,sCAAsC,GACtC,SAAS,oBAAoB,KAAK;IAChC,MAAM,EACJ,aAAa,EACd,GAAG,aAAa;IACjB,OAAO;QACL,UAAU;QACV,SAAS;QACT,SAAQ,KAAK,EAAE,KAAK,EAAE,QAAQ;YAC5B,uDAAuD;YACvD,4FAA4F;YAC5F,MAAM,OAAO,CAAC,GAAG,CAAC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,MAAM,IAAI,IAAI;YACvG,MAAM,SAAS,CAAC,aAAa,CAAC,MAAM,OAAO,EAAE,MAAM,MAAM;QAC3D;QACA,WAAW;QACX,UAAU,OAAO,IAAI,CAAC,YAAY,MAAM,CAAC,CAAC,KAAK,MAAQ,CAAC;gBACtD,GAAG,GAAG;gBACN,CAAC,IAAI,EAAE,cAAc;YACvB,CAAC,GAAG,CAAC;QACL,QAAQ;YACN,IAAI;YACJ,MAAM,EACJ,MAAM,EACN,QAAQ,EACT,GAAG,MAAM,QAAQ;YAClB,IAAI,CAAC,sBAAsB,SAAS,SAAS,KAAK,QAAQ,oBAAoB,OAAO,IAAI,OAAO,QAAQ,EAAE,OAAO,QAAQ,CAAC,aAAa,CAAC,SAAS,SAAS,CAAC,OAAO;QACpK;QACA,SAAS,CAAA;YACP,IAAI;YACJ,MAAM,EACJ,GAAG,EACH,MAAM,EACP,GAAG,MAAM,QAAQ;YAClB,OAAO,UAAU,IAAI,OAAO,KAAK,IAAI,OAAO,UAAU;YACtD,IAAI,CAAA,QAAS,CAAC;oBACZ,QAAQ;wBACN,GAAG,MAAM,MAAM;wBACf,WAAW;oBACb;gBACF,CAAC;YACD,OAAO,OAAO,CAAC,CAAC,mBAAmB,OAAO,QAAQ,KAAK,OAAO,mBAAmB,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC,MAAM,MAAM;gBACzG,MAAM,CAAC,WAAW,QAAQ,GAAG,UAAU,CAAC,KAAK;gBAC7C,OAAO,gBAAgB,CAAC,WAAW,OAAO;oBACxC;gBACF;YACF;QACF;QACA,YAAY;YACV,MAAM,EACJ,GAAG,EACH,MAAM,EACP,GAAG,MAAM,QAAQ;YAClB,IAAI,OAAO,SAAS,EAAE;gBACpB,IAAI;gBACJ,OAAO,OAAO,CAAC,CAAC,oBAAoB,OAAO,QAAQ,KAAK,OAAO,oBAAoB,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC,MAAM,MAAM;oBAC3G,IAAI,UAAU,OAAO,SAAS,YAAY,aAAa;wBACrD,MAAM,CAAC,UAAU,GAAG,UAAU,CAAC,KAAK;wBACpC,OAAO,SAAS,CAAC,mBAAmB,CAAC,WAAW;oBAClD;gBACF;gBACA,IAAI,CAAA,QAAS,CAAC;wBACZ,QAAQ;4BACN,GAAG,MAAM,MAAM;4BACf,WAAW;wBACb;oBACF,CAAC;YACH;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2362, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/%40react-three/fiber/dist/react-three-fiber.esm.js"], "sourcesContent": ["import { c as createPointerEvents, e as extend, u as useMutableCallback, a as useIsomorphicLayoutEffect, b as createRoot, i as isRef, E as ErrorBoundary, B as Block, d as unmountComponentAtNode } from './events-776716bd.esm.js';\nexport { t as ReactThreeFiber, z as _roots, x as act, p as addAfterEffect, o as addEffect, q as addTail, n as advance, k as applyProps, y as buildGraph, g as context, f as createEvents, c as createPointerEvents, h as createPortal, b as createRoot, l as dispose, c as events, e as extend, s as flushGlobalEffects, v as flushSync, w as getRootState, m as invalidate, j as reconciler, r as render, d as unmountComponentAtNode, F as useFrame, G as useGraph, A as useInstanceHandle, H as useLoader, C as useStore, D as useThree } from './events-776716bd.esm.js';\nimport * as React from 'react';\nimport * as THREE from 'three';\nimport useMeasure from 'react-use-measure';\nimport { FiberProvider, useContextBridge } from 'its-fine';\nimport { jsx } from 'react/jsx-runtime';\nimport 'react-reconciler/constants';\nimport 'zustand';\nimport 'suspend-react';\nimport 'react-reconciler';\nimport 'scheduler';\n\nconst CanvasImpl = /*#__PURE__*/React.forwardRef(function Canvas({\n  children,\n  fallback,\n  resize,\n  style,\n  gl,\n  events = createPointerEvents,\n  eventSource,\n  eventPrefix,\n  shadows,\n  linear,\n  flat,\n  legacy,\n  orthographic,\n  frameloop,\n  dpr,\n  performance,\n  raycaster,\n  camera,\n  scene,\n  onPointerMissed,\n  onCreated,\n  ...props\n}, forwardedRef) {\n  // Create a known catalogue of Threejs-native elements\n  // This will include the entire THREE namespace by default, users can extend\n  // their own elements by using the createRoot API instead\n  React.useMemo(() => extend(THREE), []);\n  const Bridge = useContextBridge();\n  const [containerRef, containerRect] = useMeasure({\n    scroll: true,\n    debounce: {\n      scroll: 50,\n      resize: 0\n    },\n    ...resize\n  });\n  const canvasRef = React.useRef(null);\n  const divRef = React.useRef(null);\n  React.useImperativeHandle(forwardedRef, () => canvasRef.current);\n  const handlePointerMissed = useMutableCallback(onPointerMissed);\n  const [block, setBlock] = React.useState(false);\n  const [error, setError] = React.useState(false);\n\n  // Suspend this component if block is a promise (2nd run)\n  if (block) throw block;\n  // Throw exception outwards if anything within canvas throws\n  if (error) throw error;\n  const root = React.useRef(null);\n  useIsomorphicLayoutEffect(() => {\n    const canvas = canvasRef.current;\n    if (containerRect.width > 0 && containerRect.height > 0 && canvas) {\n      if (!root.current) root.current = createRoot(canvas);\n      root.current.configure({\n        gl,\n        events,\n        shadows,\n        linear,\n        flat,\n        legacy,\n        orthographic,\n        frameloop,\n        dpr,\n        performance,\n        raycaster,\n        camera,\n        scene,\n        size: containerRect,\n        // Pass mutable reference to onPointerMissed so it's free to update\n        onPointerMissed: (...args) => handlePointerMissed.current == null ? void 0 : handlePointerMissed.current(...args),\n        onCreated: state => {\n          // Connect to event source\n          state.events.connect == null ? void 0 : state.events.connect(eventSource ? isRef(eventSource) ? eventSource.current : eventSource : divRef.current);\n          // Set up compute function\n          if (eventPrefix) {\n            state.setEvents({\n              compute: (event, state) => {\n                const x = event[eventPrefix + 'X'];\n                const y = event[eventPrefix + 'Y'];\n                state.pointer.set(x / state.size.width * 2 - 1, -(y / state.size.height) * 2 + 1);\n                state.raycaster.setFromCamera(state.pointer, state.camera);\n              }\n            });\n          }\n          // Call onCreated callback\n          onCreated == null ? void 0 : onCreated(state);\n        }\n      });\n      root.current.render( /*#__PURE__*/jsx(Bridge, {\n        children: /*#__PURE__*/jsx(ErrorBoundary, {\n          set: setError,\n          children: /*#__PURE__*/jsx(React.Suspense, {\n            fallback: /*#__PURE__*/jsx(Block, {\n              set: setBlock\n            }),\n            children: children != null ? children : null\n          })\n        })\n      }));\n    }\n  });\n  React.useEffect(() => {\n    const canvas = canvasRef.current;\n    if (canvas) return () => unmountComponentAtNode(canvas);\n  }, []);\n\n  // When the event source is not this div, we need to set pointer-events to none\n  // Or else the canvas will block events from reaching the event source\n  const pointerEvents = eventSource ? 'none' : 'auto';\n  return /*#__PURE__*/jsx(\"div\", {\n    ref: divRef,\n    style: {\n      position: 'relative',\n      width: '100%',\n      height: '100%',\n      overflow: 'hidden',\n      pointerEvents,\n      ...style\n    },\n    ...props,\n    children: /*#__PURE__*/jsx(\"div\", {\n      ref: containerRef,\n      style: {\n        width: '100%',\n        height: '100%'\n      },\n      children: /*#__PURE__*/jsx(\"canvas\", {\n        ref: canvasRef,\n        style: {\n          display: 'block'\n        },\n        children: fallback\n      })\n    })\n  });\n});\n\n/**\r\n * A DOM canvas which accepts threejs elements as children.\r\n * @see https://docs.pmnd.rs/react-three-fiber/api/canvas\r\n */\nconst Canvas = /*#__PURE__*/React.forwardRef(function CanvasWrapper(props, ref) {\n  return /*#__PURE__*/jsx(FiberProvider, {\n    children: /*#__PURE__*/jsx(CanvasImpl, {\n      ...props,\n      ref: ref\n    })\n  });\n});\n\nexport { Canvas };\n"], "names": [], "mappings": ";;;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;;;;;;;;;;;;;;AAEA,MAAM,aAAa,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,KAAE,SAAS,OAAO,EAC/D,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,KAAK,EACL,EAAE,EACF,SAAS,iLAAA,CAAA,IAAmB,EAC5B,WAAW,EACX,WAAW,EACX,OAAO,EACP,MAAM,EACN,IAAI,EACJ,MAAM,EACN,YAAY,EACZ,SAAS,EACT,GAAG,EACH,WAAW,EACX,SAAS,EACT,MAAM,EACN,KAAK,EACL,eAAe,EACf,SAAS,EACT,GAAG,OACJ,EAAE,YAAY;;IACb,sDAAsD;IACtD,4EAA4E;IAC5E,yDAAyD;IACzD,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;qCAAE,IAAM,CAAA,GAAA,iLAAA,CAAA,IAAM,AAAD,EAAE;oCAAQ,EAAE;IACrC,MAAM,SAAS,CAAA,GAAA,+IAAA,CAAA,mBAAgB,AAAD;IAC9B,MAAM,CAAC,cAAc,cAAc,GAAG,CAAA,GAAA,2JAAA,CAAA,UAAU,AAAD,EAAE;QAC/C,QAAQ;QACR,UAAU;YACR,QAAQ;YACR,QAAQ;QACV;QACA,GAAG,MAAM;IACX;IACA,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAY,AAAD,EAAE;IAC/B,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,SAAY,AAAD,EAAE;IAC5B,CAAA,GAAA,6JAAA,CAAA,sBAAyB,AAAD,EAAE;iDAAc,IAAM,UAAU,OAAO;;IAC/D,MAAM,sBAAsB,CAAA,GAAA,iLAAA,CAAA,IAAkB,AAAD,EAAE;IAC/C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;IAEzC,yDAAyD;IACzD,IAAI,OAAO,MAAM;IACjB,4DAA4D;IAC5D,IAAI,OAAO,MAAM;IACjB,MAAM,OAAO,CAAA,GAAA,6JAAA,CAAA,SAAY,AAAD,EAAE;IAC1B,CAAA,GAAA,iLAAA,CAAA,IAAyB,AAAD;uDAAE;YACxB,MAAM,SAAS,UAAU,OAAO;YAChC,IAAI,cAAc,KAAK,GAAG,KAAK,cAAc,MAAM,GAAG,KAAK,QAAQ;gBACjE,IAAI,CAAC,KAAK,OAAO,EAAE,KAAK,OAAO,GAAG,CAAA,GAAA,iLAAA,CAAA,IAAU,AAAD,EAAE;gBAC7C,KAAK,OAAO,CAAC,SAAS,CAAC;oBACrB;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA,MAAM;oBACN,mEAAmE;oBACnE,eAAe;uEAAE,CAAC,GAAG,OAAS,oBAAoB,OAAO,IAAI,OAAO,KAAK,IAAI,oBAAoB,OAAO,IAAI;;oBAC5G,SAAS;uEAAE,CAAA;4BACT,0BAA0B;4BAC1B,MAAM,MAAM,CAAC,OAAO,IAAI,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC,OAAO,CAAC,cAAc,CAAA,GAAA,iLAAA,CAAA,IAAK,AAAD,EAAE,eAAe,YAAY,OAAO,GAAG,cAAc,OAAO,OAAO;4BAClJ,0BAA0B;4BAC1B,IAAI,aAAa;gCACf,MAAM,SAAS,CAAC;oCACd,OAAO;uFAAE,CAAC,OAAO;4CACf,MAAM,IAAI,KAAK,CAAC,cAAc,IAAI;4CAClC,MAAM,IAAI,KAAK,CAAC,cAAc,IAAI;4CAClC,MAAM,OAAO,CAAC,GAAG,CAAC,IAAI,MAAM,IAAI,CAAC,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC,IAAI,MAAM,IAAI,CAAC,MAAM,IAAI,IAAI;4CAC/E,MAAM,SAAS,CAAC,aAAa,CAAC,MAAM,OAAO,EAAE,MAAM,MAAM;wCAC3D;;gCACF;4BACF;4BACA,0BAA0B;4BAC1B,aAAa,OAAO,KAAK,IAAI,UAAU;wBACzC;;gBACF;gBACA,KAAK,OAAO,CAAC,MAAM,CAAE,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,QAAQ;oBAC5C,UAAU,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,iLAAA,CAAA,IAAa,EAAE;wBACxC,KAAK;wBACL,UAAU,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,6JAAA,CAAA,WAAc,EAAE;4BACzC,UAAU,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,iLAAA,CAAA,IAAK,EAAE;gCAChC,KAAK;4BACP;4BACA,UAAU,YAAY,OAAO,WAAW;wBAC1C;oBACF;gBACF;YACF;QACF;;IACA,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;uCAAE;YACd,MAAM,SAAS,UAAU,OAAO;YAChC,IAAI,QAAQ;+CAAO,IAAM,CAAA,GAAA,iLAAA,CAAA,IAAsB,AAAD,EAAE;;QAClD;sCAAG,EAAE;IAEL,+EAA+E;IAC/E,sEAAsE;IACtE,MAAM,gBAAgB,cAAc,SAAS;IAC7C,OAAO,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,OAAO;QAC7B,KAAK;QACL,OAAO;YACL,UAAU;YACV,OAAO;YACP,QAAQ;YACR,UAAU;YACV;YACA,GAAG,KAAK;QACV;QACA,GAAG,KAAK;QACR,UAAU,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,OAAO;YAChC,KAAK;YACL,OAAO;gBACL,OAAO;gBACP,QAAQ;YACV;YACA,UAAU,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,UAAU;gBACnC,KAAK;gBACL,OAAO;oBACL,SAAS;gBACX;gBACA,UAAU;YACZ;QACF;IACF;AACF;;QA3GiB,+IAAA,CAAA,mBAAgB;QACO,2JAAA,CAAA,UAAU;QAWpB,iLAAA,CAAA,IAAkB;QAS9C,iLAAA,CAAA,IAAyB;;;KAjDrB;AAyIN;;;CAGC,GACD,MAAM,SAAS,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAAE,SAAS,cAAc,KAAK,EAAE,GAAG;IAC5E,OAAO,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,+IAAA,CAAA,gBAAa,EAAE;QACrC,UAAU,WAAW,GAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,YAAY;YACrC,GAAG,KAAK;YACR,KAAK;QACP;IACF;AACF", "ignoreList": [0], "debugId": null}}]}