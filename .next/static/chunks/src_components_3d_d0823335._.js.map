{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/3d/shaders/particleShader.ts"], "sourcesContent": ["\nimport * as THREE from 'three';\n\nexport const createParticleShader = (color: string, texture: THREE.Texture | null) => {\n  return {\n    uniforms: {\n      pointTexture: { value: texture || new THREE.TextureLoader().load('/images/particle.png') },\n      time: { value: 0 },\n      mousePosition: { value: new THREE.Vector2(0, 0) },\n      color: { value: new THREE.Color(color) }\n    },\n    vertexShader: `\n      uniform float time;\n      uniform vec2 mousePosition;\n      \n      attribute float size;\n      varying vec3 vColor;\n      \n      void main() {\n        // Add subtle variation to color based on position\n        vColor = vec3(${color.replace('#', '0x')}) * (1.0 + sin(position.x * 0.5 + time * 0.2) * 0.2);\n        \n        // Apply time-based animation\n        vec4 mvPosition = modelViewMatrix * vec4(position, 1.0);\n        \n        // Apply subtle wave effect\n        float wave = sin(position.x * 1.5 + time) * 0.1 + \n                     sin(position.y * 1.5 + time * 0.8) * 0.1;\n        mvPosition.z += wave;\n        \n        // Mouse interaction\n        float dist = length(mousePosition - vec2(position.xy));\n        float mouseEffect = max(0.0, 1.0 - dist / 5.0);\n        mvPosition.z += mouseEffect * 0.5;\n        \n        gl_Position = projectionMatrix * mvPosition;\n        \n        // Vary size slightly based on position for more natural look\n        float sizeFactor = 1.0 + sin(position.x * 3.0 + position.y * 2.0) * 0.3;\n        gl_PointSize = size * sizeFactor * (300.0 / -mvPosition.z);\n      }\n    `,\n    fragmentShader: `\n      uniform sampler2D pointTexture;\n      varying vec3 vColor;\n      \n      void main() {\n        vec4 texColor = texture2D(pointTexture, gl_PointCoord);\n        gl_FragColor = vec4(vColor, 1.0) * texColor;\n        \n        // Enhance the alpha blending to match the nebula effect\n        if (gl_FragColor.a < 0.05) discard;\n      }\n    `\n  };\n};\n"], "names": [], "mappings": ";;;AACA;;AAEO,MAAM,uBAAuB,CAAC,OAAe;IAClD,OAAO;QACL,UAAU;YACR,cAAc;gBAAE,OAAO,WAAW,IAAI,oJAAA,CAAA,gBAAmB,GAAG,IAAI,CAAC;YAAwB;YACzF,MAAM;gBAAE,OAAO;YAAE;YACjB,eAAe;gBAAE,OAAO,IAAI,oJAAA,CAAA,UAAa,CAAC,GAAG;YAAG;YAChD,OAAO;gBAAE,OAAO,IAAI,oJAAA,CAAA,QAAW,CAAC;YAAO;QACzC;QACA,cAAc,CAAC;;;;;;;;;sBASG,EAAE,MAAM,OAAO,CAAC,KAAK,MAAM;;;;;;;;;;;;;;;;;;;;;IAqB7C,CAAC;QACD,gBAAgB,CAAC;;;;;;;;;;;IAWjB,CAAC;IACH;AACF", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/3d/InteractiveThreeScene.tsx"], "sourcesContent": ["\nimport { useRef, useState, useEffect } from 'react';\nimport { useFrame, useThree } from '@react-three/fiber';\nimport * as THREE from 'three';\nimport { motion } from 'framer-motion';\nimport { useSpring } from '@react-spring/three';\nimport { createParticleShader } from './shaders/particleShader';\n\ninterface InteractiveThreeSceneProps {\n  color?: string;\n  count?: number;\n  size?: number;\n  mouseInfluence?: number;\n}\n\nconst InteractiveThreeScene = ({\n  color = \"#3fb950\",\n  count = 2000,\n  size = 0.06,\n  mouseInfluence = 0.05,\n}: InteractiveThreeSceneProps) => {\n  // Create a reference to the points object\n  const pointsRef = useRef<THREE.Points>(null!);\n  \n  // Get the mouse and viewport from Three\n  const { mouse, viewport } = useThree();\n  \n  // Store the original particle positions\n  const [particlePositions, setParticlePositions] = useState<Float32Array | null>(null);\n  \n  // Store texture\n  const [texture, setTexture] = useState<THREE.Texture | null>(null);\n  \n  // Load texture\n  useEffect(() => {\n    const textureLoader = new THREE.TextureLoader();\n    textureLoader.load('/images/particle.png', (loadedTexture) => {\n      loadedTexture.premultiplyAlpha = true;\n      setTexture(loadedTexture);\n    });\n  }, []);\n  \n  // Spring for smooth camera movement\n  const [{ cameraX, cameraY }] = useSpring(() => ({\n    cameraX: 0,\n    cameraY: 0,\n    config: { mass: 1, tension: 100, friction: 30 },\n  }));\n  \n  // Create particle positions\n  useEffect(() => {\n    const positions = new Float32Array(count * 3);\n    \n    for (let i = 0; i < count; i++) {\n      // Create particles in a more cloud-like formation to match the nebula image\n      const radius = Math.random() * 5 + 0.5;\n      const phi = Math.acos((Math.random() * 2) - 1);\n      const theta = Math.random() * Math.PI * 2;\n      \n      positions[i * 3] = radius * Math.sin(phi) * Math.cos(theta);     // x\n      positions[i * 3 + 1] = radius * Math.sin(phi) * Math.sin(theta); // y\n      positions[i * 3 + 2] = radius * Math.cos(phi) * 0.5;            // z - flatter on z-axis\n    }\n    \n    setParticlePositions(positions);\n  }, [count]);\n  \n  // Animation frame loop\n  useFrame((state) => {\n    if (!pointsRef.current || !particlePositions) return;\n    \n    // Rotate the particle system\n    pointsRef.current.rotation.y += 0.0008;\n    \n    // Apply mouse influence to camera\n    state.camera.position.x = THREE.MathUtils.lerp(\n      state.camera.position.x,\n      mouse.x * mouseInfluence,\n      0.05\n    );\n    state.camera.position.y = THREE.MathUtils.lerp(\n      state.camera.position.y,\n      mouse.y * mouseInfluence,\n      0.05\n    );\n    \n    // Look at center\n    state.camera.lookAt(0, 0, 0);\n    \n    // Update uniforms\n    if (pointsRef.current.material instanceof THREE.ShaderMaterial) {\n      pointsRef.current.material.uniforms.time.value = state.clock.getElapsedTime();\n      pointsRef.current.material.uniforms.mousePosition.value.set(\n        mouse.x * viewport.width / 2,\n        mouse.y * viewport.height / 2\n      );\n    }\n  });\n  \n  const particleShader = createParticleShader(color, texture);\n  \n  if (!particlePositions) return null;\n  \n  return (\n    <group>\n      <points ref={pointsRef}>\n        <bufferGeometry>\n          <bufferAttribute\n            attach=\"attributes-position\"\n            count={count}\n            array={particlePositions}\n            itemSize={3}\n          />\n        </bufferGeometry>\n        <shaderMaterial\n          attach=\"material\"\n          args={[particleShader]}\n          transparent\n          depthWrite={false}\n          blending={THREE.AdditiveBlending}\n        />\n      </points>\n    </group>\n  );\n};\n\nexport default InteractiveThreeScene;\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AACA;AAEA;AAAA;AACA;;;;;;;;AASA,MAAM,wBAAwB,CAAC,EAC7B,QAAQ,SAAS,EACjB,QAAQ,IAAI,EACZ,OAAO,IAAI,EACX,iBAAiB,IAAI,EACM;;IAC3B,0CAA0C;IAC1C,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAgB;IAEvC,wCAAwC;IACxC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;IAEnC,wCAAwC;IACxC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IAEhF,gBAAgB;IAChB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IAE7D,eAAe;IACf,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,MAAM,gBAAgB,IAAI,oJAAA,CAAA,gBAAmB;YAC7C,cAAc,IAAI,CAAC;mDAAwB,CAAC;oBAC1C,cAAc,gBAAgB,GAAG;oBACjC,WAAW;gBACb;;QACF;0CAAG,EAAE;IAEL,oCAAoC;IACpC,MAAM,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,GAAG,CAAA,GAAA,uMAAA,CAAA,YAAS,AAAD;2CAAE,IAAM,CAAC;gBAC9C,SAAS;gBACT,SAAS;gBACT,QAAQ;oBAAE,MAAM;oBAAG,SAAS;oBAAK,UAAU;gBAAG;YAChD,CAAC;;IAED,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2CAAE;YACR,MAAM,YAAY,IAAI,aAAa,QAAQ;YAE3C,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;gBAC9B,4EAA4E;gBAC5E,MAAM,SAAS,KAAK,MAAM,KAAK,IAAI;gBACnC,MAAM,MAAM,KAAK,IAAI,CAAC,AAAC,KAAK,MAAM,KAAK,IAAK;gBAC5C,MAAM,QAAQ,KAAK,MAAM,KAAK,KAAK,EAAE,GAAG;gBAExC,SAAS,CAAC,IAAI,EAAE,GAAG,SAAS,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,CAAC,QAAY,IAAI;gBACrE,SAAS,CAAC,IAAI,IAAI,EAAE,GAAG,SAAS,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,CAAC,QAAQ,IAAI;gBACrE,SAAS,CAAC,IAAI,IAAI,EAAE,GAAG,SAAS,KAAK,GAAG,CAAC,OAAO,KAAgB,wBAAwB;YAC1F;YAEA,qBAAqB;QACvB;0CAAG;QAAC;KAAM;IAEV,uBAAuB;IACvB,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;0CAAE,CAAC;YACR,IAAI,CAAC,UAAU,OAAO,IAAI,CAAC,mBAAmB;YAE9C,6BAA6B;YAC7B,UAAU,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI;YAEhC,kCAAkC;YAClC,MAAM,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,oJAAA,CAAA,YAAe,CAAC,IAAI,CAC5C,MAAM,MAAM,CAAC,QAAQ,CAAC,CAAC,EACvB,MAAM,CAAC,GAAG,gBACV;YAEF,MAAM,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,oJAAA,CAAA,YAAe,CAAC,IAAI,CAC5C,MAAM,MAAM,CAAC,QAAQ,CAAC,CAAC,EACvB,MAAM,CAAC,GAAG,gBACV;YAGF,iBAAiB;YACjB,MAAM,MAAM,CAAC,MAAM,CAAC,GAAG,GAAG;YAE1B,kBAAkB;YAClB,IAAI,UAAU,OAAO,CAAC,QAAQ,YAAY,oJAAA,CAAA,iBAAoB,EAAE;gBAC9D,UAAU,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,GAAG,MAAM,KAAK,CAAC,cAAc;gBAC3E,UAAU,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,CACzD,MAAM,CAAC,GAAG,SAAS,KAAK,GAAG,GAC3B,MAAM,CAAC,GAAG,SAAS,MAAM,GAAG;YAEhC;QACF;;IAEA,MAAM,iBAAiB,CAAA,GAAA,uJAAA,CAAA,uBAAoB,AAAD,EAAE,OAAO;IAEnD,IAAI,CAAC,mBAAmB,OAAO;IAE/B,qBACE,6LAAC;kBACC,cAAA,6LAAC;YAAO,KAAK;;8BACX,6LAAC;8BACC,cAAA,6LAAC;wBACC,QAAO;wBACP,OAAO;wBACP,OAAO;wBACP,UAAU;;;;;;;;;;;8BAGd,6LAAC;oBACC,QAAO;oBACP,MAAM;wBAAC;qBAAe;oBACtB,WAAW;oBACX,YAAY;oBACZ,UAAU,oJAAA,CAAA,mBAAsB;;;;;;;;;;;;;;;;;AAK1C;GA7GM;;QAUwB,kNAAA,CAAA,WAAQ;QAkBL,uMAAA,CAAA,YAAS;QAyBxC,kNAAA,CAAA,WAAQ;;;KArDJ;uCA+GS", "debugId": null}}]}