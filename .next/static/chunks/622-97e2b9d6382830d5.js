"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[622],{20135:function(t,e,i){i.d(e,{Z:function(){return o}});var n=i(2265),r={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"},s=i(7656);let o=(0,n.forwardRef)((t,e)=>{let{color:i="currentColor",size:o=24,strokeWidth:a=2,absoluteStrokeWidth:l,className:u="",children:h,iconNode:c,...d}=t;return(0,n.createElement)("svg",{ref:e,...r,width:o,height:o,stroke:i,strokeWidth:l?24*Number(a)/Number(o):a,className:(0,s.z)("lucide",u),...d},[...c.map(t=>{let[e,i]=t;return(0,n.createElement)(e,i)}),...Array.isArray(h)?h:[h]])})},43168:function(t,e,i){i.d(e,{Z:function(){return o}});var n=i(2265),r=i(7656),s=i(20135);let o=(t,e)=>{let i=(0,n.forwardRef)((i,o)=>{let{className:a,...l}=i;return(0,n.createElement)(s.Z,{ref:o,iconNode:e,className:(0,r.z)("lucide-".concat((0,r.m)(t)),a),...l})});return i.displayName="".concat(t),i}},7656:function(t,e,i){i.d(e,{m:function(){return n},z:function(){return r}});let n=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),r=function(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];return e.filter((t,e,i)=>!!t&&""!==t.trim()&&i.indexOf(t)===e).join(" ").trim()}},98575:function(t,e,i){i.d(e,{F:function(){return s},e:function(){return o}});var n=i(2265);function r(t,e){if("function"==typeof t)return t(e);null!=t&&(t.current=e)}function s(...t){return e=>{let i=!1,n=t.map(t=>{let n=r(t,e);return i||"function"!=typeof n||(i=!0),n});if(i)return()=>{for(let e=0;e<n.length;e++){let i=n[e];"function"==typeof i?i():r(t[e],null)}}}}function o(...t){return n.useCallback(s(...t),t)}},37053:function(t,e,i){i.d(e,{Z8:function(){return o},g7:function(){return a},sA:function(){return u}});var n=i(2265),r=i(98575),s=i(57437);function o(t){let e=function(t){let e=n.forwardRef((t,e)=>{let{children:i,...s}=t;if(n.isValidElement(i)){let t,o;let a=(t=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning?i.ref:(t=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning?i.props.ref:i.props.ref||i.ref,l=function(t,e){let i={...e};for(let n in e){let r=t[n],s=e[n];/^on[A-Z]/.test(n)?r&&s?i[n]=(...t)=>{let e=s(...t);return r(...t),e}:r&&(i[n]=r):"style"===n?i[n]={...r,...s}:"className"===n&&(i[n]=[r,s].filter(Boolean).join(" "))}return{...t,...i}}(s,i.props);return i.type!==n.Fragment&&(l.ref=e?(0,r.F)(e,a):a),n.cloneElement(i,l)}return n.Children.count(i)>1?n.Children.only(null):null});return e.displayName=`${t}.SlotClone`,e}(t),i=n.forwardRef((t,i)=>{let{children:r,...o}=t,a=n.Children.toArray(r),l=a.find(h);if(l){let t=l.props.children,r=a.map(e=>e!==l?e:n.Children.count(t)>1?n.Children.only(null):n.isValidElement(t)?t.props.children:null);return(0,s.jsx)(e,{...o,ref:i,children:n.isValidElement(t)?n.cloneElement(t,void 0,r):null})}return(0,s.jsx)(e,{...o,ref:i,children:r})});return i.displayName=`${t}.Slot`,i}var a=o("Slot"),l=Symbol("radix.slottable");function u(t){let e=({children:t})=>(0,s.jsx)(s.Fragment,{children:t});return e.displayName=`${t}.Slottable`,e.__radixId=l,e}function h(t){return n.isValidElement(t)&&"function"==typeof t.type&&"__radixId"in t.type&&t.type.__radixId===l}},61994:function(t,e,i){function n(){for(var t,e,i=0,n="",r=arguments.length;i<r;i++)(t=arguments[i])&&(e=function t(e){var i,n,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e){if(Array.isArray(e)){var s=e.length;for(i=0;i<s;i++)e[i]&&(n=t(e[i]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n)}return r}(t))&&(n&&(n+=" "),n+=e);return n}i.d(e,{W:function(){return n}}),e.Z=n},49637:function(t,e,i){i.d(e,{oO:function(){return s}});var n=i(2265),r=i(64252);function s(t=!0){let e=(0,n.useContext)(r.O);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:s,register:o}=e,a=(0,n.useId)();(0,n.useEffect)(()=>{if(t)return o(a)},[t]);let l=(0,n.useCallback)(()=>t&&s&&s(a),[a,s,t]);return!i&&s?[!1,l]:[!0]}},58881:function(t,e,i){i.d(e,{p:function(){return n}});let n=(0,i(2265).createContext)({})},45750:function(t,e,i){i.d(e,{_:function(){return n}});let n=(0,i(2265).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},64252:function(t,e,i){i.d(e,{O:function(){return n}});let n=(0,i(2265).createContext)(null)},8833:function(t,e,i){function n(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function r(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function s(t,e,i,n){if("function"==typeof e){let[s,o]=r(n);e=e(void 0!==i?i:t.custom,s,o)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[s,o]=r(n);e=e(void 0!==i?i:t.custom,s,o)}return e}function o(t,e,i){let n=t.getProps();return s(n,e,void 0!==i?i:n.custom,t)}function a(t,e){return t?.[e]??t?.default??t}i.d(e,{E:function(){return rl}});var l,u,h,c=i(26147);let d=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],p=new Set(d),m=new Set(["width","height","top","left","right","bottom",...d]);var f=i(94357);let g=t=>Array.isArray(t);var y=i(21457),v=i(87493);function x(t,e){let i=t.getValue("willChange");if((0,v.i)(i)&&i.add)return i.add(e);if(!i&&y.c.WillChange){let i=new y.c.WillChange("auto");t.addValue("willChange",i),i.add(e)}}let b=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),w="data-"+b("framerAppearId");var T=i(28441),P=i(56277),S=i(41927),A=i(69118);let k=t=>180*t/Math.PI,M=t=>E(k(Math.atan2(t[1],t[0]))),V={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:M,rotateZ:M,skewX:t=>k(Math.atan(t[1])),skewY:t=>k(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},E=t=>((t%=360)<0&&(t+=360),t),C=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),D=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),R={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:C,scaleY:D,scale:t=>(C(t)+D(t))/2,rotateX:t=>E(k(Math.atan2(t[6],t[5]))),rotateY:t=>E(k(Math.atan2(-t[2],t[0]))),rotateZ:M,rotate:M,skewX:t=>k(Math.atan(t[4])),skewY:t=>k(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function j(t){return t.includes("scale")?1:0}function L(t,e){let i,n;if(!t||"none"===t)return j(e);let r=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(r)i=R,n=r;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=V,n=e}if(!n)return j(e);let s=i[e],o=n[1].split(",").map(B);return"function"==typeof s?s(o):o[s]}let F=(t,e)=>{let{transform:i="none"}=getComputedStyle(t);return L(i,e)};function B(t){return parseFloat(t.trim())}var O=i(61799),$=i(92854);let W=t=>t===O.Rx||t===$.px,I=new Set(["x","y","z"]),z=d.filter(t=>!I.has(t)),N={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>L(e,"x"),y:(t,{transform:e})=>L(e,"y")};N.translateX=N.x,N.translateY=N.y;let U=new Set,X=!1,Y=!1,Z=!1;function K(){if(Y){let t=Array.from(U).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return z.forEach(i=>{let n=t.getValue(i);void 0!==n&&(e.push([i,n.get()]),n.set(i.startsWith("scale")?1:0))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}Y=!1,X=!1,U.forEach(t=>t.complete(Z)),U.clear()}function _(){U.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(Y=!0)})}class q{constructor(t,e,i,n,r,s=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=n,this.element=r,this.isAsync=s}scheduleResolve(){this.state="scheduled",this.isAsync?(U.add(this),X||(X=!0,c.Wi.read(_),c.Wi.resolveKeyframes(K))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:n}=this;if(null===t[0]){let r=n?.get(),s=t[t.length-1];if(void 0!==r)t[0]=r;else if(i&&e){let n=i.readValue(e,s);null!=n&&(t[0]=n)}void 0===t[0]&&(t[0]=s),n&&void 0===r&&n.set(t[0])}!function(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}(t)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),U.delete(this)}cancel(){"scheduled"===this.state&&(U.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}var G=i(29711),H=i(62035);let J=t=>t.startsWith("--");function Q(t){let e;return()=>(void 0===e&&(e=t()),e)}let tt=Q(()=>void 0!==window.ScrollTimeline);var te=i(9868),ti=i(35818),tn=i(20557),tr=i(53857);let ts={},to=function(t,e){let i=Q(t);return()=>ts[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing");var ta=i(96092);let tl=([t,e,i,n])=>`cubic-bezier(${t}, ${e}, ${i}, ${n})`,tu={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:tl([0,.65,.55,1]),circOut:tl([.55,0,1,.45]),backIn:tl([.31,.01,.66,-.59]),backOut:tl([.33,1.53,.69,.99])};function th(t){return"function"==typeof t&&"applyToOptions"in t}class tc extends te.T{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:n,pseudoElement:r,allowFlatten:s=!1,finalKeyframe:o,onComplete:a}=t;this.isPseudoElement=!!r,this.allowFlatten=s,this.options=t,(0,H.k)("string"!=typeof t.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:t,...e}){return th(t)&&to()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:n=0,duration:r=300,repeat:s=0,repeatType:o="loop",ease:a="easeOut",times:l}={},u){let h={[e]:i};l&&(h.offset=l);let c=function t(e,i){if(e)return"function"==typeof e?to()?(0,ta.w)(e,i):"ease-out":(0,tr.q)(e)?tl(e):Array.isArray(e)?e.map(e=>t(e,i)||tu.easeOut):tu[e]}(a,r);Array.isArray(c)&&(h.easing=c),tn.f.value&&ti.P.waapi++;let d={delay:n,duration:r,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:s+1,direction:"reverse"===o?"alternate":"normal"};u&&(d.pseudoElement=u);let p=t.animate(h,d);return tn.f.value&&p.finished.finally(()=>{ti.P.waapi--}),p}(e,i,n,l,r),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!r){let t=(0,A.$)(n,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(t):J(i)?e.style.setProperty(i,t):e.style[i]=t,this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){let t=this.animation.effect?.getComputedTiming?.().duration||0;return(0,G.X)(Number(t))}get time(){return(0,G.X)(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=(0,G.w)(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&tt())?(this.animation.timeline=t,P.Z):e(this)}}var td=i(98133),tp=i(43273),tm=i(75567),tf=i(52927);let tg={anticipate:tp.L,backInOut:tm.XL,circInOut:tf.X7};class ty extends tc{constructor(t){"string"==typeof t.ease&&t.ease in tg&&(t.ease=tg[t.ease]),(0,td.f)(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:n,element:r,...s}=this.options;if(!e)return;if(void 0!==t){e.set(t);return}let o=new T.L({...s,autoplay:!1}),a=(0,G.w)(this.finishedTime??this.time);e.setWithVelocity(o.sample(a-10).value,o.sample(a).value,10),o.stop()}}var tv=i(65050);let tx=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tv.P.test(t)||"0"===t)&&!t.startsWith("url("));var tb=i(13537);let tw=new Set(["opacity","clipPath","filter","transform"]),tT=Q(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class tP extends te.T{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:n=0,repeatDelay:r=0,repeatType:s="loop",keyframes:o,name:a,motionValue:l,element:u,...h}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=S.X.now();let c={autoplay:t,delay:e,type:i,repeat:n,repeatDelay:r,repeatType:s,name:a,motionValue:l,element:u,...h},d=u?.KeyframeResolver||q;this.keyframeResolver=new d(o,(t,e,i)=>this.onKeyframesResolved(t,e,c,!i),a,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,n){this.keyframeResolver=void 0;let{name:r,type:s,velocity:o,delay:a,isHandoff:l,onUpdate:u}=i;this.resolvedAt=S.X.now(),!function(t,e,i,n){let r=t[0];if(null===r)return!1;if("display"===e||"visibility"===e)return!0;let s=t[t.length-1],o=tx(r,e),a=tx(s,e);return(0,H.K)(o===a,`You are trying to animate ${e} from "${r}" to "${s}". ${r} is not an animatable value - to enable this animation set ${r} to a value animatable to ${s} via the \`style\` property.`),!!o&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||th(i))&&n)}(t,r,s,o)&&((y.c.instantAnimations||!a)&&u?.(A.$(t,i,e)),t[0]=t[t.length-1],i.duration=0,i.repeat=0);let h={startTime:n?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},c=!l&&function(t){let{motionValue:e,name:i,repeatDelay:n,repeatType:r,damping:s,type:o}=t;if(!(0,tb.R)(e?.owner?.current))return!1;let{onUpdate:a,transformTemplate:l}=e.owner.getProps();return tT()&&i&&tw.has(i)&&("transform"!==i||!l)&&!a&&!n&&"mirror"!==r&&0!==s&&"inertia"!==o}(h)?new ty({...h,element:h.motionValue.owner.current}):new T.L(h);c.finished.then(()=>this.notifyFinished()).catch(P.Z),this.pendingTimeline&&(this.stopTimeline=c.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=c}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),Z=!0,_(),K(),Z=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let tS=t=>null!==t,tA={type:"spring",stiffness:500,damping:25,restSpeed:10},tk=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),tM={type:"keyframes",duration:.8},tV={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},tE=(t,{keyframes:e})=>e.length>2?tM:p.has(t)?t.startsWith("scale")?tk(e[1]):tA:tV,tC=(t,e,i,n={},r,s)=>o=>{let l=a(n,t)||{},u=l.delay||n.delay||0,{elapsed:h=0}=n;h-=(0,G.w)(u);let d={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...l,delay:-h,onUpdate:t=>{e.set(t),l.onUpdate&&l.onUpdate(t)},onComplete:()=>{o(),l.onComplete&&l.onComplete()},name:t,motionValue:e,element:s?void 0:r};!function({when:t,delay:e,delayChildren:i,staggerChildren:n,staggerDirection:r,repeat:s,repeatType:o,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(l)&&Object.assign(d,tE(t,d)),d.duration&&(d.duration=(0,G.w)(d.duration)),d.repeatDelay&&(d.repeatDelay=(0,G.w)(d.repeatDelay)),void 0!==d.from&&(d.keyframes[0]=d.from);let p=!1;if(!1!==d.type&&(0!==d.duration||d.repeatDelay)||(d.duration=0,0!==d.delay||(p=!0)),(y.c.instantAnimations||y.c.skipAnimations)&&(p=!0,d.duration=0,d.delay=0),d.allowFlatten=!l.type&&!l.ease,p&&!s&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:i="loop"},n){let r=t.filter(tS),s=e&&"loop"!==i&&e%2==1?0:r.length-1;return r[s]}(d.keyframes,l);if(void 0!==t){c.Wi.update(()=>{d.onUpdate(t),d.onComplete()});return}}return l.isSync?new T.L(d):new tP(d)};function tD(t,e,{delay:i=0,transitionOverride:n,type:r}={}){let{transition:s=t.getDefaultTransition(),transitionEnd:l,...u}=e;n&&(s=n);let h=[],d=r&&t.animationState&&t.animationState.getState()[r];for(let e in u){let n=t.getValue(e,t.latestValues[e]??null),r=u[e];if(void 0===r||d&&function({protectedKeys:t,needsAnimating:e},i){let n=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,n}(d,e))continue;let o={delay:i,...a(s||{},e)},l=n.get();if(void 0!==l&&!n.isAnimating&&!Array.isArray(r)&&r===l&&!o.velocity)continue;let p=!1;if(window.MotionHandoffAnimation){let i=t.props[w];if(i){let t=window.MotionHandoffAnimation(i,e,c.Wi);null!==t&&(o.startTime=t,p=!0)}}x(t,e),n.start(tC(e,n,r,t.shouldReduceMotion&&m.has(e)?{type:!1}:o,t,p));let f=n.animation;f&&h.push(f)}return l&&Promise.all(h).then(()=>{c.Wi.update(()=>{l&&function(t,e){let{transitionEnd:i={},transition:n={},...r}=o(t,e)||{};for(let e in r={...r,...i}){var s;let i=g(s=r[e])?s[s.length-1]||0:s;t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,(0,f.BX)(i))}}(t,l)})}),h}function tR(t,e,i={}){let n=o(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:r=t.getDefaultTransition()||{}}=n||{};i.transitionOverride&&(r=i.transitionOverride);let s=n?()=>Promise.all(tD(t,n,i)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(n=0)=>{let{delayChildren:s=0,staggerChildren:o,staggerDirection:a}=r;return function(t,e,i=0,n=0,r=1,s){let o=[],a=(t.variantChildren.size-1)*n,l=1===r?(t=0)=>t*n:(t=0)=>a-t*n;return Array.from(t.variantChildren).sort(tj).forEach((t,n)=>{t.notify("AnimationStart",e),o.push(tR(t,e,{...s,delay:i+l(n)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(o)}(t,e,s+n,o,a,i)}:()=>Promise.resolve(),{when:l}=r;if(!l)return Promise.all([s(),a(i.delay)]);{let[t,e]="beforeChildren"===l?[s,a]:[a,s];return t().then(()=>e())}}function tj(t,e){return t.sortNodePosition(e)}function tL(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let n=0;n<i;n++)if(e[n]!==t[n])return!1;return!0}function tF(t){return"string"==typeof t||Array.isArray(t)}let tB=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],tO=["initial",...tB],t$=tO.length,tW=[...tB].reverse(),tI=tB.length;function tz(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function tN(){return{animate:tz(!0),whileInView:tz(),whileHover:tz(),whileTap:tz(),whileDrag:tz(),whileFocus:tz(),exit:tz()}}class tU{constructor(t){this.isMounted=!1,this.node=t}update(){}}class tX extends tU{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let n;if(t.notify("AnimationStart",e),Array.isArray(e))n=Promise.all(e.map(e=>tR(t,e,i)));else if("string"==typeof e)n=tR(t,e,i);else{let r="function"==typeof e?o(t,e,i.custom):e;n=Promise.all(tD(t,r,i))}return n.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=tN(),r=!0,s=e=>(i,n)=>{let r=o(t,n,"exit"===e?t.presenceContext?.custom:void 0);if(r){let{transition:t,transitionEnd:e,...n}=r;i={...i,...n,...e}}return i};function a(a){let{props:l}=t,u=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<t$;t++){let n=tO[t],r=e.props[n];(tF(r)||!1===r)&&(i[n]=r)}return i}(t.parent)||{},h=[],c=new Set,d={},p=1/0;for(let e=0;e<tI;e++){var m;let o=tW[e],f=i[o],y=void 0!==l[o]?l[o]:u[o],v=tF(y),x=o===a?f.isActive:null;!1===x&&(p=e);let b=y===u[o]&&y!==l[o]&&v;if(b&&r&&t.manuallyAnimateOnMount&&(b=!1),f.protectedKeys={...d},!f.isActive&&null===x||!y&&!f.prevProp||n(y)||"boolean"==typeof y)continue;let w=(m=f.prevProp,"string"==typeof y?y!==m:!!Array.isArray(y)&&!tL(y,m)),T=w||o===a&&f.isActive&&!b&&v||e>p&&v,P=!1,S=Array.isArray(y)?y:[y],A=S.reduce(s(o),{});!1===x&&(A={});let{prevResolvedValues:k={}}=f,M={...k,...A},V=e=>{T=!0,c.has(e)&&(P=!0,c.delete(e)),f.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in M){let e=A[t],i=k[t];if(!d.hasOwnProperty(t))(g(e)&&g(i)?tL(e,i):e===i)?void 0!==e&&c.has(t)?V(t):f.protectedKeys[t]=!0:null!=e?V(t):c.add(t)}f.prevProp=y,f.prevResolvedValues=A,f.isActive&&(d={...d,...A}),r&&t.blockInitialAnimation&&(T=!1);let E=!(b&&w)||P;T&&E&&h.push(...S.map(t=>({animation:t,options:{type:o}})))}if(c.size){let e={};if("boolean"!=typeof l.initial){let i=o(t,Array.isArray(l.initial)?l.initial[0]:l.initial);i&&i.transition&&(e.transition=i.transition)}c.forEach(i=>{let n=t.getBaseTarget(i),r=t.getValue(i);r&&(r.liveStyle=!0),e[i]=n??null}),h.push({animation:e})}let f=!!h.length;return r&&(!1===l.initial||l.initial===l.animate)&&!t.manuallyAnimateOnMount&&(f=!1),r=!1,f?e(h):Promise.resolve()}return{animateChanges:a,setActive:function(e,n){if(i[e].isActive===n)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,n)),i[e].isActive=n;let r=a(e);for(let t in i)i[t].protectedKeys={};return r},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=tN(),r=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();n(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let tY=0;class tZ extends tU{constructor(){super(...arguments),this.id=tY++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let n=this.node.animationState.setActive("exit",!t);e&&!t&&n.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}let tK={x:!1,y:!1};var t_=i(33811);function tq(t,e,i,n={passive:!0}){return t.addEventListener(e,i,n),()=>t.removeEventListener(e,i)}let tG=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function tH(t){return{point:{x:t.pageX,y:t.pageY}}}let tJ=t=>e=>tG(e)&&t(e,tH(e));function tQ(t,e,i,n){return tq(t,e,tJ(i),n)}function t0({top:t,left:e,right:i,bottom:n}){return{x:{min:e,max:i},y:{min:t,max:n}}}function t1(t){return t.max-t.min}function t5(t,e,i,n=.5){t.origin=n,t.originPoint=(0,t_.t)(e.min,e.max,t.origin),t.scale=t1(i)/t1(e),t.translate=(0,t_.t)(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function t2(t,e,i,n){t5(t.x,e.x,i.x,n?n.originX:void 0),t5(t.y,e.y,i.y,n?n.originY:void 0)}function t3(t,e,i){t.min=i.min+e.min,t.max=t.min+t1(e)}function t6(t,e,i){t.min=e.min-i.min,t.max=t.min+t1(e)}function t4(t,e,i){t6(t.x,e.x,i.x),t6(t.y,e.y,i.y)}let t9=()=>({translate:0,scale:1,origin:0,originPoint:0}),t7=()=>({x:t9(),y:t9()}),t8=()=>({min:0,max:0}),et=()=>({x:t8(),y:t8()});function ee(t){return[t("x"),t("y")]}function ei(t){return void 0===t||1===t}function en({scale:t,scaleX:e,scaleY:i}){return!ei(t)||!ei(e)||!ei(i)}function er(t){return en(t)||es(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function es(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function eo(t,e,i,n,r){return void 0!==r&&(t=n+r*(t-n)),n+i*(t-n)+e}function ea(t,e=0,i=1,n,r){t.min=eo(t.min,e,i,n,r),t.max=eo(t.max,e,i,n,r)}function el(t,{x:e,y:i}){ea(t.x,e.translate,e.scale,e.originPoint),ea(t.y,i.translate,i.scale,i.originPoint)}function eu(t,e){t.min=t.min+e,t.max=t.max+e}function eh(t,e,i,n,r=.5){let s=(0,t_.t)(t.min,t.max,r);ea(t,e,i,s,n)}function ec(t,e){eh(t.x,e.x,e.scaleX,e.scale,e.originX),eh(t.y,e.y,e.scaleY,e.scale,e.originY)}function ed(t,e){return t0(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),n=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:n.y,right:n.x}}(t.getBoundingClientRect(),e))}let ep=({current:t})=>t?t.ownerDocument.defaultView:null;function em(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}var ef=i(24750);let eg=(t,e)=>Math.abs(t-e);class ey{constructor(t,e,{transformPagePoint:i,contextWindow:n,dragSnapToOrigin:r=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{var t,e;if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let i=eb(this.lastMoveEventInfo,this.history),n=null!==this.startEvent,r=(t=i.offset,e={x:0,y:0},Math.sqrt(eg(t.x,e.x)**2+eg(t.y,e.y)**2)>=3);if(!n&&!r)return;let{point:s}=i,{timestamp:o}=c.frameData;this.history.push({...s,timestamp:o});let{onStart:a,onMove:l}=this.handlers;n||(a&&a(this.lastMoveEvent,i),this.startEvent=this.lastMoveEvent),l&&l(this.lastMoveEvent,i)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=ev(e,this.transformPagePoint),c.Wi.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:n,resumeAnimation:r}=this.handlers;if(this.dragSnapToOrigin&&r&&r(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=eb("pointercancel"===t.type?this.lastMoveEventInfo:ev(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,s),n&&n(t,s)},!tG(t))return;this.dragSnapToOrigin=r,this.handlers=e,this.transformPagePoint=i,this.contextWindow=n||window;let s=ev(tH(t),this.transformPagePoint),{point:o}=s,{timestamp:a}=c.frameData;this.history=[{...o,timestamp:a}];let{onSessionStart:l}=e;l&&l(t,eb(s,this.history)),this.removeListeners=(0,ef.z)(tQ(this.contextWindow,"pointermove",this.handlePointerMove),tQ(this.contextWindow,"pointerup",this.handlePointerUp),tQ(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),(0,c.Pn)(this.updatePoint)}}function ev(t,e){return e?{point:e(t.point)}:t}function ex(t,e){return{x:t.x-e.x,y:t.y-e.y}}function eb({point:t},e){return{point:t,delta:ex(t,ew(e)),offset:ex(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,n=null,r=ew(t);for(;i>=0&&(n=t[i],!(r.timestamp-n.timestamp>(0,G.w)(.1)));)i--;if(!n)return{x:0,y:0};let s=(0,G.X)(r.timestamp-n.timestamp);if(0===s)return{x:0,y:0};let o={x:(r.x-n.x)/s,y:(r.y-n.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,0)}}function ew(t){return t[t.length-1]}var eT=i(81645),eP=i(21865);function eS(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function eA(t,e){let i=e.min-t.min,n=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,n]=[n,i]),{min:i,max:n}}function ek(t,e,i){return{min:eM(t,e),max:eM(t,i)}}function eM(t,e){return"number"==typeof t?t:t[e]||0}let eV=new WeakMap;class eE{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=et(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:n}=this.getProps();this.panSession=new ey(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(tH(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:n,onDragStart:r}=this.getProps();if(i&&!n&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===i||"y"===i?tK[i]?null:(tK[i]=!0,()=>{tK[i]=!1}):tK.x||tK.y?null:(tK.x=tK.y=!0,()=>{tK.x=tK.y=!1}),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),ee(t=>{let e=this.getAxisMotionValue(t).get()||0;if($.aQ.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let n=i.layout.layoutBox[t];if(n){let t=t1(n);e=parseFloat(e)/100*t}}}this.originPoint[t]=e}),r&&c.Wi.postRender(()=>r(t,e)),x(this.visualElement,"transform");let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:n,onDirectionLock:r,onDrag:s}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:o}=e;if(n&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(o),null!==this.currentDirection&&r&&r(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),s&&s(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>ee(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:n,contextWindow:ep(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:n}=e;this.startAnimation(n);let{onDragEnd:r}=this.getProps();r&&c.Wi.postRender(()=>r(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:n}=this.getProps();if(!i||!eC(t,n,this.currentDirection))return;let r=this.getAxisMotionValue(t),s=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(s=function(t,{min:e,max:i},n){return void 0!==e&&t<e?t=n?(0,t_.t)(e,t,n.min):Math.max(t,e):void 0!==i&&t>i&&(t=n?(0,t_.t)(i,t,n.max):Math.min(t,i)),t}(s,this.constraints[t],this.elastic[t])),r.set(s)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,n=this.constraints;t&&em(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:n,right:r}){return{x:eS(t.x,i,r),y:eS(t.y,e,n)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:ek(t,"left","right"),y:ek(t,"top","bottom")}}(e),n!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&ee(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!em(e))return!1;let n=e.current;(0,H.k)(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:r}=this.visualElement;if(!r||!r.layout)return!1;let s=function(t,e,i){let n=ed(t,i),{scroll:r}=e;return r&&(eu(n.x,r.offset.x),eu(n.y,r.offset.y)),n}(n,r.root,this.visualElement.getTransformPagePoint()),o={x:eA((t=r.layout.layoutBox).x,s.x),y:eA(t.y,s.y)};if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=t0(t))}return o}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:n,dragTransition:r,dragSnapToOrigin:s,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(ee(o=>{if(!eC(o,e,this.currentDirection))return;let l=a&&a[o]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[o]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...r,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return x(this.visualElement,t),i.start(tC(t,i,0,e,this.visualElement,!1))}stopAnimation(){ee(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){ee(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){ee(e=>{let{drag:i}=this.getProps();if(!eC(e,i,this.currentDirection))return;let{projection:n}=this.visualElement,r=this.getAxisMotionValue(e);if(n&&n.layout){let{min:i,max:s}=n.layout.layoutBox[e];r.set(t[e]-(0,t_.t)(i,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!em(e)||!i||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};ee(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();n[t]=function(t,e){let i=.5,n=t1(t),r=t1(e);return r>n?i=(0,eT.Y)(e.min,e.max-n,t.min):n>r&&(i=(0,eT.Y)(t.min,t.max-r,e.min)),(0,eP.u)(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),ee(e=>{if(!eC(e,t,null))return;let i=this.getAxisMotionValue(e),{min:r,max:s}=this.constraints[e];i.set((0,t_.t)(r,s,n[e]))})}addListeners(){if(!this.visualElement.current)return;eV.set(this.visualElement,this);let t=tQ(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();em(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,n=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),c.Wi.read(e);let r=tq(window,"resize",()=>this.scalePositionWithinConstraints()),s=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(ee(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{r(),t(),n(),s&&s()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:n=!1,dragConstraints:r=!1,dragElastic:s=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:n,dragConstraints:r,dragElastic:s,dragMomentum:o}}}function eC(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class eD extends tU{constructor(t){super(t),this.removeGroupControls=P.Z,this.removeListeners=P.Z,this.controls=new eE(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||P.Z}unmount(){this.removeGroupControls(),this.removeListeners()}}let eR=t=>(e,i)=>{t&&c.Wi.postRender(()=>t(e,i))};class ej extends tU{constructor(){super(...arguments),this.removePointerDownListener=P.Z}onPointerDown(t){this.session=new ey(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:ep(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:n}=this.node.getProps();return{onSessionStart:eR(t),onStart:eR(e),onMove:i,onEnd:(t,e)=>{delete this.session,n&&c.Wi.postRender(()=>n(t,e))}}}mount(){this.removePointerDownListener=tQ(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var eL=i(57437);let{schedule:eF,cancel:eB}=(0,i(62862).Z)(queueMicrotask,!1);var eO=i(2265),e$=i(49637),eW=i(58881);let eI=(0,eO.createContext)({}),ez={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function eN(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let eU={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!$.px.test(t))return t;t=parseFloat(t)}let i=eN(t,e.target.x),n=eN(t,e.target.y);return`${i}% ${n}%`}};var eX=i(38596);let eY={};class eZ extends eO.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:n}=this.props,{projection:r}=t;!function(t){for(let e in t)eY[e]=t[e],(0,eX.f)(e)&&(eY[e].isCSSVariable=!0)}(e_),r&&(e.group&&e.group.add(r),i&&i.register&&n&&i.register(r),r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),ez.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:n,isPresent:r}=this.props,{projection:s}=i;return s&&(s.isPresent=r,n||t.layoutDependency!==e||void 0===e||t.isPresent!==r?s.willUpdate():this.safeToRemove(),t.isPresent===r||(r?s.promote():s.relegate()||c.Wi.postRender(()=>{let t=s.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),eF.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:n}=t;n&&(n.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(n),i&&i.deregister&&i.deregister(n))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function eK(t){let[e,i]=(0,e$.oO)(),n=(0,eO.useContext)(eW.p);return(0,eL.jsx)(eZ,{...t,layoutGroup:n,switchLayoutGroup:(0,eO.useContext)(eI),isPresent:e,safeToRemove:i})}let e_={borderRadius:{...eU,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:eU,borderTopRightRadius:eU,borderBottomLeftRadius:eU,borderBottomRightRadius:eU,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let n=tv.P.parse(t);if(n.length>5)return t;let r=tv.P.createTransformer(t),s="number"!=typeof n[0]?1:0,o=i.x.scale*e.x,a=i.y.scale*e.y;n[0+s]/=o,n[1+s]/=a;let l=(0,t_.t)(o,a,.5);return"number"==typeof n[2+s]&&(n[2+s]/=l),"number"==typeof n[3+s]&&(n[3+s]/=l),r(n)}}};var eq=i(41464);function eG(t){return(0,eq.K)(t)&&"ownerSVGElement"in t}var eH=i(94513),eJ=i(98425);let eQ=(t,e)=>t.depth-e.depth;class e0{constructor(){this.children=[],this.isDirty=!1}add(t){(0,eJ.y4)(this.children,t),this.isDirty=!0}remove(t){(0,eJ.cl)(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(eQ),this.isDirty=!1,this.children.forEach(t)}}function e1(t){return(0,v.i)(t)?t.get():t}let e5=["TopLeft","TopRight","BottomLeft","BottomRight"],e2=e5.length,e3=t=>"string"==typeof t?parseFloat(t):t,e6=t=>"number"==typeof t||$.px.test(t);function e4(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let e9=e8(0,.5,tf.Bn),e7=e8(.5,.95,P.Z);function e8(t,e,i){return n=>n<t?0:n>e?1:i((0,eT.Y)(t,e,n))}function it(t,e){t.min=e.min,t.max=e.max}function ie(t,e){it(t.x,e.x),it(t.y,e.y)}function ii(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function ir(t,e,i,n,r){return t-=e,t=n+1/i*(t-n),void 0!==r&&(t=n+1/r*(t-n)),t}function is(t,e,[i,n,r],s,o){!function(t,e=0,i=1,n=.5,r,s=t,o=t){if($.aQ.test(e)&&(e=parseFloat(e),e=(0,t_.t)(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=(0,t_.t)(s.min,s.max,n);t===s&&(a-=e),t.min=ir(t.min,e,i,a,r),t.max=ir(t.max,e,i,a,r)}(t,e[i],e[n],e[r],e.scale,s,o)}let io=["x","scaleX","originX"],ia=["y","scaleY","originY"];function il(t,e,i,n){is(t.x,e,io,i?i.x:void 0,n?n.x:void 0),is(t.y,e,ia,i?i.y:void 0,n?n.y:void 0)}function iu(t){return 0===t.translate&&1===t.scale}function ih(t){return iu(t.x)&&iu(t.y)}function ic(t,e){return t.min===e.min&&t.max===e.max}function id(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function ip(t,e){return id(t.x,e.x)&&id(t.y,e.y)}function im(t){return t1(t.x)/t1(t.y)}function ig(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class iy{constructor(){this.members=[]}add(t){(0,eJ.y4)(this.members,t),t.scheduleRender()}remove(t){if((0,eJ.cl)(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e;let i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:n}=t.options;!1===n&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let iv={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},ix=["","X","Y","Z"],ib={visibility:"hidden"},iw=0;function iT(t,e,i,n){let{latestValues:r}=e;r[t]&&(i[t]=r[t],e.setStaticValue(t,0),n&&(n[t]=0))}function iP({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:n,resetTransform:r}){return class{constructor(t={},i=e?.()){this.id=iw++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,tn.f.value&&(iv.nodes=iv.calculatedTargetDeltas=iv.calculatedProjections=0),this.nodes.forEach(ik),this.nodes.forEach(ij),this.nodes.forEach(iL),this.nodes.forEach(iM),tn.f.addProjectionMetrics&&tn.f.addProjectionMetrics(iv)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new e0)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new eH.L),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=eG(e)&&!(eG(e)&&"svg"===e.tagName),this.instance=e;let{layoutId:i,layout:n,visualElement:r}=this.options;if(r&&!r.current&&r.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(n||i)&&(this.isLayoutDirty=!0),t){let i;let n=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=S.X.now(),n=({timestamp:e})=>{let r=e-i;r>=250&&((0,c.Pn)(n),t(r-250))};return c.Wi.setup(n,!0),()=>(0,c.Pn)(n)}(n,0),ez.hasAnimatedSinceResize&&(ez.hasAnimatedSinceResize=!1,this.nodes.forEach(iR))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&r&&(i||n)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:n})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let s=this.options.transition||r.getDefaultTransition()||iI,{onLayoutAnimationStart:o,onLayoutAnimationComplete:l}=r.getProps(),u=!this.targetLayout||!ip(this.targetLayout,n),h=!e&&i;if(this.options.layoutRoot||this.resumeFrom||h||e&&(u||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,h);let e={...a(s,"layout"),onPlay:o,onComplete:l};(r.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||iR(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),(0,c.Pn)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(iF),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let n=i.props[w];if(window.MotionHasOptimisedAnimation(n,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",c.Wi,!(t||i))}let{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&t(r)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(iE);return}this.isUpdating||this.nodes.forEach(iC),this.isUpdating=!1,this.nodes.forEach(iD),this.nodes.forEach(iS),this.nodes.forEach(iA),this.clearAllSnapshots();let t=S.X.now();c.frameData.delta=(0,eP.u)(0,1e3/60,t-c.frameData.timestamp),c.frameData.timestamp=t,c.frameData.isProcessing=!0,c.yL.update.process(c.frameData),c.yL.preRender.process(c.frameData),c.yL.render.process(c.frameData),c.frameData.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,eF.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(iV),this.sharedNodes.forEach(iB)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,c.Wi.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){c.Wi.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),!this.snapshot||t1(this.snapshot.measuredBox.x)||t1(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=et(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=n(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!r)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!ih(this.projectionDelta),i=this.getTransformTemplate(),n=i?i(this.latestValues,""):void 0,s=n!==this.prevTransformTemplateValue;t&&this.instance&&(e||er(this.latestValues)||s)&&(r(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),n=this.removeElementScroll(i);return t&&(n=this.removeTransform(n)),iU((e=n).x),iU(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return et();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(iY))){let{scroll:t}=this.root;t&&(eu(e.x,t.offset.x),eu(e.y,t.offset.y))}return e}removeElementScroll(t){let e=et();if(ie(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let n=this.path[i],{scroll:r,options:s}=n;n!==this.root&&r&&s.layoutScroll&&(r.wasRoot&&ie(e,t),eu(e.x,r.offset.x),eu(e.y,r.offset.y))}return e}applyTransform(t,e=!1){let i=et();ie(i,t);for(let t=0;t<this.path.length;t++){let n=this.path[t];!e&&n.options.layoutScroll&&n.scroll&&n!==n.root&&ec(i,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),er(n.latestValues)&&ec(i,n.latestValues)}return er(this.latestValues)&&ec(i,this.latestValues),i}removeTransform(t){let e=et();ie(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!er(i.latestValues))continue;en(i.latestValues)&&i.updateSnapshot();let n=et();ie(n,i.measurePageBox()),il(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,n)}return er(this.latestValues)&&il(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==c.frameData.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:n,layoutId:r}=this.options;if(this.layout&&(n||r)){if(this.resolvedRelativeTargetAt=c.frameData.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=et(),this.relativeTargetOrigin=et(),t4(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),ie(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=et(),this.targetWithTransforms=et()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var s,o,a;this.forceRelativeParentToResolveTarget(),s=this.target,o=this.relativeTarget,a=this.relativeParent.target,t3(s.x,o.x,a.x),t3(s.y,o.y,a.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):ie(this.target,this.layout.layoutBox),el(this.target,this.targetDelta)):ie(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=et(),this.relativeTargetOrigin=et(),t4(this.relativeTargetOrigin,this.target,t.target),ie(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}tn.f.value&&iv.calculatedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||en(this.parent.latestValues)||es(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===c.frameData.timestamp&&(i=!1),i)return;let{layout:n,layoutId:r}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||r))return;ie(this.layoutCorrected,this.layout.layoutBox);let s=this.treeScale.x,o=this.treeScale.y;!function(t,e,i,n=!1){let r,s;let o=i.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){s=(r=i[a]).projectionDelta;let{visualElement:o}=r.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(n&&r.options.layoutScroll&&r.scroll&&r!==r.root&&ec(t,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),s&&(e.x*=s.x.scale,e.y*=s.y.scale,el(t,s)),n&&er(r.latestValues)&&ec(t,r.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=et());let{target:a}=t;if(!a){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(ii(this.prevProjectionDelta.x,this.projectionDelta.x),ii(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),t2(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===s&&this.treeScale.y===o&&ig(this.projectionDelta.x,this.prevProjectionDelta.x)&&ig(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),tn.f.value&&iv.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=t7(),this.projectionDelta=t7(),this.projectionDeltaWithTransform=t7()}setAnimationOrigin(t,e=!1){let i;let n=this.snapshot,r=n?n.latestValues:{},s={...this.latestValues},o=t7();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=et(),l=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,c=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(iW));this.animationProgress=0,this.mixTargetDelta=e=>{let n=e/1e3;if(iO(o.x,t.x,n),iO(o.y,t.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,d,p,m;t4(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,i$(p.x,m.x,a.x,n),i$(p.y,m.y,a.y,n),i&&(u=this.relativeTarget,d=i,ic(u.x,d.x)&&ic(u.y,d.y))&&(this.isProjectionDirty=!1),i||(i=et()),ie(i,this.relativeTarget)}l&&(this.animationValues=s,function(t,e,i,n,r,s){r?(t.opacity=(0,t_.t)(0,i.opacity??1,e9(n)),t.opacityExit=(0,t_.t)(e.opacity??1,0,e7(n))):s&&(t.opacity=(0,t_.t)(e.opacity??1,i.opacity??1,n));for(let r=0;r<e2;r++){let s=`border${e5[r]}Radius`,o=e4(e,s),a=e4(i,s);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||e6(o)===e6(a)?(t[s]=Math.max((0,t_.t)(e3(o),e3(a),n),0),($.aQ.test(a)||$.aQ.test(o))&&(t[s]+="%")):t[s]=a)}(e.rotate||i.rotate)&&(t.rotate=(0,t_.t)(e.rotate||0,i.rotate||0,n))}(s,r,this.latestValues,n,c,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(!1),this.resumingFrom?.currentAnimation?.stop(!1),this.pendingAnimation&&((0,c.Pn)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=c.Wi.update(()=>{ez.hasAnimatedSinceResize=!0,ti.P.layout++,this.motionValue||(this.motionValue=(0,f.BX)(0)),this.currentAnimation=function(t,e,i){let n=(0,v.i)(t)?t:(0,f.BX)(t);return n.start(tC("",n,e,i)),n.animation}(this.motionValue,[0,1e3],{...t,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{ti.P.layout--},onComplete:()=>{ti.P.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop(!1)),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:n,latestValues:r}=t;if(e&&i&&n){if(this!==t&&this.layout&&n&&iX(this.options.animationType,this.layout.layoutBox,n.layoutBox)){i=this.target||et();let e=t1(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let n=t1(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+n}ie(e,i),ec(e,r),t2(this.projectionDeltaWithTransform,this.layoutCorrected,e,r)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new iy),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let n=this.getStack();n&&n.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let n={};i.z&&iT("z",t,n,this.animationValues);for(let e=0;e<ix.length;e++)iT(`rotate${ix[e]}`,t,n,this.animationValues),iT(`skew${ix[e]}`,t,n,this.animationValues);for(let e in t.render(),n)t.setStaticValue(e,n[e]),this.animationValues&&(this.animationValues[e]=n[e]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return ib;let e={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=e1(t?.pointerEvents)||"",e.transform=i?i(this.latestValues,""):"none",e;let n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=e1(t?.pointerEvents)||""),this.hasProjected&&!er(this.latestValues)&&(e.transform=i?i({},""):"none",this.hasProjected=!1),e}let r=n.animationValues||n.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,i){let n="",r=t.x.translate/e.x,s=t.y.translate/e.y,o=i?.z||0;if((r||s||o)&&(n=`translate3d(${r}px, ${s}px, ${o}px) `),(1!==e.x||1!==e.y)&&(n+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:r,rotateY:s,skewX:o,skewY:a}=i;t&&(n=`perspective(${t}px) ${n}`),e&&(n+=`rotate(${e}deg) `),r&&(n+=`rotateX(${r}deg) `),s&&(n+=`rotateY(${s}deg) `),o&&(n+=`skewX(${o}deg) `),a&&(n+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(n+=`scale(${a}, ${l})`),n||"none"}(this.projectionDeltaWithTransform,this.treeScale,r),i&&(e.transform=i(r,e.transform));let{x:s,y:o}=this.projectionDelta;for(let t in e.transformOrigin=`${100*s.origin}% ${100*o.origin}% 0`,n.animationValues?e.opacity=n===this?r.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:r.opacityExit:e.opacity=n===this?void 0!==r.opacity?r.opacity:"":void 0!==r.opacityExit?r.opacityExit:0,eY){if(void 0===r[t])continue;let{correct:i,applyTo:s,isCSSVariable:o}=eY[t],a="none"===e.transform?r[t]:i(r[t],n);if(s){let t=s.length;for(let i=0;i<t;i++)e[s[i]]=a}else o?this.options.visualElement.renderState.vars[t]=a:e[t]=a}return this.options.layoutId&&(e.pointerEvents=n===this?e1(t?.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop(!1)),this.root.nodes.forEach(iE),this.root.sharedNodes.clear()}}}function iS(t){t.updateLayout()}function iA(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:n}=t.layout,{animationType:r}=t.options,s=e.source!==t.layout.source;"size"===r?ee(t=>{let n=s?e.measuredBox[t]:e.layoutBox[t],r=t1(n);n.min=i[t].min,n.max=n.min+r}):iX(r,e.layoutBox,i)&&ee(n=>{let r=s?e.measuredBox[n]:e.layoutBox[n],o=t1(i[n]);r.max=r.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[n].max=t.relativeTarget[n].min+o)});let o=t7();t2(o,i,e.layoutBox);let a=t7();s?t2(a,t.applyTransform(n,!0),e.measuredBox):t2(a,i,e.layoutBox);let l=!ih(o),u=!1;if(!t.resumeFrom){let n=t.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:r,layout:s}=n;if(r&&s){let o=et();t4(o,e.layoutBox,r.layoutBox);let a=et();t4(a,i,s.layoutBox),ip(o,a)||(u=!0),n.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=n)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function ik(t){tn.f.value&&iv.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function iM(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function iV(t){t.clearSnapshot()}function iE(t){t.clearMeasurements()}function iC(t){t.isLayoutDirty=!1}function iD(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function iR(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function ij(t){t.resolveTargetDelta()}function iL(t){t.calcProjection()}function iF(t){t.resetSkewAndRotation()}function iB(t){t.removeLeadSnapshot()}function iO(t,e,i){t.translate=(0,t_.t)(e.translate,0,i),t.scale=(0,t_.t)(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function i$(t,e,i,n){t.min=(0,t_.t)(e.min,i.min,n),t.max=(0,t_.t)(e.max,i.max,n)}function iW(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let iI={duration:.45,ease:[.4,0,.1,1]},iz=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),iN=iz("applewebkit/")&&!iz("chrome/")?Math.round:P.Z;function iU(t){t.min=iN(t.min),t.max=iN(t.max)}function iX(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(im(e)-im(i)))}function iY(t){return t!==t.root&&t.scroll?.wasRoot}let iZ=iP({attachResizeListener:(t,e)=>tq(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),iK={current:void 0},i_=iP({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!iK.current){let t=new iZ({});t.mount(window),t.setOptions({layoutScroll:!0}),iK.current=t}return iK.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function iq(t,e){let i=function(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document,i=(void 0)??e.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t),n=new AbortController;return[i,{passive:!0,...e,signal:n.signal},()=>n.abort()]}function iG(t){return!("touch"===t.pointerType||tK.x||tK.y)}function iH(t,e,i){let{props:n}=t;t.animationState&&n.whileHover&&t.animationState.setActive("whileHover","Start"===i);let r=n["onHover"+i];r&&c.Wi.postRender(()=>r(e,tH(e)))}class iJ extends tU{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,r,s]=iq(t,i),o=t=>{if(!iG(t))return;let{target:i}=t,n=e(i,t);if("function"!=typeof n||!i)return;let s=t=>{iG(t)&&(n(t),i.removeEventListener("pointerleave",s))};i.addEventListener("pointerleave",s,r)};return n.forEach(t=>{t.addEventListener("pointerenter",o,r)}),s}(t,(t,e)=>(iH(this.node,e,"Start"),t=>iH(this.node,t,"End"))))}unmount(){}}class iQ extends tU{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=(0,ef.z)(tq(this.node.current,"focus",()=>this.onFocus()),tq(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let i0=(t,e)=>!!e&&(t===e||i0(t,e.parentElement)),i1=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),i5=new WeakSet;function i2(t){return e=>{"Enter"===e.key&&t(e)}}function i3(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let i6=(t,e)=>{let i=t.currentTarget;if(!i)return;let n=i2(()=>{if(i5.has(i))return;i3(i,"down");let t=i2(()=>{i3(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>i3(i,"cancel"),e)});i.addEventListener("keydown",n,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",n),e)};function i4(t){return tG(t)&&!(tK.x||tK.y)}function i9(t,e,i){let{props:n}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&n.whileTap&&t.animationState.setActive("whileTap","Start"===i);let r=n["onTap"+("End"===i?"":i)];r&&c.Wi.postRender(()=>r(e,tH(e)))}class i7 extends tU{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,r,s]=iq(t,i),o=t=>{let n=t.currentTarget;if(!i4(t))return;i5.add(n);let s=e(n,t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),i5.has(n)&&i5.delete(n),i4(t)&&"function"==typeof s&&s(t,{success:e})},a=t=>{o(t,n===window||n===document||i.useGlobalTarget||i0(n,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,r),window.addEventListener("pointercancel",l,r)};return n.forEach(t=>{(i.useGlobalTarget?window:t).addEventListener("pointerdown",o,r),(0,tb.R)(t)&&(t.addEventListener("focus",t=>i6(t,r)),i1.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),s}(t,(t,e)=>(i9(this.node,e,"Start"),(t,{success:e})=>i9(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let i8=new WeakMap,nt=new WeakMap,ne=t=>{let e=i8.get(t.target);e&&e(t)},ni=t=>{t.forEach(ne)},nn={some:0,all:1};class nr extends tU{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:n="some",once:r}=t,s={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof n?n:nn[n]};return function(t,e,i){let n=function({root:t,...e}){let i=t||document;nt.has(i)||nt.set(i,{});let n=nt.get(i),r=JSON.stringify(e);return n[r]||(n[r]=new IntersectionObserver(ni,{root:t,...e})),n[r]}(e);return i8.set(t,i),n.observe(t),()=>{i8.delete(t),n.unobserve(t)}}(this.node.current,s,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,r&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:n}=this.node.getProps(),s=e?i:n;s&&s(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let ns=(0,eO.createContext)({strict:!1});var no=i(45750);let na=(0,eO.createContext)({});function nl(t){return n(t.animate)||tO.some(e=>tF(t[e]))}function nu(t){return!!(nl(t)||t.variants)}function nh(t){return Array.isArray(t)?t.join(" "):t}var nc=i(44563);let nd={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},np={};for(let t in nd)np[t]={isEnabled:e=>nd[t].some(t=>!!e[t])};let nm=Symbol.for("motionComponentSymbol");var nf=i(64252),ng=i(11534);function ny(t,{layout:e,layoutId:i}){return p.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!eY[t]||"opacity"===t)}let nv=(t,e)=>e&&"number"==typeof t?e.transform(t):t,nx={...O.Rx,transform:Math.round},nb={rotate:$.RW,rotateX:$.RW,rotateY:$.RW,rotateZ:$.RW,scale:O.bA,scaleX:O.bA,scaleY:O.bA,scaleZ:O.bA,skew:$.RW,skewX:$.RW,skewY:$.RW,distance:$.px,translateX:$.px,translateY:$.px,translateZ:$.px,x:$.px,y:$.px,z:$.px,perspective:$.px,transformPerspective:$.px,opacity:O.Fq,originX:$.$C,originY:$.$C,originZ:$.px},nw={borderWidth:$.px,borderTopWidth:$.px,borderRightWidth:$.px,borderBottomWidth:$.px,borderLeftWidth:$.px,borderRadius:$.px,radius:$.px,borderTopLeftRadius:$.px,borderTopRightRadius:$.px,borderBottomRightRadius:$.px,borderBottomLeftRadius:$.px,width:$.px,maxWidth:$.px,height:$.px,maxHeight:$.px,top:$.px,right:$.px,bottom:$.px,left:$.px,padding:$.px,paddingTop:$.px,paddingRight:$.px,paddingBottom:$.px,paddingLeft:$.px,margin:$.px,marginTop:$.px,marginRight:$.px,marginBottom:$.px,marginLeft:$.px,backgroundPositionX:$.px,backgroundPositionY:$.px,...nb,zIndex:nx,fillOpacity:O.Fq,strokeOpacity:O.Fq,numOctaves:nx},nT={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},nP=d.length;function nS(t,e,i){let{style:n,vars:r,transformOrigin:s}=t,o=!1,a=!1;for(let t in e){let i=e[t];if(p.has(t)){o=!0;continue}if((0,eX.f)(t)){r[t]=i;continue}{let e=nv(i,nw[t]);t.startsWith("origin")?(a=!0,s[t]=e):n[t]=e}}if(!e.transform&&(o||i?n.transform=function(t,e,i){let n="",r=!0;for(let s=0;s<nP;s++){let o=d[s],a=t[o];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===(o.startsWith("scale")?1:0):0===parseFloat(a))||i){let t=nv(a,nw[o]);if(!l){r=!1;let e=nT[o]||o;n+=`${e}(${t}) `}i&&(e[o]=t)}}return n=n.trim(),i?n=i(e,r?"":n):r&&(n="none"),n}(e,t.transform,i):n.transform&&(n.transform="none")),a){let{originX:t="50%",originY:e="50%",originZ:i=0}=s;n.transformOrigin=`${t} ${e} ${i}`}}let nA=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function nk(t,e,i){for(let n in e)(0,v.i)(e[n])||ny(n,i)||(t[n]=e[n])}let nM={offset:"stroke-dashoffset",array:"stroke-dasharray"},nV={offset:"strokeDashoffset",array:"strokeDasharray"};function nE(t,{attrX:e,attrY:i,attrScale:n,pathLength:r,pathSpacing:s=1,pathOffset:o=0,...a},l,u,h){if(nS(t,a,u),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:c,style:d}=t;c.transform&&(d.transform=c.transform,delete c.transform),(d.transform||c.transformOrigin)&&(d.transformOrigin=c.transformOrigin??"50% 50%",delete c.transformOrigin),d.transform&&(d.transformBox=h?.transformBox??"fill-box",delete c.transformBox),void 0!==e&&(c.x=e),void 0!==i&&(c.y=i),void 0!==n&&(c.scale=n),void 0!==r&&function(t,e,i=1,n=0,r=!0){t.pathLength=1;let s=r?nM:nV;t[s.offset]=$.px.transform(-n);let o=$.px.transform(e),a=$.px.transform(i);t[s.array]=`${o} ${a}`}(c,r,s,o,!1)}let nC=()=>({...nA(),attrs:{}}),nD=t=>"string"==typeof t&&"svg"===t.toLowerCase(),nR=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function nj(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||nR.has(t)}let nL=t=>!nj(t);try{(l=require("@emotion/is-prop-valid").default)&&(nL=t=>t.startsWith("on")?!nj(t):l(t))}catch{}let nF=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function nB(t){if("string"!=typeof t||t.includes("-"));else if(nF.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var nO=i(53576);let n$=t=>(e,i)=>{let r=(0,eO.useContext)(na),o=(0,eO.useContext)(nf.O),a=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,r,o){return{latestValues:function(t,e,i,r){let o={},a=r(t,{});for(let t in a)o[t]=e1(a[t]);let{initial:l,animate:u}=t,h=nl(t),c=nu(t);e&&c&&!h&&!1!==t.inherit&&(void 0===l&&(l=e.initial),void 0===u&&(u=e.animate));let d=!!i&&!1===i.initial,p=(d=d||!1===l)?u:l;if(p&&"boolean"!=typeof p&&!n(p)){let e=Array.isArray(p)?p:[p];for(let i=0;i<e.length;i++){let n=s(t,e[i]);if(n){let{transitionEnd:t,transition:e,...i}=n;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=d?e.length-1:0;e=e[t]}null!==e&&(o[t]=e)}for(let e in t)o[e]=t[e]}}}return o}(i,r,o,t),renderState:e()}})(t,e,r,o);return i?a():(0,nO.h)(a)};function nW(t,e,i){let{style:n}=t,r={};for(let s in n)((0,v.i)(n[s])||e.style&&(0,v.i)(e.style[s])||ny(s,t)||i?.getValue(s)?.liveStyle!==void 0)&&(r[s]=n[s]);return r}let nI={useVisualState:n$({scrapeMotionValuesFromProps:nW,createRenderState:nA})};function nz(t,e,i){let n=nW(t,e,i);for(let i in t)((0,v.i)(t[i])||(0,v.i)(e[i]))&&(n[-1!==d.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return n}let nN={useVisualState:n$({scrapeMotionValuesFromProps:nz,createRenderState:nC})},nU=t=>e=>e.test(t),nX=[O.Rx,$.px,$.aQ,$.RW,$.vw,$.vh,{test:t=>"auto"===t,parse:t=>t}],nY=t=>nX.find(nU(t)),nZ=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),nK=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,n_=t=>/^0[^.\s]+$/u.test(t);var nq=i(3855);let nG=new Set(["brightness","contrast","saturate","opacity"]);function nH(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[n]=i.match(nq.K)||[];if(!n)return t;let r=i.replace(n,""),s=nG.has(e)?1:0;return n!==i&&(s*=100),e+"("+s+r+")"}let nJ=/\b([a-z-]*)\(.*?\)/gu,nQ={...tv.P,getAnimatableNone:t=>{let e=t.match(nJ);return e?e.map(nH).join(" "):t}};var n0=i(7537);let n1={...nw,color:n0.$,backgroundColor:n0.$,outlineColor:n0.$,fill:n0.$,stroke:n0.$,borderColor:n0.$,borderTopColor:n0.$,borderRightColor:n0.$,borderBottomColor:n0.$,borderLeftColor:n0.$,filter:nQ,WebkitFilter:nQ},n5=t=>n1[t];function n2(t,e){let i=n5(t);return i!==nQ&&(i=tv.P),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let n3=new Set(["auto","none","0"]);class n6 extends q{constructor(t,e,i,n,r){super(t,e,i,n,r,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let n=t[i];if("string"==typeof n&&(n=n.trim(),(0,eX.t)(n))){let r=function t(e,i,n=1){(0,H.k)(n<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[r,s]=function(t){let e=nK.exec(t);if(!e)return[,];let[,i,n,r]=e;return[`--${i??n}`,r]}(e);if(!r)return;let o=window.getComputedStyle(i).getPropertyValue(r);if(o){let t=o.trim();return nZ(t)?parseFloat(t):t}return(0,eX.t)(s)?t(s,i,n+1):s}(n,e.current);void 0!==r&&(t[i]=r),i===t.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!m.has(i)||2!==t.length)return;let[n,r]=t,s=nY(n),o=nY(r);if(s!==o){if(W(s)&&W(o))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else N[i]&&(this.needsMeasurement=!0)}}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var n;(null===t[e]||("number"==typeof(n=t[e])?0===n:null===n||"none"===n||"0"===n||n_(n)))&&i.push(e)}i.length&&function(t,e,i){let n,r=0;for(;r<t.length&&!n;){let e=t[r];"string"==typeof e&&!n3.has(e)&&(0,tv.V)(e).values.length&&(n=t[r]),r++}if(n&&i)for(let r of e)t[r]=n2(i,n)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=N[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let n=e[e.length-1];void 0!==n&&t.getValue(i,n).jump(n,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let n=t.getValue(e);n&&n.jump(this.measuredOrigin,!1);let r=i.length-1,s=i[r];i[r]=N[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==s&&void 0===this.finalKeyframe&&(this.finalKeyframe=s),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}let n4=[...nX,n0.$,tv.P],n9=t=>n4.find(nU(t)),n7={current:null},n8={current:!1},rt=new WeakMap,re=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class ri{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:n,blockInitialAnimation:r,visualState:s},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=q,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=S.X.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,c.Wi.render(this.render,!1,!0))};let{latestValues:a,renderState:l}=s;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=n,this.options=o,this.blockInitialAnimation=!!r,this.isControllingVariants=nl(e),this.isVariantNode=nu(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:u,...h}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in h){let e=h[t];void 0!==a[t]&&(0,v.i)(e)&&e.set(a[t],!1)}}mount(t){this.current=t,rt.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),n8.current||function(){if(n8.current=!0,nc.j){if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>n7.current=t.matches;t.addListener(e),e()}else n7.current=!1}}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||n7.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),(0,c.Pn)(this.notifyUpdate),(0,c.Pn)(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let n=p.has(t);n&&this.onBindTransform&&this.onBindTransform();let r=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&c.Wi.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),s=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{r(),s(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in np){let e=np[t];if(!e)continue;let{isEnabled:i,Feature:n}=e;if(!this.features[t]&&n&&i(this.props)&&(this.features[t]=new n(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):et()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<re.length;e++){let i=re[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let n=t["on"+i];n&&(this.propEventSubscriptions[i]=this.on(i,n))}this.prevMotionValues=function(t,e,i){for(let n in e){let r=e[n],s=i[n];if((0,v.i)(r))t.addValue(n,r);else if((0,v.i)(s))t.addValue(n,(0,f.BX)(r,{owner:t}));else if(s!==r){if(t.hasValue(n)){let e=t.getValue(n);!0===e.liveStyle?e.jump(r):e.hasAnimated||e.set(r)}else{let e=t.getStaticValue(n);t.addValue(n,(0,f.BX)(void 0!==e?e:r,{owner:t}))}}}for(let n in i)void 0===e[n]&&t.removeValue(n);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=(0,f.BX)(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=i&&("string"==typeof i&&(nZ(i)||n_(i))?i=parseFloat(i):!n9(i)&&tv.P.test(e)&&(i=n2(t,e)),this.setBaseTarget(t,(0,v.i)(i)?i.get():i)),(0,v.i)(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e;let{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let n=s(this.props,i,this.presenceContext?.custom);n&&(e=n[t])}if(i&&void 0!==e)return e;let n=this.getBaseTargetFromProps(this.props,t);return void 0===n||(0,v.i)(n)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:n}on(t,e){return this.events[t]||(this.events[t]=new eH.L),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class rn extends ri{constructor(){super(...arguments),this.KeyframeResolver=n6}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;(0,v.i)(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}function rr(t,{style:e,vars:i},n,r){for(let s in Object.assign(t.style,e,r&&r.getProjectionStyles(n)),i)t.style.setProperty(s,i[s])}class rs extends rn{constructor(){super(...arguments),this.type="html",this.renderInstance=rr}readValueFromInstance(t,e){if(p.has(e))return this.projection?.isProjecting?j(e):F(t,e);{let i=window.getComputedStyle(t),n=((0,eX.f)(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(t,{transformPagePoint:e}){return ed(t,e)}build(t,e,i){nS(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return nW(t,e,i)}}let ro=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class ra extends rn{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=et}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(p.has(e)){let t=n5(e);return t&&t.default||0}return e=ro.has(e)?e:b(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return nz(t,e,i)}build(t,e,i){nE(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,n){!function(t,e,i,n){for(let i in rr(t,e,void 0,n),e.attrs)t.setAttribute(ro.has(i)?i:b(i),e.attrs[i])}(t,e,0,n)}mount(t){this.isSVGTag=nD(t.tagName),super.mount(t)}}let rl=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,n)=>"create"===n?t:(e.has(n)||e.set(n,t(n)),e.get(n))})}((u={animation:{Feature:tX},exit:{Feature:tZ},inView:{Feature:nr},tap:{Feature:i7},focus:{Feature:iQ},hover:{Feature:iJ},pan:{Feature:ej},drag:{Feature:eD,ProjectionNode:i_,MeasureLayout:eK},layout:{ProjectionNode:i_,MeasureLayout:eK}},h=(t,e)=>nB(t)?new ra(e):new rs(e,{allowProjection:t!==eO.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function(t){var e,i;let{preloadedFeatures:n,createVisualElement:r,useRender:s,useVisualState:o,Component:a}=t;function l(t,e){var i;let n;let l={...(0,eO.useContext)(no._),...t,layoutId:function(t){let{layoutId:e}=t,i=(0,eO.useContext)(eW.p).id;return i&&void 0!==e?i+"-"+e:e}(t)},{isStatic:u}=l,h=function(t){let{initial:e,animate:i}=function(t,e){if(nl(t)){let{initial:e,animate:i}=t;return{initial:!1===e||tF(e)?e:void 0,animate:tF(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,eO.useContext)(na));return(0,eO.useMemo)(()=>({initial:e,animate:i}),[nh(e),nh(i)])}(t),c=o(t,u);if(!u&&nc.j){(0,eO.useContext)(ns).strict;let t=function(t){let{drag:e,layout:i}=np;if(!e&&!i)return{};let n={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(l);n=t.MeasureLayout,h.visualElement=function(t,e,i,n,r){let{visualElement:s}=(0,eO.useContext)(na),o=(0,eO.useContext)(ns),a=(0,eO.useContext)(nf.O),l=(0,eO.useContext)(no._).reducedMotion,u=(0,eO.useRef)(null);n=n||o.renderer,!u.current&&n&&(u.current=n(t,{visualState:e,parent:s,props:i,presenceContext:a,blockInitialAnimation:!!a&&!1===a.initial,reducedMotionConfig:l}));let h=u.current,c=(0,eO.useContext)(eI);h&&!h.projection&&r&&("html"===h.type||"svg"===h.type)&&function(t,e,i,n){let{layoutId:r,layout:s,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:h}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:r,layout:s,alwaysMeasureLayout:!!o||a&&em(a),visualElement:t,animationType:"string"==typeof s?s:"both",initialPromotionConfig:n,crossfade:h,layoutScroll:l,layoutRoot:u})}(u.current,i,r,c);let d=(0,eO.useRef)(!1);(0,eO.useInsertionEffect)(()=>{h&&d.current&&h.update(i,a)});let p=i[w],m=(0,eO.useRef)(!!p&&!window.MotionHandoffIsComplete?.(p)&&window.MotionHasOptimisedAnimation?.(p));return(0,ng.L)(()=>{h&&(d.current=!0,window.MotionIsMounted=!0,h.updateFeatures(),eF.render(h.render),m.current&&h.animationState&&h.animationState.animateChanges())}),(0,eO.useEffect)(()=>{h&&(!m.current&&h.animationState&&h.animationState.animateChanges(),m.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(p)}),m.current=!1))}),h}(a,c,l,r,t.ProjectionNode)}return(0,eL.jsxs)(na.Provider,{value:h,children:[n&&h.visualElement?(0,eL.jsx)(n,{visualElement:h.visualElement,...l}):null,s(a,t,(i=h.visualElement,(0,eO.useCallback)(t=>{t&&c.onMount&&c.onMount(t),i&&(t?i.mount(t):i.unmount()),e&&("function"==typeof e?e(t):em(e)&&(e.current=t))},[i])),c,u,h.visualElement)]})}n&&function(t){for(let e in t)np[e]={...np[e],...t[e]}}(n),l.displayName="motion.".concat("string"==typeof a?a:"create(".concat(null!==(i=null!==(e=a.displayName)&&void 0!==e?e:a.name)&&void 0!==i?i:"",")"));let u=(0,eO.forwardRef)(l);return u[nm]=a,u}({...nB(t)?nN:nI,preloadedFeatures:u,useRender:function(t=!1){return(e,i,n,{latestValues:r},s)=>{let o=(nB(e)?function(t,e,i,n){let r=(0,eO.useMemo)(()=>{let i=nC();return nE(i,e,nD(n),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};nk(e,t.style,t),r.style={...e,...r.style}}return r}:function(t,e){let i={},n=function(t,e){let i=t.style||{},n={};return nk(n,i,t),Object.assign(n,function({transformTemplate:t},e){return(0,eO.useMemo)(()=>{let i=nA();return nS(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),n}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=n,i})(i,r,s,e),a=function(t,e,i){let n={};for(let r in t)("values"!==r||"object"!=typeof t.values)&&(nL(r)||!0===i&&nj(r)||!e&&!nj(r)||t.draggable&&r.startsWith("onDrag"))&&(n[r]=t[r]);return n}(i,"string"==typeof e,t),l=e!==eO.Fragment?{...a,...o,ref:n}:{},{children:u}=i,h=(0,eO.useMemo)(()=>(0,v.i)(u)?u.get():u,[u]);return(0,eO.createElement)(e,{...l,children:h})}}(e),createVisualElement:h,Component:t})}))},44563:function(t,e,i){i.d(e,{j:function(){return n}});let n="undefined"!=typeof window},53576:function(t,e,i){i.d(e,{h:function(){return r}});var n=i(2265);function r(t){let e=(0,n.useRef)(null);return null===e.current&&(e.current=t()),e.current}},11534:function(t,e,i){i.d(e,{L:function(){return r}});var n=i(2265);let r=i(44563).j?n.useLayoutEffect:n.useEffect},28441:function(t,e,i){i.d(e,{L:function(){return v}});var n=i(24750),r=i(21865),s=i(29711),o=i(41927),a=i(35818),l=i(63723),u=i(26147);let h=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>u.Wi.update(e,t),stop:()=>(0,u.Pn)(e),now:()=>u.frameData.isProcessing?u.frameData.timestamp:o.X.now()}};var c=i(85898),d=i(29207),p=i(44013),m=i(69118),f=i(98133),g=i(9868);let y=t=>t/100;class v extends g.T{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=(t=!0)=>{if(t){let{motionValue:t}=this.options;t&&t.updatedAt!==o.X.now()&&this.tick(o.X.now())}this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},a.P.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;(0,f.f)(t);let{type:e=d.F,repeat:i=0,repeatDelay:r=0,repeatType:s,velocity:o=0}=t,{keyframes:a}=t,u=e||d.F;u!==d.F&&"number"!=typeof a[0]&&(this.mixKeyframes=(0,n.z)(y,(0,l.C)(a[0],a[1])),a=[0,100]);let h=u({...t,keyframes:a});"mirror"===s&&(this.mirroredGenerator=u({...t,keyframes:[...a].reverse(),velocity:-o})),null===h.calculatedDuration&&(h.calculatedDuration=(0,p.i)(h));let{calculatedDuration:c}=h;this.calculatedDuration=c,this.resolvedDuration=c+r,this.totalDuration=this.resolvedDuration*(i+1)-r,this.generator=h}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:n,mixKeyframes:s,mirroredGenerator:o,resolvedDuration:a,calculatedDuration:l}=this;if(null===this.startTime)return i.next(0);let{delay:u=0,keyframes:h,repeat:d,repeatType:p,repeatDelay:f,type:g,onUpdate:y,finalKeyframe:v}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-n/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let x=this.currentTime-u*(this.playbackSpeed>=0?1:-1),b=this.playbackSpeed>=0?x<0:x>n;this.currentTime=Math.max(x,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=n);let w=this.currentTime,T=i;if(d){let t=Math.min(this.currentTime,n)/a,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,d+1))%2&&("reverse"===p?(i=1-i,f&&(i-=f/a)):"mirror"===p&&(T=o)),w=(0,r.u)(0,1,i)*a}let P=b?{done:!1,value:h[0]}:T.next(w);s&&(P.value=s(P.value));let{done:S}=P;b||null===l||(S=this.playbackSpeed>=0?this.currentTime>=n:this.currentTime<=0);let A=null===this.holdTime&&("finished"===this.state||"running"===this.state&&S);return A&&g!==c.I&&(P.value=(0,m.$)(h,this.options,v,this.speed)),y&&y(P.value),A&&this.finish(),P}then(t,e){return this.finished.then(t,e)}get duration(){return(0,s.X)(this.calculatedDuration)}get time(){return(0,s.X)(this.currentTime)}set time(t){t=(0,s.w)(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(o.X.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=(0,s.X)(this.currentTime))}play(){if(this.isStopped)return;let{driver:t=h,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=e??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(o.X.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,a.P.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}},85898:function(t,e,i){i.d(e,{I:function(){return s}});var n=i(68930),r=i(59989);function s({keyframes:t,velocity:e=0,power:i=.8,timeConstant:s=325,bounceDamping:o=10,bounceStiffness:a=500,modifyTarget:l,min:u,max:h,restDelta:c=.5,restSpeed:d}){let p,m;let f=t[0],g={done:!1,value:f},y=t=>void 0!==u&&t<u||void 0!==h&&t>h,v=t=>void 0===u?h:void 0===h?u:Math.abs(u-t)<Math.abs(h-t)?u:h,x=i*e,b=f+x,w=void 0===l?b:l(b);w!==b&&(x=w-f);let T=t=>-x*Math.exp(-t/s),P=t=>w+T(t),S=t=>{let e=T(t),i=P(t);g.done=Math.abs(e)<=c,g.value=g.done?w:i},A=t=>{y(g.value)&&(p=t,m=(0,n.S)({keyframes:[g.value,v(g.value)],velocity:(0,r.P)(P,t,g.value),damping:o,stiffness:a,restDelta:c,restSpeed:d}))};return A(0),{calculatedDuration:null,next:t=>{let e=!1;return(m||void 0!==p||(e=!0,S(t),A(t)),void 0!==p&&t>=p)?m.next(t-p):(e||S(t),g)}}}},29207:function(t,e,i){i.d(e,{F:function(){return b}});var n=i(60068);let r=(0,n._)(.42,0,1,1),s=(0,n._)(0,0,.58,1),o=(0,n._)(.42,0,.58,1),a=t=>Array.isArray(t)&&"number"!=typeof t[0];var l=i(62035),u=i(56277),h=i(43273),c=i(75567),d=i(52927),p=i(53857);let m={linear:u.Z,easeIn:r,easeInOut:o,easeOut:s,circIn:d.Z7,circInOut:d.X7,circOut:d.Bn,backIn:c.G2,backInOut:c.XL,backOut:c.CG,anticipate:h.L},f=t=>"string"==typeof t,g=t=>{if((0,p.q)(t)){(0,l.k)(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,r,s]=t;return(0,n._)(e,i,r,s)}return f(t)?((0,l.k)(void 0!==m[t],`Invalid easing type '${t}'`),m[t]):t};var y=i(28460),v=i(81645),x=i(33811);function b({duration:t=300,keyframes:e,times:i,ease:n="easeInOut"}){let r=a(n)?n.map(g):g(n),s={done:!1,value:e[0]},l=(i&&i.length===e.length?i:function(t){let e=[0];return function(t,e){let i=t[t.length-1];for(let n=1;n<=e;n++){let r=(0,v.Y)(0,e,n);t.push((0,x.t)(i,1,r))}}(e,t.length-1),e}(e)).map(e=>e*t),u=(0,y.s)(l,e,{ease:Array.isArray(r)?r:e.map(()=>r||o).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(s.value=u(e),s.done=e>=t,s)}}},68930:function(t,e,i){i.d(e,{S:function(){return m}});var n=i(21865),r=i(29711),s=i(96092),o=i(44013),a=i(59989);let l={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};var u=i(62035);function h(t,e){return t*Math.sqrt(1-e*e)}let c=["duration","bounce"],d=["stiffness","damping","mass"];function p(t,e){return e.some(e=>void 0!==t[e])}function m(t=l.visualDuration,e=l.bounce){let i;let m="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:f,restDelta:g}=m,y=m.keyframes[0],v=m.keyframes[m.keyframes.length-1],x={done:!1,value:y},{stiffness:b,damping:w,mass:T,duration:P,velocity:S,isResolvedFromDuration:A}=function(t){let e={velocity:l.velocity,stiffness:l.stiffness,damping:l.damping,mass:l.mass,isResolvedFromDuration:!1,...t};if(!p(t,d)&&p(t,c)){if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),r=i*i,s=2*(0,n.u)(.05,1,1-(t.bounce||0))*Math.sqrt(r);e={...e,mass:l.mass,stiffness:r,damping:s}}else{let i=function({duration:t=l.duration,bounce:e=l.bounce,velocity:i=l.velocity,mass:s=l.mass}){let o,a;(0,u.K)(t<=(0,r.w)(l.maxDuration),"Spring duration must be 10 seconds or less");let c=1-e;c=(0,n.u)(l.minDamping,l.maxDamping,c),t=(0,n.u)(l.minDuration,l.maxDuration,(0,r.X)(t)),c<1?(o=e=>{let n=e*c,r=n*t;return .001-(n-i)/h(e,c)*Math.exp(-r)},a=e=>{let n=e*c*t,r=Math.pow(c,2)*Math.pow(e,2)*t,s=h(Math.pow(e,2),c);return(n*i+i-r)*Math.exp(-n)*(-o(e)+.001>0?-1:1)/s}):(o=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),a=e=>t*t*(i-e)*Math.exp(-e*t));let d=function(t,e,i){let n=i;for(let i=1;i<12;i++)n-=t(n)/e(n);return n}(o,a,5/t);if(t=(0,r.w)(t),isNaN(d))return{stiffness:l.stiffness,damping:l.damping,duration:t};{let e=Math.pow(d,2)*s;return{stiffness:e,damping:2*c*Math.sqrt(s*e),duration:t}}}(t);(e={...e,...i,mass:l.mass}).isResolvedFromDuration=!0}}return e}({...m,velocity:-(0,r.X)(m.velocity||0)}),k=S||0,M=w/(2*Math.sqrt(b*T)),V=v-y,E=(0,r.X)(Math.sqrt(b/T)),C=5>Math.abs(V);if(f||(f=C?l.restSpeed.granular:l.restSpeed.default),g||(g=C?l.restDelta.granular:l.restDelta.default),M<1){let t=h(E,M);i=e=>v-Math.exp(-M*E*e)*((k+M*E*V)/t*Math.sin(t*e)+V*Math.cos(t*e))}else if(1===M)i=t=>v-Math.exp(-E*t)*(V+(k+E*V)*t);else{let t=E*Math.sqrt(M*M-1);i=e=>{let i=Math.exp(-M*E*e),n=Math.min(t*e,300);return v-i*((k+M*E*V)*Math.sinh(n)+t*V*Math.cosh(n))/t}}let D={calculatedDuration:A&&P||null,next:t=>{let e=i(t);if(A)x.done=t>=P;else{let n=0===t?k:0;M<1&&(n=0===t?(0,r.w)(k):(0,a.P)(i,t,e));let s=Math.abs(n)<=f,o=Math.abs(v-e)<=g;x.done=s&&o}return x.value=x.done?v:e,x},toString:()=>{let t=Math.min((0,o.i)(D),o.E),e=(0,s.w)(e=>D.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return D}m.applyToOptions=t=>{let e=function(t,e=100,i){let n=i({...t,keyframes:[0,e]}),s=Math.min((0,o.i)(n),o.E);return{type:"keyframes",ease:t=>n.next(s*t).value/e,duration:(0,r.X)(s)}}(t,100,m);return t.ease=e.ease,t.duration=(0,r.w)(e.duration),t.type="keyframes",t}},44013:function(t,e,i){i.d(e,{E:function(){return n},i:function(){return r}});let n=2e4;function r(t){let e=0,i=t.next(e);for(;!i.done&&e<n;)e+=50,i=t.next(e);return e>=n?1/0:e}},59989:function(t,e,i){i.d(e,{P:function(){return r}});var n=i(88294);function r(t,e,i){let r=Math.max(e-5,0);return(0,n.R)(i-t(r),e-r)}},69118:function(t,e,i){i.d(e,{$:function(){return r}});let n=t=>null!==t;function r(t,{repeat:e,repeatType:i="loop"},r,s=1){let o=t.filter(n),a=s<0||e&&"loop"!==i&&e%2==1?0:o.length-1;return a&&void 0!==r?r:o[a]}},9868:function(t,e,i){i.d(e,{T:function(){return n}});class n{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}},38596:function(t,e,i){i.d(e,{f:function(){return r},t:function(){return o}});let n=t=>e=>"string"==typeof e&&e.startsWith(t),r=n("--"),s=n("var(--"),o=t=>!!s(t)&&a.test(t.split("/*")[0].trim()),a=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu},98133:function(t,e,i){i.d(e,{f:function(){return a}});var n=i(85898),r=i(29207),s=i(68930);let o={decay:n.I,inertia:n.I,tween:r.F,keyframes:r.F,spring:s.S};function a(t){"string"==typeof t.type&&(t.type=o[t.type])}},96092:function(t,e,i){i.d(e,{w:function(){return n}});let n=(t,e,i=10)=>{let n="",r=Math.max(Math.round(e/i),2);for(let e=0;e<r;e++)n+=t(e/(r-1))+", ";return`linear(${n.substring(0,n.length-2)})`}},62862:function(t,e,i){i.d(e,{Z:function(){return o}});var n=i(21457);let r=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"];var s=i(20557);function o(t,e){let i=!1,o=!0,a={delta:0,timestamp:0,isProcessing:!1},l=()=>i=!0,u=r.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,n=new Set,r=!1,o=!1,a=new WeakSet,l={delta:0,timestamp:0,isProcessing:!1},u=0;function h(e){a.has(e)&&(c.schedule(e),t()),u++,e(l)}let c={schedule:(t,e=!1,s=!1)=>{let o=s&&r?i:n;return e&&a.add(t),o.has(t)||o.add(t),t},cancel:t=>{n.delete(t),a.delete(t)},process:t=>{if(l=t,r){o=!0;return}r=!0,[i,n]=[n,i],i.forEach(h),e&&s.f.value&&s.f.value.frameloop[e].push(u),u=0,i.clear(),r=!1,o&&(o=!1,c.process(t))}};return c}(l,e?i:void 0),t),{}),{setup:h,read:c,resolveKeyframes:d,preUpdate:p,update:m,preRender:f,render:g,postRender:y}=u,v=()=>{let r=n.c.useManualTiming?a.timestamp:performance.now();i=!1,n.c.useManualTiming||(a.delta=o?1e3/60:Math.max(Math.min(r-a.timestamp,40),1)),a.timestamp=r,a.isProcessing=!0,h.process(a),c.process(a),d.process(a),p.process(a),m.process(a),f.process(a),g.process(a),y.process(a),a.isProcessing=!1,i&&e&&(o=!1,t(v))},x=()=>{i=!0,o=!0,a.isProcessing||t(v)};return{schedule:r.reduce((t,e)=>{let n=u[e];return t[e]=(t,e=!1,r=!1)=>(i||x(),n.schedule(t,e,r)),t},{}),cancel:t=>{for(let e=0;e<r.length;e++)u[r[e]].cancel(t)},state:a,steps:u}}},26147:function(t,e,i){i.d(e,{Pn:function(){return s},Wi:function(){return r},frameData:function(){return o},yL:function(){return a}});var n=i(56277);let{schedule:r,cancel:s,state:o,steps:a}=(0,i(62862).Z)("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:n.Z,!0)},41927:function(t,e,i){let n;i.d(e,{X:function(){return a}});var r=i(21457),s=i(26147);function o(){n=void 0}let a={now:()=>(void 0===n&&a.set(s.frameData.isProcessing||r.c.useManualTiming?s.frameData.timestamp:performance.now()),n),set:t=>{n=t,queueMicrotask(o)}}},35818:function(t,e,i){i.d(e,{P:function(){return n}});let n={layout:0,mainThread:0,waapi:0}},20557:function(t,e,i){i.d(e,{f:function(){return n}});let n={value:null,addProjectionMetrics:null}},28460:function(t,e,i){i.d(e,{s:function(){return h}});var n=i(21457),r=i(56277),s=i(24750),o=i(62035),a=i(81645),l=i(21865),u=i(63723);function h(t,e,{clamp:i=!0,ease:h,mixer:c}={}){let d=t.length;if((0,o.k)(d===e.length,"Both input and output ranges must be the same length"),1===d)return()=>e[0];if(2===d&&e[0]===e[1])return()=>e[1];let p=t[0]===t[1];t[0]>t[d-1]&&(t=[...t].reverse(),e=[...e].reverse());let m=function(t,e,i){let o=[],a=i||n.c.mix||u.C,l=t.length-1;for(let i=0;i<l;i++){let n=a(t[i],t[i+1]);if(e){let t=Array.isArray(e)?e[i]||r.Z:e;n=(0,s.z)(t,n)}o.push(n)}return o}(e,h,c),f=m.length,g=i=>{if(p&&i<t[0])return e[0];let n=0;if(f>1)for(;n<t.length-2&&!(i<t[n+1]);n++);let r=(0,a.Y)(t[n],t[n+1],i);return m[n](r)};return i?e=>g((0,l.u)(t[0],t[d-1],e)):g}},13537:function(t,e,i){i.d(e,{R:function(){return r}});var n=i(41464);function r(t){return(0,n.K)(t)&&"offsetHeight"in t}},63723:function(t,e,i){i.d(e,{C:function(){return A}});var n=i(24750),r=i(62035),s=i(38596),o=i(7537),a=i(65050),l=i(31664),u=i(32017);function h(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}var c=i(80230);function d(t,e){return i=>i>0?e:t}var p=i(33811);let m=(t,e,i)=>{let n=t*t,r=i*(e*e-n)+n;return r<0?0:Math.sqrt(r)},f=[l.$,c.m,u.J],g=t=>f.find(e=>e.test(t));function y(t){let e=g(t);if((0,r.K)(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===u.J&&(i=function({hue:t,saturation:e,lightness:i,alpha:n}){t/=360,i/=100;let r=0,s=0,o=0;if(e/=100){let n=i<.5?i*(1+e):i+e-i*e,a=2*i-n;r=h(a,n,t+1/3),s=h(a,n,t),o=h(a,n,t-1/3)}else r=s=o=i;return{red:Math.round(255*r),green:Math.round(255*s),blue:Math.round(255*o),alpha:n}}(i)),i}let v=(t,e)=>{let i=y(t),n=y(e);if(!i||!n)return d(t,e);let r={...i};return t=>(r.red=m(i.red,n.red,t),r.green=m(i.green,n.green,t),r.blue=m(i.blue,n.blue,t),r.alpha=(0,p.t)(i.alpha,n.alpha,t),c.m.transform(r))},x=new Set(["none","hidden"]);function b(t,e){return i=>(0,p.t)(t,e,i)}function w(t){return"number"==typeof t?b:"string"==typeof t?(0,s.t)(t)?d:o.$.test(t)?v:S:Array.isArray(t)?T:"object"==typeof t?o.$.test(t)?v:P:d}function T(t,e){let i=[...t],n=i.length,r=t.map((t,i)=>w(t)(t,e[i]));return t=>{for(let e=0;e<n;e++)i[e]=r[e](t);return i}}function P(t,e){let i={...t,...e},n={};for(let r in i)void 0!==t[r]&&void 0!==e[r]&&(n[r]=w(t[r])(t[r],e[r]));return t=>{for(let e in n)i[e]=n[e](t);return i}}let S=(t,e)=>{let i=a.P.createTransformer(e),s=(0,a.V)(t),o=(0,a.V)(e);return s.indexes.var.length===o.indexes.var.length&&s.indexes.color.length===o.indexes.color.length&&s.indexes.number.length>=o.indexes.number.length?x.has(t)&&!o.values.length||x.has(e)&&!s.values.length?x.has(t)?i=>i<=0?t:e:i=>i>=1?e:t:(0,n.z)(T(function(t,e){let i=[],n={color:0,var:0,number:0};for(let r=0;r<e.values.length;r++){let s=e.types[r],o=t.indexes[s][n[s]],a=t.values[o]??0;i[r]=a,n[s]++}return i}(s,o),o.values),i):((0,r.K)(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),d(t,e))};function A(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?(0,p.t)(t,e,i):w(t)(t,e)}},33811:function(t,e,i){i.d(e,{t:function(){return n}});let n=(t,e,i)=>t+(e-t)*i},94357:function(t,e,i){i.d(e,{BX:function(){return h},S1:function(){return l}});var n=i(94513),r=i(88294),s=i(41927),o=i(26147);let a=t=>!isNaN(parseFloat(t)),l={current:void 0};class u{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=s.X.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=s.X.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=a(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new n.L);let i=this.events[t].add(e);return"change"===t?()=>{i(),o.Wi.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return l.current&&l.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){let t=s.X.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return(0,r.R)(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function h(t,e){return new u(t,e)}},31664:function(t,e,i){i.d(e,{$:function(){return r}});var n=i(80230);let r={test:(0,i(48017).i)("#"),parse:function(t){let e="",i="",n="",r="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),n=t.substring(5,7),r=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),n=t.substring(3,4),r=t.substring(4,5),e+=e,i+=i,n+=n,r+=r),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(n,16),alpha:r?parseInt(r,16)/255:1}},transform:n.m.transform}},32017:function(t,e,i){i.d(e,{J:function(){return a}});var n=i(61799),r=i(92854),s=i(51224),o=i(48017);let a={test:(0,o.i)("hsl","hue"),parse:(0,o.d)("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:o=1})=>"hsla("+Math.round(t)+", "+r.aQ.transform((0,s.N)(e))+", "+r.aQ.transform((0,s.N)(i))+", "+(0,s.N)(n.Fq.transform(o))+")"}},7537:function(t,e,i){i.d(e,{$:function(){return o}});var n=i(31664),r=i(32017),s=i(80230);let o={test:t=>s.m.test(t)||n.$.test(t)||r.J.test(t),parse:t=>s.m.test(t)?s.m.parse(t):r.J.test(t)?r.J.parse(t):n.$.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?s.m.transform(t):r.J.transform(t)}},80230:function(t,e,i){i.d(e,{m:function(){return u}});var n=i(21865),r=i(61799),s=i(51224),o=i(48017);let a=t=>(0,n.u)(0,255,t),l={...r.Rx,transform:t=>Math.round(a(t))},u={test:(0,o.i)("rgb","red"),parse:(0,o.d)("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:n=1})=>"rgba("+l.transform(t)+", "+l.transform(e)+", "+l.transform(i)+", "+(0,s.N)(r.Fq.transform(n))+")"}},48017:function(t,e,i){i.d(e,{i:function(){return s},d:function(){return o}});var n=i(3855);let r=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,s=(t,e)=>i=>!!("string"==typeof i&&r.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),o=(t,e,i)=>r=>{if("string"!=typeof r)return r;let[s,o,a,l]=r.match(n.K);return{[t]:parseFloat(s),[e]:parseFloat(o),[i]:parseFloat(a),alpha:void 0!==l?parseFloat(l):1}}},65050:function(t,e,i){i.d(e,{V:function(){return h},P:function(){return m}});var n=i(7537);let r=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;var s=i(3855),o=i(51224);let a="number",l="color",u=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function h(t){let e=t.toString(),i=[],r={color:[],number:[],var:[]},s=[],o=0,h=e.replace(u,t=>(n.$.test(t)?(r.color.push(o),s.push(l),i.push(n.$.parse(t))):t.startsWith("var(")?(r.var.push(o),s.push("var"),i.push(t)):(r.number.push(o),s.push(a),i.push(parseFloat(t))),++o,"${}")).split("${}");return{values:i,split:h,indexes:r,types:s}}function c(t){return h(t).values}function d(t){let{split:e,types:i}=h(t),r=e.length;return t=>{let s="";for(let u=0;u<r;u++)if(s+=e[u],void 0!==t[u]){let e=i[u];e===a?s+=(0,o.N)(t[u]):e===l?s+=n.$.transform(t[u]):s+=t[u]}return s}}let p=t=>"number"==typeof t?0:t,m={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(s.K)?.length||0)+(t.match(r)?.length||0)>0},parse:c,createTransformer:d,getAnimatableNone:function(t){let e=c(t);return d(t)(e.map(p))}}},61799:function(t,e,i){i.d(e,{Fq:function(){return s},Rx:function(){return r},bA:function(){return o}});var n=i(21865);let r={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},s={...r,transform:t=>(0,n.u)(0,1,t)},o={...r,default:1}},92854:function(t,e,i){i.d(e,{$C:function(){return u},RW:function(){return r},aQ:function(){return s},px:function(){return o},vh:function(){return a},vw:function(){return l}});let n=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),r=n("deg"),s=n("%"),o=n("px"),a=n("vh"),l=n("vw"),u={...s,parse:t=>s.parse(t)/100,transform:t=>s.transform(100*t)}},3855:function(t,e,i){i.d(e,{K:function(){return n}});let n=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu},51224:function(t,e,i){i.d(e,{N:function(){return n}});let n=t=>Math.round(1e5*t)/1e5},87493:function(t,e,i){i.d(e,{i:function(){return n}});let n=t=>!!(t&&t.getVelocity)},98425:function(t,e,i){function n(t,e){-1===t.indexOf(e)&&t.push(e)}function r(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}i.d(e,{cl:function(){return r},y4:function(){return n}})},21865:function(t,e,i){i.d(e,{u:function(){return n}});let n=(t,e,i)=>i>e?e:i<t?t:i},43273:function(t,e,i){i.d(e,{L:function(){return r}});var n=i(75567);let r=t=>(t*=2)<1?.5*(0,n.G2)(t):.5*(2-Math.pow(2,-10*(t-1)))},75567:function(t,e,i){i.d(e,{CG:function(){return o},G2:function(){return a},XL:function(){return l}});var n=i(60068),r=i(52333),s=i(79015);let o=(0,n._)(.33,1.53,.69,.99),a=(0,s.M)(o),l=(0,r.o)(a)},52927:function(t,e,i){i.d(e,{Bn:function(){return o},X7:function(){return a},Z7:function(){return s}});var n=i(52333),r=i(79015);let s=t=>1-Math.sin(Math.acos(t)),o=(0,r.M)(s),a=(0,n.o)(s)},60068:function(t,e,i){i.d(e,{_:function(){return s}});var n=i(56277);let r=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function s(t,e,i,s){if(t===e&&i===s)return n.Z;let o=e=>(function(t,e,i,n,s){let o,a;let l=0;do(o=r(a=e+(i-e)/2,n,s)-t)>0?i=a:e=a;while(Math.abs(o)>1e-7&&++l<12);return a})(e,0,1,t,i);return t=>0===t||1===t?t:r(o(t),e,s)}},52333:function(t,e,i){i.d(e,{o:function(){return n}});let n=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2},79015:function(t,e,i){i.d(e,{M:function(){return n}});let n=t=>e=>1-t(1-e)},53857:function(t,e,i){i.d(e,{q:function(){return n}});let n=t=>Array.isArray(t)&&"number"==typeof t[0]},62035:function(t,e,i){i.d(e,{K:function(){return n},k:function(){return r}});let n=()=>{},r=()=>{}},21457:function(t,e,i){i.d(e,{c:function(){return n}});let n={}},41464:function(t,e,i){i.d(e,{K:function(){return n}});function n(t){return"object"==typeof t&&null!==t}},56277:function(t,e,i){i.d(e,{Z:function(){return n}});let n=t=>t},24750:function(t,e,i){i.d(e,{z:function(){return r}});let n=(t,e)=>i=>e(t(i)),r=(...t)=>t.reduce(n)},81645:function(t,e,i){i.d(e,{Y:function(){return n}});let n=(t,e,i)=>{let n=e-t;return 0===n?1:(i-t)/n}},94513:function(t,e,i){i.d(e,{L:function(){return r}});var n=i(98425);class r{constructor(){this.subscriptions=[]}add(t){return(0,n.y4)(this.subscriptions,t),()=>(0,n.cl)(this.subscriptions,t)}notify(t,e,i){let n=this.subscriptions.length;if(n){if(1===n)this.subscriptions[0](t,e,i);else for(let r=0;r<n;r++){let n=this.subscriptions[r];n&&n(t,e,i)}}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}},29711:function(t,e,i){i.d(e,{X:function(){return r},w:function(){return n}});let n=t=>1e3*t,r=t=>t/1e3},88294:function(t,e,i){i.d(e,{R:function(){return n}});function n(t,e){return e?1e3/e*t:0}},53335:function(t,e,i){i.d(e,{m6:function(){return q}});let n=t=>{let e=a(t),{conflictingClassGroups:i,conflictingClassGroupModifiers:n}=t;return{getClassGroupId:t=>{let i=t.split("-");return""===i[0]&&1!==i.length&&i.shift(),r(i,e)||o(t)},getConflictingClassGroupIds:(t,e)=>{let r=i[t]||[];return e&&n[t]?[...r,...n[t]]:r}}},r=(t,e)=>{if(0===t.length)return e.classGroupId;let i=t[0],n=e.nextPart.get(i),s=n?r(t.slice(1),n):void 0;if(s)return s;if(0===e.validators.length)return;let o=t.join("-");return e.validators.find(({validator:t})=>t(o))?.classGroupId},s=/^\[(.+)\]$/,o=t=>{if(s.test(t)){let e=s.exec(t)[1],i=e?.substring(0,e.indexOf(":"));if(i)return"arbitrary.."+i}},a=t=>{let{theme:e,prefix:i}=t,n={nextPart:new Map,validators:[]};return c(Object.entries(t.classGroups),i).forEach(([t,i])=>{l(i,n,t,e)}),n},l=(t,e,i,n)=>{t.forEach(t=>{if("string"==typeof t){(""===t?e:u(e,t)).classGroupId=i;return}if("function"==typeof t){if(h(t)){l(t(n),e,i,n);return}e.validators.push({validator:t,classGroupId:i});return}Object.entries(t).forEach(([t,r])=>{l(r,u(e,t),i,n)})})},u=(t,e)=>{let i=t;return e.split("-").forEach(t=>{i.nextPart.has(t)||i.nextPart.set(t,{nextPart:new Map,validators:[]}),i=i.nextPart.get(t)}),i},h=t=>t.isThemeGetter,c=(t,e)=>e?t.map(([t,i])=>[t,i.map(t=>"string"==typeof t?e+t:"object"==typeof t?Object.fromEntries(Object.entries(t).map(([t,i])=>[e+t,i])):t)]):t,d=t=>{if(t<1)return{get:()=>void 0,set:()=>{}};let e=0,i=new Map,n=new Map,r=(r,s)=>{i.set(r,s),++e>t&&(e=0,n=i,i=new Map)};return{get(t){let e=i.get(t);return void 0!==e?e:void 0!==(e=n.get(t))?(r(t,e),e):void 0},set(t,e){i.has(t)?i.set(t,e):r(t,e)}}},p=t=>{let{separator:e,experimentalParseClassName:i}=t,n=1===e.length,r=e[0],s=e.length,o=t=>{let i;let o=[],a=0,l=0;for(let u=0;u<t.length;u++){let h=t[u];if(0===a){if(h===r&&(n||t.slice(u,u+s)===e)){o.push(t.slice(l,u)),l=u+s;continue}if("/"===h){i=u;continue}}"["===h?a++:"]"===h&&a--}let u=0===o.length?t:t.substring(l),h=u.startsWith("!"),c=h?u.substring(1):u;return{modifiers:o,hasImportantModifier:h,baseClassName:c,maybePostfixModifierPosition:i&&i>l?i-l:void 0}};return i?t=>i({className:t,parseClassName:o}):o},m=t=>{if(t.length<=1)return t;let e=[],i=[];return t.forEach(t=>{"["===t[0]?(e.push(...i.sort(),t),i=[]):i.push(t)}),e.push(...i.sort()),e},f=t=>({cache:d(t.cacheSize),parseClassName:p(t),...n(t)}),g=/\s+/,y=(t,e)=>{let{parseClassName:i,getClassGroupId:n,getConflictingClassGroupIds:r}=e,s=[],o=t.trim().split(g),a="";for(let t=o.length-1;t>=0;t-=1){let e=o[t],{modifiers:l,hasImportantModifier:u,baseClassName:h,maybePostfixModifierPosition:c}=i(e),d=!!c,p=n(d?h.substring(0,c):h);if(!p){if(!d||!(p=n(h))){a=e+(a.length>0?" "+a:a);continue}d=!1}let f=m(l).join(":"),g=u?f+"!":f,y=g+p;if(s.includes(y))continue;s.push(y);let v=r(p,d);for(let t=0;t<v.length;++t){let e=v[t];s.push(g+e)}a=e+(a.length>0?" "+a:a)}return a};function v(){let t,e,i=0,n="";for(;i<arguments.length;)(t=arguments[i++])&&(e=x(t))&&(n&&(n+=" "),n+=e);return n}let x=t=>{let e;if("string"==typeof t)return t;let i="";for(let n=0;n<t.length;n++)t[n]&&(e=x(t[n]))&&(i&&(i+=" "),i+=e);return i},b=t=>{let e=e=>e[t]||[];return e.isThemeGetter=!0,e},w=/^\[(?:([a-z-]+):)?(.+)\]$/i,T=/^\d+\/\d+$/,P=new Set(["px","full","screen"]),S=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,A=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,k=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,M=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,V=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,E=t=>D(t)||P.has(t)||T.test(t),C=t=>X(t,"length",Y),D=t=>!!t&&!Number.isNaN(Number(t)),R=t=>X(t,"number",D),j=t=>!!t&&Number.isInteger(Number(t)),L=t=>t.endsWith("%")&&D(t.slice(0,-1)),F=t=>w.test(t),B=t=>S.test(t),O=new Set(["length","size","percentage"]),$=t=>X(t,O,Z),W=t=>X(t,"position",Z),I=new Set(["image","url"]),z=t=>X(t,I,_),N=t=>X(t,"",K),U=()=>!0,X=(t,e,i)=>{let n=w.exec(t);return!!n&&(n[1]?"string"==typeof e?n[1]===e:e.has(n[1]):i(n[2]))},Y=t=>A.test(t)&&!k.test(t),Z=()=>!1,K=t=>M.test(t),_=t=>V.test(t),q=function(t,...e){let i,n,r;let s=function(a){return n=(i=f(e.reduce((t,e)=>e(t),t()))).cache.get,r=i.cache.set,s=o,o(a)};function o(t){let e=n(t);if(e)return e;let s=y(t,i);return r(t,s),s}return function(){return s(v.apply(null,arguments))}}(()=>{let t=b("colors"),e=b("spacing"),i=b("blur"),n=b("brightness"),r=b("borderColor"),s=b("borderRadius"),o=b("borderSpacing"),a=b("borderWidth"),l=b("contrast"),u=b("grayscale"),h=b("hueRotate"),c=b("invert"),d=b("gap"),p=b("gradientColorStops"),m=b("gradientColorStopPositions"),f=b("inset"),g=b("margin"),y=b("opacity"),v=b("padding"),x=b("saturate"),w=b("scale"),T=b("sepia"),P=b("skew"),S=b("space"),A=b("translate"),k=()=>["auto","contain","none"],M=()=>["auto","hidden","clip","visible","scroll"],V=()=>["auto",F,e],O=()=>[F,e],I=()=>["",E,C],X=()=>["auto",D,F],Y=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],Z=()=>["solid","dashed","dotted","double","none"],K=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],_=()=>["start","end","center","between","around","evenly","stretch"],q=()=>["","0",F],G=()=>["auto","avoid","all","avoid-page","page","left","right","column"],H=()=>[D,F];return{cacheSize:500,separator:":",theme:{colors:[U],spacing:[E,C],blur:["none","",B,F],brightness:H(),borderColor:[t],borderRadius:["none","","full",B,F],borderSpacing:O(),borderWidth:I(),contrast:H(),grayscale:q(),hueRotate:H(),invert:q(),gap:O(),gradientColorStops:[t],gradientColorStopPositions:[L,C],inset:V(),margin:V(),opacity:H(),padding:O(),saturate:H(),scale:H(),sepia:q(),skew:H(),space:O(),translate:O()},classGroups:{aspect:[{aspect:["auto","square","video",F]}],container:["container"],columns:[{columns:[B]}],"break-after":[{"break-after":G()}],"break-before":[{"break-before":G()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...Y(),F]}],overflow:[{overflow:M()}],"overflow-x":[{"overflow-x":M()}],"overflow-y":[{"overflow-y":M()}],overscroll:[{overscroll:k()}],"overscroll-x":[{"overscroll-x":k()}],"overscroll-y":[{"overscroll-y":k()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[f]}],"inset-x":[{"inset-x":[f]}],"inset-y":[{"inset-y":[f]}],start:[{start:[f]}],end:[{end:[f]}],top:[{top:[f]}],right:[{right:[f]}],bottom:[{bottom:[f]}],left:[{left:[f]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",j,F]}],basis:[{basis:V()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",F]}],grow:[{grow:q()}],shrink:[{shrink:q()}],order:[{order:["first","last","none",j,F]}],"grid-cols":[{"grid-cols":[U]}],"col-start-end":[{col:["auto",{span:["full",j,F]},F]}],"col-start":[{"col-start":X()}],"col-end":[{"col-end":X()}],"grid-rows":[{"grid-rows":[U]}],"row-start-end":[{row:["auto",{span:[j,F]},F]}],"row-start":[{"row-start":X()}],"row-end":[{"row-end":X()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",F]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",F]}],gap:[{gap:[d]}],"gap-x":[{"gap-x":[d]}],"gap-y":[{"gap-y":[d]}],"justify-content":[{justify:["normal",..._()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",..._(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[..._(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[v]}],px:[{px:[v]}],py:[{py:[v]}],ps:[{ps:[v]}],pe:[{pe:[v]}],pt:[{pt:[v]}],pr:[{pr:[v]}],pb:[{pb:[v]}],pl:[{pl:[v]}],m:[{m:[g]}],mx:[{mx:[g]}],my:[{my:[g]}],ms:[{ms:[g]}],me:[{me:[g]}],mt:[{mt:[g]}],mr:[{mr:[g]}],mb:[{mb:[g]}],ml:[{ml:[g]}],"space-x":[{"space-x":[S]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[S]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",F,e]}],"min-w":[{"min-w":[F,e,"min","max","fit"]}],"max-w":[{"max-w":[F,e,"none","full","min","max","fit","prose",{screen:[B]},B]}],h:[{h:[F,e,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[F,e,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[F,e,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[F,e,"auto","min","max","fit"]}],"font-size":[{text:["base",B,C]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",R]}],"font-family":[{font:[U]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",F]}],"line-clamp":[{"line-clamp":["none",D,R]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",E,F]}],"list-image":[{"list-image":["none",F]}],"list-style-type":[{list:["none","disc","decimal",F]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[t]}],"placeholder-opacity":[{"placeholder-opacity":[y]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[t]}],"text-opacity":[{"text-opacity":[y]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...Z(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",E,C]}],"underline-offset":[{"underline-offset":["auto",E,F]}],"text-decoration-color":[{decoration:[t]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:O()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",F]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",F]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[y]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...Y(),W]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",$]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},z]}],"bg-color":[{bg:[t]}],"gradient-from-pos":[{from:[m]}],"gradient-via-pos":[{via:[m]}],"gradient-to-pos":[{to:[m]}],"gradient-from":[{from:[p]}],"gradient-via":[{via:[p]}],"gradient-to":[{to:[p]}],rounded:[{rounded:[s]}],"rounded-s":[{"rounded-s":[s]}],"rounded-e":[{"rounded-e":[s]}],"rounded-t":[{"rounded-t":[s]}],"rounded-r":[{"rounded-r":[s]}],"rounded-b":[{"rounded-b":[s]}],"rounded-l":[{"rounded-l":[s]}],"rounded-ss":[{"rounded-ss":[s]}],"rounded-se":[{"rounded-se":[s]}],"rounded-ee":[{"rounded-ee":[s]}],"rounded-es":[{"rounded-es":[s]}],"rounded-tl":[{"rounded-tl":[s]}],"rounded-tr":[{"rounded-tr":[s]}],"rounded-br":[{"rounded-br":[s]}],"rounded-bl":[{"rounded-bl":[s]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[y]}],"border-style":[{border:[...Z(),"hidden"]}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[y]}],"divide-style":[{divide:Z()}],"border-color":[{border:[r]}],"border-color-x":[{"border-x":[r]}],"border-color-y":[{"border-y":[r]}],"border-color-s":[{"border-s":[r]}],"border-color-e":[{"border-e":[r]}],"border-color-t":[{"border-t":[r]}],"border-color-r":[{"border-r":[r]}],"border-color-b":[{"border-b":[r]}],"border-color-l":[{"border-l":[r]}],"divide-color":[{divide:[r]}],"outline-style":[{outline:["",...Z()]}],"outline-offset":[{"outline-offset":[E,F]}],"outline-w":[{outline:[E,C]}],"outline-color":[{outline:[t]}],"ring-w":[{ring:I()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[t]}],"ring-opacity":[{"ring-opacity":[y]}],"ring-offset-w":[{"ring-offset":[E,C]}],"ring-offset-color":[{"ring-offset":[t]}],shadow:[{shadow:["","inner","none",B,N]}],"shadow-color":[{shadow:[U]}],opacity:[{opacity:[y]}],"mix-blend":[{"mix-blend":[...K(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":K()}],filter:[{filter:["","none"]}],blur:[{blur:[i]}],brightness:[{brightness:[n]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",B,F]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[h]}],invert:[{invert:[c]}],saturate:[{saturate:[x]}],sepia:[{sepia:[T]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[i]}],"backdrop-brightness":[{"backdrop-brightness":[n]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[h]}],"backdrop-invert":[{"backdrop-invert":[c]}],"backdrop-opacity":[{"backdrop-opacity":[y]}],"backdrop-saturate":[{"backdrop-saturate":[x]}],"backdrop-sepia":[{"backdrop-sepia":[T]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[o]}],"border-spacing-x":[{"border-spacing-x":[o]}],"border-spacing-y":[{"border-spacing-y":[o]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",F]}],duration:[{duration:H()}],ease:[{ease:["linear","in","out","in-out",F]}],delay:[{delay:H()}],animate:[{animate:["none","spin","ping","pulse","bounce",F]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[w]}],"scale-x":[{"scale-x":[w]}],"scale-y":[{"scale-y":[w]}],rotate:[{rotate:[j,F]}],"translate-x":[{"translate-x":[A]}],"translate-y":[{"translate-y":[A]}],"skew-x":[{"skew-x":[P]}],"skew-y":[{"skew-y":[P]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",F]}],accent:[{accent:["auto",t]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",F]}],"caret-color":[{caret:[t]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":O()}],"scroll-mx":[{"scroll-mx":O()}],"scroll-my":[{"scroll-my":O()}],"scroll-ms":[{"scroll-ms":O()}],"scroll-me":[{"scroll-me":O()}],"scroll-mt":[{"scroll-mt":O()}],"scroll-mr":[{"scroll-mr":O()}],"scroll-mb":[{"scroll-mb":O()}],"scroll-ml":[{"scroll-ml":O()}],"scroll-p":[{"scroll-p":O()}],"scroll-px":[{"scroll-px":O()}],"scroll-py":[{"scroll-py":O()}],"scroll-ps":[{"scroll-ps":O()}],"scroll-pe":[{"scroll-pe":O()}],"scroll-pt":[{"scroll-pt":O()}],"scroll-pr":[{"scroll-pr":O()}],"scroll-pb":[{"scroll-pb":O()}],"scroll-pl":[{"scroll-pl":O()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",F]}],fill:[{fill:[t,"none"]}],"stroke-w":[{stroke:[E,C,R]}],stroke:[{stroke:[t,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}})}}]);