(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[160],{63943:function(e,t,n){Promise.resolve().then(n.bind(n,45086))},45086:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return m}});var i=n(57437),a=n(27648),r=n(8833),s=n(14938),o=n(73247),l=n(32660),c=n(2265),d=n(37053),u=n(90535),f=n(93448);let h=(0,u.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),g=c.forwardRef((e,t)=>{let{className:n,variant:a,size:r,asChild:s=!1,...o}=e,l=s?d.g7:"button";return(0,i.jsx)(l,{className:(0,f.cn)(h({variant:a,size:r,className:n})),ref:t,...o})});function m(){return(0,i.jsx)("div",{className:"min-h-screen bg-github-dark text-github-text flex items-center justify-center",children:(0,i.jsx)("div",{className:"max-w-md mx-auto text-center px-6",children:(0,i.jsxs)(r.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:[(0,i.jsx)(r.E.div,{className:"text-8xl font-bold text-neon-green mb-8",initial:{scale:.5},animate:{scale:1},transition:{duration:.5,type:"spring",stiffness:200},children:"404"}),(0,i.jsx)(r.E.h1,{className:"text-3xl font-bold text-white mb-4",initial:{opacity:0},animate:{opacity:1},transition:{delay:.2},children:"Page Not Found"}),(0,i.jsx)(r.E.p,{className:"text-github-text mb-8 leading-relaxed",initial:{opacity:0},animate:{opacity:1},transition:{delay:.3},children:"Oops! The page you're looking for doesn't exist. It might have been moved, deleted, or you entered the wrong URL."}),(0,i.jsxs)(r.E.div,{className:"flex flex-col sm:flex-row gap-4 justify-center",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},children:[(0,i.jsx)(g,{asChild:!0,className:"bg-neon-green text-black hover:bg-neon-green/90",children:(0,i.jsxs)(a.default,{href:"/",className:"flex items-center gap-2",children:[(0,i.jsx)(s.Z,{size:18}),"Go Home"]})}),(0,i.jsx)(g,{variant:"outline",asChild:!0,className:"border-github-border text-github-text hover:bg-github-light",children:(0,i.jsxs)(a.default,{href:"/#contact",className:"flex items-center gap-2",children:[(0,i.jsx)(o.Z,{size:18}),"Contact Support"]})})]}),(0,i.jsx)(r.E.div,{className:"mt-8",initial:{opacity:0},animate:{opacity:1},transition:{delay:.5},children:(0,i.jsxs)(g,{variant:"ghost",onClick:()=>window.history.back(),className:"text-github-text hover:text-white flex items-center gap-2",children:[(0,i.jsx)(l.Z,{size:18}),"Go Back"]})}),(0,i.jsx)(r.E.div,{className:"absolute top-1/4 left-1/4 w-2 h-2 bg-neon-green rounded-full opacity-50",animate:{scale:[1,1.5,1],opacity:[.5,1,.5]},transition:{duration:2,repeat:1/0,ease:"easeInOut"}}),(0,i.jsx)(r.E.div,{className:"absolute top-1/3 right-1/4 w-1 h-1 bg-neon-blue rounded-full opacity-50",animate:{scale:[1,2,1],opacity:[.3,.8,.3]},transition:{duration:3,repeat:1/0,ease:"easeInOut",delay:1}})]})})})}g.displayName="Button"},93448:function(e,t,n){"use strict";n.d(t,{cn:function(){return r}});var i=n(61994),a=n(53335);function r(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,a.m6)((0,i.W)(t))}},90535:function(e,t,n){"use strict";n.d(t,{j:function(){return s}});var i=n(61994);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,r=i.W,s=(e,t)=>n=>{var i;if((null==t?void 0:t.variants)==null)return r(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:s,defaultVariants:o}=t,l=Object.keys(s).map(e=>{let t=null==n?void 0:n[e],i=null==o?void 0:o[e];if(null===t)return null;let r=a(t)||a(i);return s[e][r]}),c=n&&Object.entries(n).reduce((e,t)=>{let[n,i]=t;return void 0===i||(e[n]=i),e},{});return r(e,l,null==t?void 0:null===(i=t.compoundVariants)||void 0===i?void 0:i.reduce((e,t)=>{let{class:n,className:i,...a}=t;return Object.entries(a).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...o,...c}[t]):({...o,...c})[t]===n})?[...e,n,i]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}}},function(e){e.O(0,[622,280,971,117,744],function(){return e(e.s=63943)}),_N_E=e.O()}]);