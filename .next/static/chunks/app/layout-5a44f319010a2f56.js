(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{30347:()=>{},49552:(e,t,n)=>{"use strict";n.d(t,{Providers:()=>G});var s=n(95155),o=n(72922),a=n(26715),r=n(12115),i=n(61764),l=n(59434);let d=i.Kq;i.bL,i.l9,r.forwardRef((e,t)=>{let{className:n,sideOffset:o=4,...a}=e;return(0,s.jsx)(i.UC,{ref:t,sideOffset:o,className:(0,l.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",n),...a})}).displayName=i.UC.displayName;let c=(0,r.createContext)(void 0),u=e=>{let{children:t}=e,[n,o]=(0,r.useState)("dark");return(0,r.useEffect)(()=>{let e=localStorage.getItem("theme"),t=window.matchMedia("(prefers-color-scheme: dark)").matches;e?(o(e),document.documentElement.classList.toggle("dark","dark"===e)):t&&(o("dark"),document.documentElement.classList.add("dark"))},[]),(0,s.jsx)(c.Provider,{value:{theme:n,toggleTheme:()=>{let e="dark"===n?"light":"dark";o(e),document.documentElement.classList.toggle("dark","dark"===e),localStorage.setItem("theme",e)}},children:t})};var m=n(87481),p=n(26621),h=n(74466),x=n(54416);let g=p.Kq,f=r.forwardRef((e,t)=>{let{className:n,...o}=e;return(0,s.jsx)(p.LM,{ref:t,className:(0,l.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",n),...o})});f.displayName=p.LM.displayName;let v=(0,h.F)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),b=r.forwardRef((e,t)=>{let{className:n,variant:o,...a}=e;return(0,s.jsx)(p.bL,{ref:t,className:(0,l.cn)(v({variant:o}),n),...a})});b.displayName=p.bL.displayName,r.forwardRef((e,t)=>{let{className:n,...o}=e;return(0,s.jsx)(p.rc,{ref:t,className:(0,l.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",n),...o})}).displayName=p.rc.displayName;let w=r.forwardRef((e,t)=>{let{className:n,...o}=e;return(0,s.jsx)(p.bm,{ref:t,className:(0,l.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",n),"toast-close":"",...o,children:(0,s.jsx)(x.A,{className:"h-4 w-4"})})});w.displayName=p.bm.displayName;let y=r.forwardRef((e,t)=>{let{className:n,...o}=e;return(0,s.jsx)(p.hE,{ref:t,className:(0,l.cn)("text-sm font-semibold",n),...o})});y.displayName=p.hE.displayName;let j=r.forwardRef((e,t)=>{let{className:n,...o}=e;return(0,s.jsx)(p.VY,{ref:t,className:(0,l.cn)("text-sm opacity-90",n),...o})});function N(){let{toasts:e}=(0,m.dj)();return(0,s.jsxs)(g,{children:[e.map(function(e){let{id:t,title:n,description:o,action:a,...r}=e;return(0,s.jsxs)(b,{...r,children:[(0,s.jsxs)("div",{className:"grid gap-1",children:[n&&(0,s.jsx)(y,{children:n}),o&&(0,s.jsx)(j,{children:o})]}),a,(0,s.jsx)(w,{})]},t)}),(0,s.jsx)(f,{})]})}j.displayName=p.VY.displayName;var E=n(51362),k=n(56671);let T=e=>{let{...t}=e,{theme:n="system"}=(0,E.D)();return(0,s.jsx)(k.l$,{theme:n,className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}},...t})};var S=n(58892);let A=()=>{let[e,t]=(0,r.useState)(0),[n,o]=(0,r.useState)("Initializing system..."),[a,i]=(0,r.useState)(!0),[l,d]=(0,r.useState)(!1),c=[{text:"Initializing system...",duration:1200},{text:"Establishing secure connection...",duration:1e3},{text:"Authenticating credentials...",duration:800},{text:"Bypassing security protocols...",duration:1500},{text:"Loading developer assets...",duration:1e3},{text:"Compiling portfolio data...",duration:1200},{text:"Optimizing display modules...",duration:900},{text:"Rendering interface...",duration:1300},{text:"System ready. Welcome to GreenHacker portfolio v2.0",duration:1e3}];return(0,r.useEffect)(()=>{let e=setInterval(()=>{i(e=>!e)},500),n=0,s=setTimeout(function e(){if(n<c.length){let{text:s,duration:a}=c[n];o(s),t(Math.min(100,Math.round((n+1)/c.length*100))),n++,setTimeout(e,a)}else d(!0),setTimeout(()=>{{let e=new Event("loadingComplete");window.dispatchEvent(e)}},1e3)},500);return()=>{clearInterval(e),clearTimeout(s)}},[]),(0,s.jsxs)(S.P.div,{className:"fixed inset-0 bg-black flex items-center justify-center z-50",initial:{opacity:1},exit:{opacity:0},transition:{duration:.6,ease:"easeInOut"},children:[(0,s.jsxs)(S.P.div,{className:"w-full max-w-3xl bg-black border border-neon-green p-6 rounded-md shadow-neon-green terminal-window",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{duration:.8}}},initial:"hidden",animate:"visible",children:[(0,s.jsxs)("div",{className:"terminal-header flex items-center justify-between mb-4",children:[(0,s.jsx)("div",{className:"text-neon-green font-mono text-sm",children:"~/green-hacker/portfolio"}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)("div",{className:"w-3 h-3 rounded-full bg-red-500"}),(0,s.jsx)("div",{className:"w-3 h-3 rounded-full bg-yellow-500"}),(0,s.jsx)("div",{className:"w-3 h-3 rounded-full bg-green-500"})]})]}),(0,s.jsxs)("div",{className:"terminal-content space-y-2 font-mono text-sm overflow-hidden",children:[(0,s.jsxs)("div",{className:"line",children:[(0,s.jsx)("span",{className:"text-neon-blue",children:"$ "}),(0,s.jsx)("span",{className:"text-white",children:"load portfolio --env=production --secure"})]}),(0,s.jsxs)(S.P.div,{className:"line text-neon-green",initial:{opacity:0},animate:{opacity:1},transition:{delay:.3},children:[n,a?"▋":" "]}),(0,s.jsxs)(S.P.div,{className:"line",initial:{opacity:0},animate:{opacity:1},transition:{delay:.5},children:[(0,s.jsxs)("div",{className:"text-github-text",children:["Progress: ",e,"%"]}),(0,s.jsx)("div",{className:"w-full bg-github-dark rounded-full h-2 mt-1",children:(0,s.jsx)(S.P.div,{className:"h-2 rounded-full bg-neon-green",initial:{width:0},animate:{width:"".concat(e,"%")},transition:{duration:.5}})})]}),l&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(S.P.div,{className:"line",initial:{opacity:0},animate:{opacity:1},transition:{delay:.2},children:[(0,s.jsx)("span",{className:"text-neon-blue",children:"$ "}),(0,s.jsx)("span",{className:"text-white",children:"launch --mode=interactive"})]}),(0,s.jsx)(S.P.div,{className:"line text-neon-purple",initial:{opacity:0},animate:{opacity:1},transition:{delay:.4},children:"Launching portfolio interface..."})]})]}),(0,s.jsx)("div",{className:"ascii-art mt-8 text-neon-green font-mono text-xs whitespace-pre",children:" ██████╗ ██████╗ ███████╗███████╗███╗   ██╗██╗  ██╗ █████╗  ██████╗██╗  ██╗███████╗██████╗\n██╔════╝ ██╔══██╗██╔════╝██╔════╝████╗  ██║██║  ██║██╔══██╗██╔════╝██║ ██╔╝██╔════╝██╔══██╗\n██║  ███╗██████╔╝█████╗  █████╗  ██╔██╗ ██║███████║███████║██║     █████╔╝ █████╗  ██████╔╝\n██║   ██║██╔══██╗██╔══╝  ██╔══╝  ██║╚██╗██║██╔══██║██╔══██║██║     ██╔═██╗ ██╔══╝  ██╔══██╗\n╚██████╔╝██║  ██║███████╗███████╗██║ ╚████║██║  ██║██║  ██║╚██████╗██║  ██╗███████╗██║  ██║\n ╚═════╝ ╚═╝  ╚═╝╚══════╝╚══════╝╚═╝  ╚═══╝╚═╝  ╚═╝╚═╝  ╚═╝ ╚═════╝╚═╝  ╚═╝╚══════╝╚═╝  ╚═╝"}),l&&(0,s.jsxs)(S.P.div,{className:"mt-6 text-center",initial:{opacity:0},animate:{opacity:1},transition:{delay:.8},children:[(0,s.jsx)("span",{className:"text-github-text text-sm",children:"Press "}),(0,s.jsx)("span",{className:"px-2 py-1 bg-github-light rounded text-white text-sm mx-1",children:"ENTER"}),(0,s.jsx)("span",{className:"text-github-text text-sm",children:" to continue"})]})]}),(0,s.jsx)("style",{dangerouslySetInnerHTML:{__html:"\n        .terminal-window {\n          box-shadow: 0 0 10px rgba(63, 185, 80, 0.3), 0 0 20px rgba(63, 185, 80, 0.2);\n        }\n\n        @keyframes scan {\n          from { top: 0; }\n          to { top: 100%; }\n        }\n\n        .terminal-window::before {\n          content: '';\n          position: absolute;\n          top: 0;\n          left: 0;\n          right: 0;\n          height: 3px;\n          background-color: rgba(63, 185, 80, 0.5);\n          animation: scan 3s linear infinite;\n        }\n      "}})]})},I=()=>{let[e,t]=(0,r.useState)({x:0,y:0}),[n,o]=(0,r.useState)(!1),[a,i]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{let e=e=>{t({x:e.clientX,y:e.clientY})},n=()=>{o(!0),setTimeout(()=>o(!1),300)},s=()=>{document.body.style.cursor="none"},a=()=>{document.body.style.cursor="auto"},r=e=>{let t=e.target;i(!!("button"===t.tagName.toLowerCase()||"a"===t.tagName.toLowerCase()||t.closest("button")||t.closest("a")))};return document.addEventListener("mousemove",e),document.addEventListener("mousedown",n),document.addEventListener("mouseenter",s),document.addEventListener("mouseleave",a),document.addEventListener("mouseover",r),()=>{document.removeEventListener("mousemove",e),document.removeEventListener("mousedown",n),document.removeEventListener("mouseenter",s),document.removeEventListener("mouseleave",a),document.removeEventListener("mouseover",r),document.body.style.cursor="auto"}},[]),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(S.P.div,{className:"fixed top-0 left-0 w-8 h-8 rounded-full border border-neon-green z-[9999] pointer-events-none",animate:{x:e.x-16,y:e.y-16,scale:n?.8:a?1.5:1},transition:{type:"spring",stiffness:300,damping:20,mass:.5}}),(0,s.jsx)(S.P.div,{className:"fixed top-0 left-0 w-2 h-2 bg-neon-green rounded-full z-[10000] pointer-events-none",animate:{x:e.x-4,y:e.y-4,opacity:n?.5:1},transition:{type:"spring",stiffness:400,damping:15}})]})};var R=n(8619),C=n(37602);let L=()=>{let e=(0,R.d)(0),t=(0,R.d)(0),n={stiffness:50,damping:50},o=(0,C.z)(e,n),a=(0,C.z)(t,n),i=(0,r.useRef)(null);return(0,r.useEffect)(()=>{let n=n=>{if(!i.current)return;let s=i.current.getBoundingClientRect(),o=s.left+s.width/2,a=s.top+s.height/2,r=(n.clientX-o)/(s.width/2),l=(n.clientY-a)/(s.height/2);e.set(10*r),t.set(10*l)};return window.addEventListener("mousemove",n),()=>{window.removeEventListener("mousemove",n)}},[e,t]),(0,s.jsx)("div",{ref:i,className:"fixed inset-0 pointer-events-none z-0 overflow-hidden",children:(0,s.jsxs)(S.P.div,{className:"absolute inset-0 opacity-20",style:{translateX:o,translateY:a},children:[(0,s.jsx)("div",{className:"absolute top-0 -left-4 w-[50vw] h-[50vw] bg-neon-purple rounded-full mix-blend-screen filter blur-[100px] opacity-70"}),(0,s.jsx)("div",{className:"absolute top-[30%] -right-[10%] w-[40vw] h-[40vw] bg-neon-green rounded-full mix-blend-screen filter blur-[100px] opacity-70"}),(0,s.jsx)("div",{className:"absolute -bottom-[20%] left-[20%] w-[60vw] h-[60vw] bg-neon-blue rounded-full mix-blend-screen filter blur-[100px] opacity-40"})]})})};var P=n(60760),z=n(41684),M=n(74311),O=n(14362),D=n(12486);let F={help:["\uD83D\uDCBB Available Commands:","- help: Display this help message","- about: Learn about Green Hacker","- skills: View technical skills","- projects: Show recent projects","- contact: Get contact information","- clear: Clear the terminal","- exit: Close the chatbot","","You can also just chat naturally!"],about:["Hey there! \uD83D\uDC4B I'm Green Hacker, a full-stack developer and ML enthusiast.","When I'm not coding, I'm probably hiking, gaming, or learning something new.","I specialize in creating interactive web experiences and AI-powered applications."],skills:["\uD83D\uDE80 Technical Skills:","- Frontend: React, TypeScript, Tailwind CSS, Framer Motion","- Backend: Node.js, Express, FastAPI, GraphQL","- ML/AI: PyTorch, TensorFlow, Computer Vision","- DevOps: Docker, AWS, CI/CD, Kubernetes","- Other: Three.js, React Three Fiber, WebGL"],projects:["\uD83D\uDCC1 Recent Projects:","1. AI Photo Platform - Face recognition for intelligent photo organization","2. Portfolio Website - You're looking at it right now!","3. ML Research Tool - Natural language processing for scientific papers","4. Real-time Collaboration App - WebRTC and WebSockets for seamless teamwork","",'Type "project [number]" for more details!'],"project 1":["\uD83D\uDCF7 AI Photo Platform","A machine learning application that uses facial recognition to organize and tag photos.","Tech stack: React, TypeScript, PyTorch, AWS S3, Tailwind CSS","Features: Face recognition, automatic tagging, search by person, cloud storage"],"project 2":["\uD83C\uDF10 Portfolio Website","An interactive portfolio showcasing my projects and skills with 3D elements.","Tech stack: React, Three.js, Framer Motion, Tailwind CSS","Features: 3D visualization, interactive components, responsive design"],"project 3":["\uD83D\uDCDA ML Research Tool","An AI-powered tool that helps researchers find relevant papers and extract insights.","Tech stack: Python, TensorFlow, FastAPI, React","Features: Paper recommendation, text summarization, citation network analysis"],"project 4":["\uD83D\uDC65 Real-time Collaboration App","A platform for teams to collaborate with document sharing and real-time editing.","Tech stack: React, Node.js, Socket.io, WebRTC, MongoDB","Features: Live document editing, video chat, project management tools"],contact:["\uD83D\uDCEB Contact Information:","Email: <EMAIL>","GitHub: github.com/greenhacker","LinkedIn: linkedin.com/in/greenhacker","Twitter: @greenhacker"],clear:[""],exit:["\uD83D\uDC4B Goodbye! You can open me again by clicking the terminal icon."]},_=()=>{let[e,t]=(0,r.useState)(!1),[n,o]=(0,r.useState)(!1),[a,i]=(0,r.useState)([{type:"bot",content:["\uD83D\uDC4B Hi there! I'm GREENHACKER's AI assistant.","I can tell you about GREENHACKER, their skills, projects, or how to get in touch.",'Type "help" to see what I can do!']}]),[l,d]=(0,r.useState)(""),[c,u]=(0,r.useState)(!1),m=(0,r.useRef)(null),p=(0,r.useRef)(null);(0,r.useEffect)(()=>{var e;null==(e=m.current)||e.scrollIntoView({behavior:"smooth"})},[a]),(0,r.useEffect)(()=>{if(e){var t;null==(t=p.current)||t.focus()}},[e]);let h=()=>{t(!e)},g=e=>{let n=e.toLowerCase().trim();if("exit"===n){i([...a,{type:"user",content:[e]},{type:"bot",content:F.exit}]),setTimeout(()=>t(!1),1e3);return}return"clear"===n?void i([]):F[n]?void i([...a,{type:"user",content:[e]},{type:"bot",content:F[n]}]):void(i([...a,{type:"user",content:[e]}]),u(!0),setTimeout(()=>{let t=f(e);i(e=>[...e,{type:"bot",content:t}]),u(!1)},1e3+1e3*Math.random()))},f=e=>{let t=e.toLowerCase();if(t.includes("hi")||t.includes("hello")||t.includes("hey"))return["Hello! How can I help you today? \uD83D\uDE0A",'Type "help" to see what I can do.'];if(t.includes("thanks")||t.includes("thank you"))return["You're welcome! Anything else you'd like to know?"];if(t.includes("experience")||t.includes("work"))return["GREENHACKER has over 5 years of experience in full-stack development and machine learning projects.","They've worked with startups and enterprise companies on various AI-powered applications."];if(t.includes("education"))return["GREENHACKER has a Master's degree in Computer Science with a specialization in Artificial Intelligence.","They're also continually learning through courses and self-study."];if(t.includes("name"))return["My name is GreenBot! I'm GREENHACKER's AI assistant."];else return["I'm not sure I understand that query.",'Type "help" to see what commands are available.']};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(S.P.button,{className:"fixed bottom-8 right-8 bg-neon-green text-black h-12 w-12 rounded-full flex items-center justify-center shadow-lg hover:scale-110 transition-transform z-50",whileHover:{scale:1.1},whileTap:{scale:.9},onClick:h,children:(0,s.jsx)(z.A,{size:20})}),(0,s.jsx)(P.N,{children:e&&(0,s.jsxs)(S.P.div,{className:"fixed ".concat(n?"inset-4 md:inset-10":"bottom-24 right-8 w-[350px] md:w-[400px] h-[500px]"," bg-black border border-neon-green/50 rounded-lg shadow-lg overflow-hidden z-50 flex flex-col"),initial:{opacity:0,y:50},animate:{opacity:1,y:0},exit:{opacity:0,y:50},transition:{duration:.3},children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-3 border-b border-neon-green/30 bg-black",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(z.A,{className:"text-neon-green mr-2",size:18}),(0,s.jsx)("h3",{className:"text-neon-green font-mono text-sm",children:"GREENHACKER Terminal"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("button",{className:"text-neon-green hover:text-white transition-colors focus:outline-none",onClick:()=>{o(!n)},children:n?(0,s.jsx)(M.A,{size:16}):(0,s.jsx)(O.A,{size:16})}),(0,s.jsx)("button",{className:"text-neon-green hover:text-white transition-colors focus:outline-none",onClick:h,children:(0,s.jsx)(x.A,{size:16})})]})]}),(0,s.jsx)("div",{className:"flex-grow overflow-y-auto p-4",style:{backgroundColor:"#0d1117"},children:(0,s.jsxs)("div",{className:"space-y-4",children:[a.map((e,t)=>(0,s.jsxs)("div",{className:"".concat("user"===e.type?"ml-auto max-w-[80%]":"mr-auto max-w-[80%]"),children:[(0,s.jsx)("div",{className:"rounded-lg p-3 ".concat("user"===e.type?"bg-neon-green/20 text-white":"bg-github-light text-neon-green"),children:e.content.map((e,t)=>(0,s.jsx)(r.Fragment,{children:""===e?(0,s.jsx)("br",{}):(0,s.jsx)("p",{className:"font-mono text-sm",children:e})},t))}),(0,s.jsxs)("p",{className:"text-xs text-github-text mt-1",children:["user"===e.type?"You":"GREENHACKER Bot"," • ",new Date().toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})]})]},t)),c&&(0,s.jsx)("div",{className:"mr-auto",children:(0,s.jsx)("div",{className:"bg-github-light rounded-lg p-3 max-w-[80%]",children:(0,s.jsxs)("div",{className:"flex space-x-1",children:[(0,s.jsx)("div",{className:"h-2 w-2 bg-neon-green rounded-full animate-bounce"}),(0,s.jsx)("div",{className:"h-2 w-2 bg-neon-green rounded-full animate-bounce",style:{animationDelay:"0.2s"}}),(0,s.jsx)("div",{className:"h-2 w-2 bg-neon-green rounded-full animate-bounce",style:{animationDelay:"0.4s"}})]})})}),(0,s.jsx)("div",{ref:m})]})}),(0,s.jsx)("form",{onSubmit:e=>{e.preventDefault(),l.trim()&&(g(l),d(""))},className:"p-3 border-t border-neon-green/30 bg-github-dark",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("span",{className:"text-neon-green font-mono mr-2",children:"$"}),(0,s.jsx)("input",{ref:p,type:"text",value:l,onChange:e=>d(e.target.value),className:"flex-grow bg-transparent border-none text-white font-mono focus:outline-none text-sm",placeholder:"Type a message or command..."}),(0,s.jsx)("button",{type:"submit",className:"text-neon-green hover:text-white transition-colors focus:outline-none",children:(0,s.jsx)(D.A,{size:16})})]})})]})})]})},H=new o.E({defaultOptions:{queries:{staleTime:6e4,refetchOnWindowFocus:!1}}});function G(e){let{children:t}=e,[n,o]=(0,r.useState)(!0),[i,l]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{let e=()=>{l(window.innerWidth<768)};if(e(),window.addEventListener("resize",e),sessionStorage.getItem("loadingShown"))o(!1);else{let t=()=>{setTimeout(()=>{o(!1),sessionStorage.setItem("loadingShown","true")},1e3)};window.addEventListener("loadingComplete",t);let n=setTimeout(()=>{o(!1),sessionStorage.setItem("loadingShown","true")},12e3);return()=>{window.removeEventListener("loadingComplete",t),window.removeEventListener("resize",e),clearTimeout(n)}}return()=>{window.removeEventListener("resize",e)}},[]),(0,s.jsx)(u,{children:(0,s.jsx)(a.Ht,{client:H,children:(0,s.jsxs)(d,{children:[(0,s.jsx)(N,{}),(0,s.jsx)(T,{}),n&&(0,s.jsx)(A,{}),(0,s.jsx)(L,{}),!i&&(0,s.jsx)(I,{}),t,(0,s.jsx)(_,{})]})})})}},59434:(e,t,n)=>{"use strict";n.d(t,{cn:()=>a});var s=n(52596),o=n(39688);function a(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,o.QP)((0,s.$)(t))}},61232:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,78346,23)),Promise.resolve().then(n.t.bind(n,30347,23)),Promise.resolve().then(n.bind(n,49552))},87481:(e,t,n)=>{"use strict";n.d(t,{dj:()=>m});var s=n(12115);let o=0,a=new Map,r=e=>{if(a.has(e))return;let t=setTimeout(()=>{a.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);a.set(e,t)},i=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:n}=t;return n?r(n):e.toasts.forEach(e=>{r(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===n||void 0===n?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],d={toasts:[]};function c(e){d=i(d,e),l.forEach(e=>{e(d)})}function u(e){let{...t}=e,n=(o=(o+1)%Number.MAX_SAFE_INTEGER).toString(),s=()=>c({type:"DISMISS_TOAST",toastId:n});return c({type:"ADD_TOAST",toast:{...t,id:n,open:!0,onOpenChange:e=>{e||s()}}}),{id:n,dismiss:s,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:n}})}}function m(){let[e,t]=s.useState(d);return s.useEffect(()=>(l.push(t),()=>{let e=l.indexOf(t);e>-1&&l.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}}},e=>{var t=t=>e(e.s=t);e.O(0,[838,710,107,469,441,684,358],()=>t(61232)),_N_E=e.O()}]);