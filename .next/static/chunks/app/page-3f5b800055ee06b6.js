(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{9207:(e,t,i)=>{Promise.resolve().then(i.bind(i,30486))},30486:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>e3});var s=i(95155),a=i(12115),n=i(50802),r=i(49088),o=i(36682),l=i(58892),c=i(6874),d=i.n(c);let h=()=>{let[e,t]=(0,a.useState)(!1),[i,n]=(0,a.useState)(!1);(0,a.useEffect)(()=>{let i=()=>{let i=window.scrollY>20;i!==e&&t(i)};return window.addEventListener("scroll",i),()=>window.removeEventListener("scroll",i)},[e]);let r=[{name:"About",href:"#about"},{name:"Skills",href:"#skills"},{name:"Projects",href:"#projects"},{name:"Experience",href:"#experience"},{name:"Contact",href:"#contact"}];return(0,s.jsxs)(l.P.header,{initial:{y:-100},animate:{y:0},transition:{duration:.5},className:"fixed top-0 w-full z-50 transition-all duration-300 ".concat(e?"bg-github-dark/80 backdrop-blur-md shadow-lg border-b border-github-border":"bg-transparent"),children:[(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex justify-between items-center py-4",children:[(0,s.jsxs)(d(),{href:"/",className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:"w-10 h-10 rounded-full bg-gradient-to-br from-neon-green to-neon-blue flex items-center justify-center",children:(0,s.jsx)("span",{className:"font-mono font-bold text-white",children:"GH"})}),(0,s.jsx)("span",{className:"font-bold text-xl",children:"GreenHacker"})]}),(0,s.jsx)("nav",{className:"hidden md:flex space-x-8",children:r.map(e=>(0,s.jsxs)("a",{href:e.href,className:"text-github-text hover:text-white transition-colors group relative",children:[(0,s.jsx)("span",{children:e.name}),(0,s.jsx)("span",{className:"absolute -bottom-1 left-0 w-0 h-0.5 bg-neon-green transition-all group-hover:w-full"})]},e.name))}),(0,s.jsx)("button",{onClick:()=>n(!i),className:"md:hidden text-gray-300 hover:text-white","aria-label":"Toggle menu",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",className:"w-6 h-6",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:i?"M6 18L18 6M6 6l12 12":"M4 6h16M4 12h16M4 18h16"})})})]})}),(0,s.jsx)(l.P.div,{initial:!1,animate:{height:i?"auto":0,opacity:+!!i},transition:{duration:.3},className:"md:hidden overflow-hidden",children:(0,s.jsx)("div",{className:"px-4 py-2 space-y-1 bg-github-light border-t border-github-border",children:r.map(e=>(0,s.jsx)("a",{href:e.href,className:"block py-2 px-4 text-github-text hover:bg-github-dark hover:text-white rounded-md",onClick:()=>n(!1),children:e.name},e.name))})})]})};var m=i(75684),x=i(72894);let p=()=>(0,s.jsx)(l.P.footer,{initial:{opacity:0},whileInView:{opacity:1},transition:{duration:.5},className:"bg-github-darker border-t border-github-border",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("div",{className:"w-10 h-10 rounded-full bg-gradient-to-br from-neon-green to-neon-blue flex items-center justify-center",children:(0,s.jsx)("span",{className:"font-mono font-bold text-white",children:"GH"})}),(0,s.jsx)("span",{className:"font-bold text-xl text-white",children:"GreenHacker"})]}),(0,s.jsx)("p",{className:"text-sm text-github-text mt-4 max-w-sm",children:"Passionate developer and open-source contributor currently working on a photo-sharing platform with face recognition."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-white font-medium mb-4",children:"Quick Links"}),(0,s.jsx)("ul",{className:"space-y-2",children:["About","Projects","Skills","Experience","Contact"].map(e=>(0,s.jsx)("li",{children:(0,s.jsx)("a",{href:"#".concat(e.toLowerCase()),className:"text-github-text hover:text-white transition-colors hover:underline",children:e})},e))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-white font-medium mb-4",children:"Connect With Me"}),(0,s.jsxs)("div",{className:"flex space-x-4",children:[(0,s.jsx)("a",{href:"https://instagram.com",target:"_blank",rel:"noreferrer",className:"bg-github-light p-2 rounded-full hover:bg-neon-green/20 transition-colors",children:(0,s.jsx)(m.A,{className:"w-5 h-5 text-github-text hover:text-white"})}),(0,s.jsx)("a",{href:"https://linkedin.com",target:"_blank",rel:"noreferrer",className:"bg-github-light p-2 rounded-full hover:bg-neon-blue/20 transition-colors",children:(0,s.jsx)(x.A,{className:"w-5 h-5 text-github-text hover:text-white"})})]}),(0,s.jsxs)("div",{className:"mt-4",children:[(0,s.jsx)("p",{className:"text-sm text-github-text",children:"harsh_hirawat"}),(0,s.jsx)("p",{className:"text-sm text-github-text mt-1",children:"Pune, Maharashtra"})]})]})]}),(0,s.jsx)("div",{className:"mt-8 pt-8 border-t border-github-border text-center",children:(0,s.jsxs)("p",{className:"text-sm text-github-text",children:["\xa9 ",new Date().getFullYear()," GreenHacker. All rights reserved."]})})]})});var g=i(36203);let u=()=>(0,s.jsx)("div",{className:"absolute inset-0 bg-github-darker z-0 opacity-80",children:(0,s.jsxs)("div",{className:"absolute inset-0 opacity-30",children:[(0,s.jsx)("div",{className:"absolute top-0 -left-4 w-72 h-72 bg-neon-purple rounded-full mix-blend-screen filter blur-xl opacity-70 animate-float"}),(0,s.jsx)("div",{className:"absolute top-8 -right-4 w-72 h-72 bg-neon-green rounded-full mix-blend-screen filter blur-xl opacity-70 animate-float",style:{animationDelay:"2s"}}),(0,s.jsx)("div",{className:"absolute -bottom-8 left-20 w-72 h-72 bg-neon-blue rounded-full mix-blend-screen filter blur-xl opacity-70 animate-float",style:{animationDelay:"4s"}})]})}),b=(0,i(55028).default)(()=>Promise.all([i.e(367),i.e(311)]).then(i.bind(i,311)).then(e=>({default:e.Canvas})),{loadableGenerated:{webpack:()=>[311]},ssr:!1}),f=e=>{let{mounted:t}=e;return(0,s.jsx)("div",{className:"absolute inset-0 z-0",children:(0,s.jsx)(g.tH,{fallback:(0,s.jsx)(u,{}),onError:(e,t)=>{console.error("3D Background Error:",e,t)},children:(0,s.jsx)(a.Suspense,{fallback:(0,s.jsx)(u,{}),children:t&&b&&(0,s.jsx)(b,{camera:{position:[0,0,6],fov:50},dpr:[1,2],style:{background:"transparent"},gl:{antialias:!0,alpha:!0,powerPreference:"high-performance",preserveDrawingBuffer:!1,failIfMajorPerformanceCaveat:!1},onCreated:e=>{let{gl:t}=e;t.setClearColor(0,0),console.log("3D Canvas initialized successfully")},onError:e=>{console.error("Canvas error:",e)}})})})})},j=()=>(0,s.jsxs)("div",{className:"max-w-3xl",children:[(0,s.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"overflow-hidden",children:(0,s.jsxs)("h2",{className:"text-neon-green text-lg md:text-xl font-mono mb-2 flex items-center",children:[(0,s.jsx)("span",{className:"wave-emoji mr-2 inline-block",children:"\uD83D\uDC4B"})," Hello, I'm"]})}),(0,s.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.2},children:(0,s.jsxs)("h1",{className:"text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-3 relative",children:["Green Hacker",(0,s.jsx)("span",{className:"absolute -bottom-2 left-0 h-1 bg-neon-green w-0 animate-expand"})]})}),(0,s.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.4},children:(0,s.jsxs)("h2",{className:"text-xl md:text-2xl text-github-text font-medium mb-6 flex flex-wrap gap-2 md:gap-4",children:[(0,s.jsxs)("span",{className:"flex items-center",children:[(0,s.jsx)("span",{className:"bg-neon-green w-2 h-2 rounded-full mr-2"}),"Full Stack Developer"]}),(0,s.jsxs)("span",{className:"flex items-center",children:[(0,s.jsx)("span",{className:"bg-neon-purple w-2 h-2 rounded-full mr-2"}),"ML Expert"]}),(0,s.jsxs)("span",{className:"flex items-center",children:[(0,s.jsx)("span",{className:"bg-neon-blue w-2 h-2 rounded-full mr-2"}),"OSS Contributor"]})]})})]}),y=()=>(0,s.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.6},className:"mt-8",children:[(0,s.jsx)("p",{className:"text-lg text-github-text max-w-2xl mb-8 leading-relaxed",children:"I'm currently working on a photo-sharing platform with face recognition. Passionate about open-source and applying Machine Learning to solve real-world problems."}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,s.jsx)(l.P.a,{href:"#projects",className:"px-6 py-3 bg-neon-green text-black font-medium rounded-md hover:bg-neon-green/90 transition-colors",whileHover:{scale:1.05},whileTap:{scale:.95},children:"View Projects"}),(0,s.jsx)(l.P.a,{href:"#contact",className:"px-6 py-3 bg-transparent border border-neon-green text-neon-green font-medium rounded-md hover:bg-neon-green/10 transition-colors",whileHover:{scale:1.05},whileTap:{scale:.95},children:"Contact Me"}),(0,s.jsx)(l.P.a,{href:"#resume",className:"px-6 py-3 bg-transparent border border-neon-purple text-neon-purple font-medium rounded-md hover:bg-neon-purple/10 transition-colors",whileHover:{scale:1.05},whileTap:{scale:.95},children:"View Resume"})]})]}),v=["Building cool stuff with React & ML","Creating interactive 3D web experiences","Developing with React Three Fiber","Exploring AI and machine learning","Crafting beautiful UI animations"],w=()=>{let[e,t]=(0,a.useState)(""),[i,n]=(0,a.useState)(!1),[r,o]=(0,a.useState)(0),[c,d]=(0,a.useState)(100),[h,m]=(0,a.useState)(!0);return(0,a.useEffect)(()=>{let s,a=v[r];return i||e!==a?i&&""===e?(n(!1),o((r+1)%v.length),d(100),m(!1)):(m(!1),s=setTimeout(()=>{t(i?a.substring(0,e.length-1):a.substring(0,e.length+1))},c)):s=setTimeout(()=>{m(!0),n(!0),d(50)},2e3),()=>clearTimeout(s)},[e,i,r,c]),(0,s.jsx)(l.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.8,duration:.5},className:"mt-12",children:(0,s.jsxs)("p",{className:"text-github-text text-lg flex items-center",children:[(0,s.jsx)("span",{className:"mr-2",children:"Currently:"}),(0,s.jsxs)("span",{className:"text-neon-green font-mono relative",children:[e,(0,s.jsx)("span",{className:"absolute inset-y-0 right-[-0.7ch] w-[0.5ch] bg-neon-green ".concat(h?"animate-cursor-blink":"")})]})]})})},N=()=>(0,s.jsx)(l.P.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.8,repeat:1/0,repeatType:"reverse",repeatDelay:1},className:"absolute bottom-10 left-1/2 transform -translate-x-1/2 z-10",children:(0,s.jsxs)("a",{href:"#about",className:"flex flex-col items-center text-github-text hover:text-white transition-colors",children:[(0,s.jsx)("span",{className:"mb-2 text-sm",children:"Scroll Down"}),(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"animate-bounce",children:[(0,s.jsx)("path",{d:"M12 5v14"}),(0,s.jsx)("path",{d:"m19 12-7 7-7-7"})]})]})}),k=()=>{let[e,t]=(0,a.useState)(!1),i=(0,a.useRef)(null);return(0,a.useEffect)(()=>{let e=setTimeout(()=>{t(!0)},100),s=()=>{if(i.current){let e=window.innerHeight;i.current.style.height="".concat(e,"px")}};return s(),window.addEventListener("resize",s),()=>{clearTimeout(e),window.removeEventListener("resize",s)}},[]),(0,s.jsxs)("section",{id:"home",ref:i,className:"relative flex items-center overflow-hidden",children:[(0,s.jsx)(u,{}),e&&(0,s.jsx)(f,{mounted:e}),(0,s.jsxs)("div",{className:"section-container relative z-10",children:[(0,s.jsx)(j,{}),(0,s.jsx)(y,{}),(0,s.jsx)(w,{})]}),(0,s.jsx)(N,{}),(0,s.jsx)("style",{dangerouslySetInnerHTML:{__html:"\n        @keyframes expand {\n          to { width: 100%; }\n        }\n\n        .animate-expand {\n          animation: expand 1.5s ease-out forwards;\n          animation-delay: 0.8s;\n        }\n\n        .wave-emoji {\n          animation: wave 2.5s infinite;\n          transform-origin: 70% 70%;\n          display: inline-block;\n        }\n\n        @keyframes wave {\n          0% { transform: rotate(0deg); }\n          10% { transform: rotate(14deg); }\n          20% { transform: rotate(-8deg); }\n          30% { transform: rotate(14deg); }\n          40% { transform: rotate(-4deg); }\n          50% { transform: rotate(10deg); }\n          60% { transform: rotate(0deg); }\n          100% { transform: rotate(0deg); }\n        }\n\n        .typewriter {\n          overflow: hidden;\n          border-right: .15em solid #3fb950;\n          white-space: nowrap;\n          margin: 0 auto;\n          letter-spacing: .15em;\n          animation: typing 3.5s steps(40, end), blink-caret .75s step-end infinite;\n        }\n\n        @keyframes typing {\n          from { width: 0 }\n          to { width: 100% }\n        }\n\n        @keyframes blink-caret {\n          from, to { border-color: transparent }\n          50% { border-color: #3fb950 }\n        }\n\n        @keyframes float {\n          0% { transform: translateY(0px); }\n          50% { transform: translateY(-20px); }\n          100% { transform: translateY(0px); }\n        }\n\n        .animate-float {\n          animation: float 6s ease-in-out infinite;\n        }\n      "}})]})},S=()=>{let e={hidden:{opacity:0,y:20},visible:{opacity:1,y:0}};return(0,s.jsx)("section",{id:"about",className:"bg-github-light py-20",children:(0,s.jsxs)(l.P.div,{variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.2}}},initial:"hidden",whileInView:"visible",viewport:{once:!0,amount:.2},className:"section-container",children:[(0,s.jsx)(l.P.h2,{variants:e,className:"section-title",children:"About Me"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[(0,s.jsxs)(l.P.div,{variants:e,className:"md:col-span-2 space-y-4",children:[(0,s.jsx)("p",{className:"text-lg",children:"\uD83D\uDE80 I'm currently working on a photo-sharing platform with face recognition."}),(0,s.jsx)("p",{className:"text-lg",children:"\uD83D\uDC50 Open to contributing to the open-source community."}),(0,s.jsx)("p",{className:"text-lg",children:"\uD83E\uDDE0 Learning Machine Learning for face detection."}),(0,s.jsx)("p",{className:"text-lg",children:"\uD83D\uDCBB Passionate developer and open-source contributor."}),(0,s.jsx)("p",{className:"text-lg",children:"⚡ Fun fact: I can spend hours debugging code but still forget where I kept my phone! \uD83D\uDE04"}),(0,s.jsxs)("div",{className:"pt-4",children:[(0,s.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"Call to Action:"}),(0,s.jsx)("p",{className:"text-lg",children:"Feel free to reach out if you'd like to collaborate, discuss tech, or share some awesome ideas!"})]})]}),(0,s.jsx)(l.P.div,{variants:e,className:"md:col-span-1",children:(0,s.jsxs)("div",{className:"bg-github-dark border border-github-border rounded-2xl overflow-hidden card-hover",children:[(0,s.jsxs)("div",{className:"aspect-square w-full relative overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-neon-green/20 to-neon-purple/20 z-10"}),(0,s.jsx)("img",{src:"https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=MnwxfDB8MXxyYW5kb218MHx8dGVjaHx8fHx8fDE2MjM2MzYyODE&ixlib=rb-1.2.1&q=80&utm_campaign=api-credit&utm_medium=referral&utm_source=unsplash_source&w=1080",alt:"Code on screen",className:"w-full h-full object-cover object-center"}),(0,s.jsx)("div",{className:"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-github-dark to-transparent h-1/3 z-20"})]}),(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsx)("h3",{className:"text-xl font-bold mb-2",children:"Socials:"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("a",{href:"https://instagram.com",className:"flex items-center space-x-2 text-github-text hover:text-neon-pink transition-colors",target:"_blank",rel:"noopener noreferrer",children:[(0,s.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{d:"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"})}),(0,s.jsx)("span",{children:"Instagram"})]}),(0,s.jsxs)("a",{href:"https://linkedin.com",className:"flex items-center space-x-2 text-github-text hover:text-neon-blue transition-colors",target:"_blank",rel:"noopener noreferrer",children:[(0,s.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{d:"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"})}),(0,s.jsx)("span",{children:"LinkedIn"})]}),(0,s.jsxs)("a",{href:"mailto:<EMAIL>",className:"flex items-center space-x-2 text-github-text hover:text-white transition-colors",children:[(0,s.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})}),(0,s.jsx)("span",{children:"Email"})]})]})]})]})})]})]})})};n.os.registerPlugin(r.u,o.I);let A=()=>{document.querySelectorAll('a[href^="#"]').forEach(e=>{e.addEventListener("click",function(e){e.preventDefault();let t=document.querySelector(this.getAttribute("href")||"");t&&n.os.to(window,{duration:1,scrollTo:{y:t,offsetY:80},ease:"power3.inOut"})})})},D=()=>{n.os.utils.toArray(".section-title").forEach(e=>{n.os.fromTo(e,{y:50,opacity:0},{y:0,opacity:1,duration:.6,ease:"power3.out",scrollTrigger:{trigger:e,start:"top bottom-=100",toggleActions:"play none none none"}})}),n.os.utils.toArray(".skills-grid").forEach(e=>{let t=n.os.utils.toArray(".skill-item",e);n.os.fromTo(t,{y:30,opacity:0},{y:0,opacity:1,stagger:.05,duration:.5,ease:"power3.out",scrollTrigger:{trigger:e,start:"top bottom-=50",toggleActions:"play none none none"}})}),n.os.utils.toArray(".project-card").forEach(e=>{n.os.fromTo(e,{y:40,opacity:0},{y:0,opacity:1,duration:.6,ease:"power2.out",scrollTrigger:{trigger:e,start:"top bottom-=50",toggleActions:"play none none none"}})})},C=()=>{let e=document.querySelectorAll(".github-cell");n.os.fromTo(e,{opacity:0,scale:.8},{opacity:1,scale:1,stagger:{grid:[7,52],from:"start",amount:1.5},ease:"power2.out",scrollTrigger:{trigger:".github-graph",start:"top bottom-=100"}})},T=e=>{let t=n.os.timeline({repeat:-1,repeatDelay:5});return t.to(e,{textShadow:"0 0 10px rgba(63, 185, 80, 0.8), 0 0 20px rgba(63, 185, 80, 0.5)",duration:.1}).to(e,{textShadow:"none",duration:.1}).to(e,{textShadow:"0 0 10px rgba(63, 185, 80, 0.8), 0 0 20px rgba(63, 185, 80, 0.5)",duration:.1}).to(e,{textShadow:"none",duration:.1}).to(e,{textShadow:"0 0 10px rgba(63, 185, 80, 0.8), 0 0 20px rgba(63, 185, 80, 0.5)",duration:.1}),t},P=()=>{n.os.utils.toArray("section").forEach(e=>{let t=e.querySelector(".section-bg");t&&n.os.to(t,{y:"30%",ease:"none",scrollTrigger:{trigger:e,start:"top bottom",end:"bottom top",scrub:!0}})})},E=(e,t)=>{t?n.os.to(e,{y:-5,boxShadow:"0 10px 25px -5px rgba(0, 0, 0, 0.2)",duration:.3,ease:"power2.out"}):n.os.to(e,{y:0,boxShadow:"none",duration:.3,ease:"power2.in"})},I=JSON.parse('{"H":{"categories":[{"name":"Programming Languages","description":"Core programming languages I use for development","skills":[{"name":"C++","color":"bg-blue-600","level":85},{"name":"DART","color":"bg-blue-400","level":70},{"name":"JAVASCRIPT","color":"bg-yellow-500","level":95},{"name":"PYTHON","color":"bg-blue-500","level":90},{"name":"TYPESCRIPT","color":"bg-blue-700","level":92},{"name":"RUST","color":"bg-orange-700","level":60},{"name":"POWERSHELL","color":"bg-blue-300","level":75},{"name":"BASH SCRIPT","color":"bg-gray-700","level":80}]},{"name":"Frontend Development","description":"Technologies I use to build beautiful and interactive UIs","skills":[{"name":"HTML5","color":"bg-red-600","level":98},{"name":"CSS3","color":"bg-purple-600","level":95},{"name":"REACT","color":"bg-blue-400","level":92},{"name":"REACT NATIVE","color":"bg-blue-500","level":88},{"name":"ANGULAR","color":"bg-red-700","level":75},{"name":"VUE.JS","color":"bg-green-600","level":80},{"name":"BOOTSTRAP","color":"bg-purple-700","level":90},{"name":"TAILWINDCSS","color":"bg-teal-500","level":95},{"name":"NEXT","color":"bg-black","level":85},{"name":"IONIC","color":"bg-blue-600","level":70}]},{"name":"Backend Development","description":"Server-side technologies and frameworks","skills":[{"name":"NODE.JS","color":"bg-green-700","level":90},{"name":"EXPRESS.JS","color":"bg-gray-800","level":88},{"name":"DJANGO","color":"bg-green-900","level":82},{"name":"FLASK","color":"bg-gray-700","level":85},{"name":"FASTAPI","color":"bg-teal-700","level":80},{"name":"SPRING","color":"bg-green-600","level":65}]},{"name":"Cloud & Deployment","description":"Services and platforms I use for deployment","skills":[{"name":"AWS","color":"bg-yellow-600","level":82},{"name":"AZURE","color":"bg-blue-700","level":75},{"name":"FIREBASE","color":"bg-yellow-500","level":90},{"name":"GOOGLECLOUD","color":"bg-blue-500","level":78},{"name":"NETLIFY","color":"bg-teal-800","level":92},{"name":"RENDER","color":"bg-green-600","level":85},{"name":"VERCEL","color":"bg-black","level":95}]},{"name":"Databases","description":"Database technologies I work with","skills":[{"name":"MYSQL","color":"bg-blue-900","level":88},{"name":"SQLITE","color":"bg-blue-800","level":90},{"name":"MONGODB","color":"bg-green-700","level":92},{"name":"SUPABASE","color":"bg-green-600","level":85}]},{"name":"DevOps & Tools","description":"Development operations and tooling","skills":[{"name":"GITHUB ACTIONS","color":"bg-blue-600","level":85},{"name":"GIT","color":"bg-red-600","level":95},{"name":"DOCKER","color":"bg-blue-500","level":80},{"name":"POSTMAN","color":"bg-orange-500","level":92},{"name":"KUBERNETES","color":"bg-blue-700","level":70},{"name":"GITHUB","color":"bg-gray-800","level":96}]},{"name":"Data Science & ML","description":"Libraries and tools for data analysis and machine learning","skills":[{"name":"MATPLOTLIB","color":"bg-gray-700","level":85},{"name":"NUMPY","color":"bg-blue-800","level":90},{"name":"PANDAS","color":"bg-purple-800","level":92},{"name":"TENSORFLOW","color":"bg-orange-600","level":85},{"name":"PYTORCH","color":"bg-red-700","level":80}]},{"name":"UI/UX & Design","description":"Design tools and technologies","skills":[{"name":"FIGMA","color":"bg-red-600","level":85},{"name":"CANVA","color":"bg-teal-500","level":90},{"name":"BLENDER","color":"bg-orange-600","level":75},{"name":"ADOBE CREATIVE CLOUD","color":"bg-red-800","level":80},{"name":"ADOBE PHOTOSHOP","color":"bg-blue-900","level":85}]}],"topSkills":[{"name":"JAVASCRIPT","level":95},{"name":"REACT","level":92},{"name":"NODE.JS","level":90},{"name":"PYTHON","level":90},{"name":"TYPESCRIPT","level":92},{"name":"MONGODB","level":92}]},"d":[{"title":"Portfolio","description":"Personal portfolio website built with HTML and showcasing my projects and skills.","tags":["HTML","CSS","JavaScript"],"category":"web","imageUrl":"https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?crop=entropy&cs=tinysrgb&fit=crop&fm=jpg&h=800&ixid=MnwxfDB8MXxyYW5kb218MHx8dGVjaHx8fHx8fDE2MjM2MzYyODE&ixlib=rb-1.2.1&q=80&utm_campaign=api-credit&utm_medium=referral&utm_source=unsplash_source&w=1200"},{"title":"SNW","description":"A CSS-based interactive web application with modern design principles.","tags":["CSS","React","Tailwind"],"category":"web","imageUrl":"https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?crop=entropy&cs=tinysrgb&fit=crop&fm=jpg&h=800&ixid=MnwxfDB8MXxyYW5kb218MHx8dGVjaHx8fHx8fDE2MjM2MzYyODE&ixlib=rb-1.2.1&q=80&utm_campaign=api-credit&utm_medium=referral&utm_source=unsplash_source&w=1200"},{"title":"Nirmaan","description":"A CSS framework for creating responsive and accessible web interfaces.","tags":["CSS","JavaScript","Design"],"category":"design","imageUrl":"https://images.unsplash.com/photo-1461749280684-dccba630e2f6?crop=entropy&cs=tinysrgb&fit=crop&fm=jpg&h=800&ixid=MnwxfDB8MXxyYW5kb218MHx8cHJvZ3JhbW1pbmd8fHx8fHwxNjIzNjM2MzU4&ixlib=rb-1.2.1&q=80&utm_campaign=api-credit&utm_medium=referral&utm_source=unsplash_source&w=1200"},{"title":"Storage-NextJs","description":"A NextJS-based storage solution with TypeScript integration.","tags":["TypeScript","Next.js","Cloud"],"category":"app","imageUrl":"https://images.unsplash.com/photo-1498050108023-c5249f4df085?crop=entropy&cs=tinysrgb&fit=crop&fm=jpg&h=800&ixid=MnwxfDB8MXxyYW5kb218MHx8Y29kZXx8fHx8fDE2MjM2MzYzNzg&ixlib=rb-1.2.1&q=80&utm_campaign=api-credit&utm_medium=referral&utm_source=unsplash_source&w=1200"}]}'),R=()=>I.H,M=()=>I.d,L=e=>{let{displayStyle:t,toggleDisplayStyle:i}=e;return(0,s.jsx)("div",{className:"flex justify-end mb-6",children:(0,s.jsx)("button",{onClick:i,className:"text-sm bg-github-light/30 px-4 py-2 rounded-md text-neon-green hover:bg-github-light/50 transition-colors",children:(()=>{switch(t){case"tabs":return"Switch to Keyboard View";case"keyboard":return"Switch to Tabs View";default:return"Switch View"}})()})})};var z=i(18880),O=i(59434);let H=z.bL,_=a.forwardRef((e,t)=>{let{className:i,...a}=e;return(0,s.jsx)(z.B8,{ref:t,className:(0,O.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",i),...a})});_.displayName=z.B8.displayName;let B=a.forwardRef((e,t)=>{let{className:i,...a}=e;return(0,s.jsx)(z.l9,{ref:t,className:(0,O.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",i),...a})});B.displayName=z.l9.displayName;let F=a.forwardRef((e,t)=>{let{className:i,...a}=e;return(0,s.jsx)(z.UC,{ref:t,className:(0,O.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",i),...a})});F.displayName=z.UC.displayName;var W=i(47937);let V=e=>{let{name:t,color:i,...a}=e,n=W[({"C++":"Code2",DART:"Code",JAVASCRIPT:"Code",PYTHON:"FileCode",TYPESCRIPT:"Code",RUST:"FileCode",POWERSHELL:"Terminal","BASH SCRIPT":"Terminal",HTML5:"Code",CSS3:"Code",REACT:"Code","REACT NATIVE":"Code",ANGULAR:"Code","VUE.JS":"Code",BOOTSTRAP:"LayoutDashboard",TAILWINDCSS:"Code",NEXT:"Code",IONIC:"Code","NODE.JS":"Server","EXPRESS.JS":"Server",DJANGO:"Server",FLASK:"Server",FASTAPI:"Server",SPRING:"Server",AWS:"Cloud",AZURE:"Cloud",FIREBASE:"Database",GOOGLECLOUD:"Cloud",NETLIFY:"Cloud",RENDER:"Cloud",VERCEL:"Cloud",MYSQL:"Database",SQLITE:"Database",MONGODB:"Database",SUPABASE:"Database","GITHUB ACTIONS":"Github",GIT:"Github",DOCKER:"Code",POSTMAN:"Code",KUBERNETES:"Code",GITHUB:"Github",MATPLOTLIB:"ChartBar",NUMPY:"Table",PANDAS:"Table",TENSORFLOW:"Code",PYTORCH:"Code",FIGMA:"Code",CANVA:"Image",BLENDER:"Code","ADOBE CREATIVE CLOUD":"Image","ADOBE PHOTOSHOP":"Image"})[t]||"Code"];return(0,s.jsx)("div",{className:"skill-icon flex items-center justify-center p-2 rounded-md bg-github-darker",...a,children:(0,s.jsx)(n,{size:24,color:i||"#c9d1d9",className:"skill-icon-svg"})})},G=e=>{let{skill:t,index:i,isHovered:a,onHover:n}=e,r="skill-".concat(t.name.replace(/[^a-zA-Z0-9]/g,"-").toLowerCase(),"-").concat(i);return(0,s.jsxs)(l.P.div,{id:r,initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.05*i},viewport:{once:!0},className:"bg-github-light rounded-lg p-4 border border-github-border skill-item transition-all duration-300",onMouseEnter:e=>n(e.currentTarget,t.name,!0),onMouseLeave:e=>n(e.currentTarget,t.name,!1),children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,s.jsx)(V,{name:t.name,color:a?"#3fb950":void 0}),(0,s.jsxs)("div",{className:"flex justify-between items-center w-full",children:[(0,s.jsx)("span",{className:"text-white font-medium",children:t.name}),(0,s.jsxs)("span",{className:"text-sm text-neon-green",children:[t.level,"%"]})]})]}),(0,s.jsx)("div",{className:"w-full bg-github-dark rounded-full h-2.5",children:(0,s.jsx)(l.P.div,{initial:{width:0},whileInView:{width:"".concat(t.level,"%")},transition:{duration:1,ease:"easeOut"},viewport:{once:!0},className:"h-2.5 rounded-full ".concat(t.color)})})]})},U=e=>{let{category:t,hoveredSkill:i,onSkillHover:a}=e;return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"bg-github-light/20 rounded-lg p-4 mb-6",children:[(0,s.jsx)("h3",{className:"text-xl text-white font-medium mb-2",children:t.name}),(0,s.jsx)("p",{className:"text-github-text",children:t.description})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 skills-grid",children:t.skills.map((e,t)=>(0,s.jsx)(G,{skill:e,index:t,isHovered:i===e.name,onHover:a},"".concat(e.name,"-").concat(t)))})]})},J=e=>{let{skills:t,onSkillHover:i}=e;return(0,s.jsxs)("div",{className:"mt-12",children:[(0,s.jsx)("h3",{className:"text-xl text-white font-bold mb-4",children:"Top Skills at a Glance"}),(0,s.jsx)("div",{className:"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4",children:t.map((e,t)=>{let a="top-skill-".concat(e.name.replace(/[^a-zA-Z0-9]/g,"-").toLowerCase(),"-").concat(t);return(0,s.jsxs)(l.P.div,{id:a,initial:{opacity:0,scale:.9},whileInView:{opacity:1,scale:1},transition:{duration:.5,delay:.1*t},viewport:{once:!0},className:"bg-github-light/30 p-3 rounded-lg text-center flex flex-col items-center transition-all duration-300 hover:bg-github-light/50",onMouseEnter:t=>i(t.currentTarget,e.name,!0),onMouseLeave:t=>i(t.currentTarget,e.name,!1),children:[(0,s.jsx)(V,{name:e.name,color:"#3fb950"}),(0,s.jsx)("span",{className:"text-white mt-2",children:e.name}),(0,s.jsxs)("span",{className:"text-neon-green text-sm",children:[e.level,"%"]})]},a)})})]})},Y=e=>{let{categories:t,topSkills:i,hoveredSkill:a,onSkillHover:n}=e;return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"mb-10",children:(0,s.jsx)(l.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.2},viewport:{once:!0},children:(0,s.jsxs)(H,{defaultValue:t[0].name,className:"w-full",children:[(0,s.jsx)(_,{className:"flex flex-wrap mb-6 bg-github-light/20",children:t.map(e=>(0,s.jsx)(B,{value:e.name,className:"data-[state=active]:bg-neon-green data-[state=active]:text-black",children:e.name},e.name))}),t.map(e=>(0,s.jsx)(F,{value:e.name,children:(0,s.jsx)(U,{category:e,hoveredSkill:a,onSkillHover:n})},e.name))]})})}),(0,s.jsx)(J,{skills:i,onSkillHover:n})]})};var q=i(40501),K=i(87481);let X=[{id:"js",name:"JavaScript",description:"A versatile scripting language that conforms to the ECMAScript specification.",logo:"javascript",color:"#F7DF1E",experience:5,proficiency:95,projects:["Portfolio Website","E-commerce Platform","Social Media Dashboard"],strengths:["ES6+","Async/Await","DOM Manipulation","Functional Programming"],category:"language"},{id:"ts",name:"TypeScript",description:"A strongly typed programming language that builds on JavaScript.",logo:"typescript",color:"#3178C6",experience:3,proficiency:90,projects:["Enterprise CRM","Financial Dashboard","API Gateway"],strengths:["Type Safety","Interface Design","Generics","Advanced Types"],category:"language"},{id:"react",name:"React",description:"A JavaScript library for building user interfaces.",logo:"react",color:"#61DAFB",experience:4,proficiency:92,projects:["E-commerce Platform","Social Media Dashboard","Portfolio Website"],strengths:["Hooks","Context API","Custom Hooks","Performance Optimization"],category:"frontend"},{id:"node",name:"Node.js",description:"A JavaScript runtime built on Chrome's V8 JavaScript engine.",logo:"nodejs",color:"#339933",experience:4,proficiency:88,projects:["REST API Services","Real-time Chat Application","Data Processing Pipeline"],strengths:["Express.js","API Development","Async Programming","Performance Tuning"],category:"backend"},{id:"python",name:"Python",description:"A high-level, interpreted programming language with dynamic semantics.",logo:"python",color:"#3776AB",experience:3,proficiency:85,projects:["Data Analysis Tool","Machine Learning Model","Automation Scripts"],strengths:["Data Processing","Machine Learning","Scripting","Web Scraping"],category:"language"},{id:"aws",name:"AWS",description:"A comprehensive cloud computing platform provided by Amazon.",logo:"aws",color:"#FF9900",experience:3,proficiency:80,projects:["Serverless Application","Cloud Migration","Scalable Web Services"],strengths:["Lambda","S3","EC2","CloudFormation","DynamoDB"],category:"devops"},{id:"docker",name:"Docker",description:"A platform for developing, shipping, and running applications in containers.",logo:"docker",color:"#2496ED",experience:3,proficiency:85,projects:["Microservices Architecture","CI/CD Pipeline","Development Environment"],strengths:["Containerization","Docker Compose","Multi-stage Builds","Optimization"],category:"devops"},{id:"mongodb",name:"MongoDB",description:"A cross-platform document-oriented database program.",logo:"mongodb",color:"#47A248",experience:3,proficiency:82,projects:["E-commerce Platform","Content Management System","Analytics Dashboard"],strengths:["Schema Design","Aggregation Pipeline","Indexing","Performance Tuning"],category:"database"},{id:"postgres",name:"PostgreSQL",description:"A powerful, open source object-relational database system.",logo:"postgresql",color:"#336791",experience:4,proficiency:88,projects:["Financial System","Inventory Management","Data Warehouse"],strengths:["Complex Queries","Performance Tuning","Data Integrity","JSON Support"],category:"database"},{id:"graphql",name:"GraphQL",description:"A query language for APIs and a runtime for executing those queries.",logo:"graphql",color:"#E10098",experience:2,proficiency:78,projects:["API Gateway","Content Platform","Mobile App Backend"],strengths:["Schema Design","Resolvers","Type System","Query Optimization"],category:"backend"},{id:"vue",name:"Vue.js",description:"A progressive JavaScript framework for building user interfaces.",logo:"vuejs",color:"#4FC08D",experience:2,proficiency:75,projects:["Admin Dashboard","E-commerce Frontend","Interactive Documentation"],strengths:["Component System","Reactivity","Vue Router","Vuex"],category:"frontend"},{id:"tailwind",name:"Tailwind CSS",description:"A utility-first CSS framework for rapidly building custom designs.",logo:"tailwindcss",color:"#06B6D4",experience:3,proficiency:90,projects:["Portfolio Website","Marketing Site","Web Application UI"],strengths:["Rapid Prototyping","Responsive Design","Custom Theming","Dark Mode"],category:"frontend"},{id:"threejs",name:"Three.js",description:"A cross-browser JavaScript library used to create and display animated 3D computer graphics.",logo:"threejs",color:"#000000",experience:2,proficiency:75,projects:["3D Portfolio","Interactive Product Viewer","Data Visualization"],strengths:["WebGL","3D Modeling","Animation","Performance Optimization"],category:"frontend"},{id:"git",name:"Git",description:"A distributed version control system for tracking changes in source code.",logo:"git",color:"#F05032",experience:5,proficiency:92,projects:["All Projects","Open Source Contributions","Team Collaboration"],strengths:["Branching Strategy","Conflict Resolution","Git Flow","Advanced Commands"],category:"devops"},{id:"nextjs",name:"Next.js",description:"A React framework with hybrid static & server rendering, TypeScript support, and route pre-fetching.",logo:"nextjs",color:"#000000",experience:3,proficiency:85,projects:["E-commerce Platform","Corporate Website","Blog Platform"],strengths:["SSR/SSG","API Routes","Image Optimization","Incremental Static Regeneration"],category:"frontend"}],Q=X.reduce((e,t,i)=>(e[t.id]=i,e),{}),Z=e=>{let t=Q[e];return"number"==typeof t?X[t]:void 0},$={STANDARD:[1,.35,1],WIDE_1_25:[1.25,.35,1],WIDE_1_5:[1.5,.35,1],WIDE_1_75:[1.75,.35,1],WIDE_2:[2,.35,1],WIDE_2_25:[2.25,.35,1],WIDE_2_75:[2.75,.35,1],SPACE:[6.25,.35,1]},ee=[[{id:"escape",label:"ESC",physical:{size:$.STANDARD,position:[0,0,0]}},{id:"js",label:"JS",skillId:"js",physical:{size:$.STANDARD,position:[1.05,0,0]}},{id:"ts",label:"TS",skillId:"ts",physical:{size:$.STANDARD,position:[2.1,0,0]}},{id:"react",label:"React",skillId:"react",physical:{size:$.STANDARD,position:[3.15,0,0]}},{id:"node",label:"Node",skillId:"node",physical:{size:$.STANDARD,position:[4.2,0,0]}},{id:"python",label:"PY",skillId:"python",physical:{size:$.STANDARD,position:[5.25,0,0]}},{id:"aws",label:"AWS",skillId:"aws",physical:{size:$.STANDARD,position:[6.3,0,0]}},{id:"docker",label:"Docker",skillId:"docker",physical:{size:$.STANDARD,position:[7.35,0,0]}},{id:"mongodb",label:"MongoDB",skillId:"mongodb",physical:{size:$.STANDARD,position:[8.4,0,0]}},{id:"postgres",label:"PG",skillId:"postgres",physical:{size:$.STANDARD,position:[9.45,0,0]}},{id:"graphql",label:"GQL",skillId:"graphql",physical:{size:$.STANDARD,position:[10.5,0,0]}},{id:"vue",label:"Vue",skillId:"vue",physical:{size:$.STANDARD,position:[11.55,0,0]}},{id:"tailwind",label:"TW",skillId:"tailwind",physical:{size:$.STANDARD,position:[12.6,0,0]}},{id:"backspace",label:"⌫",physical:{size:$.WIDE_2,position:[14.15,0,0]}}],[{id:"tab",label:"Tab",physical:{size:$.WIDE_1_5,position:[.75,1.05,0]}},{id:"threejs",label:"Three",skillId:"threejs",physical:{size:$.STANDARD,position:[2.05,1.05,0]}},{id:"git",label:"Git",skillId:"git",physical:{size:$.STANDARD,position:[3.1,1.05,0]}},{id:"nextjs",label:"Next",skillId:"nextjs",physical:{size:$.STANDARD,position:[4.15,1.05,0]}},{id:"key-t",label:"T",physical:{size:$.STANDARD,position:[5.2,1.05,0]}},{id:"key-y",label:"Y",physical:{size:$.STANDARD,position:[6.25,1.05,0]}},{id:"key-u",label:"U",physical:{size:$.STANDARD,position:[7.3,1.05,0]}},{id:"key-i",label:"I",physical:{size:$.STANDARD,position:[8.35,1.05,0]}},{id:"key-o",label:"O",physical:{size:$.STANDARD,position:[9.4,1.05,0]}},{id:"key-p",label:"P",physical:{size:$.STANDARD,position:[10.45,1.05,0]}},{id:"key-bracket-left",label:"[",physical:{size:$.STANDARD,position:[11.5,1.05,0]}},{id:"key-bracket-right",label:"]",physical:{size:$.STANDARD,position:[12.55,1.05,0]}},{id:"key-backslash",label:"\\",physical:{size:$.WIDE_1_5,position:[13.85,1.05,0]}}],[{id:"caps-lock",label:"Caps",physical:{size:$.WIDE_1_75,position:[.875,2.1,0]}},{id:"key-a",label:"A",physical:{size:$.STANDARD,position:[2.25,2.1,0]}},{id:"key-s",label:"S",physical:{size:$.STANDARD,position:[3.3,2.1,0]}},{id:"key-d",label:"D",physical:{size:$.STANDARD,position:[4.35,2.1,0]}},{id:"key-f",label:"F",physical:{size:$.STANDARD,position:[5.4,2.1,0]}},{id:"key-g",label:"G",physical:{size:$.STANDARD,position:[6.45,2.1,0]}},{id:"key-h",label:"H",physical:{size:$.STANDARD,position:[7.5,2.1,0]}},{id:"key-j",label:"J",physical:{size:$.STANDARD,position:[8.55,2.1,0]}},{id:"key-k",label:"K",physical:{size:$.STANDARD,position:[9.6,2.1,0]}},{id:"key-l",label:"L",physical:{size:$.STANDARD,position:[10.65,2.1,0]}},{id:"key-semicolon",label:";",physical:{size:$.STANDARD,position:[11.7,2.1,0]}},{id:"key-quote",label:"'",physical:{size:$.STANDARD,position:[12.75,2.1,0]}},{id:"enter",label:"Enter",physical:{size:$.WIDE_2_25,position:[14,2.1,0]}}],[{id:"left-shift",label:"Shift",physical:{size:$.WIDE_2_25,position:[1.125,3.15,0]}},{id:"key-z",label:"Z",physical:{size:$.STANDARD,position:[2.75,3.15,0]}},{id:"key-x",label:"X",physical:{size:$.STANDARD,position:[3.8,3.15,0]}},{id:"key-c",label:"C",physical:{size:$.STANDARD,position:[4.85,3.15,0]}},{id:"key-v",label:"V",physical:{size:$.STANDARD,position:[5.9,3.15,0]}},{id:"key-b",label:"B",physical:{size:$.STANDARD,position:[6.95,3.15,0]}},{id:"key-n",label:"N",physical:{size:$.STANDARD,position:[8,3.15,0]}},{id:"key-m",label:"M",physical:{size:$.STANDARD,position:[9.05,3.15,0]}},{id:"key-comma",label:",",physical:{size:$.STANDARD,position:[10.1,3.15,0]}},{id:"key-period",label:".",physical:{size:$.STANDARD,position:[11.15,3.15,0]}},{id:"key-slash",label:"/",physical:{size:$.STANDARD,position:[12.2,3.15,0]}},{id:"right-shift",label:"Shift",physical:{size:$.WIDE_2_75,position:[14,3.15,0]}}],[{id:"left-ctrl",label:"Ctrl",physical:{size:$.WIDE_1_25,position:[.625,4.2,0]}},{id:"left-win",label:"Win",physical:{size:$.WIDE_1_25,position:[2,4.2,0]}},{id:"left-alt",label:"Alt",physical:{size:$.WIDE_1_25,position:[3.375,4.2,0]}},{id:"space",label:"Space",physical:{size:$.SPACE,position:[7.125,4.2,0],isSpecial:!0}},{id:"right-alt",label:"Alt",physical:{size:$.WIDE_1_25,position:[10.875,4.2,0]}},{id:"right-win",label:"Win",physical:{size:$.WIDE_1_25,position:[12.25,4.2,0]}},{id:"right-ctrl",label:"Ctrl",physical:{size:$.WIDE_1_25,position:[13.625,4.2,0]}}]],et=ee.flat(),ei=e=>et.find(t=>t.id===e),es=()=>(0,s.jsxs)("div",{className:"absolute inset-0 flex flex-col items-center justify-center bg-github-dark/80 backdrop-blur-sm z-10",children:[(0,s.jsx)("div",{className:"w-16 h-16 border-4 border-neon-green border-t-transparent rounded-full animate-spin mb-4"}),(0,s.jsx)("p",{className:"text-white text-lg",children:"Loading Keyboard..."})]}),ea=e=>{let{skill:t,isVisible:i,onClose:a}=e;return t?(0,s.jsxs)(l.P.div,{initial:{opacity:0,y:20,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-20,scale:.95},transition:{type:"spring",stiffness:300,damping:30,duration:.4},className:"absolute bottom-8 left-1/2 transform -translate-x-1/2 bg-github-dark/95 border border-github-border rounded-lg p-5 w-full max-w-md z-50 shadow-xl",style:{backdropFilter:"blur(12px)"},children:[(0,s.jsx)("button",{onClick:a,className:"absolute top-3 right-3 text-github-text hover:text-white transition-colors","aria-label":"Close skill details",children:(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,s.jsx)("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),(0,s.jsx)("line",{x1:"6",y1:"6",x2:"18",y2:"18"})]})}),(0,s.jsxs)("div",{className:"flex items-center gap-4 mb-4",children:[(0,s.jsx)("div",{className:"w-16 h-16 rounded-lg flex items-center justify-center text-white text-2xl font-bold shadow-md",style:{backgroundColor:t.color},children:t.logo}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h3",{className:"text-2xl font-bold text-white mb-1",children:t.name}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"text-github-text font-medium",children:(e=>e<1?"Beginner":e<2?"Intermediate":e<4?"Advanced":"Expert")(t.experience)}),(0,s.jsx)("span",{className:"text-github-text text-sm",children:"•"}),(0,s.jsxs)("span",{className:"text-github-text",children:[t.experience," years"]})]})]})]}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-1",children:[(0,s.jsx)("span",{className:"text-white font-medium",children:"Proficiency"}),(0,s.jsxs)("span",{className:"text-white font-bold",children:[t.proficiency,"%"]})]}),(0,s.jsx)("div",{className:"h-3 bg-github-light/30 rounded-full overflow-hidden",children:(0,s.jsx)(l.P.div,{initial:{width:0},animate:{width:"".concat(t.proficiency,"%")},transition:{duration:1,ease:"easeOut"},className:"h-full rounded-full",style:{backgroundColor:(e=>e<60?"#f97316":e<80?"#22c55e":"#3b82f6")(t.proficiency)}})})]}),(0,s.jsx)("p",{className:"text-github-text mb-4 leading-relaxed",children:t.description}),t.projects&&t.projects.length>0&&(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsxs)("h4",{className:"text-white font-medium mb-2 flex items-center",children:[(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-2",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:(0,s.jsx)("path",{d:"M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"})}),"Projects"]}),(0,s.jsx)("ul",{className:"space-y-1 text-github-text",children:t.projects.map((e,t)=>(0,s.jsxs)("li",{className:"flex items-start",children:[(0,s.jsx)("span",{className:"text-xs mr-2 mt-1",children:"•"}),e]},t))})]}),t.strengths&&t.strengths.length>0&&(0,s.jsxs)("div",{children:[(0,s.jsxs)("h4",{className:"text-white font-medium mb-2 flex items-center",children:[(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-2",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,s.jsx)("path",{d:"M12 2L2 7l10 5 10-5-10-5z"}),(0,s.jsx)("path",{d:"M2 17l10 5 10-5"}),(0,s.jsx)("path",{d:"M2 12l10 5 10-5"})]}),"Key Strengths"]}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:t.strengths.map((e,i)=>(0,s.jsx)(l.P.span,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{delay:.1*i},className:"px-3 py-1 rounded-full text-sm font-medium",style:{backgroundColor:"".concat(t.color,"22"),color:t.color,border:"1px solid ".concat(t.color,"44")},children:e},i))})]})]}):null},en=()=>{let[e,t]=(0,a.useState)(null),[i,n]=(0,a.useState)(!1),[r,o]=(0,a.useState)(!0),[c,d]=(0,a.useState)(null),h=(0,a.useRef)(null),m=e=>{try{let i=ei(e);if(i){let e=i.skillId;if(e){let i=Z(e);t(i),n(!0)}}}catch(e){console.error("Error selecting key:",e),d("Failed to select key.")}},x=()=>{n(!1),t(null)};return((0,a.useEffect)(()=>{let e=e=>{try{let t=e.key.toLowerCase();if("Escape"===e.key&&i)return void x();let s=ee.flat().find(e=>e.label.toLowerCase()===t||e.id.toLowerCase()===t);s&&m(s.id)}catch(e){console.error("Error processing key press:",e)}};return window.addEventListener("keydown",e),()=>{window.removeEventListener("keydown",e)}},[i]),c)?(0,s.jsx)("div",{className:"w-full h-[400px] flex items-center justify-center bg-github-dark/50 rounded-lg",children:(0,s.jsxs)("div",{className:"text-center p-6",children:[(0,s.jsxs)("p",{className:"text-red-400 mb-2",children:["Error: ",c]}),(0,s.jsx)("button",{onClick:()=>d(null),className:"px-4 py-2 bg-github-light/30 text-neon-green rounded-md hover:bg-github-light/50 transition-colors",children:"Retry"})]})}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(l.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.5},className:"my-4 w-full flex flex-col items-center justify-center",children:[e&&(0,s.jsxs)(l.P.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},className:"mb-8 text-center",children:[(0,s.jsx)("h3",{className:"text-2xl font-bold text-white mb-2",children:e.name}),(0,s.jsx)("p",{className:"text-github-text max-w-xl",children:e.description})]}),(0,s.jsxs)("div",{className:"w-full max-w-4xl mx-auto relative h-[600px]",children:[r&&(0,s.jsx)(es,{}),(0,s.jsx)(q.A,{scene:"https://prod.spline.design/bnffRvBtBHvfSiOW/scene.splinecode",onLoad:e=>{h.current=e,o(!1);try{ee.flatMap(e=>e.map(e=>e.id)).forEach(t=>{try{e.findObjectByName(t)&&e.addEventListener("mouseDown",e=>{e.target&&e.target.name===t&&m(t)})}catch(e){console.warn("Could not set up interaction for key: ".concat(t),e)}})}catch(e){console.error("Error setting up Spline interactions:",e),d("Failed to set up keyboard interactions."),(0,K.oR)({title:"Error",description:"Failed to load keyboard interactions. Please try refreshing the page.",variant:"destructive"})}},style:{width:"100%",height:"100%"}}),(0,s.jsx)("div",{className:"absolute inset-0 pointer-events-none",children:(0,s.jsx)("div",{className:"pointer-events-auto",children:(0,s.jsx)(ea,{skill:e,isVisible:i&&!!e,onClose:x})})})]})]}),(0,s.jsx)(l.P.div,{className:"text-center mt-2 text-white/70 font-medium",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.5},children:(0,s.jsx)("p",{children:"(hint: click on a key to explore skills)"})})]})},er=()=>{let[e,t]=(0,a.useState)(null),[i,n]=(0,a.useState)("tabs"),r=(0,a.useRef)(null),{categories:o,topSkills:c}=R();return(0,a.useEffect)(()=>{D()},[]),(0,s.jsx)("section",{id:"skills",className:"py-20 bg-github-dark",ref:r,children:(0,s.jsxs)("div",{className:"section-container relative",children:[(0,s.jsx)(l.P.h2,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5},viewport:{once:!0},className:"section-title",children:"Skills"}),(0,s.jsx)(L,{displayStyle:i,toggleDisplayStyle:()=>{n(e=>"tabs"===e?"keyboard":"tabs")}}),"tabs"===i?(0,s.jsx)(Y,{categories:o,topSkills:c,hoveredSkill:e,onSkillHover:(e,i,s)=>{t(s?i:null),E(e,s)}}):(0,s.jsx)(en,{})]})})},eo=e=>{let{filter:t,setFilter:i}=e;return(0,s.jsxs)(l.P.div,{initial:{opacity:0,y:10},whileInView:{opacity:1,y:0},transition:{duration:.3},viewport:{once:!0},className:"flex flex-wrap gap-3 mb-8 justify-center sm:justify-start",children:[(0,s.jsx)("button",{onClick:()=>i("all"),className:"px-4 py-2 rounded-md text-sm transition-colors ".concat("all"===t?"bg-neon-green text-black font-medium":"bg-github-dark text-github-text hover:bg-github-dark/80"),children:"All Projects"}),(0,s.jsx)("button",{onClick:()=>i("web"),className:"px-4 py-2 rounded-md text-sm transition-colors ".concat("web"===t?"bg-neon-green text-black font-medium":"bg-github-dark text-github-text hover:bg-github-dark/80"),children:"Web"}),(0,s.jsx)("button",{onClick:()=>i("app"),className:"px-4 py-2 rounded-md text-sm transition-colors ".concat("app"===t?"bg-neon-green text-black font-medium":"bg-github-dark text-github-text hover:bg-github-dark/80"),children:"Apps"}),(0,s.jsx)("button",{onClick:()=>i("design"),className:"px-4 py-2 rounded-md text-sm transition-colors ".concat("design"===t?"bg-neon-green text-black font-medium":"bg-github-dark text-github-text hover:bg-github-dark/80"),children:"Design"})]})};var el=i(8619),ec=i(37602),ed=i(58829);let eh=e=>{let{children:t,className:i="",depth:n=20,shadowColor:r="rgba(63, 185, 80, 0.4)"}=e,o=(0,a.useRef)(null),[c,d]=(0,a.useState)(!1),h=(0,el.d)(0),m=(0,el.d)(0),x={stiffness:150,damping:20},p=(0,ec.z)(h,x),g=(0,ec.z)(m,x),u=(0,ed.G)([p,g],e=>{let[t,i]=e,s="number"==typeof t?t:0,a="number"==typeof i?i:0;return .5*Math.sqrt(s*s+a*a)}),b=(0,ec.z)(0,x);return(0,a.useEffect)(()=>()=>{h.set(0),m.set(0),b.set(0)},[h,m,b]),(0,s.jsx)(l.P.div,{ref:o,className:"relative ".concat(i),onMouseMove:e=>{if(!o.current)return;let t=o.current.getBoundingClientRect(),i=t.left+t.width/2,s=t.top+t.height/2,a=e.clientX-i,n=-((e.clientY-s)/(t.height/2)*10),r=a/(t.width/2)*10;h.set(n),m.set(r)},onMouseEnter:()=>{d(!0),b.set(n)},onMouseLeave:()=>{d(!1),h.set(0),m.set(0),b.set(0)},style:{perspective:1e3,transformStyle:"preserve-3d"},whileTap:{scale:.98},children:(0,s.jsxs)(l.P.div,{style:{rotateX:p,rotateY:g,z:b,boxShadow:(0,ed.G)(u,e=>"0 ".concat(2*e,"px ").concat(4*e,"px ").concat(r)),transformStyle:"preserve-3d"},className:"h-full w-full transition-colors duration-300",children:[t,(0,s.jsx)(l.P.div,{className:"absolute inset-0 rounded-lg pointer-events-none border border-neon-green",style:{opacity:(0,ed.G)(b,[0,n],[0,.3])}})]})})},em=e=>{let{project:t,delay:i=0}=e,[n,r]=(0,a.useState)(!1);return(0,s.jsx)(l.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:i},viewport:{once:!0},children:(0,s.jsx)(eh,{className:"h-full",children:(0,s.jsxs)("div",{className:"bg-github-dark border border-github-border rounded-lg overflow-hidden h-full transition-all duration-300",onMouseEnter:()=>r(!0),onMouseLeave:()=>r(!1),children:[(0,s.jsx)("div",{className:"aspect-video w-full overflow-hidden",children:(0,s.jsx)(l.P.img,{src:t.imageUrl,alt:t.title,className:"w-full h-full object-cover object-center",animate:{scale:n?1.05:1},transition:{duration:.4}})}),(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"flex justify-between items-start",children:[(0,s.jsx)(l.P.h3,{className:"text-xl font-bold text-white",animate:{x:3*!!n},transition:{duration:.2},children:t.title}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:t.tags.map((e,t)=>(0,s.jsx)("span",{className:"tech-badge px-2 py-1 text-xs rounded ".concat("HTML"===e?"bg-tech-html/20 text-tech-html border border-tech-html/30":"CSS"===e?"bg-tech-css/20 text-tech-css border border-tech-css/30":"TypeScript"===e?"bg-tech-ts/20 text-tech-ts border border-tech-ts/30":"JavaScript"===e?"bg-yellow-500/20 text-yellow-500 border border-yellow-500/30":"React"===e?"bg-blue-400/20 text-blue-400 border border-blue-400/30":"Next.js"===e?"bg-black/40 text-white border border-white/30":"Tailwind"===e?"bg-teal-500/20 text-teal-500 border border-teal-500/30":"Design"===e?"bg-purple-500/20 text-purple-500 border border-purple-500/30":"Cloud"===e?"bg-blue-600/20 text-blue-600 border border-blue-600/30":"bg-gray-500/20 text-gray-500 border border-gray-500/30"),children:e},t))})]}),(0,s.jsx)("p",{className:"mt-4 text-github-text",children:t.description}),(0,s.jsxs)(l.P.div,{className:"mt-6 flex gap-3",animate:{y:10*!n,opacity:n?1:.8},transition:{duration:.3},children:[(0,s.jsx)("a",{href:"#",className:"px-4 py-2 bg-neon-green/20 text-neon-green rounded-md hover:bg-neon-green/30 transition-colors",children:"Demo"}),(0,s.jsx)("a",{href:"#",className:"px-4 py-2 bg-github-light text-github-text rounded-md hover:bg-github-light/80 transition-colors",children:"Source"})]})]})]})})})},ex=e=>{let{projects:t,filter:i}=e,a="all"===i?t:t.filter(e=>e.category===i);return(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:a.map((e,t)=>(0,s.jsx)(em,{project:e,delay:.1*t},e.title))})},ep=()=>(0,s.jsx)(l.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.5},viewport:{once:!0},className:"mt-10 text-center",children:(0,s.jsxs)("a",{href:"#contact",className:"inline-flex items-center px-6 py-3 bg-neon-green text-black font-medium rounded-md hover:bg-neon-green/90 transition-colors",children:[(0,s.jsx)("span",{children:"Interested in working together?"}),(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 ml-2",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,s.jsx)("path",{d:"M5 12h14"}),(0,s.jsx)("path",{d:"m12 5 7 7-7 7"})]})]})}),eg=()=>{let[e,t]=(0,a.useState)("all"),i=M();return(0,s.jsx)("section",{id:"projects",className:"py-20 bg-github-light",children:(0,s.jsxs)("div",{className:"section-container",children:[(0,s.jsx)(l.P.h2,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5},viewport:{once:!0},className:"section-title",children:"Projects"}),(0,s.jsx)(eo,{filter:e,setFilter:t}),(0,s.jsx)(ex,{projects:i,filter:e}),(0,s.jsx)(ep,{})]})})};var eu=i(88106);let eb=eu.bL,ef=eu.R6,ej=eu.Ke,ey=()=>{let[e,t]=(0,a.useState)("work"),[i,n]=(0,a.useState)(null),r=e=>{n(i===e?null:e)},o={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1}}},c={hidden:{opacity:0,y:20},visible:{opacity:1,y:0}};return(0,s.jsx)("section",{id:"experience",className:"py-20 bg-github-dark",children:(0,s.jsxs)("div",{className:"section-container",children:[(0,s.jsx)(l.P.h2,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5},viewport:{once:!0},className:"section-title",children:"Experience"}),(0,s.jsx)("div",{className:"flex justify-center mb-12",children:(0,s.jsxs)("div",{className:"inline-flex rounded-md shadow-sm p-1 bg-github-light",children:[(0,s.jsx)("button",{onClick:()=>t("work"),className:"px-4 py-2 text-sm font-medium rounded-md ".concat("work"===e?"bg-neon-green text-black":"text-github-text hover:text-white"),children:"Work Experience"}),(0,s.jsx)("button",{onClick:()=>t("education"),className:"px-4 py-2 text-sm font-medium rounded-md ".concat("education"===e?"bg-neon-purple text-black":"text-github-text hover:text-white"),children:"Education"}),(0,s.jsx)("button",{onClick:()=>t("certifications"),className:"px-4 py-2 text-sm font-medium rounded-md ".concat("certifications"===e?"bg-neon-blue text-black":"text-github-text hover:text-white"),children:"Certifications"}),(0,s.jsx)("button",{onClick:()=>t("achievements"),className:"px-4 py-2 text-sm font-medium rounded-md ".concat("achievements"===e?"bg-neon-green text-black":"text-github-text hover:text-white"),children:"Achievements"})]})}),(0,s.jsxs)("div",{children:["work"===e&&(0,s.jsxs)(l.P.div,{variants:o,initial:"hidden",animate:"visible",className:"space-y-4",children:[(0,s.jsxs)(l.P.h3,{variants:c,className:"text-xl font-bold text-white flex items-center",children:[(0,s.jsx)("span",{className:"inline-block w-3 h-3 bg-neon-green rounded-full mr-2"}),"Work Experience"]}),[{id:"exp1",title:"Field Associate",company:"Sachetparyant",period:"Sep 2021 - Jul 2023",location:"India",type:"Part-time",description:"Database Head & Field Associate at Sachetparyant's Project Shikshan. Responsible for managing multiple database systems and ensuring data integrity across projects.",skills:["Database Management","Team Leadership","Data Analysis"]},{id:"exp2",title:"Field Associate",company:"Sachetparyant",period:"Sep 2021 - Apr 2022",location:"India",type:"Part-time",description:"Database Head & Field Associate at Sachetparyant's Project Shikshan. Worked with stakeholders to identify data needs and implement appropriate solutions.",skills:["Team Management","Data Analysis","Stakeholder Communication"]},{id:"exp3",title:"Field Associate",company:"Sachetparyant",period:"Sep 2021 - Sep 2021",location:"India",type:"Internship",description:"Assisted in field operations and data collection activities. Participated in team meetings and contributed to project documentation.",skills:["Field Operations","Data Collection","Documentation"]},{id:"exp4",title:"Executive",company:"Sachetparyant",period:"Jul 2021 - Sep 2021",location:"India",type:"Internship",description:"Supported executive team in daily operations and special projects. Developed reporting templates and assisted with presentation materials.",skills:["Executive Support","Reporting","Office Administration"]}].map(e=>(0,s.jsx)(l.P.div,{variants:c,className:"bg-github-light rounded-lg border border-github-border overflow-hidden transition-all duration-300",children:(0,s.jsxs)(eb,{open:i===e.id,onOpenChange:()=>r(e.id),children:[(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-center",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold text-white text-lg",children:e.title}),(0,s.jsx)("p",{className:"text-neon-green",children:e.company})]}),(0,s.jsxs)("div",{className:"mt-2 sm:mt-0 text-right",children:[(0,s.jsx)("p",{className:"text-github-text text-sm",children:e.type}),(0,s.jsx)("p",{className:"mt-1 text-github-text text-sm",children:e.period})]})]}),(0,s.jsxs)(ef,{className:"mt-4 text-sm text-github-text hover:text-white transition-colors flex items-center",children:[i===e.id?"Show less":"Show more",(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"ml-2 transition-transform ".concat(i===e.id?"rotate-180":""),children:(0,s.jsx)("path",{d:"m6 9 6 6 6-6"})})]})]}),(0,s.jsx)(ej,{children:(0,s.jsxs)("div",{className:"px-6 pb-6 border-t border-github-border pt-4 mt-4",children:[e.description&&(0,s.jsx)("p",{className:"text-github-text",children:e.description}),e.skills&&(0,s.jsx)("div",{className:"mt-3 flex flex-wrap gap-2",children:e.skills.map((e,t)=>(0,s.jsx)("span",{className:"tech-badge bg-github-dark",children:e},t))})]})})]})},e.id))]}),"education"===e&&(0,s.jsxs)(l.P.div,{variants:o,initial:"hidden",animate:"visible",className:"space-y-4",children:[(0,s.jsxs)(l.P.h3,{variants:c,className:"text-xl font-bold text-white flex items-center",children:[(0,s.jsx)("span",{className:"inline-block w-3 h-3 bg-neon-purple rounded-full mr-2"}),"Education"]}),[{id:"edu1",school:"Newton School of Technology",degree:"Bachelor of Technology - BTech, Computer Science",period:"Aug 2024 - Jul 2028",description:"Focusing on advanced programming concepts, data structures, algorithms, and software engineering principles. Participating in coding competitions and tech community events.",skills:["Programming","Python","Data Structures","Algorithms"]},{id:"edu2",school:"Royal Senior Secondary School",degree:"Senior Secondary School, PCM",period:"Apr 2022 - May 2024",description:"Completed secondary education with focus on Physics, Chemistry, and Mathematics (PCM). Participated in science exhibitions and mathematical olympiads.",skills:["Mathematics","Physics","Chemistry","Problem Solving"]}].map(e=>(0,s.jsx)(l.P.div,{variants:c,className:"bg-github-light rounded-lg border border-github-border overflow-hidden",children:(0,s.jsxs)(eb,{open:i===e.id,onOpenChange:()=>r(e.id),children:[(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsx)("h4",{className:"font-semibold text-white text-lg",children:e.school}),(0,s.jsx)("p",{className:"text-neon-purple",children:e.degree}),(0,s.jsx)("p",{className:"mt-2 text-github-text text-sm",children:e.period}),(0,s.jsxs)(ef,{className:"mt-4 text-sm text-github-text hover:text-white transition-colors flex items-center",children:[i===e.id?"Show less":"Show more",(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"ml-2 transition-transform ".concat(i===e.id?"rotate-180":""),children:(0,s.jsx)("path",{d:"m6 9 6 6 6-6"})})]})]}),(0,s.jsx)(ej,{children:(0,s.jsxs)("div",{className:"px-6 pb-6 border-t border-github-border pt-4 mt-4",children:[e.description&&(0,s.jsx)("p",{className:"text-github-text",children:e.description}),e.skills&&(0,s.jsx)("div",{className:"mt-3 flex flex-wrap gap-2",children:e.skills.map((e,t)=>(0,s.jsx)("span",{className:"tech-badge bg-github-dark",children:e},t))})]})})]})},e.id))]}),"certifications"===e&&(0,s.jsxs)(l.P.div,{variants:o,initial:"hidden",animate:"visible",className:"space-y-4",children:[(0,s.jsxs)(l.P.h3,{variants:c,className:"text-xl font-bold text-white flex items-center",children:[(0,s.jsx)("span",{className:"inline-block w-3 h-3 bg-neon-blue rounded-full mr-2"}),"Certifications"]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[{id:"cert1",name:"AI For Everyone",issuer:"DeepLearning.AI",date:"Mar 2025",certId:"GQIFS41IFAYR"},{id:"cert2",name:"Generative AI for Everyone",issuer:"DeepLearning.AI",date:"Mar 2025",certId:"R2CGBN98KY1W"}].map(e=>(0,s.jsxs)(l.P.div,{variants:c,whileHover:{y:-5,boxShadow:"0 10px 25px -5px rgba(0, 0, 0, 0.1)"},className:"bg-github-light rounded-lg p-6 border border-github-border",children:[(0,s.jsx)("h4",{className:"font-semibold text-white text-lg",children:e.name}),(0,s.jsx)("p",{className:"text-neon-blue",children:e.issuer}),(0,s.jsxs)("div",{className:"mt-2 text-github-text text-sm flex justify-between",children:[(0,s.jsxs)("span",{children:["Issued ",e.date]}),(0,s.jsxs)("span",{className:"text-xs bg-github-dark px-2 py-1 rounded-full",children:["ID: ",e.certId]})]}),(0,s.jsx)("div",{className:"mt-4 flex",children:(0,s.jsx)("button",{className:"text-sm text-white px-3 py-1 border border-neon-blue/50 rounded hover:bg-neon-blue/10 transition-colors",children:"Show credential"})})]},e.id))})]}),"achievements"===e&&(0,s.jsxs)(l.P.div,{variants:o,initial:"hidden",animate:"visible",className:"space-y-4",children:[(0,s.jsxs)(l.P.h3,{variants:c,className:"text-xl font-bold text-white flex items-center",children:[(0,s.jsx)("span",{className:"inline-block w-3 h-3 bg-neon-green rounded-full mr-2"}),"Achievements"]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[{id:"ach1",title:"Postman API Fundamentals Student Expert",date:"Feb 2025",description:"Mastered API development and testing with Postman."},{id:"ach2",title:"Certified Machine Learning Specialist",date:"Jan 2025",description:"Completed a comprehensive course on machine learning."},{id:"ach3",title:"1st Place - Robo Soccer Competition",date:"Dec 2024",description:"Led a team to victory at the national Robo Soccer competition."},{id:"ach4",title:"Tekron 2025 Organizing Committee Member",date:"Nov 2024",description:"Contributed to organizing one of the largest technical events at my university."}].map(e=>(0,s.jsxs)(l.P.div,{variants:c,whileHover:{y:-5,boxShadow:"0 10px 25px -5px rgba(0, 0, 0, 0.1)"},className:"bg-github-light rounded-lg p-6 border border-github-border",children:[(0,s.jsxs)("div",{className:"flex justify-between items-start",children:[(0,s.jsx)("h4",{className:"font-semibold text-white text-lg",children:e.title}),(0,s.jsx)("span",{className:"bg-github-dark text-neon-green px-2 py-1 text-xs rounded-full",children:e.date})]}),(0,s.jsx)("p",{className:"mt-2 text-github-text",children:e.description})]},e.id))})]})]})]})})};var ev=i(83540),ew=i(3401),eN=i(96025),ek=i(16238),eS=i(94517),eA=i(83394),eD=i(54811),eC=i(8782),eT=i(34e3);let eP=()=>{let[e,t]=(0,a.useState)("contributions"),i=(0,a.useRef)(null);(0,a.useEffect)(()=>{i.current&&C()},[i.current]);let n={hidden:{opacity:0,y:20},visible:{opacity:1,y:0}},r={stars:47,commits:430,prs:28,issues:15,contributions:12},o=[{month:"May",contributions:42},{month:"Jun",contributions:38},{month:"Jul",contributions:56},{month:"Aug",contributions:72},{month:"Sep",contributions:48},{month:"Oct",contributions:62},{month:"Nov",contributions:54},{month:"Dec",contributions:38},{month:"Jan",contributions:45},{month:"Feb",contributions:67},{month:"Mar",contributions:52},{month:"Apr",contributions:49}],c=[{name:"JavaScript",value:38,color:"#f1e05a"},{name:"TypeScript",value:24,color:"#3178c6"},{name:"Python",value:18,color:"#3572A5"},{name:"HTML",value:10,color:"#e34c26"},{name:"CSS",value:10,color:"#563d7c"}];return(0,s.jsx)("section",{id:"stats",className:"py-20 bg-github-light",children:(0,s.jsxs)("div",{className:"section-container",children:[(0,s.jsx)(l.P.h2,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5},viewport:{once:!0},className:"section-title",children:"GitHub Stats"}),(0,s.jsx)("div",{className:"mb-10 flex justify-center",children:(0,s.jsxs)("div",{className:"flex rounded-lg overflow-hidden border border-github-border",children:[(0,s.jsx)("button",{onClick:()=>t("contributions"),className:"px-4 py-2 ".concat("contributions"===e?"bg-neon-green text-black":"bg-github-dark text-github-text"),children:"Contributions"}),(0,s.jsx)("button",{onClick:()=>t("languages"),className:"px-4 py-2 ".concat("languages"===e?"bg-neon-green text-black":"bg-github-dark text-github-text"),children:"Languages"}),(0,s.jsx)("button",{onClick:()=>t("repos"),className:"px-4 py-2 ".concat("repos"===e?"bg-neon-green text-black":"bg-github-dark text-github-text"),children:"Top Repos"})]})}),(0,s.jsxs)(l.P.div,{variants:{hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.2}}},initial:"hidden",whileInView:"visible",viewport:{once:!0,amount:.1},className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,s.jsxs)(l.P.div,{variants:n,className:"bg-github-dark p-6 rounded-xl border border-github-border",children:[(0,s.jsx)("h3",{className:"text-xl font-bold text-white mb-6",children:"Green Hacker's GitHub Stats"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)(l.P.div,{className:"p-4 bg-github-light/50 rounded-lg hover:bg-github-light transition-colors",whileHover:{y:-5},children:[(0,s.jsx)("p",{className:"text-sm text-github-text",children:"Total Stars Earned:"}),(0,s.jsx)(l.P.p,{className:"text-2xl font-bold text-white",initial:{opacity:0},whileInView:{opacity:1},transition:{duration:1},viewport:{once:!0},children:r.stars})]}),(0,s.jsxs)(l.P.div,{className:"p-4 bg-github-light/50 rounded-lg hover:bg-github-light transition-colors",whileHover:{y:-5},children:[(0,s.jsx)("p",{className:"text-sm text-github-text",children:"Total Commits (2025):"}),(0,s.jsx)(l.P.p,{className:"text-2xl font-bold text-white",initial:{opacity:0},whileInView:{opacity:1},transition:{duration:1},viewport:{once:!0},children:r.commits})]}),(0,s.jsxs)(l.P.div,{className:"p-4 bg-github-light/50 rounded-lg hover:bg-github-light transition-colors",whileHover:{y:-5},children:[(0,s.jsx)("p",{className:"text-sm text-github-text",children:"Total PRs:"}),(0,s.jsx)(l.P.p,{className:"text-2xl font-bold text-white",initial:{opacity:0},whileInView:{opacity:1},transition:{duration:1},viewport:{once:!0},children:r.prs})]}),(0,s.jsxs)(l.P.div,{className:"p-4 bg-github-light/50 rounded-lg hover:bg-github-light transition-colors",whileHover:{y:-5},children:[(0,s.jsx)("p",{className:"text-sm text-github-text",children:"Total Issues:"}),(0,s.jsx)(l.P.p,{className:"text-2xl font-bold text-white",initial:{opacity:0},whileInView:{opacity:1},transition:{duration:1},viewport:{once:!0},children:r.issues})]}),(0,s.jsxs)(l.P.div,{className:"p-4 bg-github-light/50 rounded-lg col-span-2 hover:bg-github-light transition-colors",whileHover:{y:-5},children:[(0,s.jsx)("p",{className:"text-sm text-github-text",children:"Contributed to (last year):"}),(0,s.jsxs)(l.P.p,{className:"text-2xl font-bold text-white",initial:{opacity:0},whileInView:{opacity:1},transition:{duration:1},viewport:{once:!0},children:[r.contributions," Open Source Projects"]})]})]}),(0,s.jsxs)("div",{className:"mt-8 grid grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{className:"p-5 flex flex-col items-center justify-center",children:[(0,s.jsx)(l.P.p,{className:"text-3xl font-bold text-white",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5},viewport:{once:!0},children:"623"}),(0,s.jsx)("p",{className:"text-sm text-github-text text-center mt-2",children:"This Year"}),(0,s.jsx)("p",{className:"text-xs text-github-text text-center mt-1",children:"Contributions"})]}),(0,s.jsxs)("div",{className:"p-5 flex flex-col items-center justify-center",children:[(0,s.jsx)(l.P.div,{className:"w-20 h-20 rounded-full bg-github-light flex items-center justify-center border-4 border-neon-green/70",initial:{scale:0},whileInView:{scale:1},transition:{duration:.5,type:"spring"},viewport:{once:!0},children:(0,s.jsx)("p",{className:"text-3xl font-bold text-white",children:"3"})}),(0,s.jsx)("p",{className:"text-sm text-github-text text-center mt-2",children:"Current Streak"})]}),(0,s.jsxs)("div",{className:"p-5 flex flex-col items-center justify-center",children:[(0,s.jsx)(l.P.p,{className:"text-3xl font-bold text-white",initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5},viewport:{once:!0},children:"17"}),(0,s.jsx)("p",{className:"text-sm text-github-text text-center mt-2",children:"Longest Streak"}),(0,s.jsx)("p",{className:"text-xs text-github-text text-center mt-1",children:"Apr 8 - Apr 24"})]})]})]}),(0,s.jsxs)(l.P.div,{variants:n,className:"bg-github-dark p-6 rounded-xl border border-github-border",children:["contributions"===e&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("h3",{className:"text-xl font-bold text-white mb-6",children:"Contribution Activity"}),(0,s.jsx)("div",{className:"h-64",children:(0,s.jsx)(ev.u,{width:"100%",height:"100%",children:(0,s.jsxs)(ew.E,{data:o,margin:{top:20,right:30,left:0,bottom:5},children:[(0,s.jsx)(eN.W,{dataKey:"month",stroke:"#8b949e"}),(0,s.jsx)(ek.h,{stroke:"#8b949e"}),(0,s.jsx)(eS.m,{contentStyle:{backgroundColor:"#161b22",border:"1px solid #30363d"},itemStyle:{color:"#c9d1d9"},labelStyle:{color:"white",fontWeight:"bold"}}),(0,s.jsx)(eA.y,{dataKey:"contributions",fill:"#3fb950",radius:[4,4,0,0],children:o.map((e,t)=>(0,s.jsx)(eD.f,{fill:e.contributions>50?"#3fb950":"#388e3c"},"cell-".concat(t)))})]})})})]}),"languages"===e&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("h3",{className:"text-xl font-bold text-white mb-6",children:"Most Used Languages"}),(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)("div",{className:"h-64 w-1/2",children:(0,s.jsx)(ev.u,{width:"100%",height:"100%",children:(0,s.jsxs)(eC.r,{children:[(0,s.jsx)(eT.F,{data:c,cx:"50%",cy:"50%",innerRadius:60,outerRadius:80,paddingAngle:2,dataKey:"value",label:e=>{let{name:t,percent:i}=e;return"".concat(t," ").concat((100*i).toFixed(0),"%")},labelLine:!1,children:c.map((e,t)=>(0,s.jsx)(eD.f,{fill:e.color},"cell-".concat(t)))}),(0,s.jsx)(eS.m,{contentStyle:{backgroundColor:"#161b22",border:"1px solid #30363d"},formatter:e=>["".concat(e,"%"),"Usage"]})]})})}),(0,s.jsx)("div",{className:"w-1/2 flex flex-col justify-center space-y-4 pl-4",children:c.map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"w-3 h-3 rounded-full mr-2",style:{backgroundColor:e.color}}),(0,s.jsx)("span",{className:"text-white mr-2",children:e.name}),(0,s.jsxs)("span",{className:"text-sm text-github-text",children:[e.value,"%"]})]},t))})]})]}),"repos"===e&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("h3",{className:"text-xl font-bold text-white mb-6",children:"Top Repositories"}),(0,s.jsx)("div",{className:"space-y-4",children:[{name:"ML-Face-Recognition",stars:15,forks:7},{name:"React-Portfolio",stars:12,forks:5},{name:"UI-Component-Library",stars:8,forks:3},{name:"Python-Data-Analysis",stars:7,forks:2},{name:"Mobile-App-Template",stars:5,forks:1}].map((e,t)=>(0,s.jsx)(l.P.div,{className:"p-4 bg-github-light/50 rounded-lg hover:bg-github-light cursor-pointer transition-colors",whileHover:{x:5},children:(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("h4",{className:"text-neon-green font-medium",children:e.name}),(0,s.jsxs)("div",{className:"flex space-x-3 text-github-text",children:[(0,s.jsxs)("span",{className:"flex items-center text-sm",children:[(0,s.jsx)("svg",{className:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"})}),e.stars]}),(0,s.jsxs)("span",{className:"flex items-center text-sm",children:[(0,s.jsx)("svg",{className:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z"})}),e.forks]})]})]})},t))})]}),(0,s.jsxs)("div",{className:"mt-8",children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-white mb-4",children:"Recent Activity"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)(l.P.div,{className:"flex items-start space-x-3",initial:{opacity:0,x:-20},whileInView:{opacity:1,x:0},transition:{duration:.5},viewport:{once:!0},children:[(0,s.jsx)("div",{className:"w-6 h-6 bg-github-light rounded-full flex items-center justify-center flex-shrink-0 mt-1",children:(0,s.jsx)("svg",{className:"w-4 h-4 text-neon-green",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-white",children:"Created 14 commits in 5 repositories"}),(0,s.jsx)("p",{className:"text-sm text-github-text",children:"Last week"})]})]}),(0,s.jsxs)(l.P.div,{className:"flex items-start space-x-3",initial:{opacity:0,x:-20},whileInView:{opacity:1,x:0},transition:{duration:.5,delay:.2},viewport:{once:!0},children:[(0,s.jsx)("div",{className:"w-6 h-6 bg-github-light rounded-full flex items-center justify-center flex-shrink-0 mt-1",children:(0,s.jsx)("svg",{className:"w-4 h-4 text-neon-blue",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-white",children:"Created 2 new repositories"}),(0,s.jsx)("p",{className:"text-sm text-github-text",children:"This month"})]})]})]}),(0,s.jsx)(l.P.button,{className:"mt-6 w-full py-2 border border-github-border rounded-md text-github-text hover:bg-github-light transition-colors",whileHover:{backgroundColor:"rgba(255,255,255,0.1)"},whileTap:{scale:.98},children:"Show more activity"})]})]})]})]})})},eE=()=>{let[e,t]=(0,a.useState)({name:"",email:"",subject:"",message:""}),[i,n]=(0,a.useState)(!1),{toast:r}=(0,K.dj)(),o=e=>{let{name:i,value:s}=e.target;t(e=>({...e,[i]:s}))},c=async i=>{i.preventDefault(),n(!0);try{let i=await fetch("/api/contact",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),s=await i.json();i.ok?(r({title:"Message sent",description:"Thanks for reaching out! I'll get back to you soon."}),t({name:"",email:"",subject:"",message:""})):r({title:"Error",description:s.error||"Failed to send message. Please try again.",variant:"destructive"})}catch(e){r({title:"Error",description:"Failed to send message. Please check your connection and try again.",variant:"destructive"})}finally{n(!1)}};return(0,s.jsx)("section",{id:"contact",className:"py-20 bg-github-dark",children:(0,s.jsxs)("div",{className:"section-container",children:[(0,s.jsx)(l.P.h2,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5},viewport:{once:!0},className:"section-title",children:"Contact"}),(0,s.jsx)(l.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.2},viewport:{once:!0},className:"text-center max-w-xl mx-auto mb-12",children:(0,s.jsx)("p",{className:"text-lg text-github-text",children:"Feel free to reach out if you'd like to collaborate, discuss tech, or share some awesome ideas!"})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,s.jsxs)(l.P.div,{initial:{opacity:0,x:-30},whileInView:{opacity:1,x:0},transition:{duration:.5},viewport:{once:!0},className:"space-y-6",children:[(0,s.jsx)("h3",{className:"text-2xl font-bold text-white",children:"Get in Touch"}),(0,s.jsx)("p",{className:"text-github-text",children:"Whether you have a project in mind, a question about my work, or just want to say hi, I'd love to hear from you."}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,s.jsx)("div",{className:"w-10 h-10 rounded-full bg-github-light flex items-center justify-center flex-shrink-0",children:(0,s.jsx)("svg",{className:"w-5 h-5 text-neon-green",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-white",children:"Email"}),(0,s.jsx)("p",{className:"text-github-text",children:(0,s.jsx)("a",{href:"mailto:<EMAIL>",children:"<EMAIL>"})})]})]}),(0,s.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,s.jsx)("div",{className:"w-10 h-10 rounded-full bg-github-light flex items-center justify-center flex-shrink-0",children:(0,s.jsxs)("svg",{className:"w-5 h-5 text-neon-blue",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:[(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-white",children:"Location"}),(0,s.jsx)("p",{className:"text-github-text",children:"Pune, Maharashtra"})]})]}),(0,s.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,s.jsx)("div",{className:"w-10 h-10 rounded-full bg-github-light flex items-center justify-center flex-shrink-0",children:(0,s.jsx)("svg",{className:"w-5 h-5 text-neon-purple",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"})})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium text-white",children:"Portfolio"}),(0,s.jsx)("p",{className:"text-github-text",children:"https://portfolio.greenhacker.tech/"})]})]})]}),(0,s.jsxs)("div",{className:"pt-8",children:[(0,s.jsx)("h3",{className:"text-xl font-bold text-white mb-4",children:"Connect With Me"}),(0,s.jsxs)("div",{className:"flex space-x-4",children:[(0,s.jsx)("a",{href:"https://www.linkedin.com/in/harsh-hirawat-b657061b7/",target:"_blank",rel:"noreferrer",className:"w-12 h-12 rounded-full bg-github-light flex items-center justify-center hover:bg-neon-blue/20 transition-colors",children:(0,s.jsx)("svg",{className:"w-6 h-6 text-neon-blue",fill:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{d:"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"})})}),(0,s.jsx)("a",{href:"https://instagram.com/harsh_hirawat",target:"_blank",rel:"noreferrer",className:"w-12 h-12 rounded-full bg-github-light flex items-center justify-center hover:bg-neon-pink/20 transition-colors",children:(0,s.jsx)("svg",{className:"w-6 h-6 text-neon-pink",fill:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{d:"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"})})}),(0,s.jsx)("a",{href:"https://github.com/GreenHacker420",target:"_blank",rel:"noreferrer",className:"w-12 h-12 rounded-full bg-github-light flex items-center justify-center hover:bg-white/10 transition-colors",children:(0,s.jsx)("svg",{className:"w-6 h-6 text-white",fill:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)("path",{d:"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"})})})]})]})]}),(0,s.jsx)(l.P.div,{initial:{opacity:0,x:30},whileInView:{opacity:1,x:0},transition:{duration:.5},viewport:{once:!0},children:(0,s.jsxs)("form",{onSubmit:c,className:"bg-github-light p-6 rounded-lg border border-github-border",children:[(0,s.jsx)("h3",{className:"text-2xl font-bold text-white mb-6",children:"Send a Message"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"name",className:"block text-github-text text-sm font-medium mb-2",children:"Your Name"}),(0,s.jsx)("input",{type:"text",id:"name",name:"name",value:e.name,onChange:o,className:"w-full px-4 py-3 bg-github-dark border border-github-border rounded-lg focus:outline-none focus:ring-2 focus:ring-neon-green/50 text-white",placeholder:"John Doe",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-github-text text-sm font-medium mb-2",children:"Your Email"}),(0,s.jsx)("input",{type:"email",id:"email",name:"email",value:e.email,onChange:o,className:"w-full px-4 py-3 bg-github-dark border border-github-border rounded-lg focus:outline-none focus:ring-2 focus:ring-neon-green/50 text-white",placeholder:"<EMAIL>",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"subject",className:"block text-github-text text-sm font-medium mb-2",children:"Subject"}),(0,s.jsx)("input",{type:"text",id:"subject",name:"subject",value:e.subject,onChange:o,className:"w-full px-4 py-3 bg-github-dark border border-github-border rounded-lg focus:outline-none focus:ring-2 focus:ring-neon-green/50 text-white",placeholder:"Project Collaboration",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"message",className:"block text-github-text text-sm font-medium mb-2",children:"Message"}),(0,s.jsx)("textarea",{id:"message",name:"message",value:e.message,onChange:o,rows:5,className:"w-full px-4 py-3 bg-github-dark border border-github-border rounded-lg focus:outline-none focus:ring-2 focus:ring-neon-green/50 text-white",placeholder:"Hi there, I'd like to talk about...",required:!0})]}),(0,s.jsx)("button",{type:"submit",disabled:i,className:"w-full py-3 rounded-lg font-medium transition-colors ".concat(i?"bg-github-dark text-github-text cursor-not-allowed":"bg-neon-green text-black hover:bg-neon-green/90"),children:i?"Sending...":"Send Message"})]})]})})]})]})})};var eI=i(33651),eR=i(54416);let eM=eI.bL,eL=eI.l9,ez=eI.ZL;eI.bm;let eO=a.forwardRef((e,t)=>{let{className:i,...a}=e;return(0,s.jsx)(eI.hJ,{ref:t,className:(0,O.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",i),...a})});eO.displayName=eI.hJ.displayName;let eH=a.forwardRef((e,t)=>{let{className:i,children:a,...n}=e;return(0,s.jsxs)(ez,{children:[(0,s.jsx)(eO,{}),(0,s.jsxs)(eI.UC,{ref:t,className:(0,O.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",i),...n,children:[a,(0,s.jsxs)(eI.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,s.jsx)(eR.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});eH.displayName=eI.UC.displayName;let e_=a.forwardRef((e,t)=>{let{className:i,...a}=e;return(0,s.jsx)(eI.hE,{ref:t,className:(0,O.cn)("text-lg font-semibold leading-none tracking-tight",i),...a})});e_.displayName=eI.hE.displayName,a.forwardRef((e,t)=>{let{className:i,...a}=e;return(0,s.jsx)(eI.VY,{ref:t,className:(0,O.cn)("text-sm text-muted-foreground",i),...a})}).displayName=eI.VY.displayName;var eB=i(2564);let eF=a.forwardRef((e,t)=>{let{...i}=e;return(0,s.jsx)(eB.bL,{ref:t,...i})});eF.displayName=eB.bL.displayName;var eW=i(57434),eV=i(92657),eG=i(91788);let eU="/resume.pdf",eJ=()=>{let[e,t]=(0,a.useState)(!1),[i,n]=(0,a.useState)(Date.now());return(0,s.jsxs)(l.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5},viewport:{once:!0},className:"bg-github-light rounded-lg p-6 border border-github-border card-hover",children:[(0,s.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(eW.A,{className:"text-neon-green mr-3",size:24}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-white",children:"My Resume"})]}),(0,s.jsx)("span",{className:"px-3 py-1 bg-neon-green/20 text-neon-green text-xs rounded-full",children:"PDF"})]}),(0,s.jsx)("p",{className:"text-github-text mb-6",children:"Check out my professional experience, skills, and educational background. Download the PDF or view it directly on this page."}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-3",children:[(0,s.jsxs)(eM,{children:[(0,s.jsx)(eL,{asChild:!0,children:(0,s.jsxs)("button",{className:"flex items-center gap-2 px-4 py-2 bg-neon-green text-black font-medium rounded-md hover:bg-neon-green/90 transition-all",children:[(0,s.jsx)(eV.A,{size:16}),"View Resume"]})}),(0,s.jsxs)(eH,{className:"max-w-4xl w-[90vw] h-[90vh] p-0",children:[(0,s.jsx)(eF,{children:(0,s.jsx)(e_,{children:"Resume Preview"})}),(0,s.jsxs)("div",{className:"flex justify-between items-center p-4 border-b border-github-border",children:[(0,s.jsx)("h4",{className:"font-semibold text-lg",children:"Resume Preview"}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)("a",{href:eU,download:"GREENHACKER_Resume.pdf",className:"flex items-center gap-2 px-3 py-1 bg-neon-green text-black text-sm rounded-md hover:bg-neon-green/90 transition-all",children:[(0,s.jsx)(eG.A,{size:14}),"Download"]}),(0,s.jsx)("button",{onClick:()=>{t(!1),n(Date.now())},className:"p-1 rounded-full hover:bg-github-border/30 transition-colors",title:"Reload PDF",children:(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[(0,s.jsx)("path",{d:"M21 2v6h-6"}),(0,s.jsx)("path",{d:"M3 12a9 9 0 0 1 15-6.7L21 8"}),(0,s.jsx)("path",{d:"M3 12a9 9 0 0 0 6.7 15L13 21"}),(0,s.jsx)("path",{d:"M14.3 19.1L21 12"})]})}),(0,s.jsx)(eL,{asChild:!0,children:(0,s.jsx)("button",{className:"p-1 rounded-full hover:bg-github-border/30 transition-colors",children:(0,s.jsx)(eR.A,{size:18})})})]})]}),(0,s.jsxs)("div",{className:"h-full bg-gray-800 overflow-auto",children:[(0,s.jsx)("iframe",{src:eU,className:"w-full h-full",title:"Resume Preview",onLoad:()=>t(!0)},i),!e&&(0,s.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-github-dark/80",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 border-4 border-neon-green/30 border-t-neon-green rounded-full animate-spin mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-github-text",children:"Loading resume..."})]})})]})]})]}),(0,s.jsxs)("a",{href:eU,download:"GREENHACKER_Resume.pdf",className:"flex items-center gap-2 px-4 py-2 bg-transparent border border-neon-green text-neon-green font-medium rounded-md hover:bg-neon-green/10 transition-all",children:[(0,s.jsx)(eG.A,{size:16}),"Download PDF"]})]})]})};var eY=i(56671),eq=i(69037),eK=i(5040),eX=i(67312);let eQ=e=>{let{item:t,index:i,isSelected:a,onClick:n}=e;return(0,s.jsxs)(l.P.li,{initial:{opacity:0,x:-20},whileInView:{opacity:1,x:0},transition:{duration:.3,delay:.1*i},viewport:{once:!0},className:"flex items-start p-3 rounded-md transition-all cursor-pointer ".concat(a?"bg-github-border/30":"hover:bg-github-border/10"),onClick:n,children:[(0,s.jsx)("div",{className:"bg-github-dark/50 p-2 rounded-md mr-3",children:(e=>{switch(e){case"award":return(0,s.jsx)(eq.A,{className:"text-neon-green",size:18});case"book-open":return(0,s.jsx)(eK.A,{className:"text-neon-green",size:18});case"coffee":return(0,s.jsx)(eX.A,{className:"text-neon-green",size:18})}})(t.icon)}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)("span",{className:"text-neon-green text-xs uppercase tracking-wider",children:t.category})}),(0,s.jsx)("span",{className:"text-github-text block mt-1",children:t.highlight}),a&&(0,s.jsx)(l.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},className:"mt-3 text-sm text-github-text/80 border-t border-github-border/30 pt-3",children:(0,s.jsxs)("p",{children:["This highlight showcases expertise in ",t.category.toLowerCase(),". Click to collapse this additional information."]})})]})]})},eZ=[{highlight:"Implemented serverless architecture using AWS Lambda, reducing operational costs by 40%",category:"Cloud Architecture",icon:"award"},{highlight:"Led a team of 5 developers to deliver a complex e-commerce platform in 3 months",category:"Leadership",icon:"coffee"},{highlight:"Authored technical blog posts with over 100,000 monthly readers",category:"Content Creation",icon:"book-open"},{highlight:"Optimized database queries resulting in a 60% performance improvement",category:"Performance",icon:"award"},{highlight:"Designed and implemented a microservices architecture using Docker and Kubernetes",category:"DevOps",icon:"coffee"},{highlight:"Created custom data visualization libraries used by Fortune 500 companies",category:"Visualization",icon:"book-open"}],e$=async()=>new Promise(e=>{setTimeout(()=>{let t=Math.floor(Math.random()*eZ.length);e(eZ[t])},1500)}),e0=()=>{let[e,t]=(0,a.useState)([{highlight:"Full Stack Development with React, Node.js, and TypeScript",category:"Development",icon:"award"},{highlight:"Machine Learning specialization with PyTorch and TensorFlow",category:"AI/ML",icon:"book-open"},{highlight:"5+ years experience working with distributed teams",category:"Experience",icon:"coffee"},{highlight:"Open Source contributor to various GitHub projects",category:"Community",icon:"award"},{highlight:"Conference speaker on AI and web technologies",category:"Speaking",icon:"book-open"}]),[i,n]=(0,a.useState)(null),[r,o]=(0,a.useState)(!1),c=async()=>{o(!0);try{let e=await e$();t(t=>[...t,e]),eY.oR.success("New highlight generated by Gemini AI")}catch(e){eY.oR.error("Failed to generate highlight"),console.error(e)}finally{o(!1)}};return(0,s.jsxs)(l.P.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.2},viewport:{once:!0},className:"bg-github-light rounded-lg p-6 border border-github-border",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsx)("h3",{className:"text-xl font-semibold text-white",children:"Highlights"}),(0,s.jsx)("button",{onClick:c,disabled:r,className:"px-3 py-1 bg-neon-purple/20 text-neon-purple text-xs rounded-full hover:bg-neon-purple/30 transition-colors flex items-center gap-1",children:r?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("span",{className:"h-2 w-2 bg-neon-purple rounded-full animate-pulse"}),(0,s.jsx)("span",{children:"Generating..."})]}):(0,s.jsx)(s.Fragment,{children:(0,s.jsx)("span",{children:"+ Generate with Gemini"})})})]}),(0,s.jsx)("ul",{className:"space-y-4",children:e.map((e,t)=>(0,s.jsx)(eQ,{item:e,index:t,isSelected:i===t,onClick:()=>n(t===i?null:t)},t))})]})},e2=()=>(0,s.jsx)("section",{id:"resume",className:"py-20 bg-github-dark relative",children:(0,s.jsxs)("div",{className:"section-container",children:[(0,s.jsx)("h2",{className:"section-title mb-12",children:"Resume"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,s.jsx)(eJ,{}),(0,s.jsx)(e0,{})]})]})});class e1 extends a.Component{static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){console.error("ErrorBoundary caught an error:",e,t)}render(){return this.state.hasError?this.props.fallback?this.props.fallback:(0,s.jsx)("div",{className:"min-h-screen bg-github-dark text-github-text flex items-center justify-center",children:(0,s.jsxs)("div",{className:"max-w-md p-6 bg-github-light rounded-lg",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-white mb-4",children:"Something went wrong"}),(0,s.jsx)("p",{className:"mb-4",children:"We're sorry, but something went wrong with the rendering of this page."}),(0,s.jsx)("button",{onClick:()=>window.location.reload(),className:"px-4 py-2 bg-neon-green text-black rounded-md hover:bg-neon-green/90",children:"Reload page"})]})}):this.props.children}constructor(e){super(e),this.state={hasError:!1,error:null}}}n.os.registerPlugin(r.u,o.I);let e4=e=>{let{section:t}=e;return(0,s.jsx)("div",{className:"py-20 bg-github-dark",children:(0,s.jsxs)("div",{className:"section-container",children:[(0,s.jsx)("h2",{className:"section-title",children:t}),(0,s.jsx)("div",{className:"p-6 bg-github-light rounded-lg border border-github-border",children:(0,s.jsx)("p",{className:"text-white",children:"This section could not be loaded."})})]})})},e5=e=>{let{children:t,name:i}=e;return(0,s.jsx)(e1,{fallback:(0,s.jsx)(e4,{section:i}),children:(0,s.jsx)("section",{id:i.toLowerCase(),children:t})})};function e3(){return(0,a.useEffect)(()=>(A(),D(),P(),document.querySelectorAll(".section-title").forEach(e=>{T(e)}),document.querySelectorAll("section").forEach(e=>{n.os.fromTo(e,{opacity:.6,y:50},{opacity:1,y:0,scrollTrigger:{trigger:e,start:"top bottom-=100",end:"top center",scrub:!0}})}),()=>{r.u.getAll().forEach(e=>e.kill())}),[]),(0,s.jsxs)("div",{className:"min-h-screen bg-github-dark text-github-text dark:bg-github-dark dark:text-github-text",children:[(0,s.jsx)(h,{}),(0,s.jsxs)("main",{children:[(0,s.jsx)(e5,{name:"Hero",children:(0,s.jsx)(k,{})}),(0,s.jsx)(e5,{name:"About",children:(0,s.jsx)(S,{})}),(0,s.jsx)(e5,{name:"Skills",children:(0,s.jsx)(er,{})}),(0,s.jsx)(e5,{name:"Projects",children:(0,s.jsx)(eg,{})}),(0,s.jsx)(e5,{name:"Experience",children:(0,s.jsx)(ey,{})}),(0,s.jsx)(e5,{name:"Resume",children:(0,s.jsx)(e2,{})}),(0,s.jsx)(e5,{name:"Stats",children:(0,s.jsx)(eP,{})}),(0,s.jsx)(e5,{name:"Contact",children:(0,s.jsx)(eE,{})})]}),(0,s.jsx)(p,{})]})}},59434:(e,t,i)=>{"use strict";i.d(t,{cn:()=>n});var s=i(52596),a=i(39688);function n(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];return(0,a.QP)((0,s.$)(t))}},87481:(e,t,i)=>{"use strict";i.d(t,{dj:()=>m,oR:()=>h});var s=i(12115);let a=0,n=new Map,r=e=>{if(n.has(e))return;let t=setTimeout(()=>{n.delete(e),d({type:"REMOVE_TOAST",toastId:e})},1e6);n.set(e,t)},o=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:i}=t;return i?r(i):e.toasts.forEach(e=>{r(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===i||void 0===i?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],c={toasts:[]};function d(e){c=o(c,e),l.forEach(e=>{e(c)})}function h(e){let{...t}=e,i=(a=(a+1)%Number.MAX_SAFE_INTEGER).toString(),s=()=>d({type:"DISMISS_TOAST",toastId:i});return d({type:"ADD_TOAST",toast:{...t,id:i,open:!0,onOpenChange:e=>{e||s()}}}),{id:i,dismiss:s,update:e=>d({type:"UPDATE_TOAST",toast:{...e,id:i}})}}function m(){let[e,t]=s.useState(c);return s.useEffect(()=>(l.push(t),()=>{let e=l.indexOf(t);e>-1&&l.splice(e,1)}),[e]),{...e,toast:h,dismiss:e=>d({type:"DISMISS_TOAST",toastId:e})}}}},e=>{var t=t=>e(e.s=t);e.O(0,[631,568,592,710,107,390,394,441,684,358],()=>t(9207)),_N_E=e.O()}]);