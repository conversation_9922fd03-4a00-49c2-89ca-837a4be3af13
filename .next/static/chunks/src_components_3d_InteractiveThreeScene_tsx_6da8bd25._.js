(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/3d/InteractiveThreeScene.tsx [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_8ba9ff21._.js",
  "static/chunks/src_components_3d_a20b0cab._.js",
  "static/chunks/src_components_3d_InteractiveThreeScene_tsx_a179fcaf._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/3d/InteractiveThreeScene.tsx [app-client] (ecmascript)");
    });
});
}}),
}]);