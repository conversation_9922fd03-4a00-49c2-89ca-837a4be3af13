"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[99],{28099:(e,t,n)=>{n.d(t,{zh:()=>ne});var r,i,s,a,o,f=n(10697),u=S(),l=e=>_(e,u),d=S();l.write=e=>_(e,d);var h=S();l.onStart=e=>_(e,h);var c=S();l.onFrame=e=>_(e,c);var p=S();l.onFinish=e=>_(e,p);var m=[];l.setTimeout=(e,t)=>{let n=l.now()+t,r=()=>{let e=m.findIndex(e=>e.cancel==r);~e&&m.splice(e,1),x-=!!~e},i={time:n,handler:e,cancel:r};return m.splice(g(n),0,i),x+=1,w(),i};var g=e=>~(~m.findIndex(t=>t.time>e)||~m.length);l.cancel=e=>{h.delete(e),c.delete(e),p.delete(e),u.delete(e),d.delete(e)},l.sync=e=>{b=!0,l.batchedUpdates(e),b=!1},l.throttle=e=>{let t;function n(){try{e(...t)}finally{t=null}}function r(...e){t=e,l.onStart(n)}return r.handler=e,r.cancel=()=>{h.delete(n),t=null},r};var y="undefined"!=typeof window?window.requestAnimationFrame:()=>{};l.use=e=>y=e,l.now="undefined"!=typeof performance?()=>performance.now():Date.now,l.batchedUpdates=e=>e(),l.catch=console.error,l.frameLoop="always",l.advance=()=>{"demand"!==l.frameLoop?console.warn("Cannot call the manual advancement of rafz whilst frameLoop is not set as demand"):P()};var v=-1,x=0,b=!1;function _(e,t){b?(t.delete(e),e(0)):(t.add(e),w())}function w(){v<0&&(v=0,"demand"!==l.frameLoop&&y(k))}function k(){~v&&(y(k),l.batchedUpdates(P))}function P(){let e=v,t=g(v=l.now());if(t&&(A(m.splice(0,t),e=>e.handler()),x-=t),!x)return void(v=-1);h.flush(),u.flush(e?Math.min(64,v-e):16.667),c.flush(),d.flush(),p.flush()}function S(){let e=new Set,t=e;return{add(n){x+=+!(t!=e||e.has(n)),e.add(n)},delete:n=>(x-=t==e&&e.has(n)?1:0,e.delete(n)),flush(n){t.size&&(e=new Set,x-=t.size,A(t,t=>t(n)&&e.add(t)),x+=e.size,t=e)}}}function A(e,t){e.forEach(e=>{try{t(e)}catch(e){l.catch(e)}})}var C=n(12115),V=Object.defineProperty,I={};function R(){}((e,t)=>{for(var n in t)V(e,n,{get:t[n],enumerable:!0})})(I,{assign:()=>L,colors:()=>Q,createStringInterpolator:()=>r,skipAnimation:()=>N,to:()=>i,willAdvance:()=>U});var E=(e,t,n)=>Object.defineProperty(e,t,{value:n,writable:!0,configurable:!0}),j={arr:Array.isArray,obj:e=>!!e&&"Object"===e.constructor.name,fun:e=>"function"==typeof e,str:e=>"string"==typeof e,num:e=>"number"==typeof e,und:e=>void 0===e};function M(e,t){if(j.arr(e)){if(!j.arr(t)||e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}return e===t}var q=(e,t)=>e.forEach(t);function z(e,t,n){if(j.arr(e)){for(let r=0;r<e.length;r++)t.call(n,e[r],`${r}`);return}for(let r in e)e.hasOwnProperty(r)&&t.call(n,e[r],r)}var F=e=>j.und(e)?[]:j.arr(e)?e:[e];function O(e,t){if(e.size){let n=Array.from(e);e.clear(),q(n,t)}}var $=(e,...t)=>O(e,e=>e(...t)),T=()=>"undefined"==typeof window||!window.navigator||/ServerSideRendering|^Deno\//.test(window.navigator.userAgent),Q=null,N=!1,U=R,L=e=>{e.to&&(i=e.to),e.now&&(l.now=e.now),void 0!==e.colors&&(Q=e.colors),null!=e.skipAnimation&&(N=e.skipAnimation),e.createStringInterpolator&&(r=e.createStringInterpolator),e.requestAnimationFrame&&l.use(e.requestAnimationFrame),e.batchedUpdates&&(l.batchedUpdates=e.batchedUpdates),e.willAdvance&&(U=e.willAdvance),e.frameLoop&&(l.frameLoop=e.frameLoop)},D=new Set,W=[],Z=[],H=0,B={get idle(){return!D.size&&!W.length},start(e){H>e.priority?(D.add(e),l.onStart(G)):(J(e),l(Y))},advance:Y,sort(e){if(H)l.onFrame(()=>B.sort(e));else{let t=W.indexOf(e);~t&&(W.splice(t,1),X(e))}},clear(){W=[],D.clear()}};function G(){D.forEach(J),D.clear(),l(Y)}function J(e){W.includes(e)||X(e)}function X(e){W.splice(function(e,t){let n=e.findIndex(t);return n<0?e.length:n}(W,t=>t.priority>e.priority),0,e)}function Y(e){let t=Z;for(let n=0;n<W.length;n++){let r=W[n];H=r.priority,!r.idle&&(U(r),r.advance(e),r.idle||t.push(r))}return H=0,(Z=W).length=0,(W=t).length>0}var K="[-+]?\\d*\\.?\\d+",ee=K+"%";function et(...e){return"\\(\\s*("+e.join(")\\s*,\\s*(")+")\\s*\\)"}var en=RegExp("rgb"+et(K,K,K)),er=RegExp("rgba"+et(K,K,K,K)),ei=RegExp("hsl"+et(K,ee,ee)),es=RegExp("hsla"+et(K,ee,ee,K)),ea=/^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,eo=/^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,ef=/^#([0-9a-fA-F]{6})$/,eu=/^#([0-9a-fA-F]{8})$/;function el(e,t,n){return(n<0&&(n+=1),n>1&&(n-=1),n<1/6)?e+(t-e)*6*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function ed(e,t,n){let r=n<.5?n*(1+t):n+t-n*t,i=2*n-r,s=el(i,r,e+1/3);return Math.round(255*s)<<24|Math.round(255*el(i,r,e))<<16|Math.round(255*el(i,r,e-1/3))<<8}function eh(e){let t=parseInt(e,10);return t<0?0:t>255?255:t}function ec(e){return(parseFloat(e)%360+360)%360/360}function ep(e){let t=parseFloat(e);return t<0?0:t>1?255:Math.round(255*t)}function em(e){let t=parseFloat(e);return t<0?0:t>100?1:t/100}function eg(e){let t,n="number"==typeof e?e>>>0===e&&e>=0&&e<=0xffffffff?e:null:(t=ef.exec(e))?parseInt(t[1]+"ff",16)>>>0:Q&&void 0!==Q[e]?Q[e]:(t=en.exec(e))?(eh(t[1])<<24|eh(t[2])<<16|eh(t[3])<<8|255)>>>0:(t=er.exec(e))?(eh(t[1])<<24|eh(t[2])<<16|eh(t[3])<<8|ep(t[4]))>>>0:(t=ea.exec(e))?parseInt(t[1]+t[1]+t[2]+t[2]+t[3]+t[3]+"ff",16)>>>0:(t=eu.exec(e))?parseInt(t[1],16)>>>0:(t=eo.exec(e))?parseInt(t[1]+t[1]+t[2]+t[2]+t[3]+t[3]+t[4]+t[4],16)>>>0:(t=ei.exec(e))?(255|ed(ec(t[1]),em(t[2]),em(t[3])))>>>0:(t=es.exec(e))?(ed(ec(t[1]),em(t[2]),em(t[3]))|ep(t[4]))>>>0:null;if(null===n)return e;let r=(0xff000000&(n=n||0))>>>24,i=(0xff0000&n)>>>16,s=(65280&n)>>>8,a=(255&n)/255;return`rgba(${r}, ${i}, ${s}, ${a})`}var ey=(e,t,n)=>{if(j.fun(e))return e;if(j.arr(e))return ey({range:e,output:t,extrapolate:n});if(j.str(e.output[0]))return r(e);let i=e.output,s=e.range||[0,1],a=e.extrapolateLeft||e.extrapolate||"extend",o=e.extrapolateRight||e.extrapolate||"extend",f=e.easing||(e=>e);return t=>{let n=function(e,t){for(var n=1;n<t.length-1&&!(t[n]>=e);++n);return n-1}(t,s);return function(e,t,n,r,i,s,a,o,f){let u=f?f(e):e;if(u<t)if("identity"===a)return u;else"clamp"===a&&(u=t);if(u>n)if("identity"===o)return u;else"clamp"===o&&(u=n);return r===i?r:t===n?e<=t?r:i:(t===-1/0?u=-u:n===1/0?u-=t:u=(u-t)/(n-t),u=s(u),r===-1/0?u=-u:i===1/0?u+=r:u=u*(i-r)+r,u)}(t,s[n],s[n+1],i[n],i[n+1],f,a,o,e.map)}},ev=Symbol.for("FluidValue.get"),ex=Symbol.for("FluidValue.observers"),eb=e=>!!(e&&e[ev]),e_=e=>e&&e[ev]?e[ev]():e,ew=e=>e[ex]||null;function ek(e,t){let n=e[ex];n&&n.forEach(e=>{e.eventObserved?e.eventObserved(t):e(t)})}var eP=class{constructor(e){if(!e&&!(e=this.get))throw Error("Unknown getter");eS(this,e)}},eS=(e,t)=>eV(e,ev,t);function eA(e,t){if(e[ev]){let n=e[ex];n||eV(e,ex,n=new Set),!n.has(t)&&(n.add(t),e.observerAdded&&e.observerAdded(n.size,t))}return t}function eC(e,t){let n=e[ex];if(n&&n.has(t)){let r=n.size-1;r?n.delete(t):e[ex]=null,e.observerRemoved&&e.observerRemoved(r,t)}}var eV=(e,t,n)=>Object.defineProperty(e,t,{value:n,writable:!0,configurable:!0}),eI=/[+\-]?(?:0|[1-9]\d*)(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,eR=/(#(?:[0-9a-f]{2}){2,4}|(#[0-9a-f]{3})|(rgb|hsl)a?\((-?\d+%?[,\s]+){2,3}\s*[\d\.]+%?\))/gi,eE=RegExp(`(${eI.source})(%|[a-z]+)`,"i"),ej=/rgba\(([0-9\.-]+), ([0-9\.-]+), ([0-9\.-]+), ([0-9\.-]+)\)/gi,eM=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/,eq=e=>{let[t,n]=ez(e);if(!t||T())return e;let r=window.getComputedStyle(document.documentElement).getPropertyValue(t);if(r)return r.trim();if(n&&n.startsWith("--")){let e=window.getComputedStyle(document.documentElement).getPropertyValue(n);if(e)return e}else if(n&&eM.test(n))return eq(n);else if(n)return n;return e},ez=e=>{let t=eM.exec(e);if(!t)return[,];let[,n,r]=t;return[n,r]},eF=(e,t,n,r,i)=>`rgba(${Math.round(t)}, ${Math.round(n)}, ${Math.round(r)}, ${i})`,eO=e=>{s||(s=Q?RegExp(`(${Object.keys(Q).join("|")})(?!\\w)`,"g"):/^\b$/);let t=e.output.map(e=>e_(e).replace(eM,eq).replace(eR,eg).replace(s,eg)),n=t.map(e=>e.match(eI).map(Number)),r=n[0].map((e,t)=>n.map(e=>{if(!(t in e))throw Error('The arity of each "output" value must be equal');return e[t]})).map(t=>ey({...e,output:t}));return e=>{let n=!eE.test(t[0])&&t.find(e=>eE.test(e))?.replace(eI,""),i=0;return t[0].replace(eI,()=>`${r[i++](e)}${n||""}`).replace(ej,eF)}},e$="react-spring: ",eT=e=>{let t=!1;if("function"!=typeof e)throw TypeError(`${e$}once requires a function parameter`);return(...n)=>{t||(e(...n),t=!0)}},eQ=eT(console.warn),eN=eT(console.warn);function eU(e){return j.str(e)&&("#"==e[0]||/\d/.test(e)||!T()&&eM.test(e)||e in(Q||{}))}var eL=new WeakMap,eD=e=>e.forEach(({target:e,contentRect:t})=>eL.get(e)?.forEach(e=>e(t))),eW=new Set,eZ=()=>{let e=()=>{eW.forEach(e=>e({width:window.innerWidth,height:window.innerHeight}))};return window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}},eH=e=>(eW.add(e),o||(o=eZ()),()=>{eW.delete(e),!eW.size&&o&&(o(),o=void 0)}),eB=(e,t,n)=>t-e==0?1:(n-e)/(t-e),eG={x:{length:"Width",position:"Left"},y:{length:"Height",position:"Top"}},eJ=T()?C.useEffect:C.useLayoutEffect,eX=()=>{let e=(0,C.useRef)(!1);return eJ(()=>(e.current=!0,()=>{e.current=!1}),[]),e};function eY(){let e=(0,C.useState)()[1],t=eX();return()=>{t.current&&e(Math.random())}}var eK=e=>(0,C.useEffect)(e,e0),e0=[];function e1(e){let t=(0,C.useRef)();return(0,C.useEffect)(()=>{t.current=e}),t.current}var e2=Symbol.for("Animated:node"),e8=e=>!!e&&e[e2]===e,e5=e=>e&&e[e2],e9=(e,t)=>E(e,e2,t),e3=e=>e&&e[e2]&&e[e2].getPayload(),e6=class{constructor(){e9(this,this)}getPayload(){return this.payload||[]}},e4=class extends e6{constructor(e){super(),this._value=e,this.done=!0,this.durationProgress=0,j.num(this._value)&&(this.lastPosition=this._value)}static create(e){return new e4(e)}getPayload(){return[this]}getValue(){return this._value}setValue(e,t){return j.num(e)&&(this.lastPosition=e,t&&(e=Math.round(e/t)*t,this.done&&(this.lastPosition=e))),this._value!==e&&(this._value=e,!0)}reset(){let{done:e}=this;this.done=!1,j.num(this._value)&&(this.elapsedTime=0,this.durationProgress=0,this.lastPosition=this._value,e&&(this.lastVelocity=null),this.v0=null)}},e7=class extends e4{constructor(e){super(0),this._string=null,this._toString=ey({output:[e,e]})}static create(e){return new e7(e)}getValue(){let e=this._string;return null==e?this._string=this._toString(this._value):e}setValue(e){if(j.str(e)){if(e==this._string)return!1;this._string=e,this._value=1}else{if(!super.setValue(e))return!1;this._string=null}return!0}reset(e){e&&(this._toString=ey({output:[this.getValue(),e]})),this._value=0,super.reset()}},te={dependencies:null},tt=class extends e6{constructor(e){super(),this.source=e,this.setValue(e)}getValue(e){let t={};return z(this.source,(n,r)=>{e8(n)?t[r]=n.getValue(e):eb(n)?t[r]=e_(n):e||(t[r]=n)}),t}setValue(e){this.source=e,this.payload=this._makePayload(e)}reset(){this.payload&&q(this.payload,e=>e.reset())}_makePayload(e){if(e){let t=new Set;return z(e,this._addToPayload,t),Array.from(t)}}_addToPayload(e){te.dependencies&&eb(e)&&te.dependencies.add(e);let t=e3(e);t&&q(t,e=>this.add(e))}},tn=class extends tt{constructor(e){super(e)}static create(e){return new tn(e)}getValue(){return this.source.map(e=>e.getValue())}setValue(e){let t=this.getPayload();return e.length==t.length?t.map((t,n)=>t.setValue(e[n])).some(Boolean):(super.setValue(e.map(tr)),!0)}};function tr(e){return(eU(e)?e7:e4).create(e)}function ti(e){let t=e5(e);return t?t.constructor:j.arr(e)?tn:eU(e)?e7:e4}var ts=(e,t)=>{let n=!j.fun(e)||e.prototype&&e.prototype.isReactComponent;return(0,C.forwardRef)((r,i)=>{let s=(0,C.useRef)(null),a=n&&(0,C.useCallback)(e=>{s.current=function(e,t){return e&&(j.fun(e)?e(t):e.current=t),t}(i,e)},[i]),[o,f]=function(e,t){let n=new Set;return te.dependencies=n,e.style&&(e={...e,style:t.createAnimatedStyle(e.style)}),e=new tt(e),te.dependencies=null,[e,n]}(r,t),u=eY(),d=()=>{let e=s.current;(!n||e)&&!1===(!!e&&t.applyAnimatedValues(e,o.getValue(!0)))&&u()},h=new ta(d,f),c=(0,C.useRef)();eJ(()=>(c.current=h,q(f,e=>eA(e,h)),()=>{c.current&&(q(c.current.deps,e=>eC(e,c.current)),l.cancel(c.current.update))})),(0,C.useEffect)(d,[]),eK(()=>()=>{let e=c.current;q(e.deps,t=>eC(t,e))});let p=t.getComponentProps(o.getValue());return C.createElement(e,{...p,ref:a})})},ta=class{constructor(e,t){this.update=e,this.deps=t}eventObserved(e){"change"==e.type&&l.write(this.update)}},to=Symbol.for("AnimatedComponent"),tf=e=>j.str(e)?e:e&&j.str(e.displayName)?e.displayName:j.fun(e)&&e.name||null;function tu(e,...t){return j.fun(e)?e(...t):e}var tl=(e,t)=>!0===e||!!(t&&e&&(j.fun(e)?e(t):F(e).includes(t))),td=(e,t)=>j.obj(e)?t&&e[t]:e,th=(e,t)=>!0===e.default?e[t]:e.default?e.default[t]:void 0,tc=e=>e,tp=(e,t=tc)=>{let n=tm;e.default&&!0!==e.default&&(n=Object.keys(e=e.default));let r={};for(let i of n){let n=t(e[i],i);j.und(n)||(r[i]=n)}return r},tm=["config","onProps","onStart","onChange","onPause","onResume","onRest"],tg={config:1,from:1,to:1,ref:1,loop:1,reset:1,pause:1,cancel:1,reverse:1,immediate:1,default:1,delay:1,onProps:1,onStart:1,onChange:1,onPause:1,onResume:1,onRest:1,onResolve:1,items:1,trail:1,sort:1,expires:1,initial:1,enter:1,update:1,leave:1,children:1,onDestroyed:1,keys:1,callId:1,parentId:1};function ty(e){let t=function(e){let t={},n=0;if(z(e,(e,r)=>{!tg[r]&&(t[r]=e,n++)}),n)return t}(e);if(t){let n={to:t};return z(e,(e,r)=>r in t||(n[r]=e)),n}return{...e}}function tv(e){return e=e_(e),j.arr(e)?e.map(tv):eU(e)?I.createStringInterpolator({range:[0,1],output:[e,e]})(1):e}function tx(e){return j.fun(e)||j.arr(e)&&j.obj(e[0])}var tb={tension:170,friction:26,mass:1,damping:1,easing:e=>e,clamp:!1},t_=class{constructor(){this.velocity=0,Object.assign(this,tb)}};function tw(e,t){if(j.und(t.decay)){let n=!j.und(t.tension)||!j.und(t.friction);!n&&j.und(t.frequency)&&j.und(t.damping)&&j.und(t.mass)||(e.duration=void 0,e.decay=void 0),n&&(e.frequency=void 0)}else e.duration=void 0}var tk=[],tP=class{constructor(){this.changed=!1,this.values=tk,this.toValues=null,this.fromValues=tk,this.config=new t_,this.immediate=!1}};function tS(e,{key:t,props:n,defaultProps:r,state:i,actions:s}){return new Promise((a,o)=>{let f,u,d=tl(n.cancel??r?.cancel,t);if(d)p();else{j.und(n.pause)||(i.paused=tl(n.pause,t));let e=r?.pause;!0!==e&&(e=i.paused||tl(e,t)),f=tu(n.delay||0,t),e?(i.resumeQueue.add(c),s.pause()):(s.resume(),c())}function h(){i.resumeQueue.add(c),i.timeouts.delete(u),u.cancel(),f=u.time-l.now()}function c(){f>0&&!I.skipAnimation?(i.delayed=!0,u=l.setTimeout(p,f),i.pauseQueue.add(h),i.timeouts.add(u)):p()}function p(){i.delayed&&(i.delayed=!1),i.pauseQueue.delete(h),i.timeouts.delete(u),e<=(i.cancelId||0)&&(d=!0);try{s.start({...n,callId:e,cancel:d},a)}catch(e){o(e)}}})}var tA=(e,t)=>1==t.length?t[0]:t.some(e=>e.cancelled)?tI(e.get()):t.every(e=>e.noop)?tC(e.get()):tV(e.get(),t.every(e=>e.finished)),tC=e=>({value:e,noop:!0,finished:!0,cancelled:!1}),tV=(e,t,n=!1)=>({value:e,finished:t,cancelled:n}),tI=e=>({value:e,cancelled:!0,finished:!1});function tR(e,t,n,r){let{callId:i,parentId:s,onRest:a}=t,{asyncTo:o,promise:f}=n;return s||e!==o||t.reset?n.promise=(async()=>{let u,d,h;n.asyncId=i,n.asyncTo=e;let c=tp(t,(e,t)=>"onRest"===t?void 0:e),p=new Promise((e,t)=>(u=e,d=t)),m=e=>{let t=i<=(n.cancelId||0)&&tI(r)||i!==n.asyncId&&tV(r,!1);if(t)throw e.result=t,d(e),e},g=(e,t)=>{let s=new tj,a=new tM;return(async()=>{if(I.skipAnimation)throw tE(n),a.result=tV(r,!1),d(a),a;m(s);let o=j.obj(e)?{...e}:{...t,to:e};o.parentId=i,z(c,(e,t)=>{j.und(o[t])&&(o[t]=e)});let f=await r.start(o);return m(s),n.paused&&await new Promise(e=>{n.resumeQueue.add(e)}),f})()};if(I.skipAnimation)return tE(n),tV(r,!1);try{let t;t=j.arr(e)?(async e=>{for(let t of e)await g(t)})(e):Promise.resolve(e(g,r.stop.bind(r))),await Promise.all([t.then(u),p]),h=tV(r.get(),!0,!1)}catch(e){if(e instanceof tj)h=e.result;else if(e instanceof tM)h=e.result;else throw e}finally{i==n.asyncId&&(n.asyncId=s,n.asyncTo=s?o:void 0,n.promise=s?f:void 0)}return j.fun(a)&&l.batchedUpdates(()=>{a(h,r,r.item)}),h})():f}function tE(e,t){O(e.timeouts,e=>e.cancel()),e.pauseQueue.clear(),e.resumeQueue.clear(),e.asyncId=e.asyncTo=e.promise=void 0,t&&(e.cancelId=t)}var tj=class extends Error{constructor(){super("An async animation has been interrupted. You see this error because you forgot to use `await` or `.catch(...)` on its returned promise.")}},tM=class extends Error{constructor(){super("SkipAnimationSignal")}},tq=e=>e instanceof tF,tz=1,tF=class extends eP{constructor(){super(...arguments),this.id=tz++,this._priority=0}get priority(){return this._priority}set priority(e){this._priority!=e&&(this._priority=e,this._onPriorityChange(e))}get(){let e=e5(this);return e&&e.getValue()}to(...e){return I.to(this,e)}interpolate(...e){return eQ(`${e$}The "interpolate" function is deprecated in v9 (use "to" instead)`),I.to(this,e)}toJSON(){return this.get()}observerAdded(e){1==e&&this._attach()}observerRemoved(e){0==e&&this._detach()}_attach(){}_detach(){}_onChange(e,t=!1){ek(this,{type:"change",parent:this,value:e,idle:t})}_onPriorityChange(e){this.idle||B.sort(this),ek(this,{type:"priority",parent:this,priority:e})}},tO=Symbol.for("SpringPhase"),t$=e=>(1&e[tO])>0,tT=e=>(2&e[tO])>0,tQ=e=>(4&e[tO])>0,tN=(e,t)=>t?e[tO]|=3:e[tO]&=-3,tU=(e,t)=>t?e[tO]|=4:e[tO]&=-5,tL=class extends tF{constructor(e,t){if(super(),this.animation=new tP,this.defaultProps={},this._state={paused:!1,delayed:!1,pauseQueue:new Set,resumeQueue:new Set,timeouts:new Set},this._pendingCalls=new Set,this._lastCallId=0,this._lastToId=0,this._memoizedDuration=0,!j.und(e)||!j.und(t)){let n=j.obj(e)?{...e}:{...t,from:e};j.und(n.default)&&(n.default=!0),this.start(n)}}get idle(){return!(tT(this)||this._state.asyncTo)||tQ(this)}get goal(){return e_(this.animation.to)}get velocity(){let e=e5(this);return e instanceof e4?e.lastVelocity||0:e.getPayload().map(e=>e.lastVelocity||0)}get hasAnimated(){return t$(this)}get isAnimating(){return tT(this)}get isPaused(){return tQ(this)}get isDelayed(){return this._state.delayed}advance(e){let t=!0,n=!1,r=this.animation,{toValues:i}=r,{config:s}=r,a=e3(r.to);!a&&eb(r.to)&&(i=F(e_(r.to))),r.values.forEach((o,f)=>{if(o.done)return;let u=o.constructor==e7?1:a?a[f].lastPosition:i[f],l=r.immediate,d=u;if(!l){let t;if(d=o.lastPosition,s.tension<=0){o.done=!0;return}let n=o.elapsedTime+=e,i=r.fromValues[f],a=null!=o.v0?o.v0:o.v0=j.arr(s.velocity)?s.velocity[f]:s.velocity,h=s.precision||(i==u?.005:Math.min(1,.001*Math.abs(u-i)));if(j.und(s.duration))if(s.decay){let e=!0===s.decay?.998:s.decay,r=Math.exp(-(1-e)*n);d=i+a/(1-e)*(1-r),l=Math.abs(o.lastPosition-d)<=h,t=a*r}else{t=null==o.lastVelocity?a:o.lastVelocity;let n=s.restVelocity||h/10,r=s.clamp?0:s.bounce,f=!j.und(r),c=i==u?o.v0>0:i<u,p=!1,m=Math.ceil(e/1);for(let e=0;e<m&&!(!(Math.abs(t)>n)&&(l=Math.abs(u-d)<=h));++e){f&&(d==u||d>u==c)&&(t=-t*r,d=u);let e=(-(1e-6*s.tension)*(d-u)+-(.001*s.friction)*t)/s.mass;t+=+e,d+=+t}}else{let r=1;s.duration>0&&(this._memoizedDuration!==s.duration&&(this._memoizedDuration=s.duration,o.durationProgress>0&&(o.elapsedTime=s.duration*o.durationProgress,n=o.elapsedTime+=e)),o.durationProgress=r=(r=(s.progress||0)+n/this._memoizedDuration)>1?1:r<0?0:r),t=((d=i+s.easing(r)*(u-i))-o.lastPosition)/e,l=1==r}o.lastVelocity=t,Number.isNaN(d)&&(console.warn("Got NaN while animating:",this),l=!0)}a&&!a[f].done&&(l=!1),l?o.done=!0:t=!1,o.setValue(d,s.round)&&(n=!0)});let o=e5(this),f=o.getValue();if(t){let e=e_(r.to);(f!==e||n)&&!s.decay?(o.setValue(e),this._onChange(e)):n&&s.decay&&this._onChange(f),this._stop()}else n&&this._onChange(f)}set(e){return l.batchedUpdates(()=>{this._stop(),this._focus(e),this._set(e)}),this}pause(){this._update({pause:!0})}resume(){this._update({pause:!1})}finish(){if(tT(this)){let{to:e,config:t}=this.animation;l.batchedUpdates(()=>{this._onStart(),t.decay||this._set(e,!1),this._stop()})}return this}update(e){return(this.queue||(this.queue=[])).push(e),this}start(e,t){let n;return j.und(e)?(n=this.queue||[],this.queue=[]):n=[j.obj(e)?e:{...t,to:e}],Promise.all(n.map(e=>this._update(e))).then(e=>tA(this,e))}stop(e){let{to:t}=this.animation;return this._focus(this.get()),tE(this._state,e&&this._lastCallId),l.batchedUpdates(()=>this._stop(t,e)),this}reset(){this._update({reset:!0})}eventObserved(e){"change"==e.type?this._start():"priority"==e.type&&(this.priority=e.priority+1)}_prepareNode(e){let t=this.key||"",{to:n,from:r}=e;(null==(n=j.obj(n)?n[t]:n)||tx(n))&&(n=void 0),null==(r=j.obj(r)?r[t]:r)&&(r=void 0);let i={to:n,from:r};return!t$(this)&&(e.reverse&&([n,r]=[r,n]),r=e_(r),j.und(r)?e5(this)||this._set(n):this._set(r)),i}_update({...e},t){let{key:n,defaultProps:r}=this;e.default&&Object.assign(r,tp(e,(e,t)=>/^on/.test(t)?td(e,n):e)),tG(this,e,"onProps"),tJ(this,"onProps",e,this);let i=this._prepareNode(e);if(Object.isFrozen(this))throw Error("Cannot animate a `SpringValue` object that is frozen. Did you forget to pass your component to `animated(...)` before animating its props?");let s=this._state;return tS(++this._lastCallId,{key:n,props:e,defaultProps:r,state:s,actions:{pause:()=>{tQ(this)||(tU(this,!0),$(s.pauseQueue),tJ(this,"onPause",tV(this,tD(this,this.animation.to)),this))},resume:()=>{tQ(this)&&(tU(this,!1),tT(this)&&this._resume(),$(s.resumeQueue),tJ(this,"onResume",tV(this,tD(this,this.animation.to)),this))},start:this._merge.bind(this,i)}}).then(n=>{if(e.loop&&n.finished&&!(t&&n.noop)){let t=tW(e);if(t)return this._update(t,!0)}return n})}_merge(e,t,n){if(t.cancel)return this.stop(!0),n(tI(this));let r=!j.und(e.to),i=!j.und(e.from);if(r||i)if(!(t.callId>this._lastToId))return n(tI(this));else this._lastToId=t.callId;let{key:s,defaultProps:a,animation:o}=this,{to:f,from:u}=o,{to:d=f,from:h=u}=e;i&&!r&&(!t.default||j.und(d))&&(d=h),t.reverse&&([d,h]=[h,d]);let c=!M(h,u);c&&(o.from=h),h=e_(h);let p=!M(d,f);p&&this._focus(d);let m=tx(t.to),{config:g}=o,{decay:y,velocity:v}=g;(r||i)&&(g.velocity=0),t.config&&!m&&function(e,t,n){for(let r in n&&(tw(n={...n},t),t={...n,...t}),tw(e,t),Object.assign(e,t),tb)null==e[r]&&(e[r]=tb[r]);let{frequency:r,damping:i}=e,{mass:s}=e;j.und(r)||(r<.01&&(r=.01),i<0&&(i=0),e.tension=Math.pow(2*Math.PI/r,2)*s,e.friction=4*Math.PI*i*s/r)}(g,tu(t.config,s),t.config!==a.config?tu(a.config,s):void 0);let x=e5(this);if(!x||j.und(d))return n(tV(this,!0));let b=j.und(t.reset)?i&&!t.default:!j.und(h)&&tl(t.reset,s),_=b?h:this.get(),w=tv(d),k=j.num(w)||j.arr(w)||eU(w),P=!m&&(!k||tl(a.immediate||t.immediate,s));if(p){let e=ti(d);if(e!==x.constructor)if(P)x=this._set(w);else throw Error(`Cannot animate between ${x.constructor.name} and ${e.name}, as the "to" prop suggests`)}let S=x.constructor,A=eb(d),C=!1;if(!A){let e=b||!t$(this)&&c;(p||e)&&(A=!(C=M(tv(_),w))),(M(o.immediate,P)||P)&&M(g.decay,y)&&M(g.velocity,v)||(A=!0)}if(C&&tT(this)&&(o.changed&&!b?A=!0:A||this._stop(f)),!m&&((A||eb(f))&&(o.values=x.getPayload(),o.toValues=eb(d)?null:S==e7?[1]:F(w)),o.immediate!=P&&(o.immediate=P,P||b||this._set(f)),A)){let{onRest:e}=o;q(tB,e=>tG(this,t,e));let r=tV(this,tD(this,f));$(this._pendingCalls,r),this._pendingCalls.add(n),o.changed&&l.batchedUpdates(()=>{o.changed=!b,e?.(r,this),b?tu(a.onRest,r):o.onStart?.(r,this)})}b&&this._set(_),m?n(tR(t.to,t,this._state,this)):A?this._start():tT(this)&&!p?this._pendingCalls.add(n):n(tC(_))}_focus(e){let t=this.animation;e!==t.to&&(ew(this)&&this._detach(),t.to=e,ew(this)&&this._attach())}_attach(){let e=0,{to:t}=this.animation;eb(t)&&(eA(t,this),tq(t)&&(e=t.priority+1)),this.priority=e}_detach(){let{to:e}=this.animation;eb(e)&&eC(e,this)}_set(e,t=!0){let n=e_(e);if(!j.und(n)){let e=e5(this);if(!e||!M(n,e.getValue())){let r=ti(n);e&&e.constructor==r?e.setValue(n):e9(this,r.create(n)),e&&l.batchedUpdates(()=>{this._onChange(n,t)})}}return e5(this)}_onStart(){let e=this.animation;e.changed||(e.changed=!0,tJ(this,"onStart",tV(this,tD(this,e.to)),this))}_onChange(e,t){t||(this._onStart(),tu(this.animation.onChange,e,this)),tu(this.defaultProps.onChange,e,this),super._onChange(e,t)}_start(){let e=this.animation;e5(this).reset(e_(e.to)),e.immediate||(e.fromValues=e.values.map(e=>e.lastPosition)),!tT(this)&&(tN(this,!0),tQ(this)||this._resume())}_resume(){I.skipAnimation?this.finish():B.start(this)}_stop(e,t){if(tT(this)){tN(this,!1);let n=this.animation;q(n.values,e=>{e.done=!0}),n.toValues&&(n.onChange=n.onPause=n.onResume=void 0),ek(this,{type:"idle",parent:this});let r=t?tI(this.get()):tV(this.get(),tD(this,e??n.to));$(this._pendingCalls,r),n.changed&&(n.changed=!1,tJ(this,"onRest",r,this))}}};function tD(e,t){let n=tv(t);return M(tv(e.get()),n)}function tW(e,t=e.loop,n=e.to){let r=tu(t);if(r){let i=!0!==r&&ty(r),s=(i||e).reverse,a=!i||i.reset;return tZ({...e,loop:t,default:!1,pause:void 0,to:!s||tx(n)?n:void 0,from:a?e.from:void 0,reset:a,...i})}}function tZ(e){let{to:t,from:n}=e=ty(e),r=new Set;return j.obj(t)&&tH(t,r),j.obj(n)&&tH(n,r),e.keys=r.size?Array.from(r):null,e}function tH(e,t){z(e,(e,n)=>null!=e&&t.add(n))}var tB=["onStart","onRest","onChange","onPause","onResume"];function tG(e,t,n){e.animation[n]=t[n]!==th(t,n)?td(t[n],e.key):void 0}function tJ(e,t,...n){e.animation[t]?.(...n),e.defaultProps[t]?.(...n)}var tX=["onStart","onChange","onRest"],tY=1,tK=class{constructor(e,t){this.id=tY++,this.springs={},this.queue=[],this._lastAsyncId=0,this._active=new Set,this._changed=new Set,this._started=!1,this._state={paused:!1,pauseQueue:new Set,resumeQueue:new Set,timeouts:new Set},this._events={onStart:new Map,onChange:new Map,onRest:new Map},this._onFrame=this._onFrame.bind(this),t&&(this._flush=t),e&&this.start({default:!0,...e})}get idle(){return!this._state.asyncTo&&Object.values(this.springs).every(e=>e.idle&&!e.isDelayed&&!e.isPaused)}get item(){return this._item}set item(e){this._item=e}get(){let e={};return this.each((t,n)=>e[n]=t.get()),e}set(e){for(let t in e){let n=e[t];j.und(n)||this.springs[t].set(n)}}update(e){return e&&this.queue.push(tZ(e)),this}start(e){let{queue:t}=this;return(e?t=F(e).map(tZ):this.queue=[],this._flush)?this._flush(this,t):(t3(this,t),t0(this,t))}stop(e,t){if(!!e!==e&&(t=e),t){let n=this.springs;q(F(t),t=>n[t].stop(!!e))}else tE(this._state,this._lastAsyncId),this.each(t=>t.stop(!!e));return this}pause(e){if(j.und(e))this.start({pause:!0});else{let t=this.springs;q(F(e),e=>t[e].pause())}return this}resume(e){if(j.und(e))this.start({pause:!1});else{let t=this.springs;q(F(e),e=>t[e].resume())}return this}each(e){z(this.springs,e)}_onFrame(){let{onStart:e,onChange:t,onRest:n}=this._events,r=this._active.size>0,i=this._changed.size>0;(r&&!this._started||i&&!this._started)&&(this._started=!0,O(e,([e,t])=>{t.value=this.get(),e(t,this,this._item)}));let s=!r&&this._started,a=i||s&&n.size?this.get():null;i&&t.size&&O(t,([e,t])=>{t.value=a,e(t,this,this._item)}),s&&(this._started=!1,O(n,([e,t])=>{t.value=a,e(t,this,this._item)}))}eventObserved(e){if("change"==e.type)this._changed.add(e.parent),e.idle||this._active.add(e.parent);else{if("idle"!=e.type)return;this._active.delete(e.parent)}l.onFrame(this._onFrame)}};function t0(e,t){return Promise.all(t.map(t=>t1(e,t))).then(t=>tA(e,t))}async function t1(e,t,n){let{keys:r,to:i,from:s,loop:a,onRest:o,onResolve:f}=t,u=j.obj(t.default)&&t.default;a&&(t.loop=!1),!1===i&&(t.to=null),!1===s&&(t.from=null);let d=j.arr(i)||j.fun(i)?i:void 0;d?(t.to=void 0,t.onRest=void 0,u&&(u.onRest=void 0)):q(tX,n=>{let r=t[n];if(j.fun(r)){let i=e._events[n];t[n]=({finished:e,cancelled:t})=>{let n=i.get(r);n?(e||(n.finished=!1),t&&(n.cancelled=!0)):i.set(r,{value:null,finished:e||!1,cancelled:t||!1})},u&&(u[n]=t[n])}});let h=e._state;!h.paused===t.pause?(h.paused=t.pause,$(t.pause?h.pauseQueue:h.resumeQueue)):h.paused&&(t.pause=!0);let c=(r||Object.keys(e.springs)).map(n=>e.springs[n].start(t)),p=!0===t.cancel||!0===th(t,"cancel");(d||p&&h.asyncId)&&c.push(tS(++e._lastAsyncId,{props:t,state:h,actions:{pause:R,resume:R,start(t,n){p?(tE(h,e._lastAsyncId),n(tI(e))):(t.onRest=o,n(tR(d,t,h,e)))}}})),h.paused&&await new Promise(e=>{h.resumeQueue.add(e)});let m=tA(e,await Promise.all(c));if(a&&m.finished&&!(n&&m.noop)){let n=tW(t,a,i);if(n)return t3(e,[n]),t1(e,n,!0)}return f&&l.batchedUpdates(()=>f(m,e,e.item)),m}function t2(e,t){let n={...e.springs};return t&&q(F(t),e=>{j.und(e.keys)&&(e=tZ(e)),j.obj(e.to)||(e={...e,to:void 0}),t9(n,e,e=>t5(e))}),t8(e,n),n}function t8(e,t){z(t,(t,n)=>{e.springs[n]||(e.springs[n]=t,eA(t,e))})}function t5(e,t){let n=new tL;return n.key=e,t&&eA(n,t),n}function t9(e,t,n){t.keys&&q(t.keys,r=>{(e[r]||(e[r]=n(r)))._prepareNode(t)})}function t3(e,t){q(t,t=>{t9(e.springs,t,t=>t5(t,e))})}var t6=({children:e,...t})=>{let n=(0,C.useContext)(t4),r=t.pause||!!n.pause,i=t.immediate||!!n.immediate;t=function(e,t){let[n]=(0,C.useState)(()=>({inputs:t,result:e()})),r=(0,C.useRef)(),i=r.current,s=i;return s?t&&s.inputs&&function(e,t){if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}(t,s.inputs)||(s={inputs:t,result:e()}):s=n,(0,C.useEffect)(()=>{r.current=s,i==n&&(n.inputs=n.result=void 0)},[s]),s.result}(()=>({pause:r,immediate:i}),[r,i]);let{Provider:s}=t4;return C.createElement(s,{value:t},e)},t4=function(e,t){return Object.assign(e,C.createContext(t)),e.Provider._context=e,e.Consumer._context=e,e}(t6,{});t6.Provider=t4.Provider,t6.Consumer=t4.Consumer;var t7=()=>{let e=[],t=function(t){eN(`${e$}Directly calling start instead of using the api object is deprecated in v9 (use ".start" instead), this will be removed in later 0.X.0 versions`);let r=[];return q(e,(e,i)=>{if(j.und(t))r.push(e.start());else{let s=n(t,e,i);s&&r.push(e.start(s))}}),r};t.current=e,t.add=function(t){e.includes(t)||e.push(t)},t.delete=function(t){let n=e.indexOf(t);~n&&e.splice(n,1)},t.pause=function(){return q(e,e=>e.pause(...arguments)),this},t.resume=function(){return q(e,e=>e.resume(...arguments)),this},t.set=function(t){q(e,(e,n)=>{let r=j.fun(t)?t(n,e):t;r&&e.set(r)})},t.start=function(t){let n=[];return q(e,(e,r)=>{if(j.und(t))n.push(e.start());else{let i=this._getProps(t,e,r);i&&n.push(e.start(i))}}),n},t.stop=function(){return q(e,e=>e.stop(...arguments)),this},t.update=function(t){return q(e,(e,n)=>e.update(this._getProps(t,e,n))),this};let n=function(e,t,n){return j.fun(e)?e(n,t):e};return t._getProps=n,t};function ne(e,t){let n=j.fun(e),[[r],i]=function(e,t,n){let r=j.fun(t)&&t;r&&!n&&(n=[]);let i=(0,C.useMemo)(()=>r||3==arguments.length?t7():void 0,[]),s=(0,C.useRef)(0),a=eY(),o=(0,C.useMemo)(()=>({ctrls:[],queue:[],flush(e,t){let n=t2(e,t);return!(s.current>0)||o.queue.length||Object.keys(n).some(t=>!e.springs[t])?new Promise(r=>{t8(e,n),o.queue.push(()=>{r(t0(e,t))}),a()}):t0(e,t)}}),[]),f=(0,C.useRef)([...o.ctrls]),u=[],l=e1(e)||0;function d(e,n){for(let i=e;i<n;i++){let e=f.current[i]||(f.current[i]=new tK(null,o.flush)),n=r?r(i,e):t[i];n&&(u[i]=function(e){let t=tZ(e);return j.und(t.default)&&(t.default=tp(t)),t}(n))}}(0,C.useMemo)(()=>{q(f.current.slice(e,l),e=>{var t,n;t=e,n=i,t.ref?.delete(t),n?.delete(t),e.stop(!0)}),f.current.length=e,d(l,e)},[e]),(0,C.useMemo)(()=>{d(0,Math.min(l,e))},n);let h=f.current.map((e,t)=>t2(e,u[t])),c=(0,C.useContext)(t6),p=e1(c),m=c!==p&&function(e){for(let t in e)return!0;return!1}(c);eJ(()=>{s.current++,o.ctrls=f.current;let{queue:e}=o;e.length&&(o.queue=[],q(e,e=>e())),q(f.current,(e,t)=>{var n,r;i?.add(e),m&&e.start({default:c});let s=u[t];s&&(n=e,(r=s.ref)&&n.ref!==r&&(n.ref?.delete(n),r.add(n),n.ref=r),e.ref?e.queue.push(s):e.start(s))})}),eK(()=>()=>{q(o.ctrls,e=>e.stop(!0))});let g=h.map(e=>({...e}));return i?[g,i]:g}(1,n?e:[e],n?t||[]:t);return n||2==arguments.length?[r,i]:r}var nt=class extends tF{constructor(e,t){super(),this.source=e,this.idle=!0,this._active=new Set,this.calc=ey(...t);let n=this._get();e9(this,ti(n).create(n))}advance(e){let t=this._get();M(t,this.get())||(e5(this).setValue(t),this._onChange(t,this.idle)),!this.idle&&nr(this._active)&&ni(this)}_get(){let e=j.arr(this.source)?this.source.map(e_):F(e_(this.source));return this.calc(...e)}_start(){this.idle&&!nr(this._active)&&(this.idle=!1,q(e3(this),e=>{e.done=!1}),I.skipAnimation?(l.batchedUpdates(()=>this.advance()),ni(this)):B.start(this))}_attach(){let e=1;q(F(this.source),t=>{eb(t)&&eA(t,this),tq(t)&&(t.idle||this._active.add(t),e=Math.max(e,t.priority+1))}),this.priority=e,this._start()}_detach(){q(F(this.source),e=>{eb(e)&&eC(e,this)}),this._active.clear(),ni(this)}eventObserved(e){"change"==e.type?e.idle?this.advance():(this._active.add(e.parent),this._start()):"idle"==e.type?this._active.delete(e.parent):"priority"==e.type&&(this.priority=F(this.source).reduce((e,t)=>Math.max(e,(tq(t)?t.priority:0)+1),0))}};function nn(e){return!1!==e.idle}function nr(e){return!e.size||Array.from(e).every(nn)}function ni(e){e.idle||(e.idle=!0,q(e3(e),e=>{e.done=!0}),ek(e,{type:"idle",parent:e}))}I.assign({createStringInterpolator:eO,to:(e,t)=>new nt(e,t)}),B.advance;var ns=["primitive"].concat(Object.keys(n(97431)).filter(e=>/^[A-Z]/.test(e)).map(e=>e[0].toLowerCase()+e.slice(1)));I.assign({createStringInterpolator:eO,colors:{transparent:0,aliceblue:0xf0f8ffff,antiquewhite:0xfaebd7ff,aqua:0xffffff,aquamarine:0x7fffd4ff,azure:0xf0ffffff,beige:0xf5f5dcff,bisque:0xffe4c4ff,black:255,blanchedalmond:0xffebcdff,blue:65535,blueviolet:0x8a2be2ff,brown:0xa52a2aff,burlywood:0xdeb887ff,burntsienna:0xea7e5dff,cadetblue:0x5f9ea0ff,chartreuse:0x7fff00ff,chocolate:0xd2691eff,coral:0xff7f50ff,cornflowerblue:0x6495edff,cornsilk:0xfff8dcff,crimson:0xdc143cff,cyan:0xffffff,darkblue:35839,darkcyan:9145343,darkgoldenrod:0xb8860bff,darkgray:0xa9a9a9ff,darkgreen:6553855,darkgrey:0xa9a9a9ff,darkkhaki:0xbdb76bff,darkmagenta:0x8b008bff,darkolivegreen:0x556b2fff,darkorange:0xff8c00ff,darkorchid:0x9932ccff,darkred:0x8b0000ff,darksalmon:0xe9967aff,darkseagreen:0x8fbc8fff,darkslateblue:0x483d8bff,darkslategray:0x2f4f4fff,darkslategrey:0x2f4f4fff,darkturquoise:0xced1ff,darkviolet:0x9400d3ff,deeppink:0xff1493ff,deepskyblue:0xbfffff,dimgray:0x696969ff,dimgrey:0x696969ff,dodgerblue:0x1e90ffff,firebrick:0xb22222ff,floralwhite:0xfffaf0ff,forestgreen:0x228b22ff,fuchsia:0xff00ffff,gainsboro:0xdcdcdcff,ghostwhite:0xf8f8ffff,gold:0xffd700ff,goldenrod:0xdaa520ff,gray:0x808080ff,green:8388863,greenyellow:0xadff2fff,grey:0x808080ff,honeydew:0xf0fff0ff,hotpink:0xff69b4ff,indianred:0xcd5c5cff,indigo:0x4b0082ff,ivory:0xfffff0ff,khaki:0xf0e68cff,lavender:0xe6e6faff,lavenderblush:0xfff0f5ff,lawngreen:0x7cfc00ff,lemonchiffon:0xfffacdff,lightblue:0xadd8e6ff,lightcoral:0xf08080ff,lightcyan:0xe0ffffff,lightgoldenrodyellow:0xfafad2ff,lightgray:0xd3d3d3ff,lightgreen:0x90ee90ff,lightgrey:0xd3d3d3ff,lightpink:0xffb6c1ff,lightsalmon:0xffa07aff,lightseagreen:0x20b2aaff,lightskyblue:0x87cefaff,lightslategray:0x778899ff,lightslategrey:0x778899ff,lightsteelblue:0xb0c4deff,lightyellow:0xffffe0ff,lime:0xff00ff,limegreen:0x32cd32ff,linen:0xfaf0e6ff,magenta:0xff00ffff,maroon:0x800000ff,mediumaquamarine:0x66cdaaff,mediumblue:52735,mediumorchid:0xba55d3ff,mediumpurple:0x9370dbff,mediumseagreen:0x3cb371ff,mediumslateblue:0x7b68eeff,mediumspringgreen:0xfa9aff,mediumturquoise:0x48d1ccff,mediumvioletred:0xc71585ff,midnightblue:0x191970ff,mintcream:0xf5fffaff,mistyrose:0xffe4e1ff,moccasin:0xffe4b5ff,navajowhite:0xffdeadff,navy:33023,oldlace:0xfdf5e6ff,olive:0x808000ff,olivedrab:0x6b8e23ff,orange:0xffa500ff,orangered:0xff4500ff,orchid:0xda70d6ff,palegoldenrod:0xeee8aaff,palegreen:0x98fb98ff,paleturquoise:0xafeeeeff,palevioletred:0xdb7093ff,papayawhip:0xffefd5ff,peachpuff:0xffdab9ff,peru:0xcd853fff,pink:0xffc0cbff,plum:0xdda0ddff,powderblue:0xb0e0e6ff,purple:0x800080ff,rebeccapurple:0x663399ff,red:0xff0000ff,rosybrown:0xbc8f8fff,royalblue:0x4169e1ff,saddlebrown:0x8b4513ff,salmon:0xfa8072ff,sandybrown:0xf4a460ff,seagreen:0x2e8b57ff,seashell:0xfff5eeff,sienna:0xa0522dff,silver:0xc0c0c0ff,skyblue:0x87ceebff,slateblue:0x6a5acdff,slategray:0x708090ff,slategrey:0x708090ff,snow:0xfffafaff,springgreen:0xff7fff,steelblue:0x4682b4ff,tan:0xd2b48cff,teal:8421631,thistle:0xd8bfd8ff,tomato:0xff6347ff,turquoise:0x40e0d0ff,violet:0xee82eeff,wheat:0xf5deb3ff,white:0xffffffff,whitesmoke:0xf5f5f5ff,yellow:0xffff00ff,yellowgreen:0x9acd32ff},frameLoop:"demand"}),(0,f.o)(()=>{l.advance()}),((e,{applyAnimatedValues:t=()=>!1,createAnimatedStyle:n=e=>new tt(e),getComponentProps:r=e=>e}={})=>{let i={applyAnimatedValues:t,createAnimatedStyle:n,getComponentProps:r},s=e=>{let t=tf(e)||"Anonymous";return(e=j.str(e)?s[e]||(s[e]=ts(e,i)):e[to]||(e[to]=ts(e,i))).displayName=`Animated(${t})`,e};return z(e,(t,n)=>{j.arr(e)&&(n=tf(t)),s[n]=s(t)}),{animated:s}})(ns,{applyAnimatedValues:f.k}).animated}}]);