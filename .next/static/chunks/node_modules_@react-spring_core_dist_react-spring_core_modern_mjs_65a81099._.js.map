{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/%40react-spring/core/src/hooks/useChain.ts", "file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/%40react-spring/core/src/helpers.ts", "file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/%40react-spring/core/src/hooks/useSpring.ts", "file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/%40react-spring/core/src/hooks/useSprings.ts", "file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/%40react-spring/core/src/SpringValue.ts", "file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/%40react-spring/core/src/AnimationConfig.ts", "file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/%40react-spring/core/src/constants.ts", "file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/%40react-spring/core/src/Animation.ts", "file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/%40react-spring/core/src/scheduleProps.ts", "file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/%40react-spring/core/src/runAsync.ts", "file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/%40react-spring/core/src/AnimationResult.ts", "file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/%40react-spring/core/src/FrameValue.ts", "file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/%40react-spring/core/src/SpringPhase.ts", "file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/%40react-spring/core/src/Controller.ts", "file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/%40react-spring/core/src/SpringContext.tsx", "file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/%40react-spring/core/src/SpringRef.ts", "file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/%40react-spring/core/src/hooks/useSpringRef.ts", "file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/%40react-spring/core/src/hooks/useSpringValue.ts", "file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/%40react-spring/core/src/hooks/useTrail.ts", "file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/%40react-spring/core/src/hooks/useTransition.tsx", "file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/%40react-spring/core/src/hooks/useScroll.ts", "file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/%40react-spring/core/src/hooks/useResize.ts", "file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/%40react-spring/core/src/hooks/useInView.ts", "file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/%40react-spring/core/src/components/Spring.tsx", "file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/%40react-spring/core/src/components/Trail.tsx", "file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/%40react-spring/core/src/components/Transition.tsx", "file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/%40react-spring/core/src/interpolate.ts", "file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/%40react-spring/core/src/Interpolation.ts", "file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/%40react-spring/core/src/globals.ts", "file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/%40react-spring/core/src/index.ts"], "sourcesContent": ["import { each, useIsomorphicLayoutEffect } from '@react-spring/shared'\nimport { SpringRef } from '../SpringRef'\nimport { callProp } from '../helpers'\n\n/**\n * Used to orchestrate animation hooks in sequence with one another.\n * This is best used when you specifically want to orchestrate different\n * types of animation hook e.g. `useSpring` & `useTransition` in\n * sequence as opposed to multiple `useSpring` hooks.\n *\n *\n * ```jsx\n * export const MyComponent = () => {\n *  //...\n *  useChain([springRef, transitionRef])\n *  //...\n * }\n * ```\n *\n * @param refs – An array of `SpringRef`s.\n * @param timeSteps – Optional array of numbers that define the\n * delay between each animation from 0-1. The length should correlate\n * to the length of `refs`.\n * @param timeFrame – Optional number that defines the total duration\n *\n * @public\n */\nexport function useChain(\n  refs: ReadonlyArray<SpringRef>,\n  timeSteps?: number[],\n  timeFrame = 1000\n) {\n  useIsomorphicLayoutEffect(() => {\n    if (timeSteps) {\n      let prevDelay = 0\n      each(refs, (ref, i) => {\n        const controllers = ref.current\n        if (controllers.length) {\n          let delay = timeFrame * timeSteps[i]\n\n          // Use the previous delay if none exists.\n          if (isNaN(delay)) delay = prevDelay\n          else prevDelay = delay\n\n          each(controllers, ctrl => {\n            each(ctrl.queue, props => {\n              // memoizing stops recursion https://github.com/pmndrs/react-spring/issues/1367\n              const memoizedDelayProp = props.delay\n              props.delay = key => delay + callProp(memoizedDelayProp || 0, key)\n            })\n          })\n\n          ref.start()\n        }\n      })\n    } else {\n      let p: Promise<any> = Promise.resolve()\n      each(refs, ref => {\n        const controllers = ref.current\n        if (controllers.length) {\n          // Take the queue of each controller\n          const queues = controllers.map(ctrl => {\n            const q = ctrl.queue\n            ctrl.queue = []\n            return q\n          })\n\n          // Apply the queue when the previous ref stops animating\n          p = p.then(() => {\n            each(controllers, (ctrl, i) =>\n              each(queues[i] || [], update => ctrl.queue.push(update))\n            )\n            return Promise.all(ref.start())\n          })\n        }\n      })\n    }\n  })\n}\n", "import {\n  is,\n  toArray,\n  eachProp,\n  getFluidValue,\n  isAnimatedString,\n  FluidValue,\n  Globals as G,\n} from '@react-spring/shared'\nimport { AnyFn, OneOrMore, Lookup } from '@react-spring/types'\nimport { ReservedProps, ForwardProps, InferTo } from './types'\nimport type { Controller } from './Controller'\nimport type { SpringRef } from './SpringRef'\n\nexport function callProp<T>(\n  value: T,\n  ...args: T extends AnyFn ? Parameters<T> : unknown[]\n): T extends AnyFn<any, infer U> ? U : T {\n  return is.fun(value) ? value(...args) : value\n}\n\n/** Try to coerce the given value into a boolean using the given key */\nexport const matchProp = (\n  value: boolean | OneOrMore<string> | ((key: any) => boolean) | undefined,\n  key: string | undefined\n) =>\n  value === true ||\n  !!(\n    key &&\n    value &&\n    (is.fun(value) ? value(key) : toArray(value).includes(key))\n  )\n\nexport const resolveProp = <T>(\n  prop: T | Lookup<T> | undefined,\n  key: string | undefined\n) => (is.obj(prop) ? key && (prop as any)[key] : prop)\n\nexport const concatFn = <T extends AnyFn>(first: T | undefined, last: T) =>\n  first ? (...args: Parameters<T>) => (first(...args), last(...args)) : last\n\n/** Returns `true` if the given prop is having its default value set. */\nexport const hasDefaultProp = <T extends Lookup>(props: T, key: keyof T) =>\n  !is.und(getDefaultProp(props, key))\n\n/** Get the default value being set for the given `key` */\nexport const getDefaultProp = <T extends Lookup, P extends keyof T>(\n  props: T,\n  key: P\n): T[P] =>\n  props.default === true\n    ? props[key]\n    : props.default\n      ? props.default[key]\n      : undefined\n\nconst noopTransform = (value: any) => value\n\n/**\n * Extract the default props from an update.\n *\n * When the `default` prop is falsy, this function still behaves as if\n * `default: true` was used. The `default` prop is always respected when\n * truthy.\n */\nexport const getDefaultProps = <T extends Lookup>(\n  props: Lookup,\n  transform: (value: any, key: string) => any = noopTransform\n): T => {\n  let keys: readonly string[] = DEFAULT_PROPS\n  if (props.default && props.default !== true) {\n    props = props.default\n    keys = Object.keys(props)\n  }\n  const defaults: any = {}\n  for (const key of keys) {\n    const value = transform(props[key], key)\n    if (!is.und(value)) {\n      defaults[key] = value\n    }\n  }\n  return defaults\n}\n\n/**\n * These props are implicitly used as defaults when defined in a\n * declarative update (eg: render-based) or any update with `default: true`.\n *\n * Use `default: {}` or `default: false` to opt-out of these implicit defaults\n * for any given update.\n *\n * Note: These are not the only props with default values. For example, the\n * `pause`, `cancel`, and `immediate` props. But those must be updated with\n * the object syntax (eg: `default: { immediate: true }`).\n */\nexport const DEFAULT_PROPS = [\n  'config',\n  'onProps',\n  'onStart',\n  'onChange',\n  'onPause',\n  'onResume',\n  'onRest',\n] as const\n\nconst RESERVED_PROPS: {\n  [key: string]: 1 | undefined\n} = {\n  config: 1,\n  from: 1,\n  to: 1,\n  ref: 1,\n  loop: 1,\n  reset: 1,\n  pause: 1,\n  cancel: 1,\n  reverse: 1,\n  immediate: 1,\n  default: 1,\n  delay: 1,\n  onProps: 1,\n  onStart: 1,\n  onChange: 1,\n  onPause: 1,\n  onResume: 1,\n  onRest: 1,\n  onResolve: 1,\n\n  // Transition props\n  items: 1,\n  trail: 1,\n  sort: 1,\n  expires: 1,\n  initial: 1,\n  enter: 1,\n  update: 1,\n  leave: 1,\n  children: 1,\n  onDestroyed: 1,\n\n  // Internal props\n  keys: 1,\n  callId: 1,\n  parentId: 1,\n}\n\n/**\n * Extract any properties whose keys are *not* reserved for customizing your\n * animations. All hooks use this function, which means `useTransition` props\n * are reserved for `useSpring` calls, etc.\n */\nfunction getForwardProps<Props extends ReservedProps>(\n  props: Props\n): ForwardProps<Props> | undefined {\n  const forward: any = {}\n\n  let count = 0\n  eachProp(props, (value, prop) => {\n    if (!RESERVED_PROPS[prop]) {\n      forward[prop] = value\n      count++\n    }\n  })\n\n  if (count) {\n    return forward\n  }\n}\n\n/**\n * Clone the given `props` and move all non-reserved props\n * into the `to` prop.\n */\nexport function inferTo<T extends object>(props: T): InferTo<T> {\n  const to = getForwardProps(props)\n  if (to) {\n    const out: any = { to }\n    eachProp(props, (val, key) => key in to || (out[key] = val))\n    return out\n  }\n  return { ...props } as any\n}\n\n// Compute the goal value, converting \"red\" to \"rgba(255, 0, 0, 1)\" in the process\nexport function computeGoal<T>(value: T | FluidValue<T>): T {\n  value = getFluidValue(value)\n  return is.arr(value)\n    ? value.map(computeGoal)\n    : isAnimatedString(value)\n      ? (G.createStringInterpolator({\n          range: [0, 1],\n          output: [value, value] as any,\n        })(1) as any)\n      : value\n}\n\nexport function hasProps(props: object) {\n  for (const _ in props) return true\n  return false\n}\n\nexport function isAsyncTo(to: any) {\n  return is.fun(to) || (is.arr(to) && is.obj(to[0]))\n}\n\n/** Detach `ctrl` from `ctrl.ref` and (optionally) the given `ref` */\nexport function detachRefs(ctrl: Controller, ref?: SpringRef) {\n  ctrl.ref?.delete(ctrl)\n  ref?.delete(ctrl)\n}\n\n/** Replace `ctrl.ref` with the given `ref` (if defined) */\nexport function replaceRef(ctrl: Controller, ref?: SpringRef) {\n  if (ref && ctrl.ref !== ref) {\n    ctrl.ref?.delete(ctrl)\n    ref.add(ctrl)\n    ctrl.ref = ref\n  }\n}\n", "import { Lookup, Remap } from '@react-spring/types'\nimport { is } from '@react-spring/shared'\n\nimport { ControllerUpdate, PickAnimated, SpringValues } from '../types'\nimport { Valid } from '../types/common'\nimport { SpringRef } from '../SpringRef'\nimport { useSprings } from './useSprings'\n\n/**\n * The props that `useSpring` recognizes.\n */\nexport type UseSpringProps<Props extends object = any> = unknown &\n  PickAnimated<Props> extends infer State\n  ? State extends Lookup\n    ? Remap<\n        ControllerUpdate<State> & {\n          /**\n           * Used to access the imperative API.\n           *\n           * When defined, the render animation won't auto-start.\n           */\n          ref?: SpringRef<State>\n        }\n      >\n    : never\n  : never\n\n/**\n * The `props` function is only called on the first render, unless\n * `deps` change (when defined). State is inferred from forward props.\n */\nexport function useSpring<Props extends object>(\n  props:\n    | Function\n    | (() => (Props & Valid<Props, UseSpringProps<Props>>) | UseSpringProps),\n  deps?: readonly any[] | undefined\n): PickAnimated<Props> extends infer State\n  ? State extends Lookup\n    ? [SpringValues<State>, SpringRef<State>]\n    : never\n  : never\n\n/**\n * Updated on every render, with state inferred from forward props.\n */\nexport function useSpring<Props extends object>(\n  props: (Props & Valid<Props, UseSpringProps<Props>>) | UseSpringProps\n): SpringValues<PickAnimated<Props>>\n\n/**\n * Updated only when `deps` change, with state inferred from forward props.\n */\nexport function useSpring<Props extends object>(\n  props: (Props & Valid<Props, UseSpringProps<Props>>) | UseSpringProps,\n  deps: readonly any[] | undefined\n): PickAnimated<Props> extends infer State\n  ? State extends Lookup\n    ? [SpringValues<State>, SpringRef<State>]\n    : never\n  : never\n\n/** @internal */\nexport function useSpring(props: any, deps?: readonly any[]) {\n  const isFn = is.fun(props)\n  const [[values], ref] = useSprings(\n    1,\n    isFn ? props : [props],\n    isFn ? deps || [] : deps\n  )\n  return isFn || arguments.length == 2 ? [values, ref] : values\n}\n", "import { useContext, useMemo, useRef } from 'react'\nimport { Lookup } from '@react-spring/types'\nimport {\n  is,\n  each,\n  usePrev,\n  useOnce,\n  useForceUpdate,\n  useIsomorphicLayoutEffect,\n} from '@react-spring/shared'\n\nimport {\n  ControllerFlushFn,\n  ControllerUpdate,\n  PickAnimated,\n  SpringValues,\n} from '../types'\nimport { UseSpringProps } from './useSpring'\nimport { declareUpdate } from '../SpringValue'\nimport {\n  Controller,\n  getSprings,\n  flushUpdateQueue,\n  setSprings,\n} from '../Controller'\nimport { hasProps, detachRefs, replaceRef } from '../helpers'\nimport { SpringContext } from '../SpringContext'\nimport { SpringRef } from '../SpringRef'\nimport type { SpringRef as SpringRefType } from '../SpringRef'\n\nexport type UseSpringsProps<State extends Lookup = Lookup> = unknown &\n  ControllerUpdate<State> & {\n    ref?: SpringRefType<State>\n  }\n\n/**\n * When the `deps` argument exists, the `props` function is called whenever\n * the `deps` change on re-render.\n *\n * Without the `deps` argument, the `props` function is only called once.\n */\nexport function useSprings<Props extends UseSpringProps>(\n  length: number,\n  props: (i: number, ctrl: Controller) => Props,\n  deps?: readonly any[]\n): PickAnimated<Props> extends infer State\n  ? State extends Lookup<any>\n    ? [SpringValues<State>[], SpringRefType<State>]\n    : never\n  : never\n\n/**\n * Animations are updated on re-render.\n */\nexport function useSprings<Props extends UseSpringsProps>(\n  length: number,\n  props: Props[] & UseSpringsProps<PickAnimated<Props>>[]\n): SpringValues<PickAnimated<Props>>[]\n\n/**\n * When the `deps` argument exists, you get the `update` and `stop` function.\n */\nexport function useSprings<Props extends UseSpringsProps>(\n  length: number,\n  props: Props[] & UseSpringsProps<PickAnimated<Props>>[],\n  deps: readonly any[] | undefined\n): PickAnimated<Props> extends infer State\n  ? State extends Lookup<any>\n    ? [SpringValues<State>[], SpringRefType<State>]\n    : never\n  : never\n\n/** @internal */\nexport function useSprings(\n  length: number,\n  props: any[] | ((i: number, ctrl: Controller) => any),\n  deps?: readonly any[]\n): any {\n  const propsFn = is.fun(props) && props\n  if (propsFn && !deps) deps = []\n\n  // Create a local ref if a props function or deps array is ever passed.\n  const ref = useMemo(\n    () => (propsFn || arguments.length == 3 ? SpringRef() : void 0),\n    []\n  )\n\n  interface State {\n    // The controllers used for applying updates.\n    ctrls: Controller[]\n    // The queue of changes to make on commit.\n    queue: Array<() => void>\n    // The flush function used by controllers.\n    flush: ControllerFlushFn\n  }\n\n  // Set to 0 to prevent sync flush.\n  const layoutId = useRef(0)\n  const forceUpdate = useForceUpdate()\n\n  // State is updated on commit.\n  const state = useMemo(\n    (): State => ({\n      ctrls: [],\n      queue: [],\n      flush(ctrl, updates) {\n        const springs = getSprings(ctrl, updates)\n\n        // Flushing is postponed until the component's commit phase\n        // if a spring was created since the last commit.\n        const canFlushSync =\n          layoutId.current > 0 &&\n          !state.queue.length &&\n          !Object.keys(springs).some(key => !ctrl.springs[key])\n\n        return canFlushSync\n          ? flushUpdateQueue(ctrl, updates)\n          : new Promise<any>(resolve => {\n              setSprings(ctrl, springs)\n              state.queue.push(() => {\n                resolve(flushUpdateQueue(ctrl, updates))\n              })\n              forceUpdate()\n            })\n      },\n    }),\n    []\n  )\n\n  const ctrls = useRef([...state.ctrls])\n  const updates: any[] = []\n\n  // Cache old controllers to dispose in the commit phase.\n  const prevLength = usePrev(length) || 0\n\n  // Create new controllers when \"length\" increases, and destroy\n  // the affected controllers when \"length\" decreases.\n  useMemo(() => {\n    // Clean up any unused controllers\n    each(ctrls.current.slice(length, prevLength), ctrl => {\n      detachRefs(ctrl, ref)\n      ctrl.stop(true)\n    })\n    ctrls.current.length = length\n\n    declareUpdates(prevLength, length)\n  }, [length])\n\n  // Update existing controllers when \"deps\" are changed.\n  useMemo(() => {\n    declareUpdates(0, Math.min(prevLength, length))\n  }, deps)\n\n  /** Fill the `updates` array with declarative updates for the given index range. */\n  function declareUpdates(startIndex: number, endIndex: number) {\n    for (let i = startIndex; i < endIndex; i++) {\n      const ctrl =\n        ctrls.current[i] ||\n        (ctrls.current[i] = new Controller(null, state.flush))\n\n      const update: UseSpringProps<any> = propsFn\n        ? propsFn(i, ctrl)\n        : (props as any)[i]\n\n      if (update) {\n        updates[i] = declareUpdate(update)\n      }\n    }\n  }\n\n  // New springs are created during render so users can pass them to\n  // their animated components, but new springs aren't cached until the\n  // commit phase (see the `useIsomorphicLayoutEffect` callback below).\n  const springs = ctrls.current.map((ctrl, i) => getSprings(ctrl, updates[i]))\n\n  const context = useContext(SpringContext)\n  const prevContext = usePrev(context)\n  const hasContext = context !== prevContext && hasProps(context)\n\n  useIsomorphicLayoutEffect(() => {\n    layoutId.current++\n\n    // Replace the cached controllers.\n    state.ctrls = ctrls.current\n\n    // Flush the commit queue.\n    const { queue } = state\n    if (queue.length) {\n      state.queue = []\n      each(queue, cb => cb())\n    }\n\n    // Update existing controllers.\n    each(ctrls.current, (ctrl, i) => {\n      // Attach the controller to the local ref.\n      ref?.add(ctrl)\n\n      // Update the default props.\n      if (hasContext) {\n        ctrl.start({ default: context })\n      }\n\n      // Apply updates created during render.\n      const update = updates[i]\n      if (update) {\n        // Update the injected ref if needed.\n        replaceRef(ctrl, update.ref)\n\n        // When an injected ref exists, the update is postponed\n        // until the ref has its `start` method called.\n        if (ctrl.ref) {\n          ctrl.queue.push(update)\n        } else {\n          ctrl.start(update)\n        }\n      }\n    })\n  })\n\n  // Cancel the animations of all controllers on unmount.\n  useOnce(() => () => {\n    each(state.ctrls, ctrl => ctrl.stop(true))\n  })\n\n  // Return a deep copy of the `springs` array so the caller can\n  // safely mutate it during render.\n  const values = springs.map(x => ({ ...x }))\n\n  return ref ? [values, ref] : values\n}\n", "import {\n  is,\n  raf,\n  each,\n  isEqual,\n  toArray,\n  eachProp,\n  frameLoop,\n  flushCalls,\n  getFluidValue,\n  isAnimatedString,\n  FluidValue,\n  Globals as G,\n  callFluidObservers,\n  hasFluidValue,\n  addFluidObserver,\n  removeFluidObserver,\n  getFluidObservers,\n} from '@react-spring/shared'\nimport {\n  Animated,\n  AnimatedValue,\n  AnimatedString,\n  getPayload,\n  getAnimated,\n  setAnimated,\n  getAnimatedType,\n} from '@react-spring/animated'\nimport { Lookup } from '@react-spring/types'\n\nimport { Animation } from './Animation'\nimport { mergeConfig } from './AnimationConfig'\nimport { scheduleProps } from './scheduleProps'\nimport { runAsync, RunAsyncState, RunAsyncProps, stopAsync } from './runAsync'\nimport {\n  callProp,\n  computeGoal,\n  matchProp,\n  inferTo,\n  getDefaultProps,\n  getDefaultProp,\n  isAsyncTo,\n  resolveProp,\n} from './helpers'\nimport { FrameValue, isFrameValue } from './FrameValue'\nimport {\n  isAnimating,\n  isPaused,\n  setPausedBit,\n  hasAnimated,\n  setActiveBit,\n} from './SpringPhase'\nimport {\n  AnimationRange,\n  AnimationResolver,\n  EventKey,\n  PickEventFns,\n} from './types/internal'\nimport { AsyncResult, SpringUpdate, VelocityProp, SpringProps } from './types'\nimport {\n  getCombinedResult,\n  getCancelledResult,\n  getFinishedResult,\n  getNoopResult,\n} from './AnimationResult'\n\ndeclare const console: any\n\ninterface DefaultSpringProps<T>\n  extends Pick<SpringProps<T>, 'pause' | 'cancel' | 'immediate' | 'config'>,\n    PickEventFns<SpringProps<T>> {}\n\n/**\n * Only numbers, strings, and arrays of numbers/strings are supported.\n * Non-animatable strings are also supported.\n */\nexport class SpringValue<T = any> extends FrameValue<T> {\n  /** The property name used when `to` or `from` is an object. Useful when debugging too. */\n  key?: string\n\n  /** The animation state */\n  animation = new Animation<T>()\n\n  /** The queue of pending props */\n  queue?: SpringUpdate<T>[]\n\n  /** Some props have customizable default values */\n  defaultProps: DefaultSpringProps<T> = {}\n\n  /** The state for `runAsync` calls */\n  protected _state: RunAsyncState<SpringValue<T>> = {\n    paused: false,\n    delayed: false,\n    pauseQueue: new Set(),\n    resumeQueue: new Set(),\n    timeouts: new Set(),\n  }\n\n  /** The promise resolvers of pending `start` calls */\n  protected _pendingCalls = new Set<AnimationResolver<this>>()\n\n  /** The counter for tracking `scheduleProps` calls */\n  protected _lastCallId = 0\n\n  /** The last `scheduleProps` call that changed the `to` prop */\n  protected _lastToId = 0\n\n  protected _memoizedDuration = 0\n\n  constructor(from: Exclude<T, object>, props?: SpringUpdate<T>)\n  constructor(props?: SpringUpdate<T>)\n  constructor(arg1?: any, arg2?: any) {\n    super()\n    if (!is.und(arg1) || !is.und(arg2)) {\n      const props = is.obj(arg1) ? { ...arg1 } : { ...arg2, from: arg1 }\n      if (is.und(props.default)) {\n        props.default = true\n      }\n      this.start(props)\n    }\n  }\n\n  /** Equals true when not advancing on each frame. */\n  get idle() {\n    return !(isAnimating(this) || this._state.asyncTo) || isPaused(this)\n  }\n\n  get goal() {\n    return getFluidValue(this.animation.to) as T\n  }\n\n  get velocity(): VelocityProp<T> {\n    const node = getAnimated(this)!\n    return (\n      node instanceof AnimatedValue\n        ? node.lastVelocity || 0\n        : node.getPayload().map(node => node.lastVelocity || 0)\n    ) as any\n  }\n\n  /**\n   * When true, this value has been animated at least once.\n   */\n  get hasAnimated() {\n    return hasAnimated(this)\n  }\n\n  /**\n   * When true, this value has an unfinished animation,\n   * which is either active or paused.\n   */\n  get isAnimating() {\n    return isAnimating(this)\n  }\n\n  /**\n   * When true, all current and future animations are paused.\n   */\n  get isPaused() {\n    return isPaused(this)\n  }\n\n  /**\n   *\n   *\n   */\n  get isDelayed() {\n    return this._state.delayed\n  }\n\n  /** Advance the current animation by a number of milliseconds */\n  advance(dt: number) {\n    let idle = true\n    let changed = false\n\n    const anim = this.animation\n    let { toValues } = anim\n    const { config } = anim\n\n    const payload = getPayload(anim.to)\n    if (!payload && hasFluidValue(anim.to)) {\n      toValues = toArray(getFluidValue(anim.to)) as any\n    }\n\n    anim.values.forEach((node, i) => {\n      if (node.done) return\n\n      const to =\n        // Animated strings always go from 0 to 1.\n        node.constructor == AnimatedString\n          ? 1\n          : payload\n            ? payload[i].lastPosition\n            : toValues![i]\n\n      let finished = anim.immediate\n      let position = to\n\n      if (!finished) {\n        position = node.lastPosition\n\n        // Loose springs never move.\n        if (config.tension <= 0) {\n          node.done = true\n          return\n        }\n\n        let elapsed = (node.elapsedTime += dt)\n        const from = anim.fromValues[i]\n\n        const v0 =\n          node.v0 != null\n            ? node.v0\n            : (node.v0 = is.arr(config.velocity)\n                ? config.velocity[i]\n                : config.velocity)\n\n        let velocity: number\n\n        /** The smallest distance from a value before being treated like said value. */\n        /**\n         * TODO: make this value ~0.0001 by default in next breaking change\n         * for more info see – https://github.com/pmndrs/react-spring/issues/1389\n         */\n        const precision =\n          config.precision ||\n          (from == to ? 0.005 : Math.min(1, Math.abs(to - from) * 0.001))\n\n        // Duration easing\n        if (!is.und(config.duration)) {\n          let p = 1\n          if (config.duration > 0) {\n            /**\n             * Here we check if the duration has changed in the config\n             * and if so update the elapsed time to the percentage\n             * of completition so there is no jank in the animation\n             * https://github.com/pmndrs/react-spring/issues/1163\n             */\n            if (this._memoizedDuration !== config.duration) {\n              // update the memoized version to the new duration\n              this._memoizedDuration = config.duration\n\n              // if the value has started animating we need to update it\n              if (node.durationProgress > 0) {\n                // set elapsed time to be the same percentage of progress as the previous duration\n                node.elapsedTime = config.duration * node.durationProgress\n                // add the delta so the below updates work as expected\n                elapsed = node.elapsedTime += dt\n              }\n            }\n\n            // calculate the new progress\n            p = (config.progress || 0) + elapsed / this._memoizedDuration\n            // p is clamped between 0-1\n            p = p > 1 ? 1 : p < 0 ? 0 : p\n            // store our new progress\n            node.durationProgress = p\n          }\n\n          position = from + config.easing(p) * (to - from)\n          velocity = (position - node.lastPosition) / dt\n\n          finished = p == 1\n        }\n\n        // Decay easing\n        else if (config.decay) {\n          const decay = config.decay === true ? 0.998 : config.decay\n          const e = Math.exp(-(1 - decay) * elapsed)\n\n          position = from + (v0 / (1 - decay)) * (1 - e)\n          finished = Math.abs(node.lastPosition - position) <= precision\n\n          // derivative of position\n          velocity = v0 * e\n        }\n\n        // Spring easing\n        else {\n          velocity = node.lastVelocity == null ? v0 : node.lastVelocity\n\n          /** The velocity at which movement is essentially none */\n          const restVelocity = config.restVelocity || precision / 10\n\n          // Bouncing is opt-in (not to be confused with overshooting)\n          const bounceFactor = config.clamp ? 0 : config.bounce!\n          const canBounce = !is.und(bounceFactor)\n\n          /** When `true`, the value is increasing over time */\n          const isGrowing = from == to ? node.v0 > 0 : from < to\n\n          /** When `true`, the velocity is considered moving */\n          let isMoving!: boolean\n\n          /** When `true`, the velocity is being deflected or clamped */\n          let isBouncing = false\n\n          const step = 1 // 1ms\n          const numSteps = Math.ceil(dt / step)\n          for (let n = 0; n < numSteps; ++n) {\n            isMoving = Math.abs(velocity) > restVelocity\n\n            if (!isMoving) {\n              finished = Math.abs(to - position) <= precision\n              if (finished) {\n                break\n              }\n            }\n\n            if (canBounce) {\n              isBouncing = position == to || position > to == isGrowing\n\n              // Invert the velocity with a magnitude, or clamp it.\n              if (isBouncing) {\n                velocity = -velocity * bounceFactor\n                position = to\n              }\n            }\n\n            const springForce = -config.tension * 0.000001 * (position - to)\n            const dampingForce = -config.friction * 0.001 * velocity\n            const acceleration = (springForce + dampingForce) / config.mass // pt/ms^2\n\n            velocity = velocity + acceleration * step // pt/ms\n            position = position + velocity * step\n          }\n        }\n\n        node.lastVelocity = velocity\n\n        if (Number.isNaN(position)) {\n          console.warn(`Got NaN while animating:`, this)\n          finished = true\n        }\n      }\n\n      // Parent springs must finish before their children can.\n      if (payload && !payload[i].done) {\n        finished = false\n      }\n\n      if (finished) {\n        node.done = true\n      } else {\n        idle = false\n      }\n\n      if (node.setValue(position, config.round)) {\n        changed = true\n      }\n    })\n\n    const node = getAnimated(this)!\n    /**\n     * Get the node's current value, this will be different\n     * to anim.to when config.decay is true\n     */\n    const currVal = node.getValue()\n    if (idle) {\n      // get our final fluid val from the anim.to\n      const finalVal = getFluidValue(anim.to)\n      /**\n       * check if they're not equal, or if they're\n       * change and if there's no config.decay set\n       */\n      if ((currVal !== finalVal || changed) && !config.decay) {\n        // set the value to anim.to\n        node.setValue(finalVal)\n        this._onChange(finalVal)\n      } else if (changed && config.decay) {\n        /**\n         * if it's changed but there is a config.decay,\n         * just call _onChange with currrent value\n         */\n        this._onChange(currVal)\n      }\n      // call stop because the spring has stopped.\n      this._stop()\n    } else if (changed) {\n      /**\n       * if the spring has changed, but is not idle,\n       * just call the _onChange handler\n       */\n      this._onChange(currVal)\n    }\n  }\n\n  /** Set the current value, while stopping the current animation */\n  set(value: T | FluidValue<T>) {\n    raf.batchedUpdates(() => {\n      this._stop()\n\n      // These override the current value and goal value that may have\n      // been updated by `onRest` handlers in the `_stop` call above.\n      this._focus(value)\n      this._set(value)\n    })\n    return this\n  }\n\n  /**\n   * Freeze the active animation in time, as well as any updates merged\n   * before `resume` is called.\n   */\n  pause() {\n    this._update({ pause: true })\n  }\n\n  /** Resume the animation if paused. */\n  resume() {\n    this._update({ pause: false })\n  }\n\n  /** Skip to the end of the current animation. */\n  finish() {\n    if (isAnimating(this)) {\n      const { to, config } = this.animation\n      raf.batchedUpdates(() => {\n        // Ensure the \"onStart\" and \"onRest\" props are called.\n        this._onStart()\n\n        // Jump to the goal value, except for decay animations\n        // which have an undefined goal value.\n        if (!config.decay) {\n          this._set(to, false)\n        }\n\n        this._stop()\n      })\n    }\n    return this\n  }\n\n  /** Push props into the pending queue. */\n  update(props: SpringUpdate<T>) {\n    const queue = this.queue || (this.queue = [])\n    queue.push(props)\n    return this\n  }\n\n  /**\n   * Update this value's animation using the queue of pending props,\n   * and unpause the current animation (if one is frozen).\n   *\n   * When arguments are passed, a new animation is created, and the\n   * queued animations are left alone.\n   */\n  start(): AsyncResult<this>\n\n  start(props: SpringUpdate<T>): AsyncResult<this>\n\n  start(to: T, props?: SpringProps<T>): AsyncResult<this>\n\n  start(to?: any, arg2?: any) {\n    let queue: SpringUpdate<T>[]\n    if (!is.und(to)) {\n      queue = [is.obj(to) ? to : { ...arg2, to }]\n    } else {\n      queue = this.queue || []\n      this.queue = []\n    }\n\n    return Promise.all(\n      queue.map(props => {\n        const up = this._update(props)\n        return up\n      })\n    ).then(results => getCombinedResult(this, results))\n  }\n\n  /**\n   * Stop the current animation, and cancel any delayed updates.\n   *\n   * Pass `true` to call `onRest` with `cancelled: true`.\n   */\n  stop(cancel?: boolean) {\n    const { to } = this.animation\n\n    // The current value becomes the goal value.\n    this._focus(this.get())\n\n    stopAsync(this._state, cancel && this._lastCallId)\n    raf.batchedUpdates(() => this._stop(to, cancel))\n\n    return this\n  }\n\n  /** Restart the animation. */\n  reset() {\n    this._update({ reset: true })\n  }\n\n  /** @internal */\n  eventObserved(event: FrameValue.Event) {\n    if (event.type == 'change') {\n      this._start()\n    } else if (event.type == 'priority') {\n      this.priority = event.priority + 1\n    }\n  }\n\n  /**\n   * Parse the `to` and `from` range from the given `props` object.\n   *\n   * This also ensures the initial value is available to animated components\n   * during the render phase.\n   */\n  protected _prepareNode(props: {\n    to?: any\n    from?: any\n    reverse?: boolean\n    default?: any\n  }) {\n    const key = this.key || ''\n\n    let { to, from } = props\n\n    to = is.obj(to) ? to[key] : to\n    if (to == null || isAsyncTo(to)) {\n      to = undefined\n    }\n\n    from = is.obj(from) ? from[key] : from\n    if (from == null) {\n      from = undefined\n    }\n\n    // Create the range now to avoid \"reverse\" logic.\n    const range = { to, from }\n\n    // Before ever animating, this method ensures an `Animated` node\n    // exists and keeps its value in sync with the \"from\" prop.\n    if (!hasAnimated(this)) {\n      if (props.reverse) [to, from] = [from, to]\n\n      from = getFluidValue(from)\n      if (!is.und(from)) {\n        this._set(from)\n      }\n      // Use the \"to\" value if our node is undefined.\n      else if (!getAnimated(this)) {\n        this._set(to)\n      }\n    }\n\n    return range\n  }\n\n  /** Every update is processed by this method before merging. */\n  protected _update(\n    { ...props }: SpringProps<T>,\n    isLoop?: boolean\n  ): AsyncResult<SpringValue<T>> {\n    const { key, defaultProps } = this\n\n    // Update the default props immediately.\n    if (props.default)\n      Object.assign(\n        defaultProps,\n        getDefaultProps(props, (value, prop) =>\n          /^on/.test(prop) ? resolveProp(value, key) : value\n        )\n      )\n\n    mergeActiveFn(this, props, 'onProps')\n    sendEvent(this, 'onProps', props, this)\n\n    // Ensure the initial value can be accessed by animated components.\n    const range = this._prepareNode(props)\n\n    if (Object.isFrozen(this)) {\n      throw Error(\n        'Cannot animate a `SpringValue` object that is frozen. ' +\n          'Did you forget to pass your component to `animated(...)` before animating its props?'\n      )\n    }\n\n    const state = this._state\n\n    return scheduleProps(++this._lastCallId, {\n      key,\n      props,\n      defaultProps,\n      state,\n      actions: {\n        pause: () => {\n          if (!isPaused(this)) {\n            setPausedBit(this, true)\n            flushCalls(state.pauseQueue)\n            sendEvent(\n              this,\n              'onPause',\n              getFinishedResult(this, checkFinished(this, this.animation.to)),\n              this\n            )\n          }\n        },\n        resume: () => {\n          if (isPaused(this)) {\n            setPausedBit(this, false)\n            if (isAnimating(this)) {\n              this._resume()\n            }\n            flushCalls(state.resumeQueue)\n            sendEvent(\n              this,\n              'onResume',\n              getFinishedResult(this, checkFinished(this, this.animation.to)),\n              this\n            )\n          }\n        },\n        start: this._merge.bind(this, range),\n      },\n    }).then(result => {\n      if (props.loop && result.finished && !(isLoop && result.noop)) {\n        const nextProps = createLoopUpdate(props)\n        if (nextProps) {\n          return this._update(nextProps, true)\n        }\n      }\n      return result\n    })\n  }\n\n  /** Merge props into the current animation */\n  protected _merge(\n    range: AnimationRange<T>,\n    props: RunAsyncProps<SpringValue<T>>,\n    resolve: AnimationResolver<SpringValue<T>>\n  ): void {\n    // The \"cancel\" prop cancels all pending delays and it forces the\n    // active animation to stop where it is.\n    if (props.cancel) {\n      this.stop(true)\n      return resolve(getCancelledResult(this))\n    }\n\n    /** The \"to\" prop is defined. */\n    const hasToProp = !is.und(range.to)\n\n    /** The \"from\" prop is defined. */\n    const hasFromProp = !is.und(range.from)\n\n    // Avoid merging other props if implicitly prevented, except\n    // when both the \"to\" and \"from\" props are undefined.\n    if (hasToProp || hasFromProp) {\n      if (props.callId > this._lastToId) {\n        this._lastToId = props.callId\n      } else {\n        return resolve(getCancelledResult(this))\n      }\n    }\n\n    const { key, defaultProps, animation: anim } = this\n    const { to: prevTo, from: prevFrom } = anim\n    let { to = prevTo, from = prevFrom } = range\n\n    // Focus the \"from\" value if changing without a \"to\" value.\n    // For default updates, do this only if no \"to\" value exists.\n    if (hasFromProp && !hasToProp && (!props.default || is.und(to))) {\n      to = from\n    }\n\n    // Flip the current range if \"reverse\" is true.\n    if (props.reverse) [to, from] = [from, to]\n\n    /** The \"from\" value is changing. */\n    const hasFromChanged = !isEqual(from, prevFrom)\n\n    if (hasFromChanged) {\n      anim.from = from\n    }\n\n    // Coerce \"from\" into a static value.\n    from = getFluidValue(from)\n\n    /** The \"to\" value is changing. */\n    const hasToChanged = !isEqual(to, prevTo)\n\n    if (hasToChanged) {\n      this._focus(to)\n    }\n\n    /** The \"to\" prop is async. */\n    const hasAsyncTo = isAsyncTo(props.to)\n\n    const { config } = anim\n    const { decay, velocity } = config\n\n    // Reset to default velocity when goal values are defined.\n    if (hasToProp || hasFromProp) {\n      config.velocity = 0\n    }\n\n    // The \"runAsync\" function treats the \"config\" prop as a default,\n    // so we must avoid merging it when the \"to\" prop is async.\n    if (props.config && !hasAsyncTo) {\n      mergeConfig(\n        config,\n        callProp(props.config, key!),\n        // Avoid calling the same \"config\" prop twice.\n        props.config !== defaultProps.config\n          ? callProp(defaultProps.config, key!)\n          : void 0\n      )\n    }\n\n    // This instance might not have its Animated node yet. For example,\n    // the constructor can be given props without a \"to\" or \"from\" value.\n    let node = getAnimated(this)\n    if (!node || is.und(to)) {\n      return resolve(getFinishedResult(this, true))\n    }\n\n    /** When true, start at the \"from\" value. */\n    const reset =\n      // When `reset` is undefined, the `from` prop implies `reset: true`,\n      // except for declarative updates. When `reset` is defined, there\n      // must exist a value to animate from.\n      is.und(props.reset)\n        ? hasFromProp && !props.default\n        : !is.und(from) && matchProp(props.reset, key)\n\n    // The current value, where the animation starts from.\n    const value = reset ? (from as T) : this.get()\n\n    // The animation ends at this value, unless \"to\" is fluid.\n    const goal = computeGoal<any>(to)\n\n    // Only specific types can be animated to/from.\n    const isAnimatable = is.num(goal) || is.arr(goal) || isAnimatedString(goal)\n\n    // When true, the value changes instantly on the next frame.\n    const immediate =\n      !hasAsyncTo &&\n      (!isAnimatable ||\n        matchProp(defaultProps.immediate || props.immediate, key))\n\n    if (hasToChanged) {\n      const nodeType = getAnimatedType(to)\n      if (nodeType !== node.constructor) {\n        if (immediate) {\n          node = this._set(goal)!\n        } else\n          throw Error(\n            `Cannot animate between ${node.constructor.name} and ${nodeType.name}, as the \"to\" prop suggests`\n          )\n      }\n    }\n\n    // The type of Animated node for the goal value.\n    const goalType = node.constructor\n\n    // When the goal value is fluid, we don't know if its value\n    // will change before the next animation frame, so it always\n    // starts the animation to be safe.\n    let started = hasFluidValue(to)\n    let finished = false\n\n    if (!started) {\n      // When true, the current value has probably changed.\n      const hasValueChanged = reset || (!hasAnimated(this) && hasFromChanged)\n\n      // When the \"to\" value or current value are changed,\n      // start animating if not already finished.\n      if (hasToChanged || hasValueChanged) {\n        finished = isEqual(computeGoal(value), goal)\n        started = !finished\n      }\n\n      // Changing \"decay\" or \"velocity\" starts the animation.\n      if (\n        (!isEqual(anim.immediate, immediate) && !immediate) ||\n        !isEqual(config.decay, decay) ||\n        !isEqual(config.velocity, velocity)\n      ) {\n        started = true\n      }\n    }\n\n    // Was the goal value set to the current value while animating?\n    if (finished && isAnimating(this)) {\n      // If the first frame has passed, allow the animation to\n      // overshoot instead of stopping abruptly.\n      if (anim.changed && !reset) {\n        started = true\n      }\n      // Stop the animation before its first frame.\n      else if (!started) {\n        this._stop(prevTo)\n      }\n    }\n\n    if (!hasAsyncTo) {\n      // Make sure our \"toValues\" are updated even if our previous\n      // \"to\" prop is a fluid value whose current value is also ours.\n      if (started || hasFluidValue(prevTo)) {\n        anim.values = node.getPayload()\n        anim.toValues = hasFluidValue(to)\n          ? null\n          : goalType == AnimatedString\n            ? [1]\n            : toArray(goal)\n      }\n\n      if (anim.immediate != immediate) {\n        anim.immediate = immediate\n\n        // Ensure the immediate goal is used as from value.\n        if (!immediate && !reset) {\n          this._set(prevTo)\n        }\n      }\n\n      if (started) {\n        const { onRest } = anim\n\n        // Set the active handlers when an animation starts.\n        each(ACTIVE_EVENTS, type => mergeActiveFn(this, props, type))\n\n        const result = getFinishedResult(this, checkFinished(this, prevTo))\n        flushCalls(this._pendingCalls, result)\n        this._pendingCalls.add(resolve)\n\n        if (anim.changed)\n          raf.batchedUpdates(() => {\n            // Ensure `onStart` can be called after a reset.\n            anim.changed = !reset\n\n            // Call the active `onRest` handler from the interrupted animation.\n            onRest?.(result, this)\n\n            // Notify the default `onRest` of the reset, but wait for the\n            // first frame to pass before sending an `onStart` event.\n            if (reset) {\n              callProp(defaultProps.onRest, result)\n            }\n            // Call the active `onStart` handler here since the first frame\n            // has already passed, which means this is a goal update and not\n            // an entirely new animation.\n            else {\n              anim.onStart?.(result, this)\n            }\n          })\n      }\n    }\n\n    if (reset) {\n      this._set(value)\n    }\n\n    if (hasAsyncTo) {\n      resolve(runAsync(props.to, props, this._state, this))\n    }\n\n    // Start an animation\n    else if (started) {\n      this._start()\n    }\n\n    // Postpone promise resolution until the animation is finished,\n    // so that no-op updates still resolve at the expected time.\n    else if (isAnimating(this) && !hasToChanged) {\n      this._pendingCalls.add(resolve)\n    }\n\n    // Resolve our promise immediately.\n    else {\n      resolve(getNoopResult(value))\n    }\n  }\n\n  /** Update the `animation.to` value, which might be a `FluidValue` */\n  protected _focus(value: T | FluidValue<T>) {\n    const anim = this.animation\n    if (value !== anim.to) {\n      if (getFluidObservers(this)) {\n        this._detach()\n      }\n      anim.to = value\n      if (getFluidObservers(this)) {\n        this._attach()\n      }\n    }\n  }\n\n  protected _attach() {\n    let priority = 0\n\n    const { to } = this.animation\n    if (hasFluidValue(to)) {\n      addFluidObserver(to, this)\n      if (isFrameValue(to)) {\n        priority = to.priority + 1\n      }\n    }\n\n    this.priority = priority\n  }\n\n  protected _detach() {\n    const { to } = this.animation\n    if (hasFluidValue(to)) {\n      removeFluidObserver(to, this)\n    }\n  }\n\n  /**\n   * Update the current value from outside the frameloop,\n   * and return the `Animated` node.\n   */\n  protected _set(arg: T | FluidValue<T>, idle = true): Animated | undefined {\n    const value = getFluidValue(arg)\n    if (!is.und(value)) {\n      const oldNode = getAnimated(this)\n      if (!oldNode || !isEqual(value, oldNode.getValue())) {\n        // Create a new node or update the existing node.\n        const nodeType = getAnimatedType(value)\n        if (!oldNode || oldNode.constructor != nodeType) {\n          setAnimated(this, nodeType.create(value))\n        } else {\n          oldNode.setValue(value)\n        }\n        // Never emit a \"change\" event for the initial value.\n        if (oldNode) {\n          raf.batchedUpdates(() => {\n            this._onChange(value, idle)\n          })\n        }\n      }\n    }\n    return getAnimated(this)\n  }\n\n  protected _onStart() {\n    const anim = this.animation\n    if (!anim.changed) {\n      anim.changed = true\n      sendEvent(\n        this,\n        'onStart',\n        getFinishedResult(this, checkFinished(this, anim.to)),\n        this\n      )\n    }\n  }\n\n  protected _onChange(value: T, idle?: boolean) {\n    if (!idle) {\n      this._onStart()\n      callProp(this.animation.onChange, value, this)\n    }\n    callProp(this.defaultProps.onChange, value, this)\n    super._onChange(value, idle)\n  }\n\n  // This method resets the animation state (even if already animating) to\n  // ensure the latest from/to range is used, and it also ensures this spring\n  // is added to the frameloop.\n  protected _start() {\n    const anim = this.animation\n\n    // Reset the state of each Animated node.\n    getAnimated(this)!.reset(getFluidValue(anim.to))\n\n    // Use the current values as the from values.\n    if (!anim.immediate) {\n      anim.fromValues = anim.values.map(node => node.lastPosition)\n    }\n\n    if (!isAnimating(this)) {\n      setActiveBit(this, true)\n      if (!isPaused(this)) {\n        this._resume()\n      }\n    }\n  }\n\n  protected _resume() {\n    // The \"skipAnimation\" global avoids the frameloop.\n    if (G.skipAnimation) {\n      this.finish()\n    } else {\n      frameLoop.start(this)\n    }\n  }\n\n  /**\n   * Exit the frameloop and notify `onRest` listeners.\n   *\n   * Always wrap `_stop` calls with `batchedUpdates`.\n   */\n  protected _stop(goal?: any, cancel?: boolean) {\n    if (isAnimating(this)) {\n      setActiveBit(this, false)\n\n      const anim = this.animation\n      each(anim.values, node => {\n        node.done = true\n      })\n\n      // These active handlers must be reset to undefined or else\n      // they could be called while idle. But keep them defined\n      // when the goal value is dynamic.\n      if (anim.toValues) {\n        anim.onChange = anim.onPause = anim.onResume = undefined\n      }\n\n      callFluidObservers(this, {\n        type: 'idle',\n        parent: this,\n      })\n\n      const result = cancel\n        ? getCancelledResult(this.get())\n        : getFinishedResult(this.get(), checkFinished(this, goal ?? anim.to))\n\n      flushCalls(this._pendingCalls, result)\n      if (anim.changed) {\n        anim.changed = false\n        sendEvent(this, 'onRest', result, this)\n      }\n    }\n  }\n}\n\n/** Returns true when the current value and goal value are equal. */\nfunction checkFinished<T>(target: SpringValue<T>, to: T | FluidValue<T>) {\n  const goal = computeGoal(to)\n  const value = computeGoal(target.get())\n  return isEqual(value, goal)\n}\n\nexport function createLoopUpdate<T>(\n  props: T & { loop?: any; to?: any; from?: any; reverse?: any },\n  loop = props.loop,\n  to = props.to\n): T | undefined {\n  const loopRet = callProp(loop)\n  if (loopRet) {\n    const overrides = loopRet !== true && inferTo(loopRet)\n    const reverse = (overrides || props).reverse\n    const reset = !overrides || overrides.reset\n    return createUpdate({\n      ...props,\n      loop,\n\n      // Avoid updating default props when looping.\n      default: false,\n\n      // Never loop the `pause` prop.\n      pause: undefined,\n\n      // For the \"reverse\" prop to loop as expected, the \"to\" prop\n      // must be undefined. The \"reverse\" prop is ignored when the\n      // \"to\" prop is an array or function.\n      to: !reverse || isAsyncTo(to) ? to : undefined,\n\n      // Ignore the \"from\" prop except on reset.\n      from: reset ? props.from : undefined,\n      reset,\n\n      // The \"loop\" prop can return a \"useSpring\" props object to\n      // override any of the original props.\n      ...overrides,\n    })\n  }\n}\n\n/**\n * Return a new object based on the given `props`.\n *\n * - All non-reserved props are moved into the `to` prop object.\n * - The `keys` prop is set to an array of affected keys,\n *   or `null` if all keys are affected.\n */\nexport function createUpdate(props: any) {\n  const { to, from } = (props = inferTo(props))\n\n  // Collect the keys affected by this update.\n  const keys = new Set<string>()\n\n  if (is.obj(to)) findDefined(to, keys)\n  if (is.obj(from)) findDefined(from, keys)\n\n  // The \"keys\" prop helps in applying updates to affected keys only.\n  props.keys = keys.size ? Array.from(keys) : null\n\n  return props\n}\n\n/**\n * A modified version of `createUpdate` meant for declarative APIs.\n */\nexport function declareUpdate(props: any) {\n  const update = createUpdate(props)\n  if (is.und(update.default)) {\n    update.default = getDefaultProps(update)\n  }\n  return update\n}\n\n/** Find keys with defined values */\nfunction findDefined(values: Lookup, keys: Set<string>) {\n  eachProp(values, (value, key) => value != null && keys.add(key as any))\n}\n\n/** Event props with \"active handler\" support */\nconst ACTIVE_EVENTS = [\n  'onStart',\n  'onRest',\n  'onChange',\n  'onPause',\n  'onResume',\n] as const\n\nfunction mergeActiveFn<T, P extends EventKey>(\n  target: SpringValue<T>,\n  props: SpringProps<T>,\n  type: P\n) {\n  target.animation[type] =\n    props[type] !== getDefaultProp(props, type)\n      ? resolveProp<any>(props[type], target.key)\n      : undefined\n}\n\ntype EventArgs<T, P extends EventKey> = Parameters<\n  Extract<SpringProps<T>[P], Function>\n>\n\n/** Call the active handler first, then the default handler. */\nfunction sendEvent<T, P extends EventKey>(\n  target: SpringValue<T>,\n  type: P,\n  ...args: EventArgs<T, P>\n) {\n  target.animation[type]?.(...(args as [any, any]))\n  target.defaultProps[type]?.(...(args as [any, any]))\n}\n", "import { is, easings } from '@react-spring/shared'\nimport { EasingFunction } from '@react-spring/types'\nimport { config as configs } from './constants'\n\nconst defaults: any = {\n  ...configs.default,\n  mass: 1,\n  damping: 1,\n  easing: easings.linear,\n  clamp: false,\n}\n\nexport class AnimationConfig {\n  /**\n   * With higher tension, the spring will resist bouncing and try harder to stop at its end value.\n   *\n   * When tension is zero, no animation occurs.\n   *\n   * @default 170\n   */\n  tension!: number\n\n  /**\n   * The damping ratio coefficient, or just the damping ratio when `speed` is defined.\n   *\n   * When `speed` is defined, this value should be between 0 and 1.\n   *\n   * Higher friction means the spring will slow down faster.\n   *\n   * @default 26\n   */\n  friction!: number\n\n  /**\n   * The natural frequency (in seconds), which dictates the number of bounces\n   * per second when no damping exists.\n   *\n   * When defined, `tension` is derived from this, and `friction` is derived\n   * from `tension` and `damping`.\n   */\n  frequency?: number\n\n  /**\n   * The damping ratio, which dictates how the spring slows down.\n   *\n   * Set to `0` to never slow down. Set to `1` to slow down without bouncing.\n   * Between `0` and `1` is for you to explore.\n   *\n   * Only works when `frequency` is defined.\n   *\n   * @default 1\n   */\n  damping!: number\n\n  /**\n   * Higher mass means more friction is required to slow down.\n   *\n   * Defaults to 1, which works fine most of the time.\n   *\n   * @default 1\n   */\n  mass!: number\n\n  /**\n   * The initial velocity of one or more values.\n   *\n   * @default 0\n   */\n  velocity: number | number[] = 0\n\n  /**\n   * The smallest velocity before the animation is considered \"not moving\".\n   *\n   * When undefined, `precision` is used instead.\n   */\n  restVelocity?: number\n\n  /**\n   * The smallest distance from a value before that distance is essentially zero.\n   *\n   * This helps in deciding when a spring is \"at rest\". The spring must be within\n   * this distance from its final value, and its velocity must be lower than this\n   * value too (unless `restVelocity` is defined).\n   *\n   * @default 0.01\n   */\n  precision?: number\n\n  /**\n   * For `duration` animations only. Note: The `duration` is not affected\n   * by this property.\n   *\n   * Defaults to `0`, which means \"start from the beginning\".\n   *\n   * Setting to `1+` makes an immediate animation.\n   *\n   * Setting to `0.5` means \"start from the middle of the easing function\".\n   *\n   * Any number `>= 0` and `<= 1` makes sense here.\n   */\n  progress?: number\n\n  /**\n   * Animation length in number of milliseconds.\n   */\n  duration?: number\n\n  /**\n   * The animation curve. Only used when `duration` is defined.\n   *\n   * Defaults to quadratic ease-in-out.\n   */\n  easing!: EasingFunction\n\n  /**\n   * Avoid overshooting by ending abruptly at the goal value.\n   *\n   * @default false\n   */\n  clamp!: boolean\n\n  /**\n   * When above zero, the spring will bounce instead of overshooting when\n   * exceeding its goal value. Its velocity is multiplied by `-1 + bounce`\n   * whenever its current value equals or exceeds its goal. For example,\n   * setting `bounce` to `0.5` chops the velocity in half on each bounce,\n   * in addition to any friction.\n   */\n  bounce?: number\n\n  /**\n   * \"Decay animations\" decelerate without an explicit goal value.\n   * Useful for scrolling animations.\n   *\n   * Use `true` for the default exponential decay factor (`0.998`).\n   *\n   * When a `number` between `0` and `1` is given, a lower number makes the\n   * animation slow down faster. And setting to `1` would make an unending\n   * animation.\n   *\n   * @default false\n   */\n  decay?: boolean | number\n\n  /**\n   * While animating, round to the nearest multiple of this number.\n   * The `from` and `to` values are never rounded, as well as any value\n   * passed to the `set` method of an animated value.\n   */\n  round?: number\n\n  constructor() {\n    Object.assign(this, defaults)\n  }\n}\n\nexport function mergeConfig(\n  config: AnimationConfig,\n  newConfig: Partial<AnimationConfig>,\n  defaultConfig?: Partial<AnimationConfig>\n): typeof config\n\nexport function mergeConfig(\n  config: any,\n  newConfig: object,\n  defaultConfig?: object\n) {\n  if (defaultConfig) {\n    defaultConfig = { ...defaultConfig }\n    sanitizeConfig(defaultConfig, newConfig)\n    newConfig = { ...defaultConfig, ...newConfig }\n  }\n\n  sanitizeConfig(config, newConfig)\n  Object.assign(config, newConfig)\n\n  for (const key in defaults) {\n    if (config[key] == null) {\n      config[key] = defaults[key]\n    }\n  }\n\n  let { frequency, damping } = config\n  const { mass } = config\n  if (!is.und(frequency)) {\n    if (frequency < 0.01) frequency = 0.01\n    if (damping < 0) damping = 0\n    config.tension = Math.pow((2 * Math.PI) / frequency, 2) * mass\n    config.friction = (4 * Math.PI * damping * mass) / frequency\n  }\n\n  return config\n}\n\n// Prevent a config from accidentally overriding new props.\n// This depends on which \"config\" props take precedence when defined.\nfunction sanitizeConfig(\n  config: Partial<AnimationConfig>,\n  props: Partial<AnimationConfig>\n) {\n  if (!is.und(props.decay)) {\n    config.duration = undefined\n  } else {\n    const isTensionConfig = !is.und(props.tension) || !is.und(props.friction)\n    if (\n      isTensionConfig ||\n      !is.und(props.frequency) ||\n      !is.und(props.damping) ||\n      !is.und(props.mass)\n    ) {\n      config.duration = undefined\n      config.decay = undefined\n    }\n    if (isTensionConfig) {\n      config.frequency = undefined\n    }\n  }\n}\n", "// The `mass` prop defaults to 1\nexport const config = {\n  default: { tension: 170, friction: 26 },\n  gentle: { tension: 120, friction: 14 },\n  wobbly: { tension: 180, friction: 12 },\n  stiff: { tension: 210, friction: 20 },\n  slow: { tension: 280, friction: 60 },\n  molasses: { tension: 280, friction: 120 },\n} as const\n", "import { AnimatedValue } from '@react-spring/animated'\nimport { FluidValue } from '@react-spring/shared'\nimport { AnimationConfig } from './AnimationConfig'\nimport { PickEventFns } from './types/internal'\nimport { SpringProps } from './types'\n\nconst emptyArray: readonly any[] = []\n\n/** An animation being executed by the frameloop */\n// eslint-disable-next-line @typescript-eslint/no-unsafe-declaration-merging\nexport class Animation<T = any> {\n  changed = false\n  values: readonly AnimatedValue[] = emptyArray\n  toValues: readonly number[] | null = null\n  fromValues: readonly number[] = emptyArray\n\n  to!: T | FluidValue<T>\n  from!: T | FluidValue<T>\n  config = new AnimationConfig()\n  immediate = false\n}\n\nexport interface Animation<T> extends PickEventFns<SpringProps<T>> {}\n", "import { Timeout, is, raf, Globals as G } from '@react-spring/shared'\nimport { matchProp, callProp } from './helpers'\nimport { AsyncResult, MatchProp } from './types'\nimport { RunAsyncState, RunAsyncProps } from './runAsync'\nimport {\n  AnimationResolver,\n  AnimationTarget,\n  InferProps,\n  InferState,\n} from './types/internal'\n\n// The `scheduleProps` function only handles these defaults.\ntype DefaultProps<T> = { cancel?: MatchProp<T>; pause?: MatchProp<T> }\n\ninterface ScheduledProps<T extends AnimationTarget> {\n  key?: string\n  props: InferProps<T>\n  defaultProps?: DefaultProps<InferState<T>>\n  state: RunAsyncState<T>\n  actions: {\n    pause: () => void\n    resume: () => void\n    start: (props: RunAsyncProps<T>, resolve: AnimationResolver<T>) => void\n  }\n}\n\n/**\n * This function sets a timeout if both the `delay` prop exists and\n * the `cancel` prop is not `true`.\n *\n * The `actions.start` function must handle the `cancel` prop itself,\n * but the `pause` prop is taken care of.\n */\nexport function scheduleProps<T extends AnimationTarget>(\n  callId: number,\n  { key, props, defaultProps, state, actions }: ScheduledProps<T>\n): AsyncResult<T> {\n  return new Promise((resolve, reject) => {\n    let delay: number\n    let timeout: Timeout\n\n    let cancel = matchProp(props.cancel ?? defaultProps?.cancel, key)\n    if (cancel) {\n      onStart()\n    } else {\n      // The `pause` prop updates the paused flag.\n      if (!is.und(props.pause)) {\n        state.paused = matchProp(props.pause, key)\n      }\n      // The default `pause` takes precedence when true,\n      // which allows `SpringContext` to work as expected.\n      let pause = defaultProps?.pause\n      if (pause !== true) {\n        pause = state.paused || matchProp(pause, key)\n      }\n\n      delay = callProp(props.delay || 0, key)\n      if (pause) {\n        state.resumeQueue.add(onResume)\n        actions.pause()\n      } else {\n        actions.resume()\n        onResume()\n      }\n    }\n\n    function onPause() {\n      state.resumeQueue.add(onResume)\n      state.timeouts.delete(timeout)\n      timeout.cancel()\n      // Cache the remaining delay.\n      delay = timeout.time - raf.now()\n    }\n\n    function onResume() {\n      if (delay > 0 && !G.skipAnimation) {\n        state.delayed = true\n        timeout = raf.setTimeout(onStart, delay)\n        state.pauseQueue.add(onPause)\n        state.timeouts.add(timeout)\n      } else {\n        onStart()\n      }\n    }\n\n    function onStart() {\n      if (state.delayed) {\n        state.delayed = false\n      }\n\n      state.pauseQueue.delete(onPause)\n      state.timeouts.delete(timeout)\n\n      // Maybe cancelled during its delay.\n      if (callId <= (state.cancelId || 0)) {\n        cancel = true\n      }\n\n      try {\n        actions.start({ ...props, callId, cancel }, resolve)\n      } catch (err) {\n        reject(err)\n      }\n    }\n  })\n}\n", "import {\n  is,\n  raf,\n  flush,\n  eachProp,\n  Timeout,\n  Globals as G,\n} from '@react-spring/shared'\nimport { Falsy } from '@react-spring/types'\n\nimport { getDefaultProps } from './helpers'\nimport { AnimationTarget, InferState, InferProps } from './types/internal'\nimport { AnimationResult, AsyncResult, SpringChain, SpringToFn } from './types'\nimport { getCancelledResult, getFinishedResult } from './AnimationResult'\n\ntype AsyncTo<T> = SpringChain<T> | SpringToFn<T>\n\n/** @internal */\nexport type RunAsyncProps<T extends AnimationTarget = any> = InferProps<T> & {\n  callId: number\n  parentId?: number\n  cancel: boolean\n  to?: any\n}\n\n/** @internal */\nexport interface RunAsyncState<T extends AnimationTarget = any> {\n  paused: boolean\n  pauseQueue: Set<() => void>\n  resumeQueue: Set<() => void>\n  timeouts: Set<Timeout>\n  delayed?: boolean\n  asyncId?: number\n  asyncTo?: AsyncTo<InferState<T>>\n  promise?: AsyncResult<T>\n  cancelId?: number\n}\n\n/**\n * Start an async chain or an async script.\n *\n * Always call `runAsync` in the action callback of a `scheduleProps` call.\n *\n * The `T` parameter can be a set of animated values (as an object type)\n * or a primitive type for a single animated value.\n */\nexport function runAsync<T extends AnimationTarget>(\n  to: AsyncTo<InferState<T>>,\n  props: RunAsyncProps<T>,\n  state: RunAsyncState<T>,\n  target: T\n): AsyncResult<T> {\n  const { callId, parentId, onRest } = props\n  const { asyncTo: prevTo, promise: prevPromise } = state\n\n  if (!parentId && to === prevTo && !props.reset) {\n    return prevPromise!\n  }\n\n  return (state.promise = (async () => {\n    state.asyncId = callId\n    state.asyncTo = to\n\n    // The default props of any `animate` calls.\n    const defaultProps = getDefaultProps<InferProps<T>>(props, (value, key) =>\n      // The `onRest` prop is only called when the `runAsync` promise is resolved.\n      key === 'onRest' ? undefined : value\n    )\n\n    let preventBail!: () => void\n    let bail: (error: any) => void\n\n    // This promise is rejected when the animation is interrupted.\n    const bailPromise = new Promise<void>(\n      (resolve, reject) => ((preventBail = resolve), (bail = reject))\n    )\n\n    const bailIfEnded = (bailSignal: BailSignal) => {\n      const bailResult =\n        // The `cancel` prop or `stop` method was used.\n        (callId <= (state.cancelId || 0) && getCancelledResult(target)) ||\n        // The async `to` prop was replaced.\n        (callId !== state.asyncId && getFinishedResult(target, false))\n\n      if (bailResult) {\n        bailSignal.result = bailResult\n\n        // Reject the `bailPromise` to ensure the `runAsync` promise\n        // is not relying on the caller to rethrow the error for us.\n        bail(bailSignal)\n        throw bailSignal\n      }\n    }\n\n    const animate: any = (arg1: any, arg2?: any) => {\n      // Create the bail signal outside the returned promise,\n      // so the generated stack trace is relevant.\n      const bailSignal = new BailSignal()\n      const skipAnimationSignal = new SkipAnimationSignal()\n\n      return (async () => {\n        if (G.skipAnimation) {\n          /**\n           * We need to stop animations if `skipAnimation`\n           * is set in the Globals\n           *\n           */\n          stopAsync(state)\n\n          // create the rejection error that's handled gracefully\n          skipAnimationSignal.result = getFinishedResult(target, false)\n          bail(skipAnimationSignal)\n          throw skipAnimationSignal\n        }\n\n        bailIfEnded(bailSignal)\n\n        const props: any = is.obj(arg1) ? { ...arg1 } : { ...arg2, to: arg1 }\n        props.parentId = callId\n\n        eachProp(defaultProps, (value, key) => {\n          if (is.und(props[key])) {\n            props[key] = value\n          }\n        })\n\n        const result = await target.start(props)\n        bailIfEnded(bailSignal)\n\n        if (state.paused) {\n          await new Promise<void>(resume => {\n            state.resumeQueue.add(resume)\n          })\n        }\n\n        return result\n      })()\n    }\n\n    let result!: AnimationResult<T>\n\n    if (G.skipAnimation) {\n      /**\n       * We need to stop animations if `skipAnimation`\n       * is set in the Globals\n       */\n      stopAsync(state)\n      return getFinishedResult(target, false)\n    }\n\n    try {\n      let animating!: Promise<void>\n\n      // Async sequence\n      if (is.arr(to)) {\n        animating = (async (queue: any[]) => {\n          for (const props of queue) {\n            await animate(props)\n          }\n        })(to)\n      }\n\n      // Async script\n      else {\n        animating = Promise.resolve(to(animate, target.stop.bind(target)))\n      }\n\n      await Promise.all([animating.then(preventBail), bailPromise])\n      result = getFinishedResult(target.get(), true, false)\n\n      // Bail handling\n    } catch (err) {\n      if (err instanceof BailSignal) {\n        result = err.result\n      } else if (err instanceof SkipAnimationSignal) {\n        result = err.result\n      } else {\n        throw err\n      }\n\n      // Reset the async state.\n    } finally {\n      if (callId == state.asyncId) {\n        state.asyncId = parentId\n        state.asyncTo = parentId ? prevTo : undefined\n        state.promise = parentId ? prevPromise : undefined\n      }\n    }\n\n    if (is.fun(onRest)) {\n      raf.batchedUpdates(() => {\n        onRest(result, target, target.item)\n      })\n    }\n\n    return result\n  })())\n}\n\n/** Stop the current `runAsync` call with `finished: false` (or with `cancelled: true` when `cancelId` is defined) */\nexport function stopAsync(state: RunAsyncState, cancelId?: number | Falsy) {\n  flush(state.timeouts, t => t.cancel())\n  state.pauseQueue.clear()\n  state.resumeQueue.clear()\n  state.asyncId = state.asyncTo = state.promise = undefined\n  if (cancelId) state.cancelId = cancelId\n}\n\n/** This error is thrown to signal an interrupted async animation. */\nexport class BailSignal extends Error {\n  result!: AnimationResult\n  constructor() {\n    super(\n      'An async animation has been interrupted. You see this error because you ' +\n        'forgot to use `await` or `.catch(...)` on its returned promise.'\n    )\n  }\n}\n\nexport class SkipAnimationSignal extends Error {\n  result!: AnimationResult\n\n  constructor() {\n    super('SkipAnimationSignal')\n  }\n}\n", "import { AnimationResult } from './types'\nimport { Readable } from './types/internal'\n\n/** @internal */\nexport const getCombinedResult = <T extends Readable>(\n  target: T,\n  results: AnimationResult<T>[]\n): AnimationResult<T> =>\n  results.length == 1\n    ? results[0]\n    : results.some(result => result.cancelled)\n      ? getCancelledResult(target.get())\n      : results.every(result => result.noop)\n        ? getNoopResult(target.get())\n        : getFinishedResult(\n            target.get(),\n            results.every(result => result.finished)\n          )\n\n/** No-op results are for updates that never start an animation. */\nexport const getNoopResult = (value: any) => ({\n  value,\n  noop: true,\n  finished: true,\n  cancelled: false,\n})\n\nexport const getFinishedResult = (\n  value: any,\n  finished: boolean,\n  cancelled = false\n) => ({\n  value,\n  finished,\n  cancelled,\n})\n\nexport const getCancelledResult = (value: any) => ({\n  value,\n  cancelled: true,\n  finished: false,\n})\n", "import {\n  deprecateInterpolate,\n  frameLoop,\n  FluidValue,\n  Globals as G,\n  callFluidObservers,\n} from '@react-spring/shared'\nimport { InterpolatorArgs } from '@react-spring/types'\nimport { getAnimated } from '@react-spring/animated'\n\nimport { Interpolation } from './Interpolation'\n\nexport const isFrameValue = (value: any): value is FrameValue =>\n  value instanceof FrameValue\n\nlet nextId = 1\n\n/**\n * A kind of `FluidValue` that manages an `AnimatedValue` node.\n *\n * Its underlying value can be accessed and even observed.\n */\nexport abstract class FrameValue<T = any> extends FluidValue<\n  T,\n  FrameValue.Event<T>\n> {\n  readonly id = nextId++\n\n  abstract key?: string\n  abstract get idle(): boolean\n\n  protected _priority = 0\n\n  get priority() {\n    return this._priority\n  }\n  set priority(priority: number) {\n    if (this._priority != priority) {\n      this._priority = priority\n      this._onPriorityChange(priority)\n    }\n  }\n\n  /** Get the current value */\n  get(): T {\n    const node = getAnimated(this)\n    return node && node.getValue()\n  }\n\n  /** Create a spring that maps our value to another value */\n  to<Out>(...args: InterpolatorArgs<T, Out>) {\n    return G.to(this, args) as Interpolation<T, Out>\n  }\n\n  /** @deprecated Use the `to` method instead. */\n  interpolate<Out>(...args: InterpolatorArgs<T, Out>) {\n    deprecateInterpolate()\n    return G.to(this, args) as Interpolation<T, Out>\n  }\n\n  toJSON() {\n    return this.get()\n  }\n\n  protected observerAdded(count: number) {\n    if (count == 1) this._attach()\n  }\n\n  protected observerRemoved(count: number) {\n    if (count == 0) this._detach()\n  }\n\n  /** @internal */\n  abstract advance(dt: number): void\n\n  /** @internal */\n  abstract eventObserved(_event: FrameValue.Event): void\n\n  /** Called when the first child is added. */\n  protected _attach() {}\n\n  /** Called when the last child is removed. */\n  protected _detach() {}\n\n  /** Tell our children about our new value */\n  protected _onChange(value: T, idle = false) {\n    callFluidObservers(this, {\n      type: 'change',\n      parent: this,\n      value,\n      idle,\n    })\n  }\n\n  /** Tell our children about our new priority */\n  protected _onPriorityChange(priority: number) {\n    if (!this.idle) {\n      frameLoop.sort(this)\n    }\n    callFluidObservers(this, {\n      type: 'priority',\n      parent: this,\n      priority,\n    })\n  }\n}\n\n// eslint-disable-next-line @typescript-eslint/no-namespace\nexport declare namespace FrameValue {\n  /** A parent changed its value */\n  interface ChangeEvent<T = any> {\n    parent: FrameValue<T>\n    type: 'change'\n    value: T\n    idle: boolean\n  }\n\n  /** A parent changed its priority */\n  interface PriorityEvent<T = any> {\n    parent: FrameValue<T>\n    type: 'priority'\n    priority: number\n  }\n\n  /** A parent is done animating */\n  interface IdleEvent<T = any> {\n    parent: FrameValue<T>\n    type: 'idle'\n  }\n\n  /** Events sent to children of `FrameValue` objects */\n  export type Event<T = any> = ChangeEvent<T> | PriorityEvent<T> | IdleEvent<T>\n}\n", "/** The property symbol of the current animation phase. */\nconst $P = Symbol.for('SpringPhase')\n\nconst HAS_ANIMATED = 1\nconst IS_ANIMATING = 2\nconst IS_PAUSED = 4\n\n/** Returns true if the `target` has ever animated. */\nexport const hasAnimated = (target: any) => (target[$P] & HAS_ANIMATED) > 0\n\n/** Returns true if the `target` is animating (even if paused). */\nexport const isAnimating = (target: any) => (target[$P] & IS_ANIMATING) > 0\n\n/** Returns true if the `target` is paused (even if idle). */\nexport const isPaused = (target: any) => (target[$P] & IS_PAUSED) > 0\n\n/** Set the active bit of the `target` phase. */\nexport const setActiveBit = (target: any, active: boolean) =>\n  active\n    ? (target[$P] |= IS_ANIMATING | HAS_ANIMATED)\n    : (target[$P] &= ~IS_ANIMATING)\n\nexport const setPausedBit = (target: any, paused: boolean) =>\n  paused ? (target[$P] |= IS_PAUSED) : (target[$P] &= ~IS_PAUSED)\n", "import { <PERSON><PERSON>r<PERSON>ore, UnknownProps, Lookup, Falsy } from '@react-spring/types'\nimport {\n  is,\n  raf,\n  each,\n  noop,\n  flush,\n  toArray,\n  eachProp,\n  flushCalls,\n  addFluidObserver,\n  FluidObserver,\n} from '@react-spring/shared'\n\nimport { getDefaultProp } from './helpers'\nimport { FrameValue } from './FrameValue'\nimport type { SpringRef } from './SpringRef'\nimport { SpringValue, createLoopUpdate, createUpdate } from './SpringValue'\nimport { getCancelledResult, getCombinedResult } from './AnimationResult'\nimport { runAsync, RunAsyncState, stopAsync } from './runAsync'\nimport { scheduleProps } from './scheduleProps'\nimport {\n  AnimationResult,\n  AsyncResult,\n  ControllerFlushFn,\n  ControllerUpdate,\n  OnChange,\n  OnRest,\n  OnStart,\n  SpringChain,\n  SpringToFn,\n  SpringValues,\n} from './types'\n\n/** Events batched by the `Controller` class */\nconst BATCHED_EVENTS = ['onStart', 'onChange', 'onRest'] as const\n\nlet nextId = 1\n\n/** Queue of pending updates for a `Controller` instance. */\nexport interface ControllerQueue<State extends Lookup = Lookup>\n  extends Array<\n    ControllerUpdate<State, any> & {\n      /** The keys affected by this update. When null, all keys are affected. */\n      keys: string[] | null\n    }\n  > {}\n\nexport class Controller<State extends Lookup = Lookup> {\n  readonly id = nextId++\n\n  /** The animated values */\n  springs: SpringValues<State> = {} as any\n\n  /** The queue of props passed to the `update` method. */\n  queue: ControllerQueue<State> = []\n\n  /**\n   * The injected ref. When defined, render-based updates are pushed\n   * onto the `queue` instead of being auto-started.\n   */\n  ref?: SpringRef<State>\n\n  /** Custom handler for flushing update queues */\n  protected _flush?: ControllerFlushFn<this>\n\n  /** These props are used by all future spring values */\n  protected _initialProps?: Lookup\n\n  /** The counter for tracking `scheduleProps` calls */\n  protected _lastAsyncId = 0\n\n  /** The values currently being animated */\n  protected _active = new Set<FrameValue>()\n\n  /** The values that changed recently */\n  protected _changed = new Set<FrameValue>()\n\n  /** Equals false when `onStart` listeners can be called */\n  protected _started = false\n\n  private _item?: any\n\n  /** State used by the `runAsync` function */\n  protected _state: RunAsyncState<this> = {\n    paused: false,\n    pauseQueue: new Set(),\n    resumeQueue: new Set(),\n    timeouts: new Set(),\n  }\n\n  /** The event queues that are flushed once per frame maximum */\n  protected _events = {\n    onStart: new Map<\n      OnStart<SpringValue<State>, Controller<State>, any>,\n      AnimationResult\n    >(),\n    onChange: new Map<\n      OnChange<SpringValue<State>, Controller<State>, any>,\n      AnimationResult\n    >(),\n    onRest: new Map<\n      OnRest<SpringValue<State>, Controller<State>, any>,\n      AnimationResult\n    >(),\n  }\n\n  constructor(\n    props?: ControllerUpdate<State> | null,\n    flush?: ControllerFlushFn<any>\n  ) {\n    this._onFrame = this._onFrame.bind(this)\n    if (flush) {\n      this._flush = flush\n    }\n    if (props) {\n      this.start({ default: true, ...props })\n    }\n  }\n\n  /**\n   * Equals `true` when no spring values are in the frameloop, and\n   * no async animation is currently active.\n   */\n  get idle() {\n    return (\n      !this._state.asyncTo &&\n      Object.values(this.springs as Lookup<SpringValue>).every(spring => {\n        return spring.idle && !spring.isDelayed && !spring.isPaused\n      })\n    )\n  }\n\n  get item() {\n    return this._item\n  }\n\n  set item(item) {\n    this._item = item\n  }\n\n  /** Get the current values of our springs */\n  get(): State & UnknownProps {\n    const values: any = {}\n    this.each((spring, key) => (values[key] = spring.get()))\n    return values\n  }\n\n  /** Set the current values without animating. */\n  set(values: Partial<State>) {\n    for (const key in values) {\n      const value = values[key]\n      if (!is.und(value)) {\n        this.springs[key].set(value)\n      }\n    }\n  }\n\n  /** Push an update onto the queue of each value. */\n  update(props: ControllerUpdate<State> | Falsy) {\n    if (props) {\n      this.queue.push(createUpdate(props))\n    }\n    return this\n  }\n\n  /**\n   * Start the queued animations for every spring, and resolve the returned\n   * promise once all queued animations have finished or been cancelled.\n   *\n   * When you pass a queue (instead of nothing), that queue is used instead of\n   * the queued animations added with the `update` method, which are left alone.\n   */\n  start(props?: OneOrMore<ControllerUpdate<State>> | null): AsyncResult<this> {\n    let { queue } = this as any\n    if (props) {\n      queue = toArray<any>(props).map(createUpdate)\n    } else {\n      this.queue = []\n    }\n\n    if (this._flush) {\n      return this._flush(this, queue)\n    }\n\n    prepareKeys(this, queue)\n    return flushUpdateQueue(this, queue)\n  }\n\n  /** Stop all animations. */\n  stop(): this\n  /** Stop animations for the given keys. */\n  stop(keys: OneOrMore<string>): this\n  /** Cancel all animations. */\n  stop(cancel: boolean): this\n  /** Cancel animations for the given keys. */\n  stop(cancel: boolean, keys: OneOrMore<string>): this\n  /** Stop some or all animations. */\n  stop(keys?: OneOrMore<string>): this\n  /** Cancel some or all animations. */\n  stop(cancel: boolean, keys?: OneOrMore<string>): this\n  /** @internal */\n  stop(arg?: boolean | OneOrMore<string>, keys?: OneOrMore<string>) {\n    if (arg !== !!arg) {\n      keys = arg as OneOrMore<string>\n    }\n    if (keys) {\n      const springs = this.springs as Lookup<SpringValue>\n      each(toArray(keys) as string[], key => springs[key].stop(!!arg))\n    } else {\n      stopAsync(this._state, this._lastAsyncId)\n      this.each(spring => spring.stop(!!arg))\n    }\n    return this\n  }\n\n  /** Freeze the active animation in time */\n  pause(keys?: OneOrMore<string>) {\n    if (is.und(keys)) {\n      this.start({ pause: true })\n    } else {\n      const springs = this.springs as Lookup<SpringValue>\n      each(toArray(keys) as string[], key => springs[key].pause())\n    }\n    return this\n  }\n\n  /** Resume the animation if paused. */\n  resume(keys?: OneOrMore<string>) {\n    if (is.und(keys)) {\n      this.start({ pause: false })\n    } else {\n      const springs = this.springs as Lookup<SpringValue>\n      each(toArray(keys) as string[], key => springs[key].resume())\n    }\n    return this\n  }\n\n  /** Call a function once per spring value */\n  each(iterator: (spring: SpringValue, key: string) => void) {\n    eachProp(this.springs, iterator as any)\n  }\n\n  /** @internal Called at the end of every animation frame */\n  protected _onFrame() {\n    const { onStart, onChange, onRest } = this._events\n\n    const active = this._active.size > 0\n    const changed = this._changed.size > 0\n\n    if ((active && !this._started) || (changed && !this._started)) {\n      this._started = true\n      flush(onStart, ([onStart, result]) => {\n        result.value = this.get()\n        onStart(result, this, this._item)\n      })\n    }\n\n    const idle = !active && this._started\n    const values = changed || (idle && onRest.size) ? this.get() : null\n\n    if (changed && onChange.size) {\n      flush(onChange, ([onChange, result]) => {\n        result.value = values\n        onChange(result, this, this._item)\n      })\n    }\n\n    // The \"onRest\" queue is only flushed when all springs are idle.\n    if (idle) {\n      this._started = false\n      flush(onRest, ([onRest, result]) => {\n        result.value = values\n        onRest(result, this, this._item)\n      })\n    }\n  }\n\n  /** @internal */\n  eventObserved(event: FrameValue.Event) {\n    if (event.type == 'change') {\n      this._changed.add(event.parent)\n      if (!event.idle) {\n        this._active.add(event.parent)\n      }\n    } else if (event.type == 'idle') {\n      this._active.delete(event.parent)\n    }\n    // The `onFrame` handler runs when a parent is changed or idle.\n    else return\n    raf.onFrame(this._onFrame)\n  }\n}\n\n/**\n * Warning: Props might be mutated.\n */\nexport function flushUpdateQueue(\n  ctrl: Controller<any>,\n  queue: ControllerQueue\n) {\n  return Promise.all(queue.map(props => flushUpdate(ctrl, props))).then(\n    results => getCombinedResult(ctrl, results)\n  )\n}\n\n/**\n * Warning: Props might be mutated.\n *\n * Process a single set of props using the given controller.\n *\n * The returned promise resolves to `true` once the update is\n * applied and any animations it starts are finished without being\n * stopped or cancelled.\n */\nexport async function flushUpdate(\n  ctrl: Controller<any>,\n  props: ControllerQueue[number],\n  isLoop?: boolean\n): AsyncResult {\n  const { keys, to, from, loop, onRest, onResolve } = props\n  const defaults = is.obj(props.default) && props.default\n\n  // Looping must be handled in this function, or else the values\n  // would end up looping out-of-sync in many common cases.\n  if (loop) {\n    props.loop = false\n  }\n\n  // Treat false like null, which gets ignored.\n  if (to === false) props.to = null\n  if (from === false) props.from = null\n\n  const asyncTo = is.arr(to) || is.fun(to) ? to : undefined\n  if (asyncTo) {\n    props.to = undefined\n    props.onRest = undefined\n    if (defaults) {\n      defaults.onRest = undefined\n    }\n  }\n  // For certain events, use batching to prevent multiple calls per frame.\n  // However, batching is avoided when the `to` prop is async, because any\n  // event props are used as default props instead.\n  else {\n    each(BATCHED_EVENTS, key => {\n      const handler: any = props[key]\n      if (is.fun(handler)) {\n        const queue = ctrl['_events'][key]\n        props[key] = (({ finished, cancelled }: AnimationResult) => {\n          const result = queue.get(handler)\n          if (result) {\n            if (!finished) result.finished = false\n            if (cancelled) result.cancelled = true\n          } else {\n            // The \"value\" is set before the \"handler\" is called.\n            queue.set(handler, {\n              value: null,\n              finished: finished || false,\n              cancelled: cancelled || false,\n            })\n          }\n        }) as any\n\n        // Avoid using a batched `handler` as a default prop.\n        if (defaults) {\n          defaults[key] = props[key] as any\n        }\n      }\n    })\n  }\n\n  const state = ctrl['_state']\n\n  // Pause/resume the `asyncTo` when `props.pause` is true/false.\n  if (props.pause === !state.paused) {\n    state.paused = props.pause\n    flushCalls(props.pause ? state.pauseQueue : state.resumeQueue)\n  }\n  // When a controller is paused, its values are also paused.\n  else if (state.paused) {\n    props.pause = true\n  }\n\n  const promises: AsyncResult[] = (keys || Object.keys(ctrl.springs)).map(key =>\n    ctrl.springs[key]!.start(props as any)\n  )\n\n  const cancel =\n    props.cancel === true || getDefaultProp(props, 'cancel') === true\n\n  if (asyncTo || (cancel && state.asyncId)) {\n    promises.push(\n      scheduleProps(++ctrl['_lastAsyncId'], {\n        props,\n        state,\n        actions: {\n          pause: noop,\n          resume: noop,\n          start(props, resolve) {\n            if (cancel) {\n              stopAsync(state, ctrl['_lastAsyncId'])\n              resolve(getCancelledResult(ctrl))\n            } else {\n              props.onRest = onRest\n              resolve(\n                runAsync(\n                  asyncTo as SpringChain | SpringToFn,\n                  props,\n                  state,\n                  ctrl\n                )\n              )\n            }\n          },\n        },\n      })\n    )\n  }\n\n  // Pause after updating each spring, so they can be resumed separately\n  // and so their default `pause` and `cancel` props are updated.\n  if (state.paused) {\n    // Ensure `this` must be resumed before the returned promise\n    // is resolved and before starting the next `loop` repetition.\n    await new Promise<void>(resume => {\n      state.resumeQueue.add(resume)\n    })\n  }\n\n  const result = getCombinedResult<any>(ctrl, await Promise.all(promises))\n  if (loop && result.finished && !(isLoop && result.noop)) {\n    const nextProps = createLoopUpdate(props, loop, to)\n    if (nextProps) {\n      prepareKeys(ctrl, [nextProps])\n      return flushUpdate(ctrl, nextProps, true)\n    }\n  }\n  if (onResolve) {\n    raf.batchedUpdates(() => onResolve(result, ctrl, ctrl.item))\n  }\n  return result\n}\n\n/**\n * From an array of updates, get the map of `SpringValue` objects\n * by their keys. Springs are created when any update wants to\n * animate a new key.\n *\n * Springs created by `getSprings` are neither cached nor observed\n * until they're given to `setSprings`.\n */\nexport function getSprings<State extends Lookup>(\n  ctrl: Controller<Lookup<any>>,\n  props?: OneOrMore<ControllerUpdate<State>>\n) {\n  const springs = { ...ctrl.springs }\n  if (props) {\n    each(toArray(props), (props: any) => {\n      if (is.und(props.keys)) {\n        props = createUpdate(props)\n      }\n      if (!is.obj(props.to)) {\n        // Avoid passing array/function to each spring.\n        props = { ...props, to: undefined }\n      }\n      prepareSprings(springs as any, props, key => {\n        return createSpring(key)\n      })\n    })\n  }\n  setSprings(ctrl, springs)\n  return springs\n}\n\n/**\n * Tell a controller to manage the given `SpringValue` objects\n * whose key is not already in use.\n */\nexport function setSprings(\n  ctrl: Controller<Lookup<any>>,\n  springs: SpringValues<UnknownProps>\n) {\n  eachProp(springs, (spring, key) => {\n    if (!ctrl.springs[key]) {\n      ctrl.springs[key] = spring\n      addFluidObserver(spring, ctrl)\n    }\n  })\n}\n\nfunction createSpring(key: string, observer?: FluidObserver<FrameValue.Event>) {\n  const spring = new SpringValue()\n  spring.key = key\n  if (observer) {\n    addFluidObserver(spring, observer)\n  }\n  return spring\n}\n\n/**\n * Ensure spring objects exist for each defined key.\n *\n * Using the `props`, the `Animated` node of each `SpringValue` may\n * be created or updated.\n */\nfunction prepareSprings(\n  springs: SpringValues,\n  props: ControllerQueue[number],\n  create: (key: string) => SpringValue\n) {\n  if (props.keys) {\n    each(props.keys, key => {\n      const spring = springs[key] || (springs[key] = create(key))\n      spring['_prepareNode'](props)\n    })\n  }\n}\n\n/**\n * Ensure spring objects exist for each defined key, and attach the\n * `ctrl` to them for observation.\n *\n * The queue is expected to contain `createUpdate` results.\n */\nfunction prepareKeys(ctrl: Controller<any>, queue: ControllerQueue[number][]) {\n  each(queue, props => {\n    prepareSprings(ctrl.springs, props, key => {\n      return createSpring(key, ctrl)\n    })\n  })\n}\n", "import * as React from 'react'\nimport { useContext, PropsWithChildren } from 'react'\nimport { useMemoOne } from '@react-spring/shared'\n\n/**\n * This context affects all new and existing `SpringValue` objects\n * created with the hook API or the renderprops API.\n */\nexport interface SpringContext {\n  /** Pause all new and existing animations. */\n  pause?: boolean\n  /** Force all new and existing animations to be immediate. */\n  immediate?: boolean\n}\n\nexport const SpringContext = ({\n  children,\n  ...props\n}: PropsWithChildren<SpringContext>) => {\n  const inherited = useContext(ctx)\n\n  // Inherited values are dominant when truthy.\n  const pause = props.pause || !!inherited.pause,\n    immediate = props.immediate || !!inherited.immediate\n\n  // Memoize the context to avoid unwanted renders.\n  props = useMemoOne(() => ({ pause, immediate }), [pause, immediate])\n\n  const { Provider } = ctx\n  return <Provider value={props}>{children}</Provider>\n}\n\nconst ctx = makeContext(SpringContext, {} as SpringContext)\n\n// Allow `useContext(SpringContext)` in TypeScript.\nSpringContext.Provider = ctx.Provider\nSpringContext.Consumer = ctx.Consumer\n\n/** Make the `target` compatible with `useContext` */\nfunction makeContext<T>(target: any, init: T): React.Context<T> {\n  Object.assign(target, React.createContext(init))\n  target.Provider._context = target\n  target.Consumer._context = target\n  return target\n}\n", "import { each, is, deprecateDirect<PERSON>all } from '@react-spring/shared'\nimport { Lookup, Falsy, OneOrMore } from '@react-spring/types'\nimport { AsyncResult, ControllerUpdate } from './types'\nimport { Controller } from './Controller'\n\nexport interface ControllerUpdateFn<State extends Lookup = Lookup> {\n  (i: number, ctrl: Controller<State>): ControllerUpdate<State> | Falsy\n}\n\nexport interface SpringRef<State extends Lookup = Lookup> {\n  (\n    props?: ControllerUpdate<State> | ControllerUpdateFn<State>\n  ): AsyncResult<Controller<State>>[]\n  current: Controller<State>[]\n\n  /** Add a controller to this ref */\n  add(ctrl: Controller<State>): void\n\n  /** Remove a controller from this ref */\n  delete(ctrl: Controller<State>): void\n\n  /** Pause all animations. */\n  pause(): this\n  /** Pause animations for the given keys. */\n  pause(keys: OneOrMore<string>): this\n  /** Pause some or all animations. */\n  pause(keys?: OneOrMore<string>): this\n\n  /** Resume all animations. */\n  resume(): this\n  /** Resume animations for the given keys. */\n  resume(keys: OneOrMore<string>): this\n  /** Resume some or all animations. */\n  resume(keys?: OneOrMore<string>): this\n\n  /** Update the state of each controller without animating. */\n  set(values: Partial<State>): void\n  /** Update the state of each controller without animating based on their passed state. */\n  set(values: (index: number, ctrl: Controller<State>) => Partial<State>): void\n\n  /** Start the queued animations of each controller. */\n  start(): AsyncResult<Controller<State>>[]\n  /** Update every controller with the same props. */\n  start(props: ControllerUpdate<State>): AsyncResult<Controller<State>>[]\n  /** Update controllers based on their state. */\n  start(props: ControllerUpdateFn<State>): AsyncResult<Controller<State>>[]\n  /** Start animating each controller. */\n  start(\n    props?: ControllerUpdate<State> | ControllerUpdateFn<State>\n  ): AsyncResult<Controller<State>>[]\n\n  /** Stop all animations. */\n  stop(): this\n  /** Stop animations for the given keys. */\n  stop(keys: OneOrMore<string>): this\n  /** Cancel all animations. */\n  stop(cancel: boolean): this\n  /** Cancel animations for the given keys. */\n  stop(cancel: boolean, keys: OneOrMore<string>): this\n  /** Stop some or all animations. */\n  stop(keys?: OneOrMore<string>): this\n  /** Cancel some or all animations. */\n  stop(cancel: boolean, keys?: OneOrMore<string>): this\n\n  /** Add the same props to each controller's update queue. */\n  update(props: ControllerUpdate<State>): this\n  /** Generate separate props for each controller's update queue. */\n  update(props: ControllerUpdateFn<State>): this\n  /** Add props to each controller's update queue. */\n  update(props: ControllerUpdate<State> | ControllerUpdateFn<State>): this\n\n  _getProps(\n    arg: ControllerUpdate<State> | ControllerUpdateFn<State>,\n    ctrl: Controller<State>,\n    index: number\n  ): ControllerUpdate<State> | Falsy\n}\n\nexport const SpringRef = <\n  State extends Lookup = Lookup,\n>(): SpringRef<State> => {\n  const current: Controller<State>[] = []\n\n  const SpringRef: SpringRef<State> = function (props) {\n    deprecateDirectCall()\n\n    const results: AsyncResult[] = []\n\n    each(current, (ctrl, i) => {\n      if (is.und(props)) {\n        results.push(ctrl.start())\n      } else {\n        const update = _getProps(props, ctrl, i)\n        if (update) {\n          results.push(ctrl.start(update))\n        }\n      }\n    })\n\n    return results\n  }\n\n  SpringRef.current = current\n\n  /** Add a controller to this ref */\n  SpringRef.add = function (ctrl: Controller<State>) {\n    if (!current.includes(ctrl)) {\n      current.push(ctrl)\n    }\n  }\n\n  /** Remove a controller from this ref */\n  SpringRef.delete = function (ctrl: Controller<State>) {\n    const i = current.indexOf(ctrl)\n    if (~i) current.splice(i, 1)\n  }\n\n  /** Pause all animations. */\n  SpringRef.pause = function () {\n    each(current, ctrl => ctrl.pause(...arguments))\n    return this\n  }\n\n  /** Resume all animations. */\n  SpringRef.resume = function () {\n    each(current, ctrl => ctrl.resume(...arguments))\n    return this\n  }\n\n  /** Update the state of each controller without animating. */\n  SpringRef.set = function (\n    values:\n      | Partial<State>\n      | ((i: number, ctrl: Controller<State>) => Partial<State>)\n  ) {\n    each(current, (ctrl, i) => {\n      const update = is.fun(values) ? values(i, ctrl) : values\n      if (update) {\n        ctrl.set(update)\n      }\n    })\n  }\n\n  SpringRef.start = function (props?: object | ControllerUpdateFn<State>) {\n    const results: AsyncResult[] = []\n\n    each(current, (ctrl, i) => {\n      if (is.und(props)) {\n        results.push(ctrl.start())\n      } else {\n        const update = this._getProps(props, ctrl, i)\n        if (update) {\n          results.push(ctrl.start(update))\n        }\n      }\n    })\n\n    return results\n  }\n\n  /** Stop all animations. */\n  SpringRef.stop = function () {\n    each(current, ctrl => ctrl.stop(...arguments))\n    return this\n  }\n\n  SpringRef.update = function (props: object | ControllerUpdateFn<State>) {\n    each(current, (ctrl, i) => ctrl.update(this._getProps(props, ctrl, i)))\n    return this\n  }\n\n  /** Overridden by `useTrail` to manipulate props */\n  const _getProps = function (\n    arg: ControllerUpdate<State> | ControllerUpdateFn<State>,\n    ctrl: Controller<State>,\n    index: number\n  ) {\n    return is.fun(arg) ? arg(index, ctrl) : arg\n  }\n\n  SpringRef._getProps = _getProps\n\n  return SpringRef\n}\n", "import { useState } from 'react'\nimport { Lookup } from '@react-spring/types'\nimport { SpringRef } from '../SpringRef'\nimport type { SpringRef as SpringRefType } from '../SpringRef'\n\nconst initSpringRef = () => SpringRef<any>()\n\nexport const useSpringRef = <State extends Lookup = Lookup>() =>\n  useState(initSpringRef)[0] as SpringRefType<State>\n", "import { useConstant, useOnce } from '@react-spring/shared'\n\nimport { SpringValue } from '../SpringValue'\nimport { SpringUpdate } from '../types'\n\n/**\n * Creates a constant single `SpringValue` that can be interacted\n * with imperatively. This is an advanced API and does not react\n * to updates from the parent component e.g. passing a new initial value\n *\n *\n * ```jsx\n * export const MyComponent = () => {\n *   const opacity = useSpringValue(1)\n *\n *   return <animated.div style={{ opacity }} />\n * }\n * ```\n *\n * @param initial – The initial value of the `SpringValue`.\n * @param props – Typically the same props as `useSpring` e.g. `config`, `loop` etc.\n *\n * @public\n */\nexport const useSpringValue = <T>(\n  initial: Exclude<T, object>,\n  props?: SpringUpdate<T>\n) => {\n  const springValue = useConstant(() => new SpringValue(initial, props))\n\n  useOnce(() => () => {\n    springValue.stop()\n  })\n\n  return springValue\n}\n", "import { each, is, useIsomorphicLayoutEffect } from '@react-spring/shared'\nimport { Lookup } from '@react-spring/types'\n\nimport { Valid } from '../types/common'\nimport { PickAnimated, SpringValues } from '../types'\n\nimport { SpringRef } from '../SpringRef'\nimport { Controller } from '../Controller'\n\nimport { UseSpringProps } from './useSpring'\nimport { useSprings } from './useSprings'\nimport { replaceRef } from '../helpers'\n\nexport type UseTrailProps<Props extends object = any> = UseSpringProps<Props>\n\nexport function useTrail<Props extends object>(\n  length: number,\n  props: (\n    i: number,\n    ctrl: Controller\n  ) => UseTrailProps | (Props & Valid<Props, UseTrailProps<Props>>),\n  deps?: readonly any[]\n): PickAnimated<Props> extends infer State\n  ? State extends Lookup<any>\n    ? [SpringValues<State>[], SpringRef<State>]\n    : never\n  : never\n\n/**\n * This hook is an abstraction around `useSprings` and is designed to\n * automatically orchestrate the springs to stagger one after the other\n *\n * ```jsx\n * export const MyComponent = () => {\n *  const trails = useTrail(3, {opacity: 0})\n *\n *  return trails.map(styles => <animated.div style={styles} />)\n * }\n * ```\n *\n * @param length – The number of springs you want to create\n * @param propsArg – The props to pass to the internal `useSprings` hook,\n * therefore is the same as `useSprings`.\n *\n * @public\n */\nexport function useTrail<Props extends object>(\n  length: number,\n  props: UseTrailProps | (Props & Valid<Props, UseTrailProps<Props>>)\n): SpringValues<PickAnimated<Props>>[]\n\n/**\n * This hook is an abstraction around `useSprings` and is designed to\n * automatically orchestrate the springs to stagger one after the other\n *\n * ```jsx\n * export const MyComponent = () => {\n *  const trails = useTrail(3, {opacity: 0}, [])\n *\n *  return trails.map(styles => <animated.div style={styles} />)\n * }\n * ```\n *\n * @param length – The number of springs you want to create\n * @param propsArg – The props to pass to the internal `useSprings` hook,\n * therefore is the same as `useSprings`.\n * @param deps – The optional array of dependencies to pass to the internal\n * `useSprings` hook, therefore is the same as `useSprings`.\n *\n * @public\n */\nexport function useTrail<Props extends object>(\n  length: number,\n  props: UseTrailProps | (Props & Valid<Props, UseTrailProps<Props>>),\n  deps: readonly any[]\n): PickAnimated<Props> extends infer State\n  ? State extends Lookup<any>\n    ? [SpringValues<State>[], SpringRef<State>]\n    : never\n  : never\n\nexport function useTrail(\n  length: number,\n  propsArg: unknown,\n  deps?: readonly any[]\n) {\n  const propsFn = is.fun(propsArg) && propsArg\n  if (propsFn && !deps) deps = []\n\n  // The trail is reversed when every render-based update is reversed.\n  let reverse = true\n  let passedRef: SpringRef | undefined = undefined\n\n  const result = useSprings(\n    length,\n    (i, ctrl) => {\n      const props = propsFn ? propsFn(i, ctrl) : propsArg\n      passedRef = props.ref\n      reverse = reverse && props.reverse\n\n      return props\n    },\n    // Ensure the props function is called when no deps exist.\n    // This works around the 3 argument rule.\n    deps || [{}]\n  )\n\n  useIsomorphicLayoutEffect(() => {\n    /**\n     * Run through the ref passed by the `useSprings` hook.\n     */\n    each(result[1].current, (ctrl, i) => {\n      const parent = result[1].current[i + (reverse ? 1 : -1)]\n\n      /**\n       * If there's a passed ref then we replace the ctrl ref with it\n       */\n      replaceRef(ctrl, passedRef)\n\n      /**\n       * And if there's a ctrl ref then we update instead of start\n       * which means nothing is fired until the start method\n       * of said passedRef is called.\n       */\n      if (ctrl.ref) {\n        if (parent) {\n          ctrl.update({ to: parent.springs })\n        }\n\n        return\n      }\n\n      if (parent) {\n        ctrl.start({ to: parent.springs })\n      } else {\n        ctrl.start()\n      }\n    })\n  }, deps)\n\n  if (propsFn || arguments.length == 3) {\n    const ref = passedRef ?? result[1]\n\n    ref['_getProps'] = (propsArg, ctrl, i) => {\n      const props = is.fun(propsArg) ? propsArg(i, ctrl) : propsArg\n      if (props) {\n        const parent = ref.current[i + (props.reverse ? 1 : -1)]\n        if (parent) props.to = parent.springs\n        return props\n      }\n    }\n    return result\n  }\n\n  return result[0]\n}\n", "import * as React from 'react'\nimport { useContext, useRef, useMemo } from 'react'\nimport { Lookup, OneOrMore, UnknownProps } from '@react-spring/types'\nimport {\n  is,\n  toArray,\n  useForceUpdate,\n  useOnce,\n  usePrev,\n  each,\n  useIsomorphicLayoutEffect,\n} from '@react-spring/shared'\n\nimport {\n  Change,\n  ControllerUpdate,\n  ItemKeys,\n  PickAnimated,\n  TransitionFn,\n  TransitionState,\n  TransitionTo,\n  UseTransitionProps,\n} from '../types'\nimport { Valid } from '../types/common'\nimport {\n  callProp,\n  detachRefs,\n  getDefaultProps,\n  hasProps,\n  inferTo,\n  replaceRef,\n} from '../helpers'\nimport { Controller, getSprings } from '../Controller'\nimport { SpringContext } from '../SpringContext'\nimport { SpringRef } from '../SpringRef'\nimport type { SpringRef as SpringRefType } from '../SpringRef'\nimport { TransitionPhase } from '../TransitionPhase'\n\ndeclare function setTimeout(handler: Function, timeout?: number): number\ndeclare function clearTimeout(timeoutId: number): void\n\nexport function useTransition<Item, Props extends object>(\n  data: OneOrMore<Item>,\n  props: () =>\n    | UseTransitionProps<Item>\n    | (Props & Valid<Props, UseTransitionProps<Item>>),\n  deps?: any[]\n): PickAnimated<Props> extends infer State\n  ? State extends Lookup\n    ? [TransitionFn<Item, PickAnimated<Props>>, SpringRefType<State>]\n    : never\n  : never\n\nexport function useTransition<Item, Props extends object>(\n  data: OneOrMore<Item>,\n  props:\n    | UseTransitionProps<Item>\n    | (Props & Valid<Props, UseTransitionProps<Item>>)\n): TransitionFn<Item, PickAnimated<Props>>\n\nexport function useTransition<Item, Props extends object>(\n  data: OneOrMore<Item>,\n  props:\n    | UseTransitionProps<Item>\n    | (Props & Valid<Props, UseTransitionProps<Item>>),\n  deps: any[] | undefined\n): PickAnimated<Props> extends infer State\n  ? State extends Lookup\n    ? [TransitionFn<Item, State>, SpringRefType<State>]\n    : never\n  : never\n\nexport function useTransition(\n  data: unknown,\n  props: UseTransitionProps | (() => any),\n  deps?: any[]\n): any {\n  const propsFn = is.fun(props) && props\n\n  const {\n    reset,\n    sort,\n    trail = 0,\n    expires = true,\n    exitBeforeEnter = false,\n    onDestroyed,\n    ref: propsRef,\n    config: propsConfig,\n  }: UseTransitionProps<any> = propsFn ? propsFn() : props\n\n  // Return a `SpringRef` if a deps array was passed.\n  const ref = useMemo(\n    () => (propsFn || arguments.length == 3 ? SpringRef() : void 0),\n    []\n  )\n\n  // Every item has its own transition.\n  const items = toArray(data)\n  const transitions: TransitionState[] = []\n\n  // The \"onRest\" callbacks need a ref to the latest transitions.\n  const usedTransitions = useRef<TransitionState[] | null>(null)\n  const prevTransitions = reset ? null : usedTransitions.current\n\n  useIsomorphicLayoutEffect(() => {\n    usedTransitions.current = transitions\n  })\n\n  useOnce(() => {\n    /**\n     * If transitions exist on mount of the component\n     * then reattach their refs on-mount, this was required\n     * for react18 strict mode to work properly.\n     *\n     * See https://github.com/pmndrs/react-spring/issues/1890\n     */\n\n    each(transitions, t => {\n      ref?.add(t.ctrl)\n      t.ctrl.ref = ref\n    })\n\n    // Destroy all transitions on dismount.\n    return () => {\n      each(usedTransitions.current!, t => {\n        if (t.expired) {\n          clearTimeout(t.expirationId!)\n        }\n        detachRefs(t.ctrl, ref)\n        t.ctrl.stop(true)\n      })\n    }\n  })\n\n  // Keys help with reusing transitions between renders.\n  // The `key` prop can be undefined (which means the items themselves are used\n  // as keys), or a function (which maps each item to its key), or an array of\n  // keys (which are assigned to each item by index).\n  const keys = getKeys(items, propsFn ? propsFn() : props, prevTransitions)\n\n  // Expired transitions that need clean up.\n  const expired = (reset && usedTransitions.current) || []\n  useIsomorphicLayoutEffect(() =>\n    each(expired, ({ ctrl, item, key }) => {\n      detachRefs(ctrl, ref)\n      callProp(onDestroyed, item, key)\n    })\n  )\n\n  // Map old indices to new indices.\n  const reused: number[] = []\n  if (prevTransitions)\n    each(prevTransitions, (t, i) => {\n      // Expired transitions are not rendered.\n      if (t.expired) {\n        clearTimeout(t.expirationId!)\n        expired.push(t)\n      } else {\n        i = reused[i] = keys.indexOf(t.key)\n        if (~i) transitions[i] = t\n      }\n    })\n\n  // Mount new items with fresh transitions.\n  each(items, (item, i) => {\n    if (!transitions[i]) {\n      transitions[i] = {\n        key: keys[i],\n        item,\n        phase: TransitionPhase.MOUNT,\n        ctrl: new Controller(),\n      }\n\n      transitions[i].ctrl.item = item\n    }\n  })\n\n  // Update the item of any transition whose key still exists,\n  // and ensure leaving transitions are rendered until they finish.\n  if (reused.length) {\n    let i = -1\n    const { leave }: UseTransitionProps<any> = propsFn ? propsFn() : props\n    each(reused, (keyIndex, prevIndex) => {\n      const t = prevTransitions![prevIndex]\n      if (~keyIndex) {\n        i = transitions.indexOf(t)\n        transitions[i] = { ...t, item: items[keyIndex] }\n      } else if (leave) {\n        transitions.splice(++i, 0, t)\n      }\n    })\n  }\n\n  if (is.fun(sort)) {\n    transitions.sort((a, b) => sort(a.item, b.item))\n  }\n\n  // Track cumulative delay for the \"trail\" prop.\n  let delay = -trail\n\n  // Expired transitions use this to dismount.\n  const forceUpdate = useForceUpdate()\n\n  // These props are inherited by every phase change.\n  const defaultProps = getDefaultProps<UseTransitionProps>(props)\n  // Generate changes to apply in useEffect.\n  const changes = new Map<TransitionState, Change>()\n  const exitingTransitions = useRef(new Map<TransitionState, Change>())\n\n  const forceChange = useRef(false)\n  each(transitions, (t, i) => {\n    const key = t.key\n    const prevPhase = t.phase\n\n    const p: UseTransitionProps<any> = propsFn ? propsFn() : props\n\n    let to: TransitionTo<any>\n    let phase: TransitionPhase\n\n    const propsDelay = callProp(p.delay || 0, key)\n\n    if (prevPhase == TransitionPhase.MOUNT) {\n      to = p.enter\n      phase = TransitionPhase.ENTER\n    } else {\n      const isLeave = keys.indexOf(key) < 0\n      if (prevPhase != TransitionPhase.LEAVE) {\n        if (isLeave) {\n          to = p.leave\n          phase = TransitionPhase.LEAVE\n        } else if ((to = p.update)) {\n          phase = TransitionPhase.UPDATE\n        } else return\n      } else if (!isLeave) {\n        to = p.enter\n        phase = TransitionPhase.ENTER\n      } else return\n    }\n\n    // When \"to\" is a function, it can return (1) an array of \"useSpring\" props,\n    // (2) an async function, or (3) an object with any \"useSpring\" props.\n    to = callProp(to, t.item, i)\n    to = is.obj(to) ? inferTo(to) : { to }\n\n    /**\n     * This would allow us to give different delays for phases.\n     * If we were to do this, we'd have to suffle the prop\n     * spreading below to set delay last.\n     * But if we were going to do that, we should consider letting\n     * the prop trail also be part of a phase.\n     */\n    // if (to.delay) {\n    //   phaseDelay = callProp(to.delay, key)\n    // }\n\n    if (!to.config) {\n      const config = propsConfig || defaultProps.config\n      to.config = callProp(config, t.item, i, phase)\n    }\n\n    delay += trail\n\n    // The payload is used to update the spring props once the current render is committed.\n    const payload: ControllerUpdate<UnknownProps> = {\n      ...defaultProps,\n      // we need to add our props.delay value you here.\n      delay: propsDelay + delay,\n      ref: propsRef,\n      immediate: p.immediate,\n      // This prevents implied resets.\n      reset: false,\n      // Merge any phase-specific props.\n      ...(to as any),\n    }\n\n    if (phase == TransitionPhase.ENTER && is.und(payload.from)) {\n      const p = propsFn ? propsFn() : props\n      // The `initial` prop is used on the first render of our parent component,\n      // as well as when `reset: true` is passed. It overrides the `from` prop\n      // when defined, and it makes `enter` instant when null.\n      const from = is.und(p.initial) || prevTransitions ? p.from : p.initial\n\n      payload.from = callProp(from, t.item, i)\n    }\n\n    const { onResolve } = payload\n    payload.onResolve = result => {\n      callProp(onResolve, result)\n\n      const transitions = usedTransitions.current!\n      const t = transitions.find(t => t.key === key)\n      if (!t) return\n\n      // Reset the phase of a cancelled enter/leave transition, so it can\n      // retry the animation on the next render.\n      if (result.cancelled && t.phase != TransitionPhase.UPDATE) {\n        /**\n         * @legacy Reset the phase of a cancelled enter/leave transition, so it can\n         * retry the animation on the next render.\n         *\n         * Note: leaving this here made the transitioned item respawn.\n         */\n        // t.phase = prevPhase\n        return\n      }\n\n      if (t.ctrl.idle) {\n        const idle = transitions.every(t => t.ctrl.idle)\n        if (t.phase == TransitionPhase.LEAVE) {\n          const expiry = callProp(expires, t.item)\n          if (expiry !== false) {\n            const expiryMs = expiry === true ? 0 : expiry\n            t.expired = true\n\n            // Force update once the expiration delay ends.\n            if (!idle && expiryMs > 0) {\n              // The maximum timeout is 2^31-1\n              if (expiryMs <= 0x7fffffff)\n                t.expirationId = setTimeout(forceUpdate, expiryMs)\n              return\n            }\n          }\n        }\n        // Force update once idle and expired items exist.\n        if (idle && transitions.some(t => t.expired)) {\n          /**\n           * Remove the exited transition from the list\n           * this may not exist but we'll try anyway.\n           */\n          exitingTransitions.current.delete(t)\n\n          if (exitBeforeEnter) {\n            /**\n             * If we have exitBeforeEnter == true\n             * we need to force the animation to start\n             */\n            forceChange.current = true\n          }\n\n          forceUpdate()\n        }\n      }\n    }\n\n    const springs = getSprings(t.ctrl, payload)\n\n    /**\n     * Make a separate map for the exiting changes and \"regular\" changes\n     */\n    if (phase === TransitionPhase.LEAVE && exitBeforeEnter) {\n      exitingTransitions.current.set(t, { phase, springs, payload })\n    } else {\n      changes.set(t, { phase, springs, payload })\n    }\n  })\n\n  // The prop overrides from an ancestor.\n  const context = useContext(SpringContext)\n  const prevContext = usePrev(context)\n  const hasContext = context !== prevContext && hasProps(context)\n\n  // Merge the context into each transition.\n  useIsomorphicLayoutEffect(() => {\n    if (hasContext) {\n      each(transitions, t => {\n        t.ctrl.start({ default: context })\n      })\n    }\n  }, [context])\n\n  each(changes, (_, t) => {\n    /**\n     * If we have children to exit because exitBeforeEnter is\n     * set to true, we remove the transitions so they go to back\n     * to their initial state.\n     */\n    if (exitingTransitions.current.size) {\n      const ind = transitions.findIndex(state => state.key === t.key)\n      transitions.splice(ind, 1)\n    }\n  })\n\n  useIsomorphicLayoutEffect(\n    () => {\n      /*\n       * if exitingTransitions.current has a size it means we're exiting before enter\n       * so we want to map through those and fire those first.\n       */\n      each(\n        exitingTransitions.current.size ? exitingTransitions.current : changes,\n        ({ phase, payload }, t) => {\n          const { ctrl } = t\n\n          t.phase = phase\n\n          // Attach the controller to our local ref.\n          ref?.add(ctrl)\n\n          // Merge the context into new items.\n          if (hasContext && phase == TransitionPhase.ENTER) {\n            ctrl.start({ default: context })\n          }\n\n          if (payload) {\n            // Update the injected ref if needed.\n            replaceRef(ctrl, payload.ref)\n\n            /**\n             * When an injected ref exists, the update is postponed\n             * until the ref has its `start` method called.\n             * Unless we have exitBeforeEnter in which case will skip\n             * to enter the new animation straight away as if they \"overlapped\"\n             */\n            if ((ctrl.ref || ref) && !forceChange.current) {\n              ctrl.update(payload)\n            } else {\n              ctrl.start(payload)\n\n              if (forceChange.current) {\n                forceChange.current = false\n              }\n            }\n          }\n        }\n      )\n    },\n    reset ? void 0 : deps\n  )\n\n  const renderTransitions: TransitionFn = render => (\n    <>\n      {transitions.map((t, i) => {\n        const { springs } = changes.get(t) || t.ctrl\n        const elem: any = render({ ...springs }, t.item, t, i)\n        return elem && elem.type ? (\n          <elem.type\n            {...elem.props}\n            key={is.str(t.key) || is.num(t.key) ? t.key : t.ctrl.id}\n            ref={elem.ref}\n          />\n        ) : (\n          elem\n        )\n      })}\n    </>\n  )\n\n  return ref ? [renderTransitions, ref] : renderTransitions\n}\n\n/** Local state for auto-generated item keys */\nlet nextKey = 1\n\nfunction getKeys(\n  items: readonly any[],\n  { key, keys = key }: { key?: ItemKeys; keys?: ItemKeys },\n  prevTransitions: TransitionState[] | null\n): readonly any[] {\n  if (keys === null) {\n    const reused = new Set()\n    return items.map(item => {\n      const t =\n        prevTransitions &&\n        prevTransitions.find(\n          t =>\n            t.item === item &&\n            t.phase !== TransitionPhase.LEAVE &&\n            !reused.has(t)\n        )\n      if (t) {\n        reused.add(t)\n        return t.key\n      }\n      return nextKey++\n    })\n  }\n  return is.und(keys) ? items : is.fun(keys) ? items.map(keys) : toArray(keys)\n}\n", "import { MutableRefObject } from 'react'\nimport { each, onScroll, useIsomorphicLayoutEffect } from '@react-spring/shared'\n\nimport { SpringProps, SpringValues } from '../types'\n\nimport { useSpring } from './useSpring'\n\nexport interface UseScrollOptions extends Omit<SpringProps, 'to' | 'from'> {\n  container?: MutableRefObject<HTMLElement>\n}\n\n/**\n * A small utility abstraction around our signature useSpring hook. It's a great way to create\n * a scroll-linked animation. With either the raw value of distance or a 0-1 progress value.\n * You can either use the scroll values of the whole document, or just a specific element.\n *\n * \n ```jsx\n    import { useScroll, animated } from '@react-spring/web'\n\n    function MyComponent() {\n      const { scrollYProgress } = useScroll()\n\n      return (\n        <animated.div style={{ opacity: scrollYProgress }}>\n          Hello World\n        </animated.div>\n      )\n    }\n  ```\n * \n * @param {UseScrollOptions} useScrollOptions options for the useScroll hook.\n * @param {MutableRefObject<HTMLElement>} useScrollOptions.container the container to listen to scroll events on, defaults to the window.\n *\n * @returns {SpringValues<{scrollX: number; scrollY: number; scrollXProgress: number; scrollYProgress: number}>} SpringValues the collection of values returned from the inner hook\n */\nexport const useScroll = ({\n  container,\n  ...springOptions\n}: UseScrollOptions = {}): SpringValues<{\n  scrollX: number\n  scrollY: number\n  scrollXProgress: number\n  scrollYProgress: number\n}> => {\n  const [scrollValues, api] = useSpring(\n    () => ({\n      scrollX: 0,\n      scrollY: 0,\n      scrollXProgress: 0,\n      scrollYProgress: 0,\n      ...springOptions,\n    }),\n    []\n  )\n\n  useIsomorphicLayoutEffect(() => {\n    const cleanupScroll = onScroll(\n      ({ x, y }) => {\n        api.start({\n          scrollX: x.current,\n          scrollXProgress: x.progress,\n          scrollY: y.current,\n          scrollYProgress: y.progress,\n        })\n      },\n      { container: container?.current || undefined }\n    )\n\n    return () => {\n      /**\n       * Stop the springs on unmount.\n       */\n      each(Object.values(scrollValues), value => value.stop())\n\n      cleanupScroll()\n    }\n  }, [])\n\n  return scrollValues\n}\n", "import { MutableRefObject } from 'react'\nimport { onResize, each, useIsomorphicLayoutEffect } from '@react-spring/shared'\n\nimport { SpringProps, SpringValues } from '../types'\n\nimport { useSpring } from './useSpring'\n\nexport interface UseResizeOptions extends Omit<SpringProps, 'to' | 'from'> {\n  container?: MutableRefObject<HTMLElement | null | undefined>\n}\n\n/**\n * A small abstraction around the `useSpring` hook. It returns a `SpringValues` \n * object with the `width` and `height` of the element it's attached to & doesn't \n * necessarily have to be attached to the window, by passing a `container` you \n * can observe that element's size instead.\n * \n ```jsx\n    import { useResize, animated } from '@react-spring/web'\n\n    function MyComponent() {\n      const { width } = useResize()\n\n      return (\n        <animated.div style={{ width }}>\n          Hello World\n        </animated.div>\n      )\n    }\n  ```\n * \n * @param {UseResizeOptions} UseResizeOptions options for the useScroll hook.\n * @param {MutableRefObject<HTMLElement>} UseResizeOptions.container the container to listen to scroll events on, defaults to the window.\n *\n * @returns {SpringValues<{width: number; height: number;}>} SpringValues the collection of values returned from the inner hook\n */\nexport const useResize = ({\n  container,\n  ...springOptions\n}: UseResizeOptions): SpringValues<{\n  width: number\n  height: number\n}> => {\n  const [sizeValues, api] = useSpring(\n    () => ({\n      width: 0,\n      height: 0,\n      ...springOptions,\n    }),\n    []\n  )\n\n  useIsomorphicLayoutEffect(() => {\n    const cleanupScroll = onResize(\n      ({ width, height }) => {\n        api.start({\n          width,\n          height,\n          immediate:\n            sizeValues.width.get() === 0 || sizeValues.height.get() === 0,\n        })\n      },\n      { container: container?.current || undefined }\n    )\n\n    return () => {\n      /**\n       * Stop the springs on unmount.\n       */\n      each(Object.values(sizeValues), value => value.stop())\n\n      cleanupScroll()\n    }\n  }, [])\n\n  return sizeValues\n}\n", "import { RefObject, useRef, useState } from 'react'\nimport { is, useIsomorphicLayoutEffect } from '@react-spring/shared'\nimport { Lookup } from '@react-spring/types'\n\nimport { PickAnimated, SpringValues } from '../types'\nimport { useSpring, UseSpringProps } from './useSpring'\nimport { Valid } from '../types/common'\n\nexport interface IntersectionArgs\n  extends Omit<IntersectionObserverInit, 'root' | 'threshold'> {\n  root?: React.MutableRefObject<HTMLElement>\n  once?: boolean\n  amount?: 'any' | 'all' | number | number[]\n}\n\nconst defaultThresholdOptions = {\n  any: 0,\n  all: 1,\n}\n\nexport function useInView(args?: IntersectionArgs): [RefObject<any>, boolean]\nexport function useInView<Props extends object>(\n  /**\n   * TODO: make this narrower to only accept reserved props.\n   */\n  props: () => Props & Valid<Props, UseSpringProps<Props>>,\n  args?: IntersectionArgs\n): PickAnimated<Props> extends infer State\n  ? State extends Lookup\n    ? [RefObject<any>, SpringValues<State>]\n    : never\n  : never\nexport function useInView<TElement extends HTMLElement>(\n  props?: (() => UseSpringProps<any>) | IntersectionArgs,\n  args?: IntersectionArgs\n) {\n  const [isInView, setIsInView] = useState(false)\n  const ref = useRef<TElement>()\n\n  const propsFn = is.fun(props) && props\n\n  const springsProps = propsFn ? propsFn() : {}\n  const { to = {}, from = {}, ...restSpringProps } = springsProps\n\n  const intersectionArguments = propsFn ? args : props\n\n  const [springs, api] = useSpring(() => ({ from, ...restSpringProps }), [])\n\n  useIsomorphicLayoutEffect(() => {\n    const element = ref.current\n    const {\n      root,\n      once,\n      amount = 'any',\n      ...restArgs\n    } = intersectionArguments ?? {}\n\n    if (\n      !element ||\n      (once && isInView) ||\n      typeof IntersectionObserver === 'undefined'\n    )\n      return\n\n    const activeIntersections = new WeakMap<Element, VoidFunction>()\n\n    const onEnter = () => {\n      if (to) {\n        // @ts-expect-error – TODO: fix this type error\n        api.start(to)\n      }\n\n      setIsInView(true)\n\n      const cleanup = () => {\n        if (from) {\n          api.start(from)\n        }\n        setIsInView(false)\n      }\n\n      return once ? undefined : cleanup\n    }\n\n    const handleIntersection: IntersectionObserverCallback = entries => {\n      entries.forEach(entry => {\n        const onLeave = activeIntersections.get(entry.target)\n\n        if (entry.isIntersecting === Boolean(onLeave)) {\n          return\n        }\n\n        if (entry.isIntersecting) {\n          const newOnLeave = onEnter()\n          if (is.fun(newOnLeave)) {\n            activeIntersections.set(entry.target, newOnLeave)\n          } else {\n            observer.unobserve(entry.target)\n          }\n        } else if (onLeave) {\n          onLeave()\n          activeIntersections.delete(entry.target)\n        }\n      })\n    }\n\n    const observer = new IntersectionObserver(handleIntersection, {\n      root: (root && root.current) || undefined,\n      threshold:\n        typeof amount === 'number' || Array.isArray(amount)\n          ? amount\n          : defaultThresholdOptions[amount],\n      ...restArgs,\n    })\n\n    observer.observe(element)\n\n    return () => observer.unobserve(element)\n  }, [intersectionArguments])\n\n  if (propsFn) {\n    return [ref, springs]\n  }\n\n  return [ref, isInView]\n}\n", "import { NoInfer, UnknownProps } from '@react-spring/types'\nimport { useSpring, UseSpringProps } from '../hooks/useSpring'\nimport { SpringValues, SpringToFn, SpringChain } from '../types'\n\nexport type SpringComponentProps<State extends object = UnknownProps> =\n  unknown &\n    UseSpringProps<State> & {\n      children: (values: SpringValues<State>) => JSX.Element | null\n    }\n\n// Infer state from \"from\" object prop.\nexport function Spring<State extends object>(\n  props: {\n    from: State\n    to?: SpringChain<NoInfer<State>> | SpringToFn<NoInfer<State>>\n  } & Omit<SpringComponentProps<NoInfer<State>>, 'from' | 'to'>\n): JSX.Element | null\n\n// Infer state from \"to\" object prop.\nexport function Spring<State extends object>(\n  props: { to: State } & Omit<SpringComponentProps<NoInfer<State>>, 'to'>\n): JSX.Element | null\n\nexport function Spring({ children, ...props }: any) {\n  return children(useSpring(props))\n}\n", "import { ReactNode } from 'react'\nimport { NoInfer, Falsy } from '@react-spring/types'\nimport { is } from '@react-spring/shared'\n\nimport { Valid } from '../types/common'\nimport { PickAnimated, SpringValues } from '../types'\nimport { UseSpringProps } from '../hooks/useSpring'\nimport { useTrail } from '../hooks/useTrail'\n\nexport type TrailComponentProps<Item, Props extends object = any> = unknown &\n  UseSpringProps<Props> & {\n    items: readonly Item[]\n    children: (\n      item: NoInfer<Item>,\n      index: number\n    ) => ((values: SpringValues<PickAnimated<Props>>) => ReactNode) | Falsy\n  }\n\nexport function Trail<Item, Props extends TrailComponentProps<Item>>({\n  items,\n  children,\n  ...props\n}: Props & Valid<Props, TrailComponentProps<Item, Props>>) {\n  const trails: any[] = useTrail(items.length, props)\n  return items.map((item, index) => {\n    const result = children(item, index)\n    return is.fun(result) ? result(trails[index]) : result\n  })\n}\n", "import { Valid } from '../types/common'\nimport { TransitionComponentProps } from '../types'\nimport { useTransition } from '../hooks'\n\nexport function Transition<Item, Props extends TransitionComponentProps<Item>>(\n  props:\n    | TransitionComponentProps<Item>\n    | (Props & Valid<Props, TransitionComponentProps<Item, Props>>)\n): JSX.Element\n\nexport function Transition({\n  items,\n  children,\n  ...props\n}: TransitionComponentProps<any>) {\n  return useTransition(items, props)(children)\n}\n", "import { FluidValue, deprecateInterpolate } from '@react-spring/shared'\nimport {\n  Constrain,\n  OneOrMore,\n  Animatable,\n  ExtrapolateType,\n  InterpolatorConfig,\n  InterpolatorFn,\n} from '@react-spring/types'\nimport { Interpolation } from './Interpolation'\n\n/** Map the value of one or more dependencies */\nexport const to: Interpolator = (source: any, ...args: [any]) =>\n  new Interpolation(source, args)\n\n/** @deprecated Use the `to` export instead */\nexport const interpolate: Interpolator = (source: any, ...args: [any]) => (\n  deprecateInterpolate(), new Interpolation(source, args)\n)\n\n/** Extract the raw value types that are being interpolated */\nexport type Interpolated<T extends ReadonlyArray<any>> = {\n  [P in keyof T]: T[P] extends infer Element\n    ? Element extends FluidValue<infer U>\n      ? U\n      : Element\n    : never\n}\n\n/**\n * This interpolates one or more `FluidValue` objects.\n * The exported `interpolate` function uses this type.\n */\nexport interface Interpolator {\n  // Tuple of parent values\n  <Input extends ReadonlyArray<any>, Output>(\n    parents: Input,\n    interpolator: (...args: Interpolated<Input>) => Output\n  ): Interpolation<Output>\n\n  // Single parent value\n  <Input, Output>(\n    parent: FluidValue<Input> | Input,\n    interpolator: InterpolatorFn<Input, Output>\n  ): Interpolation<Output>\n\n  // Interpolation config\n  <Out>(\n    parents: OneOrMore<FluidValue>,\n    config: InterpolatorConfig<Out>\n  ): Interpolation<Animatable<Out>>\n\n  // Range shortcuts\n  <Out>(\n    parents: OneOrMore<FluidValue<number>> | FluidValue<number[]>,\n    range: readonly number[],\n    output: readonly Constrain<Out, Animatable>[],\n    extrapolate?: ExtrapolateType\n  ): Interpolation<Animatable<Out>>\n}\n", "import { Arrify, InterpolatorArgs, InterpolatorFn } from '@react-spring/types'\nimport {\n  is,\n  raf,\n  each,\n  isEqual,\n  toArray,\n  frameLoop,\n  FluidValue,\n  getFluidValue,\n  createInterpolator,\n  Globals as G,\n  callFluidObservers,\n  addFluidObserver,\n  removeFluidObserver,\n  hasFluidValue,\n} from '@react-spring/shared'\n\nimport { FrameValue, isFrameValue } from './FrameValue'\nimport {\n  getAnimated,\n  setAnimated,\n  getAnimatedType,\n  getPayload,\n} from '@react-spring/animated'\n\n/**\n * An `Interpolation` is a memoized value that's computed whenever one of its\n * `FluidValue` dependencies has its value changed.\n *\n * Other `FrameValue` objects can depend on this. For example, passing an\n * `Interpolation` as the `to` prop of a `useSpring` call will trigger an\n * animation toward the memoized value.\n */\nexport class Interpolation<\n  Input = any,\n  Output = any,\n> extends FrameValue<Output> {\n  /** Useful for debugging. */\n  key?: string\n\n  /** Equals false when in the frameloop */\n  idle = true\n\n  /** The function that maps inputs values to output */\n  readonly calc: InterpolatorFn<Input, Output>\n\n  /** The inputs which are currently animating */\n  protected _active = new Set<FluidValue>()\n\n  constructor(\n    /** The source of input values */\n    readonly source: unknown,\n    args: InterpolatorArgs<Input, Output>\n  ) {\n    super()\n    this.calc = createInterpolator(...args)\n\n    const value = this._get()\n    const nodeType = getAnimatedType(value)\n\n    // Assume the computed value never changes type.\n    setAnimated(this, nodeType.create(value))\n  }\n\n  advance(_dt?: number) {\n    const value = this._get()\n    const oldValue = this.get()\n    if (!isEqual(value, oldValue)) {\n      getAnimated(this)!.setValue(value)\n      this._onChange(value, this.idle)\n    }\n    // Become idle when all parents are idle or paused.\n    if (!this.idle && checkIdle(this._active)) {\n      becomeIdle(this)\n    }\n  }\n\n  protected _get() {\n    const inputs: Arrify<Input> = is.arr(this.source)\n      ? this.source.map(getFluidValue)\n      : (toArray(getFluidValue(this.source)) as any)\n\n    return this.calc(...inputs)\n  }\n\n  protected _start() {\n    if (this.idle && !checkIdle(this._active)) {\n      this.idle = false\n\n      each(getPayload(this)!, node => {\n        node.done = false\n      })\n\n      if (G.skipAnimation) {\n        raf.batchedUpdates(() => this.advance())\n        becomeIdle(this)\n      } else {\n        frameLoop.start(this)\n      }\n    }\n  }\n\n  // Observe our sources only when we're observed.\n  protected _attach() {\n    let priority = 1\n    each(toArray(this.source), source => {\n      if (hasFluidValue(source)) {\n        addFluidObserver(source, this)\n      }\n      if (isFrameValue(source)) {\n        if (!source.idle) {\n          this._active.add(source)\n        }\n        priority = Math.max(priority, source.priority + 1)\n      }\n    })\n    this.priority = priority\n    this._start()\n  }\n\n  // Stop observing our sources once we have no observers.\n  protected _detach() {\n    each(toArray(this.source), source => {\n      if (hasFluidValue(source)) {\n        removeFluidObserver(source, this)\n      }\n    })\n    this._active.clear()\n    becomeIdle(this)\n  }\n\n  /** @internal */\n  eventObserved(event: FrameValue.Event) {\n    // Update our value when an idle parent is changed,\n    // and enter the frameloop when a parent is resumed.\n    if (event.type == 'change') {\n      if (event.idle) {\n        this.advance()\n      } else {\n        this._active.add(event.parent)\n        this._start()\n      }\n    }\n    // Once all parents are idle, the `advance` method runs one more time,\n    // so we should avoid updating the `idle` status here.\n    else if (event.type == 'idle') {\n      this._active.delete(event.parent)\n    }\n    // Ensure our priority is greater than all parents, which means\n    // our value won't be updated until our parents have updated.\n    else if (event.type == 'priority') {\n      this.priority = toArray(this.source).reduce(\n        (highest: number, parent) =>\n          Math.max(highest, (isFrameValue(parent) ? parent.priority : 0) + 1),\n        0\n      )\n    }\n  }\n}\n\n/** Returns true for an idle source. */\nfunction isIdle(source: any) {\n  return source.idle !== false\n}\n\n/** Return true if all values in the given set are idle or paused. */\nfunction checkIdle(active: Set<FluidValue>) {\n  // Parents can be active even when paused, so the `.every` check\n  // removes us from the frameloop if all active parents are paused.\n  return !active.size || Array.from(active).every(isIdle)\n}\n\n/** Become idle if not already idle. */\nfunction becomeIdle(self: Interpolation) {\n  if (!self.idle) {\n    self.idle = true\n\n    each(getPayload(self)!, node => {\n      node.done = true\n    })\n\n    callFluidObservers(self, {\n      type: 'idle',\n      parent: self,\n    })\n  }\n}\n", "import {\n  Globals,\n  frameLoop,\n  createStringInterpolator,\n} from '@react-spring/shared'\nimport { Interpolation } from './Interpolation'\n\n// Sane defaults\nGlobals.assign({\n  createStringInterpolator,\n  to: (source, args) => new Interpolation(source, args),\n})\n\nexport { Globals }\n\n/** Advance all animations by the given time */\nexport const update = frameLoop.advance\n", "export * from './hooks'\nexport * from './components'\nexport * from './interpolate'\nexport * from './constants'\nexport * from './globals'\n\nexport { Controller } from './Controller'\nexport { SpringValue } from './SpringValue'\nexport { SpringContext } from './SpringContext'\nexport { SpringRef } from './SpringRef'\n\nexport { FrameValue } from './FrameValue'\nexport { Interpolation } from './Interpolation'\nexport { BailSignal } from './runAsync'\nexport {\n  createInterpolator,\n  useIsomorphicLayoutEffect,\n  useReducedMotion,\n  easings,\n} from '@react-spring/shared'\nexport { inferTo } from './helpers'\n\nexport * from './types'\nexport * from '@react-spring/types'\n"], "names": ["defaults", "to", "update", "is", "useContext", "is", "each", "useIsomorphicLayoutEffect", "is", "raf", "each", "toArray", "eachProp", "frameLoop", "getFluidValue", "isAnimatedString", "G", "callFluidObservers", "getAnimated", "is", "config", "is", "is", "G", "is", "G", "is", "raf", "eachProp", "G", "to", "G", "props", "is", "eachProp", "result", "raf", "FluidValue", "G", "is", "getFluidValue", "getAnimated", "node", "config", "toArray", "to", "raf", "isAnimatedString", "each", "G", "frameLoop", "callFluidObservers", "update", "eachProp", "is", "raf", "each", "flush", "toArray", "eachProp", "flushCalls", "addFluidObserver", "nextId", "flush", "is", "toArray", "each", "eachProp", "onStart", "onChange", "onRest", "raf", "to", "defaults", "result", "flushCalls", "props", "addFluidObserver", "each", "is", "SpringRef", "update", "is", "updates", "springs", "each", "update", "useContext", "useIsomorphicLayoutEffect", "is", "useOnce", "useOnce", "each", "is", "useIsomorphicLayoutEffect", "is", "useIsomorphicLayoutEffect", "each", "propsArg", "React", "useContext", "useRef", "useMemo", "is", "toArray", "useForceUpdate", "useOnce", "usePrev", "each", "useIsomorphicLayoutEffect", "is", "useMemo", "toArray", "useRef", "useIsomorphicLayoutEffect", "useOnce", "each", "useForceUpdate", "to", "config", "p", "transitions", "t", "useContext", "usePrev", "each", "useIsomorphicLayoutEffect", "useIsomorphicLayoutEffect", "each", "each", "useIsomorphicLayoutEffect", "useIsomorphicLayoutEffect", "each", "useRef", "useState", "is", "useIsomorphicLayoutEffect", "useState", "useRef", "is", "to", "useIsomorphicLayoutEffect", "is", "is", "deprecateInterpolate", "is", "raf", "each", "isEqual", "toArray", "frameLoop", "getFluidValue", "G", "callFluidObservers", "addFluidObserver", "removeFluidObserver", "hasFluidValue", "getAnimated", "setAnimated", "getAnimatedType", "getPayload", "isEqual", "is", "getFluidValue", "toArray", "each", "G", "raf", "frameLoop", "hasFluidValue", "addFluidObserver", "removeFluidObserver", "callFluidObservers", "deprecateInterpolate", "frameLoop", "frameLoop", "createInterpolator", "useIsomorphicLayoutEffect", "easings"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAAS,MAAM,iCAAiC;;;AGAhD,SAAS,cAAAI,aAAY,SAAS,cAAc;;ACA5C;AAmBA;AyBIA,cAAc;;;A5BTP,SAAS,SACd,KAAA,EAAA,GACG,IAAA,EACoC;IACvC,mNAAO,KAAA,CAAG,GAAA,CAAI,KAAK,IAAI,MAAM,GAAG,IAAI,IAAI;AAC1C;AAGO,IAAM,YAAY,CACvB,OACA,MAEA,UAAU,QACV,CAAC,CAAA,CACC,OACA,SAAA,6MACC,KAAA,CAAG,GAAA,CAAI,KAAK,IAAI,MAAM,GAAG,oNAAI,UAAA,EAAQ,KAAK,EAAE,QAAA,CAAS,GAAG,CAAA,CAAA;AAGtD,IAAM,cAAc,CACzB,MACA,kNACI,KAAA,CAAG,GAAA,CAAI,IAAI,IAAI,OAAQ,IAAA,CAAa,GAAG,CAAA,GAAI;AAU1C,IAAM,iBAAiB,CAC5B,OACA,MAEA,MAAM,OAAA,KAAY,OACd,KAAA,CAAM,GAAG,CAAA,GACT,MAAM,OAAA,GACJ,MAAM,OAAA,CAAQ,GAAG,CAAA,GACjB,KAAA;AAER,IAAM,gBAAgB,CAAC,QAAe;AAS/B,IAAM,kBAAkB,CAC7B,OACA,YAA8C,aAAA,KACxC;IACN,IAAI,OAA0B;IAC9B,IAAI,MAAM,OAAA,IAAW,MAAM,OAAA,KAAY,MAAM;QAC3C,QAAQ,MAAM,OAAA;QACd,OAAO,OAAO,IAAA,CAAK,KAAK;IAC1B;IACA,MAAMJ,YAAgB,CAAC;IACvB,KAAA,MAAW,OAAO,KAAM;QACtB,MAAM,QAAQ,UAAU,KAAA,CAAM,GAAG,CAAA,EAAG,GAAG;QACvC,IAAI,6MAAC,KAAA,CAAG,GAAA,CAAI,KAAK,GAAG;YAClBA,SAAAA,CAAS,GAAG,CAAA,GAAI;QAClB;IACF;IACA,OAAOA;AACT;AAaO,IAAM,gBAAgB;IAC3B;IACA;IACA;IACA;IACA;IACA;IACA;CACF;AAEA,IAAM,iBAEF;IACF,QAAQ;IACR,MAAM;IACN,IAAI;IACJ,KAAK;IACL,MAAM;IACN,OAAO;IACP,OAAO;IACP,QAAQ;IACR,SAAS;IACT,WAAW;IACX,SAAS;IACT,OAAO;IACP,SAAS;IACT,SAAS;IACT,UAAU;IACV,SAAS;IACT,UAAU;IACV,QAAQ;IACR,WAAW;IAAA,mBAAA;IAGX,OAAO;IACP,OAAO;IACP,MAAM;IACN,SAAS;IACT,SAAS;IACT,OAAO;IACP,QAAQ;IACR,OAAO;IACP,UAAU;IACV,aAAa;IAAA,iBAAA;IAGb,MAAM;IACN,QAAQ;IACR,UAAU;AACZ;AAOA,SAAS,gBACP,KAAA,EACiC;IACjC,MAAM,UAAe,CAAC;IAEtB,IAAI,QAAQ;IACZ,CAAA,GAAA,2MAAA,CAAA,WAAA,EAAS,OAAO,CAAC,OAAO,SAAS;QAC/B,IAAI,CAAC,cAAA,CAAe,IAAI,CAAA,EAAG;YACzB,OAAA,CAAQ,IAAI,CAAA,GAAI;YAChB;QACF;IACF,CAAC;IAED,IAAI,OAAO;QACT,OAAO;IACT;AACF;AAMO,SAAS,QAA0B,KAAA,EAAsB;IAC9D,MAAMC,MAAK,gBAAgB,KAAK;IAChC,IAAIA,KAAI;QACN,MAAM,MAAW;YAAE,IAAAA;QAAG;QACtB,CAAA,GAAA,2MAAA,CAAA,WAAA,EAAS,OAAO,CAAC,KAAK,MAAQ,OAAOA,OAAAA,CAAO,GAAA,CAAI,GAAG,CAAA,GAAI,GAAA,CAAI;QAC3D,OAAO;IACT;IACA,OAAO;QAAE,GAAG,KAAA;IAAM;AACpB;AAGO,SAAS,YAAe,KAAA,EAA6B;IAC1D,wNAAQ,gBAAA,EAAc,KAAK;IAC3B,mNAAO,KAAA,CAAG,GAAA,CAAI,KAAK,IACf,MAAM,GAAA,CAAI,WAAW,oNACrB,mBAAA,EAAiB,KAAK,gNACnB,UAAA,CAAE,wBAAA,CAAyB;QAC1B,OAAO;YAAC;YAAG,CAAC;SAAA;QACZ,QAAQ;YAAC;YAAO,KAAK;SAAA;IACvB,CAAC,EAAE,CAAC,IACJ;AACR;AAEO,SAAS,SAAS,KAAA,EAAe;IACtC,IAAA,MAAW,KAAK,MAAO,OAAO;IAC9B,OAAO;AACT;AAEO,SAAS,UAAUA,GAAAA,EAAS;IACjC,kNAAO,MAAA,CAAG,GAAA,CAAIA,GAAE,iNAAM,KAAA,CAAG,GAAA,CAAIA,GAAE,iNAAK,KAAA,CAAG,GAAA,CAAIA,GAAAA,CAAG,CAAC,CAAC;AAClD;AAGO,SAAS,WAAW,IAAA,EAAkB,GAAA,EAAiB;IAC5D,KAAK,GAAA,EAAK,OAAO,IAAI;IACrB,KAAK,OAAO,IAAI;AAClB;AAGO,SAAS,WAAW,IAAA,EAAkB,GAAA,EAAiB;IAC5D,IAAI,OAAO,KAAK,GAAA,KAAQ,KAAK;QAC3B,KAAK,GAAA,EAAK,OAAO,IAAI;QACrB,IAAI,GAAA,CAAI,IAAI;QACZ,KAAK,GAAA,GAAM;IACb;AACF;;AD/LO,SAAS,SACd,IAAA,EACA,SAAA,EACA,YAAY,GAAA,EACZ;IACA,CAAA,GAAA,2MAAA,CAAA,4BAAA;8CAA0B,MAAM;YAC9B,IAAI,WAAW;gBACb,IAAI,YAAY;gBAChB,CAAA,GAAA,2MAAA,CAAA,OAAA,EAAK;0DAAM,CAAC,KAAK,MAAM;wBACrB,MAAM,cAAc,IAAI,OAAA;wBACxB,IAAI,YAAY,MAAA,EAAQ;4BACtB,IAAI,QAAQ,YAAY,SAAA,CAAU,CAAC,CAAA;4BAGnC,IAAI,MAAM,KAAK,GAAG,QAAQ;iCACrB,YAAY;4BAEjB,CAAA,GAAA,2MAAA,CAAA,OAAA,EAAK;sEAAa,CAAA,SAAQ;oCACxB,CAAA,GAAA,2MAAA,CAAA,OAAA,EAAK,KAAK,KAAA;8EAAO,CAAA,UAAS;4CAExB,MAAM,oBAAoB,MAAM,KAAA;4CAChC,MAAM,KAAA;sFAAQ,CAAA,MAAO,QAAQ,SAAS,qBAAqB,GAAG,GAAG;;wCACnE,CAAC;;gCACH,CAAC;;4BAED,IAAI,KAAA,CAAM;wBACZ;oBACF,CAAC;;YACH,OAAO;gBACL,IAAI,IAAkB,QAAQ,OAAA,CAAQ;gBACtC,CAAA,GAAA,2MAAA,CAAA,OAAA,EAAK;0DAAM,CAAA,QAAO;wBAChB,MAAM,cAAc,IAAI,OAAA;wBACxB,IAAI,YAAY,MAAA,EAAQ;4BAEtB,MAAM,SAAS,YAAY,GAAA;6EAAI,CAAA,SAAQ;oCACrC,MAAM,IAAI,KAAK,KAAA;oCACf,KAAK,KAAA,GAAQ,CAAC,CAAA;oCACd,OAAO;gCACT,CAAC;;4BAGD,IAAI,EAAE,IAAA;sEAAK,MAAM;oCACf,CAAA,GAAA,2MAAA,CAAA,OAAA,EAAK;8EAAa,CAAC,MAAM,oNACvB,OAAA,EAAK,MAAA,CAAO,CAAC,CAAA,IAAK,CAAC,CAAA;sFAAG,CAAAC,UAAU,KAAK,KAAA,CAAM,IAAA,CAAKA,OAAM,CAAC;;;oCAEzD,OAAO,QAAQ,GAAA,CAAI,IAAI,KAAA,CAAM,CAAC;gCAChC,CAAC;;wBACH;oBACF,CAAC;;YACH;QACF,CAAC;;AACH;;;;;;;;AM7EO,IAAM,SAAS;IACpB,SAAS;QAAE,SAAS;QAAK,UAAU;IAAG;IACtC,QAAQ;QAAE,SAAS;QAAK,UAAU;IAAG;IACrC,QAAQ;QAAE,SAAS;QAAK,UAAU;IAAG;IACrC,OAAO;QAAE,SAAS;QAAK,UAAU;IAAG;IACpC,MAAM;QAAE,SAAS;QAAK,UAAU;IAAG;IACnC,UAAU;QAAE,SAAS;QAAK,UAAU;IAAI;AAC1C;;ADJA,IAAM,WAAgB;IACpB,GAAG,OAAQ,OAAA;IACX,MAAM;IACN,SAAS;IACT,oNAAQ,UAAA,CAAQ,MAAA;IAChB,OAAO;AACT;AAEO,IAAM,kBAAN,MAAsB;IA2I3B,aAAc;QAnFd;;;;KAAA,GAAA,IAAA,CAAA,QAAA,GAA8B;QAoF5B,OAAO,MAAA,CAAO,IAAA,EAAM,QAAQ;IAC9B;AACF;AAQO,SAAS,YACdkB,OAAAA,EACA,SAAA,EACA,aAAA,EACA;IACA,IAAI,eAAe;QACjB,gBAAgB;YAAE,GAAG,aAAA;QAAc;QACnC,eAAe,eAAe,SAAS;QACvC,YAAY;YAAE,GAAG,aAAA;YAAe,GAAG,SAAA;QAAU;IAC/C;IAEA,eAAeA,SAAQ,SAAS;IAChC,OAAO,MAAA,CAAOA,SAAQ,SAAS;IAE/B,IAAA,MAAW,OAAO,SAAU;QAC1B,IAAIA,OAAAA,CAAO,GAAG,CAAA,IAAK,MAAM;YACvBA,OAAAA,CAAO,GAAG,CAAA,GAAI,QAAA,CAAS,GAAG,CAAA;QAC5B;IACF;IAEA,IAAI,EAAE,SAAA,EAAW,OAAA,CAAQ,CAAA,GAAIA;IAC7B,MAAM,EAAE,IAAA,CAAK,CAAA,GAAIA;IACjB,IAAI,6MAACC,KAAAA,CAAG,GAAA,CAAI,SAAS,GAAG;QACtB,IAAI,YAAY,MAAM,YAAY;QAClC,IAAI,UAAU,GAAG,UAAU;QAC3BD,QAAO,OAAA,GAAU,KAAK,GAAA,CAAK,IAAI,KAAK,EAAA,GAAM,WAAW,CAAC,IAAI;QAC1DA,QAAO,QAAA,GAAY,IAAI,KAAK,EAAA,GAAK,UAAU,OAAQ;IACrD;IAEA,OAAOA;AACT;AAIA,SAAS,eACPA,OAAAA,EACA,KAAA,EACA;IACA,IAAI,6MAACC,KAAAA,CAAG,GAAA,CAAI,MAAM,KAAK,GAAG;QACxBD,QAAO,QAAA,GAAW,KAAA;IACpB,OAAO;QACL,MAAM,kBAAkB,6MAACC,KAAAA,CAAG,GAAA,CAAI,MAAM,OAAO,KAAK,6MAACA,KAAAA,CAAG,GAAA,CAAI,MAAM,QAAQ;QACxE,IACE,mBACA,6MAACA,KAAAA,CAAG,GAAA,CAAI,MAAM,SAAS,KACvB,6MAACA,KAAAA,CAAG,GAAA,CAAI,MAAM,OAAO,KACrB,6MAACA,KAAAA,CAAG,GAAA,CAAI,MAAM,IAAI,GAClB;YACAD,QAAO,QAAA,GAAW,KAAA;YAClBA,QAAO,KAAA,GAAQ,KAAA;QACjB;QACA,IAAI,iBAAiB;YACnBA,QAAO,SAAA,GAAY,KAAA;QACrB;IACF;AACF;;AEnNA,IAAM,aAA6B,CAAC,CAAA;AAI7B,IAAM,YAAN,MAAyB;IAAzB,aAAA;QACL,IAAA,CAAA,OAAA,GAAU;QACV,IAAA,CAAA,MAAA,GAAmC;QACnC,IAAA,CAAA,QAAA,GAAqC;QACrC,IAAA,CAAA,UAAA,GAAgC;QAIhC,IAAA,CAAA,MAAA,GAAS,IAAI,gBAAgB;QAC7B,IAAA,CAAA,SAAA,GAAY;IAAA;AACd;;ACaO,SAAS,cACd,MAAA,EACA,EAAE,GAAA,EAAK,KAAA,EAAO,YAAA,EAAc,KAAA,EAAO,OAAA,CAAQ,CAAA,EAC3B;IAChB,OAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;QACtC,IAAI;QACJ,IAAI;QAEJ,IAAI,SAAS,UAAU,MAAM,MAAA,IAAU,cAAc,QAAQ,GAAG;QAChE,IAAI,QAAQ;YACV,QAAQ;QACV,OAAO;YAEL,IAAI,6MAACI,KAAAA,CAAG,GAAA,CAAI,MAAM,KAAK,GAAG;gBACxB,MAAM,MAAA,GAAS,UAAU,MAAM,KAAA,EAAO,GAAG;YAC3C;YAGA,IAAI,QAAQ,cAAc;YAC1B,IAAI,UAAU,MAAM;gBAClB,QAAQ,MAAM,MAAA,IAAU,UAAU,OAAO,GAAG;YAC9C;YAEA,QAAQ,SAAS,MAAM,KAAA,IAAS,GAAG,GAAG;YACtC,IAAI,OAAO;gBACT,MAAM,WAAA,CAAY,GAAA,CAAI,QAAQ;gBAC9B,QAAQ,KAAA,CAAM;YAChB,OAAO;gBACL,QAAQ,MAAA,CAAO;gBACf,SAAS;YACX;QACF;QAEA,SAAS,UAAU;YACjB,MAAM,WAAA,CAAY,GAAA,CAAI,QAAQ;YAC9B,MAAM,QAAA,CAAS,MAAA,CAAO,OAAO;YAC7B,QAAQ,MAAA,CAAO;YAEf,QAAQ,QAAQ,IAAA,GAAO,8LAAA,CAAI,GAAA,CAAI;QACjC;QAEA,SAAS,WAAW;YAClB,IAAI,QAAQ,KAAK,CAACC,sNAAAA,CAAE,aAAA,EAAe;gBACjC,MAAM,OAAA,GAAU;gBAChB,kMAAU,MAAA,CAAI,UAAA,CAAW,SAAS,KAAK;gBACvC,MAAM,UAAA,CAAW,GAAA,CAAI,OAAO;gBAC5B,MAAM,QAAA,CAAS,GAAA,CAAI,OAAO;YAC5B,OAAO;gBACL,QAAQ;YACV;QACF;QAEA,SAAS,UAAU;YACjB,IAAI,MAAM,OAAA,EAAS;gBACjB,MAAM,OAAA,GAAU;YAClB;YAEA,MAAM,UAAA,CAAW,MAAA,CAAO,OAAO;YAC/B,MAAM,QAAA,CAAS,MAAA,CAAO,OAAO;YAG7B,IAAI,UAAA,CAAW,MAAM,QAAA,IAAY,CAAA,GAAI;gBACnC,SAAS;YACX;YAEA,IAAI;gBACF,QAAQ,KAAA,CAAM;oBAAE,GAAG,KAAA;oBAAO;oBAAQ;gBAAO,GAAG,OAAO;YACrD,EAAA,OAAS,KAAP;gBACA,OAAO,GAAG;YACZ;QACF;IACF,CAAC;AACH;;;AErGO,IAAM,oBAAoB,CAC/B,QACA,UAEA,QAAQ,MAAA,IAAU,IACd,OAAA,CAAQ,CAAC,CAAA,GACT,QAAQ,IAAA,CAAK,CAAA,SAAU,OAAO,SAAS,IACrC,mBAAmB,OAAO,GAAA,CAAI,CAAC,IAC/B,QAAQ,KAAA,CAAM,CAAA,SAAU,OAAO,IAAI,IACjC,cAAc,OAAO,GAAA,CAAI,CAAC,IAC1B,kBACE,OAAO,GAAA,CAAI,GACX,QAAQ,KAAA,CAAM,CAAA,SAAU,OAAO,QAAQ;AAI5C,IAAM,gBAAgB,CAAC,QAAA,CAAgB;QAC5C;QACA,MAAM;QACN,UAAU;QACV,WAAW;IACb,CAAA;AAEO,IAAM,oBAAoB,CAC/B,OACA,UACA,YAAY,KAAA,GAAA,CACR;QACJ;QACA;QACA;IACF,CAAA;AAEO,IAAM,qBAAqB,CAAC,QAAA,CAAgB;QACjD;QACA,WAAW;QACX,UAAU;IACZ,CAAA;;ADKO,SAAS,SACdK,GAAAA,EACA,KAAA,EACA,KAAA,EACA,MAAA,EACgB;IAChB,MAAM,EAAE,MAAA,EAAQ,QAAA,EAAU,MAAA,CAAO,CAAA,GAAI;IACrC,MAAM,EAAE,SAAS,MAAA,EAAQ,SAAS,WAAA,CAAY,CAAA,GAAI;IAElD,IAAI,CAAC,YAAYA,QAAO,UAAU,CAAC,MAAM,KAAA,EAAO;QAC9C,OAAO;IACT;IAEA,OAAQ,MAAM,OAAA,GAAA,CAAW,YAAY;QACnC,MAAM,OAAA,GAAU;QAChB,MAAM,OAAA,GAAUA;QAGhB,MAAM,eAAe,gBAA+B,OAAO,CAAC,OAAO,MAAA,4EAAA;YAEjE,QAAQ,WAAW,KAAA,IAAY;QAGjC,IAAI;QACJ,IAAI;QAGJ,MAAM,cAAc,IAAI,QACtB,CAAC,SAAS,SAAA,CAAa,cAAc,SAAW,OAAO,MAAA;QAGzD,MAAM,cAAc,CAAC,eAA2B;YAC9C,MAAM,aAAA,+CAAA;YAEH,UAAA,CAAW,MAAM,QAAA,IAAY,CAAA,KAAM,mBAAmB,MAAM,KAAA,oCAAA;YAE5D,WAAW,MAAM,OAAA,IAAW,kBAAkB,QAAQ,KAAK;YAE9D,IAAI,YAAY;gBACd,WAAW,MAAA,GAAS;gBAIpB,KAAK,UAAU;gBACf,MAAM;YACR;QACF;QAEA,MAAM,UAAe,CAAC,MAAW,SAAe;YAG9C,MAAM,aAAa,IAAI,WAAW;YAClC,MAAM,sBAAsB,IAAI,oBAAoB;YAEpD,OAAA,CAAQ,YAAY;gBAClB,gNAAIC,UAAAA,CAAE,aAAA,EAAe;oBAMnB,UAAU,KAAK;oBAGf,oBAAoB,MAAA,GAAS,kBAAkB,QAAQ,KAAK;oBAC5D,KAAK,mBAAmB;oBACxB,MAAM;gBACR;gBAEA,YAAY,UAAU;gBAEtB,MAAMC,qNAAaC,KAAAA,CAAG,GAAA,CAAI,IAAI,IAAI;oBAAE,GAAG,IAAA;gBAAK,IAAI;oBAAE,GAAG,IAAA;oBAAM,IAAI;gBAAK;gBACpED,OAAM,QAAA,GAAW;gOAEjBE,WAAAA,EAAS,cAAc,CAAC,OAAO,QAAQ;oBACrC,gNAAID,KAAAA,CAAG,GAAA,CAAID,MAAAA,CAAM,GAAG,CAAC,GAAG;wBACtBA,MAAAA,CAAM,GAAG,CAAA,GAAI;oBACf;gBACF,CAAC;gBAED,MAAMG,UAAS,MAAM,OAAO,KAAA,CAAMH,MAAK;gBACvC,YAAY,UAAU;gBAEtB,IAAI,MAAM,MAAA,EAAQ;oBAChB,MAAM,IAAI,QAAc,CAAA,WAAU;wBAChC,MAAM,WAAA,CAAY,GAAA,CAAI,MAAM;oBAC9B,CAAC;gBACH;gBAEA,OAAOG;YACT,CAAA,EAAG;QACL;QAEA,IAAI;QAEJ,gNAAIJ,UAAAA,CAAE,aAAA,EAAe;YAKnB,UAAU,KAAK;YACf,OAAO,kBAAkB,QAAQ,KAAK;QACxC;QAEA,IAAI;YACF,IAAI;YAGJ,gNAAIE,KAAAA,CAAG,GAAA,CAAIH,GAAE,GAAG;gBACd,YAAA,CAAa,OAAO,UAAiB;oBACnC,KAAA,MAAWE,UAAS,MAAO;wBACzB,MAAM,QAAQA,MAAK;oBACrB;gBACF,CAAA,EAAGF,GAAE;YACP,OAGK;gBACH,YAAY,QAAQ,OAAA,CAAQA,IAAG,SAAS,OAAO,IAAA,CAAK,IAAA,CAAK,MAAM,CAAC,CAAC;YACnE;YAEA,MAAM,QAAQ,GAAA,CAAI;gBAAC,UAAU,IAAA,CAAK,WAAW;gBAAG,WAAW;aAAC;YAC5D,SAAS,kBAAkB,OAAO,GAAA,CAAI,GAAG,MAAM,KAAK;QAGtD,EAAA,OAAS,KAAP;YACA,IAAI,eAAe,YAAY;gBAC7B,SAAS,IAAI,MAAA;YACf,OAAA,IAAW,eAAe,qBAAqB;gBAC7C,SAAS,IAAI,MAAA;YACf,OAAO;gBACL,MAAM;YACR;QAGF,SAAE;YACA,IAAI,UAAU,MAAM,OAAA,EAAS;gBAC3B,MAAM,OAAA,GAAU;gBAChB,MAAM,OAAA,GAAU,WAAW,SAAS,KAAA;gBACpC,MAAM,OAAA,GAAU,WAAW,cAAc,KAAA;YAC3C;QACF;QAEA,IAAIG,iNAAAA,CAAG,GAAA,CAAI,MAAM,GAAG;oMAClBG,MAAAA,CAAI,cAAA,CAAe,MAAM;gBACvB,OAAO,QAAQ,QAAQ,OAAO,IAAI;YACpC,CAAC;QACH;QAEA,OAAO;IACT,CAAA,EAAG;AACL;AAGO,SAAS,UAAU,KAAA,EAAsB,QAAA,EAA2B;IACzE,CAAA,GAAA,2MAAA,CAAA,QAAA,EAAM,MAAM,QAAA,EAAU,CAAA,IAAK,EAAE,MAAA,CAAO,CAAC;IACrC,MAAM,UAAA,CAAW,KAAA,CAAM;IACvB,MAAM,WAAA,CAAY,KAAA,CAAM;IACxB,MAAM,OAAA,GAAU,MAAM,OAAA,GAAU,MAAM,OAAA,GAAU,KAAA;IAChD,IAAI,UAAU,MAAM,QAAA,GAAW;AACjC;AAGO,IAAM,aAAN,cAAyB,MAAM;IAEpC,aAAc;QACZ,KAAA,CACE;IAGJ;AACF;AAEO,IAAM,sBAAN,cAAkC,MAAM;IAG7C,aAAc;QACZ,KAAA,CAAM,qBAAqB;IAC7B;AACF;;;AErNO,IAAM,eAAe,CAAC,QAC3B,iBAAiB;AAEnB,IAAI,SAAS;AAON,IAAe,aAAf,0NAA2CC,aAAAA,CAGhD;IAHK,aAAA;QAAA,KAAA,IAAA;QAIL,IAAA,CAAS,EAAA,GAAK;QAKd,IAAA,CAAU,SAAA,GAAY;IAAA;IAEtB,IAAI,WAAW;QACb,OAAO,IAAA,CAAK,SAAA;IACd;IACA,IAAI,SAAS,QAAA,EAAkB;QAC7B,IAAI,IAAA,CAAK,SAAA,IAAa,UAAU;YAC9B,IAAA,CAAK,SAAA,GAAY;YACjB,IAAA,CAAK,iBAAA,CAAkB,QAAQ;QACjC;IACF;IAAA,0BAAA,GAGA,MAAS;QACP,MAAM,2MAAO,cAAA,EAAY,IAAI;QAC7B,OAAO,QAAQ,KAAK,QAAA,CAAS;IAC/B;IAAA,yDAAA,GAGA,GAAA,GAAW,IAAA,EAAgC;QACzC,mNAAOC,UAAAA,CAAE,EAAA,CAAG,IAAA,EAAM,IAAI;IACxB;IAAA,6CAAA,GAGA,YAAA,GAAoB,IAAA,EAAgC;QAClD,CAAA,GAAA,2MAAA,CAAA,uBAAA,CAAqB;QACrB,mNAAOA,UAAAA,CAAE,EAAA,CAAG,IAAA,EAAM,IAAI;IACxB;IAEA,SAAS;QACP,OAAO,IAAA,CAAK,GAAA,CAAI;IAClB;IAEU,cAAc,KAAA,EAAe;QACrC,IAAI,SAAS,GAAG,IAAA,CAAK,OAAA,CAAQ;IAC/B;IAEU,gBAAgB,KAAA,EAAe;QACvC,IAAI,SAAS,GAAG,IAAA,CAAK,OAAA,CAAQ;IAC/B;IAAA,0CAAA,GASU,UAAU,CAAC;IAAA,2CAAA,GAGX,UAAU,CAAC;IAAA,0CAAA,GAGX,UAAU,KAAA,EAAU,OAAO,KAAA,EAAO;QAC1C,CAAA,GAAA,2MAAA,CAAA,qBAAA,EAAmB,IAAA,EAAM;YACvB,MAAM;YACN,QAAQ,IAAA;YACR;YACA;QACF,CAAC;IACH;IAAA,6CAAA,GAGU,kBAAkB,QAAA,EAAkB;QAC5C,IAAI,CAAC,IAAA,CAAK,IAAA,EAAM;YACd,2MAAA,CAAA,YAAA,CAAU,IAAA,CAAK,IAAI;QACrB;QACA,CAAA,GAAA,2MAAA,CAAA,qBAAA,EAAmB,IAAA,EAAM;YACvB,MAAM;YACN,QAAQ,IAAA;YACR;QACF,CAAC;IACH;AACF;;ACxGA,IAAM,KAAK,OAAO,GAAA,CAAI,aAAa;AAEnC,IAAM,eAAe;AACrB,IAAM,eAAe;AACrB,IAAM,YAAY;AAGX,IAAM,cAAc,CAAC,SAAA,CAAiB,MAAA,CAAO,EAAE,CAAA,GAAI,YAAA,IAAgB;AAGnE,IAAM,cAAc,CAAC,SAAA,CAAiB,MAAA,CAAO,EAAE,CAAA,GAAI,YAAA,IAAgB;AAGnE,IAAM,WAAW,CAAC,SAAA,CAAiB,MAAA,CAAO,EAAE,CAAA,GAAI,SAAA,IAAa;AAG7D,IAAM,eAAe,CAAC,QAAa,SACxC,SACK,MAAA,CAAO,EAAE,CAAA,IAAK,eAAe,eAC7B,MAAA,CAAO,EAAE,CAAA,IAAK,CAAC;AAEf,IAAM,eAAe,CAAC,QAAa,SACxC,SAAU,MAAA,CAAO,EAAE,CAAA,IAAK,YAAc,MAAA,CAAO,EAAE,CAAA,IAAK,CAAC;;ARqDhD,IAAM,cAAN,cAAmC,WAAc;IAmCtD,YAAY,IAAA,EAAY,IAAA,CAAY;QAClC,KAAA,CAAM;QA/BR,wBAAA,GAAA,IAAA,CAAA,SAAA,GAAY,IAAI,UAAa;QAM7B,gDAAA,GAAA,IAAA,CAAA,YAAA,GAAsC,CAAC;QAGvC,mCAAA,GAAA,IAAA,CAAU,MAAA,GAAwC;YAChD,QAAQ;YACR,SAAS;YACT,YAAY,aAAA,GAAA,IAAI,IAAI;YACpB,aAAa,aAAA,GAAA,IAAI,IAAI;YACrB,UAAU,aAAA,GAAA,IAAI,IAAI;QACpB;QAGA,mDAAA,GAAA,IAAA,CAAU,aAAA,GAAgB,aAAA,GAAA,IAAI,IAA6B;QAG3D,mDAAA,GAAA,IAAA,CAAU,WAAA,GAAc;QAGxB,6DAAA,GAAA,IAAA,CAAU,SAAA,GAAY;QAEtB,IAAA,CAAU,iBAAA,GAAoB;QAM5B,IAAI,6MAACC,KAAAA,CAAG,GAAA,CAAI,IAAI,KAAK,6MAACA,KAAAA,CAAG,GAAA,CAAI,IAAI,GAAG;YAClC,MAAM,oNAAQA,KAAAA,CAAG,GAAA,CAAI,IAAI,IAAI;gBAAE,GAAG,IAAA;YAAK,IAAI;gBAAE,GAAG,IAAA;gBAAM,MAAM;YAAK;YACjE,gNAAIA,KAAAA,CAAG,GAAA,CAAI,MAAM,OAAO,GAAG;gBACzB,MAAM,OAAA,GAAU;YAClB;YACA,IAAA,CAAK,KAAA,CAAM,KAAK;QAClB;IACF;IAAA,kDAAA,GAGA,IAAI,OAAO;QACT,OAAO,CAAA,CAAE,YAAY,IAAI,KAAK,IAAA,CAAK,MAAA,CAAO,OAAA,KAAY,SAAS,IAAI;IACrE;IAEA,IAAI,OAAO;QACT,uNAAOC,gBAAAA,EAAc,IAAA,CAAK,SAAA,CAAU,EAAE;IACxC;IAEA,IAAI,WAA4B;QAC9B,MAAM,2MAAOC,cAAAA,EAAY,IAAI;QAC7B,OACE,gBAAgB,gNAAA,GACZ,KAAK,YAAA,IAAgB,IACrB,KAAK,UAAA,CAAW,EAAE,GAAA,CAAI,CAAAC,QAAQA,MAAK,YAAA,IAAgB,CAAC;IAE5D;IAAA;;GAAA,GAKA,IAAI,cAAc;QAChB,OAAO,YAAY,IAAI;IACzB;IAAA;;;GAAA,GAMA,IAAI,cAAc;QAChB,OAAO,YAAY,IAAI;IACzB;IAAA;;GAAA,GAKA,IAAI,WAAW;QACb,OAAO,SAAS,IAAI;IACtB;IAAA;;;GAAA,GAMA,IAAI,YAAY;QACd,OAAO,IAAA,CAAK,MAAA,CAAO,OAAA;IACrB;IAAA,8DAAA,GAGA,QAAQ,EAAA,EAAY;QAClB,IAAI,OAAO;QACX,IAAI,UAAU;QAEd,MAAM,OAAO,IAAA,CAAK,SAAA;QAClB,IAAI,EAAE,QAAA,CAAS,CAAA,GAAI;QACnB,MAAM,EAAE,QAAAC,OAAAA,CAAO,CAAA,GAAI;QAEnB,MAAM,8MAAU,aAAA,EAAW,KAAK,EAAE;QAClC,IAAI,CAAC,2NAAW,gBAAA,EAAc,KAAK,EAAE,GAAG;YACtC,2NAAWC,UAAAA,kNAAQJ,gBAAAA,EAAc,KAAK,EAAE,CAAC;QAC3C;QAEA,KAAK,MAAA,CAAO,OAAA,CAAQ,CAACE,OAAM,MAAM;YAC/B,IAAIA,MAAK,IAAA,EAAM;YAEf,MAAMG,MAAA,0CAAA;YAEJH,MAAK,WAAA,IAAe,iNAAA,GAChB,IACA,UACE,OAAA,CAAQ,CAAC,CAAA,CAAE,YAAA,GACX,QAAA,CAAU,CAAC,CAAA;YAEnB,IAAI,WAAW,KAAK,SAAA;YACpB,IAAI,WAAWG;YAEf,IAAI,CAAC,UAAU;gBACb,WAAWH,MAAK,YAAA;gBAGhB,IAAIC,QAAO,OAAA,IAAW,GAAG;oBACvBD,MAAK,IAAA,GAAO;oBACZ;gBACF;gBAEA,IAAI,UAAWA,MAAK,WAAA,IAAe;gBACnC,MAAM,OAAO,KAAK,UAAA,CAAW,CAAC,CAAA;gBAE9B,MAAM,KACJA,MAAK,EAAA,IAAM,OACPA,MAAK,EAAA,GACJA,MAAK,EAAA,+MAAKH,KAAAA,CAAG,GAAA,CAAII,QAAO,QAAQ,IAC7BA,QAAO,QAAA,CAAS,CAAC,CAAA,GACjBA,QAAO,QAAA;gBAEjB,IAAI;gBAOJ,MAAM,YACJA,QAAO,SAAA,IAAA,CACN,QAAQE,MAAK,OAAQ,KAAK,GAAA,CAAI,GAAG,KAAK,GAAA,CAAIA,MAAK,IAAI,IAAI,IAAK,CAAA;gBAG/D,IAAI,6MAACN,KAAAA,CAAG,GAAA,CAAII,QAAO,QAAQ,GAAG;oBAC5B,IAAI,IAAI;oBACR,IAAIA,QAAO,QAAA,GAAW,GAAG;wBAOvB,IAAI,IAAA,CAAK,iBAAA,KAAsBA,QAAO,QAAA,EAAU;4BAE9C,IAAA,CAAK,iBAAA,GAAoBA,QAAO,QAAA;4BAGhC,IAAID,MAAK,gBAAA,GAAmB,GAAG;gCAE7BA,MAAK,WAAA,GAAcC,QAAO,QAAA,GAAWD,MAAK,gBAAA;gCAE1C,UAAUA,MAAK,WAAA,IAAe;4BAChC;wBACF;wBAGA,IAAA,CAAKC,QAAO,QAAA,IAAY,CAAA,IAAK,UAAU,IAAA,CAAK,iBAAA;wBAE5C,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;wBAE5BD,MAAK,gBAAA,GAAmB;oBAC1B;oBAEA,WAAW,OAAOC,QAAO,MAAA,CAAO,CAAC,IAAA,CAAKE,MAAK,IAAA;oBAC3C,WAAA,CAAY,WAAWH,MAAK,YAAA,IAAgB;oBAE5C,WAAW,KAAK;gBAClB,OAAA,IAGSC,QAAO,KAAA,EAAO;oBACrB,MAAM,QAAQA,QAAO,KAAA,KAAU,OAAO,QAAQA,QAAO,KAAA;oBACrD,MAAM,IAAI,KAAK,GAAA,CAAI,CAAA,CAAE,IAAI,KAAA,IAAS,OAAO;oBAEzC,WAAW,OAAQ,KAAA,CAAM,IAAI,KAAA,IAAA,CAAW,IAAI,CAAA;oBAC5C,WAAW,KAAK,GAAA,CAAID,MAAK,YAAA,GAAe,QAAQ,KAAK;oBAGrD,WAAW,KAAK;gBAClB,OAGK;oBACH,WAAWA,MAAK,YAAA,IAAgB,OAAO,KAAKA,MAAK,YAAA;oBAGjD,MAAM,eAAeC,QAAO,YAAA,IAAgB,YAAY;oBAGxD,MAAM,eAAeA,QAAO,KAAA,GAAQ,IAAIA,QAAO,MAAA;oBAC/C,MAAM,YAAY,6MAACJ,KAAAA,CAAG,GAAA,CAAI,YAAY;oBAGtC,MAAM,YAAY,QAAQM,MAAKH,MAAK,EAAA,GAAK,IAAI,OAAOG;oBAGpD,IAAI;oBAGJ,IAAI,aAAa;oBAEjB,MAAM,OAAO;oBACb,MAAM,WAAW,KAAK,IAAA,CAAK,KAAK,IAAI;oBACpC,IAAA,IAAS,IAAI,GAAG,IAAI,UAAU,EAAE,EAAG;wBACjC,WAAW,KAAK,GAAA,CAAI,QAAQ,IAAI;wBAEhC,IAAI,CAAC,UAAU;4BACb,WAAW,KAAK,GAAA,CAAIA,MAAK,QAAQ,KAAK;4BACtC,IAAI,UAAU;gCACZ;4BACF;wBACF;wBAEA,IAAI,WAAW;4BACb,aAAa,YAAYA,OAAM,WAAWA,OAAM;4BAGhD,IAAI,YAAY;gCACd,WAAW,CAAC,WAAW;gCACvB,WAAWA;4BACb;wBACF;wBAEA,MAAM,cAAc,CAACF,QAAO,OAAA,GAAU,OAAA,CAAY,WAAWE,GAAAA;wBAC7D,MAAM,eAAe,CAACF,QAAO,QAAA,GAAW,OAAQ;wBAChD,MAAM,eAAA,CAAgB,cAAc,YAAA,IAAgBA,QAAO,IAAA;wBAE3D,WAAW,WAAW,eAAe;wBACrC,WAAW,WAAW,WAAW;oBACnC;gBACF;gBAEAD,MAAK,YAAA,GAAe;gBAEpB,IAAI,OAAO,KAAA,CAAM,QAAQ,GAAG;oBAC1B,QAAQ,IAAA,CAAK,CAAA,wBAAA,CAAA,EAA4B,IAAI;oBAC7C,WAAW;gBACb;YACF;YAGA,IAAI,WAAW,CAAC,OAAA,CAAQ,CAAC,CAAA,CAAE,IAAA,EAAM;gBAC/B,WAAW;YACb;YAEA,IAAI,UAAU;gBACZA,MAAK,IAAA,GAAO;YACd,OAAO;gBACL,OAAO;YACT;YAEA,IAAIA,MAAK,QAAA,CAAS,UAAUC,QAAO,KAAK,GAAG;gBACzC,UAAU;YACZ;QACF,CAAC;QAED,MAAM,2MAAOF,cAAAA,EAAY,IAAI;QAK7B,MAAM,UAAU,KAAK,QAAA,CAAS;QAC9B,IAAI,MAAM;YAER,MAAM,2NAAWD,gBAAAA,EAAc,KAAK,EAAE;YAKtC,IAAA,CAAK,YAAY,YAAY,OAAA,KAAY,CAACG,QAAO,KAAA,EAAO;gBAEtD,KAAK,QAAA,CAAS,QAAQ;gBACtB,IAAA,CAAK,SAAA,CAAU,QAAQ;YACzB,OAAA,IAAW,WAAWA,QAAO,KAAA,EAAO;gBAKlC,IAAA,CAAK,SAAA,CAAU,OAAO;YACxB;YAEA,IAAA,CAAK,KAAA,CAAM;QACb,OAAA,IAAW,SAAS;YAKlB,IAAA,CAAK,SAAA,CAAU,OAAO;QACxB;IACF;IAAA,gEAAA,GAGA,IAAI,KAAA,EAA0B;gMAC5BG,MAAAA,CAAI,cAAA,CAAe,MAAM;YACvB,IAAA,CAAK,KAAA,CAAM;YAIX,IAAA,CAAK,MAAA,CAAO,KAAK;YACjB,IAAA,CAAK,IAAA,CAAK,KAAK;QACjB,CAAC;QACD,OAAO,IAAA;IACT;IAAA;;;GAAA,GAMA,QAAQ;QACN,IAAA,CAAK,OAAA,CAAQ;YAAE,OAAO;QAAK,CAAC;IAC9B;IAAA,oCAAA,GAGA,SAAS;QACP,IAAA,CAAK,OAAA,CAAQ;YAAE,OAAO;QAAM,CAAC;IAC/B;IAAA,8CAAA,GAGA,SAAS;QACP,IAAI,YAAY,IAAI,GAAG;YACrB,MAAM,EAAE,IAAAD,GAAAA,EAAI,QAAAF,OAAAA,CAAO,CAAA,GAAI,IAAA,CAAK,SAAA;oMAC5BG,MAAAA,CAAI,cAAA,CAAe,MAAM;gBAEvB,IAAA,CAAK,QAAA,CAAS;gBAId,IAAI,CAACH,QAAO,KAAA,EAAO;oBACjB,IAAA,CAAK,IAAA,CAAKE,KAAI,KAAK;gBACrB;gBAEA,IAAA,CAAK,KAAA,CAAM;YACb,CAAC;QACH;QACA,OAAO,IAAA;IACT;IAAA,uCAAA,GAGA,OAAO,KAAA,EAAwB;QAC7B,MAAM,QAAQ,IAAA,CAAK,KAAA,IAAA,CAAU,IAAA,CAAK,KAAA,GAAQ,CAAC,CAAA;QAC3C,MAAM,IAAA,CAAK,KAAK;QAChB,OAAO,IAAA;IACT;IAeA,MAAMA,GAAAA,EAAU,IAAA,EAAY;QAC1B,IAAI;QACJ,IAAI,4MAACN,MAAAA,CAAG,GAAA,CAAIM,GAAE,GAAG;YACf,QAAQ;4NAACN,KAAAA,CAAG,GAAA,CAAIM,GAAE,IAAIA,MAAK;oBAAE,GAAG,IAAA;oBAAM,IAAAA;gBAAG,CAAC;aAAA;QAC5C,OAAO;YACL,QAAQ,IAAA,CAAK,KAAA,IAAS,CAAC,CAAA;YACvB,IAAA,CAAK,KAAA,GAAQ,CAAC,CAAA;QAChB;QAEA,OAAO,QAAQ,GAAA,CACb,MAAM,GAAA,CAAI,CAAA,UAAS;YACjB,MAAM,KAAK,IAAA,CAAK,OAAA,CAAQ,KAAK;YAC7B,OAAO;QACT,CAAC,GACD,IAAA,CAAK,CAAA,UAAW,kBAAkB,IAAA,EAAM,OAAO,CAAC;IACpD;IAAA;;;;GAAA,GAOA,KAAK,MAAA,EAAkB;QACrB,MAAM,EAAE,IAAAA,GAAAA,CAAG,CAAA,GAAI,IAAA,CAAK,SAAA;QAGpB,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,GAAA,CAAI,CAAC;QAEtB,UAAU,IAAA,CAAK,MAAA,EAAQ,UAAU,IAAA,CAAK,WAAW;gMACjDC,MAAAA,CAAI,cAAA,CAAe,IAAM,IAAA,CAAK,KAAA,CAAMD,KAAI,MAAM,CAAC;QAE/C,OAAO,IAAA;IACT;IAAA,2BAAA,GAGA,QAAQ;QACN,IAAA,CAAK,OAAA,CAAQ;YAAE,OAAO;QAAK,CAAC;IAC9B;IAAA,cAAA,GAGA,cAAc,KAAA,EAAyB;QACrC,IAAI,MAAM,IAAA,IAAQ,UAAU;YAC1B,IAAA,CAAK,MAAA,CAAO;QACd,OAAA,IAAW,MAAM,IAAA,IAAQ,YAAY;YACnC,IAAA,CAAK,QAAA,GAAW,MAAM,QAAA,GAAW;QACnC;IACF;IAAA;;;;;GAAA,GAQU,aAAa,KAAA,EAKpB;QACD,MAAM,MAAM,IAAA,CAAK,GAAA,IAAO;QAExB,IAAI,EAAE,IAAAA,GAAAA,EAAI,IAAA,CAAK,CAAA,GAAI;QAEnBA,MAAKN,iNAAAA,CAAG,GAAA,CAAIM,GAAE,IAAIA,GAAAA,CAAG,GAAG,CAAA,GAAIA;QAC5B,IAAIA,OAAM,QAAQ,UAAUA,GAAE,GAAG;YAC/BA,MAAK,KAAA;QACP;QAEA,mNAAON,KAAAA,CAAG,GAAA,CAAI,IAAI,IAAI,IAAA,CAAK,GAAG,CAAA,GAAI;QAClC,IAAI,QAAQ,MAAM;YAChB,OAAO,KAAA;QACT;QAGA,MAAM,QAAQ;YAAE,IAAAM;YAAI;QAAK;QAIzB,IAAI,CAAC,YAAY,IAAI,GAAG;YACtB,IAAI,MAAM,OAAA,EAAS,CAACA,KAAI,IAAI,CAAA,GAAI;gBAAC;gBAAMA,GAAE;aAAA;YAEzC,OAAOL,gOAAAA,EAAc,IAAI;YACzB,IAAI,6MAACD,KAAAA,CAAG,GAAA,CAAI,IAAI,GAAG;gBACjB,IAAA,CAAK,IAAA,CAAK,IAAI;YAChB,OAAA,IAES,qMAACE,cAAAA,EAAY,IAAI,GAAG;gBAC3B,IAAA,CAAK,IAAA,CAAKI,GAAE;YACd;QACF;QAEA,OAAO;IACT;IAAA,6DAAA,GAGU,QACR,EAAE,GAAG,MAAM,CAAA,EACX,MAAA,EAC6B;QAC7B,MAAM,EAAE,GAAA,EAAK,YAAA,CAAa,CAAA,GAAI,IAAA;QAG9B,IAAI,MAAM,OAAA,EACR,OAAO,MAAA,CACL,cACA,gBAAgB,OAAO,CAAC,OAAO,OAC7B,MAAM,IAAA,CAAK,IAAI,IAAI,YAAY,OAAO,GAAG,IAAI;QAInD,cAAc,IAAA,EAAM,OAAO,SAAS;QACpC,UAAU,IAAA,EAAM,WAAW,OAAO,IAAI;QAGtC,MAAM,QAAQ,IAAA,CAAK,YAAA,CAAa,KAAK;QAErC,IAAI,OAAO,QAAA,CAAS,IAAI,GAAG;YACzB,MAAM,MACJ;QAGJ;QAEA,MAAM,QAAQ,IAAA,CAAK,MAAA;QAEnB,OAAO,cAAc,EAAE,IAAA,CAAK,WAAA,EAAa;YACvC;YACA;YACA;YACA;YACA,SAAS;gBACP,OAAO,MAAM;oBACX,IAAI,CAAC,SAAS,IAAI,GAAG;wBACnB,aAAa,IAAA,EAAM,IAAI;wBACvB,CAAA,GAAA,2MAAA,CAAA,aAAA,EAAW,MAAM,UAAU;wBAC3B,UACE,IAAA,EACA,WACA,kBAAkB,IAAA,EAAM,cAAc,IAAA,EAAM,IAAA,CAAK,SAAA,CAAU,EAAE,CAAC,GAC9D,IAAA;oBAEJ;gBACF;gBACA,QAAQ,MAAM;oBACZ,IAAI,SAAS,IAAI,GAAG;wBAClB,aAAa,IAAA,EAAM,KAAK;wBACxB,IAAI,YAAY,IAAI,GAAG;4BACrB,IAAA,CAAK,OAAA,CAAQ;wBACf;wBACA,CAAA,GAAA,2MAAA,CAAA,aAAA,EAAW,MAAM,WAAW;wBAC5B,UACE,IAAA,EACA,YACA,kBAAkB,IAAA,EAAM,cAAc,IAAA,EAAM,IAAA,CAAK,SAAA,CAAU,EAAE,CAAC,GAC9D,IAAA;oBAEJ;gBACF;gBACA,OAAO,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,IAAA,EAAM,KAAK;YACrC;QACF,CAAC,EAAE,IAAA,CAAK,CAAA,WAAU;YAChB,IAAI,MAAM,IAAA,IAAQ,OAAO,QAAA,IAAY,CAAA,CAAE,UAAU,OAAO,IAAA,GAAO;gBAC7D,MAAM,YAAY,iBAAiB,KAAK;gBACxC,IAAI,WAAW;oBACb,OAAO,IAAA,CAAK,OAAA,CAAQ,WAAW,IAAI;gBACrC;YACF;YACA,OAAO;QACT,CAAC;IACH;IAAA,2CAAA,GAGU,OACR,KAAA,EACA,KAAA,EACA,OAAA,EACM;QAGN,IAAI,MAAM,MAAA,EAAQ;YAChB,IAAA,CAAK,IAAA,CAAK,IAAI;YACd,OAAO,QAAQ,mBAAmB,IAAI,CAAC;QACzC;QAGA,MAAM,YAAY,6MAACN,KAAAA,CAAG,GAAA,CAAI,MAAM,EAAE;QAGlC,MAAM,cAAc,6MAACA,KAAAA,CAAG,GAAA,CAAI,MAAM,IAAI;QAItC,IAAI,aAAa,aAAa;YAC5B,IAAI,MAAM,MAAA,GAAS,IAAA,CAAK,SAAA,EAAW;gBACjC,IAAA,CAAK,SAAA,GAAY,MAAM,MAAA;YACzB,OAAO;gBACL,OAAO,QAAQ,mBAAmB,IAAI,CAAC;YACzC;QACF;QAEA,MAAM,EAAE,GAAA,EAAK,YAAA,EAAc,WAAW,IAAA,CAAK,CAAA,GAAI,IAAA;QAC/C,MAAM,EAAE,IAAI,MAAA,EAAQ,MAAM,QAAA,CAAS,CAAA,GAAI;QACvC,IAAI,EAAE,IAAAM,MAAK,MAAA,EAAQ,OAAO,QAAA,CAAS,CAAA,GAAI;QAIvC,IAAI,eAAe,CAAC,aAAA,CAAc,CAAC,MAAM,OAAA,gNAAWN,KAAAA,CAAG,GAAA,CAAIM,GAAE,CAAA,GAAI;YAC/DA,MAAK;QACP;QAGA,IAAI,MAAM,OAAA,EAAS,CAACA,KAAI,IAAI,CAAA,GAAI;YAAC;YAAMA,GAAE;SAAA;QAGzC,MAAM,iBAAiB,iNAAC,UAAA,EAAQ,MAAM,QAAQ;QAE9C,IAAI,gBAAgB;YAClB,KAAK,IAAA,GAAO;QACd;QAGA,uNAAOL,gBAAAA,EAAc,IAAI;QAGzB,MAAM,eAAe,CAAC,0NAAA,EAAQK,KAAI,MAAM;QAExC,IAAI,cAAc;YAChB,IAAA,CAAK,MAAA,CAAOA,GAAE;QAChB;QAGA,MAAM,aAAa,UAAU,MAAM,EAAE;QAErC,MAAM,EAAE,QAAAF,OAAAA,CAAO,CAAA,GAAI;QACnB,MAAM,EAAE,KAAA,EAAO,QAAA,CAAS,CAAA,GAAIA;QAG5B,IAAI,aAAa,aAAa;YAC5BA,QAAO,QAAA,GAAW;QACpB;QAIA,IAAI,MAAM,MAAA,IAAU,CAAC,YAAY;YAC/B,YACEA,SACA,SAAS,MAAM,MAAA,EAAQ,GAAI,GAAA,8CAAA;YAE3B,MAAM,MAAA,KAAW,aAAa,MAAA,GAC1B,SAAS,aAAa,MAAA,EAAQ,GAAI,IAClC,KAAA;QAER;QAIA,IAAI,2MAAOF,cAAAA,EAAY,IAAI;QAC3B,IAAI,CAAC,oNAAQF,KAAAA,CAAG,GAAA,CAAIM,GAAE,GAAG;YACvB,OAAO,QAAQ,kBAAkB,IAAA,EAAM,IAAI,CAAC;QAC9C;QAGA,MAAM,QAAA,oEAAA;QAAA,iEAAA;QAAA,sCAAA;oNAIJN,KAAAA,CAAG,GAAA,CAAI,MAAM,KAAK,IACd,eAAe,CAAC,MAAM,OAAA,GACtB,6MAACA,KAAAA,CAAG,GAAA,CAAI,IAAI,KAAK,UAAU,MAAM,KAAA,EAAO,GAAG;QAGjD,MAAM,QAAQ,QAAS,OAAa,IAAA,CAAK,GAAA,CAAI;QAG7C,MAAM,OAAO,YAAiBM,GAAE;QAGhC,MAAM,2NAAeN,KAAAA,CAAG,GAAA,CAAI,IAAI,iNAAKA,KAAAA,CAAG,GAAA,CAAI,IAAI,SAAKQ,+NAAAA,EAAiB,IAAI;QAG1E,MAAM,YACJ,CAAC,cAAA,CACA,CAAC,gBACA,UAAU,aAAa,SAAA,IAAa,MAAM,SAAA,EAAW,GAAG,CAAA;QAE5D,IAAI,cAAc;YAChB,MAAM,+MAAW,kBAAA,EAAgBF,GAAE;YACnC,IAAI,aAAa,KAAK,WAAA,EAAa;gBACjC,IAAI,WAAW;oBACb,OAAO,IAAA,CAAK,IAAA,CAAK,IAAI;gBACvB,OACE,MAAM,MACJ,CAAA,uBAAA,EAA0B,KAAK,WAAA,CAAY,IAAA,CAAA,KAAA,EAAY,SAAS,IAAA,CAAA,2BAAA,CAAA;YAEtE;QACF;QAGA,MAAM,WAAW,KAAK,WAAA;QAKtB,IAAI,UAAU,gOAAA,EAAcA,GAAE;QAC9B,IAAI,WAAW;QAEf,IAAI,CAAC,SAAS;YAEZ,MAAM,kBAAkB,SAAU,CAAC,YAAY,IAAI,KAAK;YAIxD,IAAI,gBAAgB,iBAAiB;gBACnC,0NAAW,WAAA,EAAQ,YAAY,KAAK,GAAG,IAAI;gBAC3C,UAAU,CAAC;YACb;YAGA,IACG,CAAC,0NAAA,EAAQ,KAAK,SAAA,EAAW,SAAS,KAAK,CAAC,aACzC,iNAAC,UAAA,EAAQF,QAAO,KAAA,EAAO,KAAK,KAC5B,CAAC,0NAAA,EAAQA,QAAO,QAAA,EAAU,QAAQ,GAClC;gBACA,UAAU;YACZ;QACF;QAGA,IAAI,YAAY,YAAY,IAAI,GAAG;YAGjC,IAAI,KAAK,OAAA,IAAW,CAAC,OAAO;gBAC1B,UAAU;YACZ,OAAA,IAES,CAAC,SAAS;gBACjB,IAAA,CAAK,KAAA,CAAM,MAAM;YACnB;QACF;QAEA,IAAI,CAAC,YAAY;YAGf,IAAI,2NAAW,gBAAA,EAAc,MAAM,GAAG;gBACpC,KAAK,MAAA,GAAS,KAAK,UAAA,CAAW;gBAC9B,KAAK,QAAA,OAAW,4NAAA,EAAcE,GAAE,IAC5B,OACA,4MAAY,iBAAA,GACV;oBAAC,CAAC;iBAAA,GACFD,0NAAAA,EAAQ,IAAI;YACpB;YAEA,IAAI,KAAK,SAAA,IAAa,WAAW;gBAC/B,KAAK,SAAA,GAAY;gBAGjB,IAAI,CAAC,aAAa,CAAC,OAAO;oBACxB,IAAA,CAAK,IAAA,CAAK,MAAM;gBAClB;YACF;YAEA,IAAI,SAAS;gBACX,MAAM,EAAE,MAAA,CAAO,CAAA,GAAI;gOAGnBI,OAAAA,EAAK,eAAe,CAAA,OAAQ,cAAc,IAAA,EAAM,OAAO,IAAI,CAAC;gBAE5D,MAAM,SAAS,kBAAkB,IAAA,EAAM,cAAc,IAAA,EAAM,MAAM,CAAC;gBAClE,CAAA,GAAA,2MAAA,CAAA,aAAA,EAAW,IAAA,CAAK,aAAA,EAAe,MAAM;gBACrC,IAAA,CAAK,aAAA,CAAc,GAAA,CAAI,OAAO;gBAE9B,IAAI,KAAK,OAAA,0LACPF,MAAAA,CAAI,cAAA,CAAe,MAAM;oBAEvB,KAAK,OAAA,GAAU,CAAC;oBAGhB,SAAS,QAAQ,IAAI;oBAIrB,IAAI,OAAO;wBACT,SAAS,aAAa,MAAA,EAAQ,MAAM;oBACtC,OAIK;wBACH,KAAK,OAAA,GAAU,QAAQ,IAAI;oBAC7B;gBACF,CAAC;YACL;QACF;QAEA,IAAI,OAAO;YACT,IAAA,CAAK,IAAA,CAAK,KAAK;QACjB;QAEA,IAAI,YAAY;YACd,QAAQ,SAAS,MAAM,EAAA,EAAI,OAAO,IAAA,CAAK,MAAA,EAAQ,IAAI,CAAC;QACtD,OAAA,IAGS,SAAS;YAChB,IAAA,CAAK,MAAA,CAAO;QACd,OAAA,IAIS,YAAY,IAAI,KAAK,CAAC,cAAc;YAC3C,IAAA,CAAK,aAAA,CAAc,GAAA,CAAI,OAAO;QAChC,OAGK;YACH,QAAQ,cAAc,KAAK,CAAC;QAC9B;IACF;IAAA,mEAAA,GAGU,OAAO,KAAA,EAA0B;QACzC,MAAM,OAAO,IAAA,CAAK,SAAA;QAClB,IAAI,UAAU,KAAK,EAAA,EAAI;YACrB,oNAAI,oBAAA,EAAkB,IAAI,GAAG;gBAC3B,IAAA,CAAK,OAAA,CAAQ;YACf;YACA,KAAK,EAAA,GAAK;YACV,oNAAI,oBAAA,EAAkB,IAAI,GAAG;gBAC3B,IAAA,CAAK,OAAA,CAAQ;YACf;QACF;IACF;IAEU,UAAU;QAClB,IAAI,WAAW;QAEf,MAAM,EAAE,IAAAD,GAAAA,CAAG,CAAA,GAAI,IAAA,CAAK,SAAA;QACpB,oNAAI,gBAAA,EAAcA,GAAE,GAAG;YACrB,CAAA,GAAA,2MAAA,CAAA,mBAAA,EAAiBA,KAAI,IAAI;YACzB,IAAI,aAAaA,GAAE,GAAG;gBACpB,WAAWA,IAAG,QAAA,GAAW;YAC3B;QACF;QAEA,IAAA,CAAK,QAAA,GAAW;IAClB;IAEU,UAAU;QAClB,MAAM,EAAE,IAAAA,GAAAA,CAAG,CAAA,GAAI,IAAA,CAAK,SAAA;QACpB,oNAAI,gBAAA,EAAcA,GAAE,GAAG;YACrB,CAAA,GAAA,2MAAA,CAAA,sBAAA,EAAoBA,KAAI,IAAI;QAC9B;IACF;IAAA;;;GAAA,GAMU,KAAK,GAAA,EAAwB,OAAO,IAAA,EAA4B;QACxE,MAAM,wNAAQL,gBAAAA,EAAc,GAAG;QAC/B,IAAI,6MAACD,KAAAA,CAAG,GAAA,CAAI,KAAK,GAAG;YAClB,MAAM,cAAUE,8MAAAA,EAAY,IAAI;YAChC,IAAI,CAAC,WAAW,iNAAC,UAAA,EAAQ,OAAO,QAAQ,QAAA,CAAS,CAAC,GAAG;gBAEnD,MAAM,+MAAW,kBAAA,EAAgB,KAAK;gBACtC,IAAI,CAAC,WAAW,QAAQ,WAAA,IAAe,UAAU;oBAC/C,CAAA,GAAA,+LAAA,CAAA,cAAA,EAAY,IAAA,EAAM,SAAS,MAAA,CAAO,KAAK,CAAC;gBAC1C,OAAO;oBACL,QAAQ,QAAA,CAAS,KAAK;gBACxB;gBAEA,IAAI,SAAS;4MACXK,MAAAA,CAAI,cAAA,CAAe,MAAM;wBACvB,IAAA,CAAK,SAAA,CAAU,OAAO,IAAI;oBAC5B,CAAC;gBACH;YACF;QACF;QACA,0MAAOL,eAAAA,EAAY,IAAI;IACzB;IAEU,WAAW;QACnB,MAAM,OAAO,IAAA,CAAK,SAAA;QAClB,IAAI,CAAC,KAAK,OAAA,EAAS;YACjB,KAAK,OAAA,GAAU;YACf,UACE,IAAA,EACA,WACA,kBAAkB,IAAA,EAAM,cAAc,IAAA,EAAM,KAAK,EAAE,CAAC,GACpD,IAAA;QAEJ;IACF;IAEU,UAAU,KAAA,EAAU,IAAA,EAAgB;QAC5C,IAAI,CAAC,MAAM;YACT,IAAA,CAAK,QAAA,CAAS;YACd,SAAS,IAAA,CAAK,SAAA,CAAU,QAAA,EAAU,OAAO,IAAI;QAC/C;QACA,SAAS,IAAA,CAAK,YAAA,CAAa,QAAA,EAAU,OAAO,IAAI;QAChD,KAAA,CAAM,UAAU,OAAO,IAAI;IAC7B;IAAA,wEAAA;IAAA,2EAAA;IAAA,6BAAA;IAKU,SAAS;QACjB,MAAM,OAAO,IAAA,CAAK,SAAA;QAGlBA,kNAAAA,EAAY,IAAI,EAAG,KAAA,gNAAMD,iBAAAA,EAAc,KAAK,EAAE,CAAC;QAG/C,IAAI,CAAC,KAAK,SAAA,EAAW;YACnB,KAAK,UAAA,GAAa,KAAK,MAAA,CAAO,GAAA,CAAI,CAAA,OAAQ,KAAK,YAAY;QAC7D;QAEA,IAAI,CAAC,YAAY,IAAI,GAAG;YACtB,aAAa,IAAA,EAAM,IAAI;YACvB,IAAI,CAAC,SAAS,IAAI,GAAG;gBACnB,IAAA,CAAK,OAAA,CAAQ;YACf;QACF;IACF;IAEU,UAAU;QAElB,gNAAIS,UAAAA,CAAE,aAAA,EAAe;YACnB,IAAA,CAAK,MAAA,CAAO;QACd,OAAO;wNACLC,YAAAA,CAAU,KAAA,CAAM,IAAI;QACtB;IACF;IAAA;;;;GAAA,GAOU,MAAM,IAAA,EAAY,MAAA,EAAkB;QAC5C,IAAI,YAAY,IAAI,GAAG;YACrB,aAAa,IAAA,EAAM,KAAK;YAExB,MAAM,OAAO,IAAA,CAAK,SAAA;2NAClBF,QAAAA,EAAK,KAAK,MAAA,EAAQ,CAAA,SAAQ;gBACxB,KAAK,IAAA,GAAO;YACd,CAAC;YAKD,IAAI,KAAK,QAAA,EAAU;gBACjB,KAAK,QAAA,GAAW,KAAK,OAAA,GAAU,KAAK,QAAA,GAAW,KAAA;YACjD;YAEAG,qOAAAA,EAAmB,IAAA,EAAM;gBACvB,MAAM;gBACN,QAAQ,IAAA;YACV,CAAC;YAED,MAAM,SAAS,SACX,mBAAmB,IAAA,CAAK,GAAA,CAAI,CAAC,IAC7B,kBAAkB,IAAA,CAAK,GAAA,CAAI,GAAG,cAAc,IAAA,EAAM,QAAQ,KAAK,EAAE,CAAC;YAEtE,CAAA,GAAA,2MAAA,CAAA,aAAA,EAAW,IAAA,CAAK,aAAA,EAAe,MAAM;YACrC,IAAI,KAAK,OAAA,EAAS;gBAChB,KAAK,OAAA,GAAU;gBACf,UAAU,IAAA,EAAM,UAAU,QAAQ,IAAI;YACxC;QACF;IACF;AACF;AAGA,SAAS,cAAiB,MAAA,EAAwBN,GAAAA,EAAuB;IACvE,MAAM,OAAO,YAAYA,GAAE;IAC3B,MAAM,QAAQ,YAAY,OAAO,GAAA,CAAI,CAAC;IACtC,uNAAO,UAAA,EAAQ,OAAO,IAAI;AAC5B;AAEO,SAAS,iBACd,KAAA,EACA,OAAO,MAAM,IAAA,EACbA,MAAK,MAAM,EAAA,EACI;IACf,MAAM,UAAU,SAAS,IAAI;IAC7B,IAAI,SAAS;QACX,MAAM,YAAY,YAAY,QAAQ,QAAQ,OAAO;QACrD,MAAM,UAAA,CAAW,aAAa,KAAA,EAAO,OAAA;QACrC,MAAM,QAAQ,CAAC,aAAa,UAAU,KAAA;QACtC,OAAO,aAAa;YAClB,GAAG,KAAA;YACH;YAAA,6CAAA;YAGA,SAAS;YAAA,+BAAA;YAGT,OAAO,KAAA;YAAA,4DAAA;YAAA,4DAAA;YAAA,qCAAA;YAKP,IAAI,CAAC,WAAW,UAAUA,GAAE,IAAIA,MAAK,KAAA;YAAA,0CAAA;YAGrC,MAAM,QAAQ,MAAM,IAAA,GAAO,KAAA;YAC3B;YAAA,2DAAA;YAAA,sCAAA;YAIA,GAAG,SAAA;QACL,CAAC;IACH;AACF;AASO,SAAS,aAAa,KAAA,EAAY;IACvC,MAAM,EAAE,IAAAA,GAAAA,EAAI,IAAA,CAAK,CAAA,GAAK,QAAQ,QAAQ,KAAK;IAG3C,MAAM,OAAO,aAAA,GAAA,IAAI,IAAY;IAE7B,IAAIN,iNAAAA,CAAG,GAAA,CAAIM,GAAE,GAAG,YAAYA,KAAI,IAAI;IACpC,gNAAIN,KAAAA,CAAG,GAAA,CAAI,IAAI,GAAG,YAAY,MAAM,IAAI;IAGxC,MAAM,IAAA,GAAO,KAAK,IAAA,GAAO,MAAM,IAAA,CAAK,IAAI,IAAI;IAE5C,OAAO;AACT;AAKO,SAAS,cAAc,KAAA,EAAY;IACxC,MAAMa,UAAS,aAAa,KAAK;IACjC,IAAIb,iNAAAA,CAAG,GAAA,CAAIa,QAAO,OAAO,GAAG;QAC1BA,QAAO,OAAA,GAAU,gBAAgBA,OAAM;IACzC;IACA,OAAOA;AACT;AAGA,SAAS,YAAY,MAAA,EAAgB,IAAA,EAAmB;oNACtDC,WAAAA,EAAS,QAAQ,CAAC,OAAO,MAAQ,SAAS,QAAQ,KAAK,GAAA,CAAI,GAAU,CAAC;AACxE;AAGA,IAAM,gBAAgB;IACpB;IACA;IACA;IACA;IACA;CACF;AAEA,SAAS,cACP,MAAA,EACA,KAAA,EACA,IAAA,EACA;IACA,OAAO,SAAA,CAAU,IAAI,CAAA,GACnB,KAAA,CAAM,IAAI,CAAA,KAAM,eAAe,OAAO,IAAI,IACtC,YAAiB,KAAA,CAAM,IAAI,CAAA,EAAG,OAAO,GAAG,IACxC,KAAA;AACR;AAOA,SAAS,UACP,MAAA,EACA,IAAA,EAAA,GACG,IAAA,EACH;IACA,OAAO,SAAA,CAAU,IAAI,CAAA,GAAI,GAAI,IAAmB;IAChD,OAAO,YAAA,CAAa,IAAI,CAAA,GAAI,GAAI,IAAmB;AACrD;;ASjlCA,IAAM,iBAAiB;IAAC;IAAW;IAAY,QAAQ;CAAA;AAEvD,IAAIS,UAAS;AAWN,IAAM,aAAN,MAAgD;IA2DrD,YACE,KAAA,EACAC,MAAAA,CACA;QA7DF,IAAA,CAAS,EAAA,GAAKD;QAGd,wBAAA,GAAA,IAAA,CAAA,OAAA,GAA+B,CAAC;QAGhC,sDAAA,GAAA,IAAA,CAAA,KAAA,GAAgC,CAAC,CAAA;QAejC,mDAAA,GAAA,IAAA,CAAU,YAAA,GAAe;QAGzB,wCAAA,GAAA,IAAA,CAAU,OAAA,GAAU,aAAA,GAAA,IAAI,IAAgB;QAGxC,qCAAA,GAAA,IAAA,CAAU,QAAA,GAAW,aAAA,GAAA,IAAI,IAAgB;QAGzC,wDAAA,GAAA,IAAA,CAAU,QAAA,GAAW;QAKrB,0CAAA,GAAA,IAAA,CAAU,MAAA,GAA8B;YACtC,QAAQ;YACR,YAAY,aAAA,GAAA,IAAI,IAAI;YACpB,aAAa,aAAA,GAAA,IAAI,IAAI;YACrB,UAAU,aAAA,GAAA,IAAI,IAAI;QACpB;QAGA,6DAAA,GAAA,IAAA,CAAU,OAAA,GAAU;YAClB,SAAS,aAAA,GAAA,IAAI,IAGX;YACF,UAAU,aAAA,GAAA,IAAI,IAGZ;YACF,QAAQ,aAAA,GAAA,IAAI,IAGV;QACJ;QAME,IAAA,CAAK,QAAA,GAAW,IAAA,CAAK,QAAA,CAAS,IAAA,CAAK,IAAI;QACvC,IAAIC,QAAO;YACT,IAAA,CAAK,MAAA,GAASA;QAChB;QACA,IAAI,OAAO;YACT,IAAA,CAAK,KAAA,CAAM;gBAAE,SAAS;gBAAM,GAAG,KAAA;YAAM,CAAC;QACxC;IACF;IAAA;;;GAAA,GAMA,IAAI,OAAO;QACT,OACE,CAAC,IAAA,CAAK,MAAA,CAAO,OAAA,IACb,OAAO,MAAA,CAAO,IAAA,CAAK,OAA8B,EAAE,KAAA,CAAM,CAAA,WAAU;YACjE,OAAO,OAAO,IAAA,IAAQ,CAAC,OAAO,SAAA,IAAa,CAAC,OAAO,QAAA;QACrD,CAAC;IAEL;IAEA,IAAI,OAAO;QACT,OAAO,IAAA,CAAK,KAAA;IACd;IAEA,IAAI,KAAK,IAAA,EAAM;QACb,IAAA,CAAK,KAAA,GAAQ;IACf;IAAA,0CAAA,GAGA,MAA4B;QAC1B,MAAM,SAAc,CAAC;QACrB,IAAA,CAAK,IAAA,CAAK,CAAC,QAAQ,MAAS,MAAA,CAAO,GAAG,CAAA,GAAI,OAAO,GAAA,CAAI,CAAE;QACvD,OAAO;IACT;IAAA,8CAAA,GAGA,IAAI,MAAA,EAAwB;QAC1B,IAAA,MAAW,OAAO,OAAQ;YACxB,MAAM,QAAQ,MAAA,CAAO,GAAG,CAAA;YACxB,IAAI,6MAACC,KAAAA,CAAG,GAAA,CAAI,KAAK,GAAG;gBAClB,IAAA,CAAK,OAAA,CAAQ,GAAG,CAAA,CAAE,GAAA,CAAI,KAAK;YAC7B;QACF;IACF;IAAA,iDAAA,GAGA,OAAO,KAAA,EAAwC;QAC7C,IAAI,OAAO;YACT,IAAA,CAAK,KAAA,CAAM,IAAA,CAAK,aAAa,KAAK,CAAC;QACrC;QACA,OAAO,IAAA;IACT;IAAA;;;;;;GAAA,GASA,MAAM,KAAA,EAAsE;QAC1E,IAAI,EAAE,KAAA,CAAM,CAAA,GAAI,IAAA;QAChB,IAAI,OAAO;YACT,wNAAQC,UAAAA,EAAa,KAAK,EAAE,GAAA,CAAI,YAAY;QAC9C,OAAO;YACL,IAAA,CAAK,KAAA,GAAQ,CAAC,CAAA;QAChB;QAEA,IAAI,IAAA,CAAK,MAAA,EAAQ;YACf,OAAO,IAAA,CAAK,MAAA,CAAO,IAAA,EAAM,KAAK;QAChC;QAEA,YAAY,IAAA,EAAM,KAAK;QACvB,OAAO,iBAAiB,IAAA,EAAM,KAAK;IACrC;IAAA,cAAA,GAeA,KAAK,GAAA,EAAmC,IAAA,EAA0B;QAChE,IAAI,QAAQ,CAAC,CAAC,KAAK;YACjB,OAAO;QACT;QACA,IAAI,MAAM;YACR,MAAM,UAAU,IAAA,CAAK,OAAA;4NACrBC,OAAAA,EAAKD,0NAAAA,EAAQ,IAAI,GAAe,CAAA,MAAO,OAAA,CAAQ,GAAG,CAAA,CAAE,IAAA,CAAK,CAAC,CAAC,GAAG,CAAC;QACjE,OAAO;YACL,UAAU,IAAA,CAAK,MAAA,EAAQ,IAAA,CAAK,YAAY;YACxC,IAAA,CAAK,IAAA,CAAK,CAAA,SAAU,OAAO,IAAA,CAAK,CAAC,CAAC,GAAG,CAAC;QACxC;QACA,OAAO,IAAA;IACT;IAAA,wCAAA,GAGA,MAAM,IAAA,EAA0B;QAC9B,gNAAID,KAAAA,CAAG,GAAA,CAAI,IAAI,GAAG;YAChB,IAAA,CAAK,KAAA,CAAM;gBAAE,OAAO;YAAK,CAAC;QAC5B,OAAO;YACL,MAAM,UAAU,IAAA,CAAK,OAAA;4NACrBE,OAAAA,kNAAKD,UAAAA,EAAQ,IAAI,GAAe,CAAA,MAAO,OAAA,CAAQ,GAAG,CAAA,CAAE,KAAA,CAAM,CAAC;QAC7D;QACA,OAAO,IAAA;IACT;IAAA,oCAAA,GAGA,OAAO,IAAA,EAA0B;QAC/B,gNAAID,KAAAA,CAAG,GAAA,CAAI,IAAI,GAAG;YAChB,IAAA,CAAK,KAAA,CAAM;gBAAE,OAAO;YAAM,CAAC;QAC7B,OAAO;YACL,MAAM,UAAU,IAAA,CAAK,OAAA;4NACrBE,OAAAA,kNAAKD,UAAAA,EAAQ,IAAI,GAAe,CAAA,MAAO,OAAA,CAAQ,GAAG,CAAA,CAAE,MAAA,CAAO,CAAC;QAC9D;QACA,OAAO,IAAA;IACT;IAAA,0CAAA,GAGA,KAAK,QAAA,EAAsD;wNACzDE,WAAAA,EAAS,IAAA,CAAK,OAAA,EAAS,QAAe;IACxC;IAAA,yDAAA,GAGU,WAAW;QACnB,MAAM,EAAE,OAAA,EAAS,QAAA,EAAU,MAAA,CAAO,CAAA,GAAI,IAAA,CAAK,OAAA;QAE3C,MAAM,SAAS,IAAA,CAAK,OAAA,CAAQ,IAAA,GAAO;QACnC,MAAM,UAAU,IAAA,CAAK,QAAA,CAAS,IAAA,GAAO;QAErC,IAAK,UAAU,CAAC,IAAA,CAAK,QAAA,IAAc,WAAW,CAAC,IAAA,CAAK,QAAA,EAAW;YAC7D,IAAA,CAAK,QAAA,GAAW;aAChBJ,uNAAAA,EAAM,SAAS,CAAC,CAACK,UAAS,MAAM,CAAA,KAAM;gBACpC,OAAO,KAAA,GAAQ,IAAA,CAAK,GAAA,CAAI;gBACxBA,SAAQ,QAAQ,IAAA,EAAM,IAAA,CAAK,KAAK;YAClC,CAAC;QACH;QAEA,MAAM,OAAO,CAAC,UAAU,IAAA,CAAK,QAAA;QAC7B,MAAM,SAAS,WAAY,QAAQ,OAAO,IAAA,GAAQ,IAAA,CAAK,GAAA,CAAI,IAAI;QAE/D,IAAI,WAAW,SAAS,IAAA,EAAM;YAC5BL,wNAAAA,EAAM,UAAU,CAAC,CAACM,WAAU,MAAM,CAAA,KAAM;gBACtC,OAAO,KAAA,GAAQ;gBACfA,UAAS,QAAQ,IAAA,EAAM,IAAA,CAAK,KAAK;YACnC,CAAC;QACH;QAGA,IAAI,MAAM;YACR,IAAA,CAAK,QAAA,GAAW;4NAChBN,QAAAA,EAAM,QAAQ,CAAC,CAACO,SAAQ,MAAM,CAAA,KAAM;gBAClC,OAAO,KAAA,GAAQ;gBACfA,QAAO,QAAQ,IAAA,EAAM,IAAA,CAAK,KAAK;YACjC,CAAC;QACH;IACF;IAAA,cAAA,GAGA,cAAc,KAAA,EAAyB;QACrC,IAAI,MAAM,IAAA,IAAQ,UAAU;YAC1B,IAAA,CAAK,QAAA,CAAS,GAAA,CAAI,MAAM,MAAM;YAC9B,IAAI,CAAC,MAAM,IAAA,EAAM;gBACf,IAAA,CAAK,OAAA,CAAQ,GAAA,CAAI,MAAM,MAAM;YAC/B;QACF,OAAA,IAAW,MAAM,IAAA,IAAQ,QAAQ;YAC/B,IAAA,CAAK,OAAA,CAAQ,MAAA,CAAO,MAAM,MAAM;QAClC,OAEK;gMACLC,MAAAA,CAAI,OAAA,CAAQ,IAAA,CAAK,QAAQ;IAC3B;AACF;AAKO,SAAS,iBACd,IAAA,EACA,KAAA,EACA;IACA,OAAO,QAAQ,GAAA,CAAI,MAAM,GAAA,CAAI,CAAA,QAAS,YAAY,MAAM,KAAK,CAAC,CAAC,EAAE,IAAA,CAC/D,CAAA,UAAW,kBAAkB,MAAM,OAAO;AAE9C;AAWA,eAAsB,YACpB,IAAA,EACA,KAAA,EACA,MAAA,EACa;IACb,MAAM,EAAE,IAAA,EAAM,IAAAC,GAAAA,EAAI,IAAA,EAAM,IAAA,EAAM,MAAA,EAAQ,SAAA,CAAU,CAAA,GAAI;IACpD,MAAMC,wNAAWT,KAAAA,CAAG,GAAA,CAAI,MAAM,OAAO,KAAK,MAAM,OAAA;IAIhD,IAAI,MAAM;QACR,MAAM,IAAA,GAAO;IACf;IAGA,IAAIQ,QAAO,OAAO,MAAM,EAAA,GAAK;IAC7B,IAAI,SAAS,OAAO,MAAM,IAAA,GAAO;IAEjC,MAAM,sNAAUR,KAAAA,CAAG,GAAA,CAAIQ,GAAE,iNAAKR,KAAAA,CAAG,GAAA,CAAIQ,GAAE,IAAIA,MAAK,KAAA;IAChD,IAAI,SAAS;QACX,MAAM,EAAA,GAAK,KAAA;QACX,MAAM,MAAA,GAAS,KAAA;QACf,IAAIC,WAAU;YACZA,UAAS,MAAA,GAAS,KAAA;QACpB;IACF,OAIK;uNACHP,QAAAA,EAAK,gBAAgB,CAAA,QAAO;YAC1B,MAAM,UAAe,KAAA,CAAM,GAAG,CAAA;YAC9B,gNAAIF,KAAAA,CAAG,GAAA,CAAI,OAAO,GAAG;gBACnB,MAAM,QAAQ,IAAA,CAAK,SAAS,CAAA,CAAE,GAAG,CAAA;gBACjC,KAAA,CAAM,GAAG,CAAA,GAAK,CAAC,EAAE,QAAA,EAAU,SAAA,CAAU,CAAA,KAAuB;oBAC1D,MAAMU,UAAS,MAAM,GAAA,CAAI,OAAO;oBAChC,IAAIA,SAAQ;wBACV,IAAI,CAAC,UAAUA,QAAO,QAAA,GAAW;wBACjC,IAAI,WAAWA,QAAO,SAAA,GAAY;oBACpC,OAAO;wBAEL,MAAM,GAAA,CAAI,SAAS;4BACjB,OAAO;4BACP,UAAU,YAAY;4BACtB,WAAW,aAAa;wBAC1B,CAAC;oBACH;gBACF;gBAGA,IAAID,WAAU;oBACZA,SAAAA,CAAS,GAAG,CAAA,GAAI,KAAA,CAAM,GAAG,CAAA;gBAC3B;YACF;QACF,CAAC;IACH;IAEA,MAAM,QAAQ,IAAA,CAAK,QAAQ,CAAA;IAG3B,IAAI,MAAM,KAAA,KAAU,CAAC,MAAM,MAAA,EAAQ;QACjC,MAAM,MAAA,GAAS,MAAM,KAAA;wNACrBE,aAAAA,EAAW,MAAM,KAAA,GAAQ,MAAM,UAAA,GAAa,MAAM,WAAW;IAC/D,OAAA,IAES,MAAM,MAAA,EAAQ;QACrB,MAAM,KAAA,GAAQ;IAChB;IAEA,MAAM,WAAA,CAA2B,QAAQ,OAAO,IAAA,CAAK,KAAK,OAAO,CAAA,EAAG,GAAA,CAAI,CAAA,MACtE,KAAK,OAAA,CAAQ,GAAG,CAAA,CAAG,KAAA,CAAM,KAAY;IAGvC,MAAM,SACJ,MAAM,MAAA,KAAW,QAAQ,eAAe,OAAO,QAAQ,MAAM;IAE/D,IAAI,WAAY,UAAU,MAAM,OAAA,EAAU;QACxC,SAAS,IAAA,CACP,cAAc,EAAE,IAAA,CAAK,cAAc,CAAA,EAAG;YACpC;YACA;YACA,SAAS;gBACP,OAAO,mNAAA;gBACP,oNAAQ,OAAA;gBACR,OAAMC,MAAAA,EAAO,OAAA,EAAS;oBACpB,IAAI,QAAQ;wBACV,UAAU,OAAO,IAAA,CAAK,cAAc,CAAC;wBACrC,QAAQ,mBAAmB,IAAI,CAAC;oBAClC,OAAO;wBACLA,OAAM,MAAA,GAAS;wBACf,QACE,SACE,SACAA,QACA,OACA;oBAGN;gBACF;YACF;QACF,CAAC;IAEL;IAIA,IAAI,MAAM,MAAA,EAAQ;QAGhB,MAAM,IAAI,QAAc,CAAA,WAAU;YAChC,MAAM,WAAA,CAAY,GAAA,CAAI,MAAM;QAC9B,CAAC;IACH;IAEA,MAAM,SAAS,kBAAuB,MAAM,MAAM,QAAQ,GAAA,CAAI,QAAQ,CAAC;IACvE,IAAI,QAAQ,OAAO,QAAA,IAAY,CAAA,CAAE,UAAU,OAAO,IAAA,GAAO;QACvD,MAAM,YAAY,iBAAiB,OAAO,MAAMJ,GAAE;QAClD,IAAI,WAAW;YACb,YAAY,MAAM;gBAAC,SAAS;aAAC;YAC7B,OAAO,YAAY,MAAM,WAAW,IAAI;QAC1C;IACF;IACA,IAAI,WAAW;gMACbD,MAAAA,CAAI,cAAA,CAAe,IAAM,UAAU,QAAQ,MAAM,KAAK,IAAI,CAAC;IAC7D;IACA,OAAO;AACT;AAUO,SAAS,WACd,IAAA,EACA,KAAA,EACA;IACA,MAAM,UAAU;QAAE,GAAG,KAAK,OAAA;IAAQ;IAClC,IAAI,OAAO;wNACTL,OAAAA,kNAAKD,UAAAA,EAAQ,KAAK,GAAG,CAACW,WAAe;YACnC,gNAAIZ,KAAAA,CAAG,GAAA,CAAIY,OAAM,IAAI,GAAG;gBACtBA,SAAQ,aAAaA,MAAK;YAC5B;YACA,IAAI,6MAACZ,KAAAA,CAAG,GAAA,CAAIY,OAAM,EAAE,GAAG;gBAErBA,SAAQ;oBAAE,GAAGA,MAAAA;oBAAO,IAAI,KAAA;gBAAU;YACpC;YACA,eAAe,SAAgBA,QAAO,CAAA,QAAO;gBAC3C,OAAO,aAAa,GAAG;YACzB,CAAC;QACH,CAAC;IACH;IACA,WAAW,MAAM,OAAO;IACxB,OAAO;AACT;AAMO,SAAS,WACd,IAAA,EACA,OAAA,EACA;oNACAT,WAAAA,EAAS,SAAS,CAAC,QAAQ,QAAQ;QACjC,IAAI,CAAC,KAAK,OAAA,CAAQ,GAAG,CAAA,EAAG;YACtB,KAAK,OAAA,CAAQ,GAAG,CAAA,GAAI;2NACpBU,oBAAAA,EAAiB,QAAQ,IAAI;QAC/B;IACF,CAAC;AACH;AAEA,SAAS,aAAa,GAAA,EAAa,QAAA,EAA4C;IAC7E,MAAM,SAAS,IAAI,YAAY;IAC/B,OAAO,GAAA,GAAM;IACb,IAAI,UAAU;wNACZA,mBAAAA,EAAiB,QAAQ,QAAQ;IACnC;IACA,OAAO;AACT;AAQA,SAAS,eACP,OAAA,EACA,KAAA,EACA,MAAA,EACA;IACA,IAAI,MAAM,IAAA,EAAM;SACdX,sNAAAA,EAAK,MAAM,IAAA,EAAM,CAAA,QAAO;YACtB,MAAM,SAAS,OAAA,CAAQ,GAAG,CAAA,IAAA,CAAM,OAAA,CAAQ,GAAG,CAAA,GAAI,OAAO,GAAG,CAAA;YACzD,MAAA,CAAO,cAAc,CAAA,CAAE,KAAK;QAC9B,CAAC;IACH;AACF;AAQA,SAAS,YAAY,IAAA,EAAuB,KAAA,EAAkC;mNAC5EA,QAAAA,EAAK,OAAO,CAAA,UAAS;QACnB,eAAe,KAAK,OAAA,EAAS,OAAO,CAAA,QAAO;YACzC,OAAO,aAAa,KAAK,IAAI;QAC/B,CAAC;IACH,CAAC;AACH;;;;ACpgBO,IAAM,gBAAgB,CAAC,EAC5B,QAAA,EACA,GAAG,OACL,KAAwC;IACtC,MAAM,6KAAY,cAAA,EAAW,GAAG;IAGhC,MAAM,QAAQ,MAAM,KAAA,IAAS,CAAC,CAAC,UAAU,KAAA,EACvC,YAAY,MAAM,SAAA,IAAa,CAAC,CAAC,UAAU,SAAA;IAG7C,wNAAQ,aAAA;oCAAW,IAAA,CAAO;gBAAE;gBAAO;YAAU,CAAA;mCAAI;QAAC;QAAO,SAAS;KAAC;IAEnE,MAAM,EAAE,QAAA,CAAS,CAAA,GAAI;IACrB,OAAO,aAAA,GAAA,CAAA,GAAA,6JAAA,CAAA,gBAAA,EAAC,UAAA;QAAS,OAAO;IAAA,GAAQ,QAAS;AAC3C;AAEA,IAAM,MAAM,YAAY,eAAe,CAAC,CAAkB;AAG1D,cAAc,QAAA,GAAW,IAAI,QAAA;AAC7B,cAAc,QAAA,GAAW,IAAI,QAAA;AAG7B,SAAS,YAAe,MAAA,EAAa,IAAA,EAA2B;IAC9D,OAAO,MAAA,CAAO,yKAAc,iBAAA,EAAc,IAAI,CAAC;IAC/C,OAAO,QAAA,CAAS,QAAA,GAAW;IAC3B,OAAO,QAAA,CAAS,QAAA,GAAW;IAC3B,OAAO;AACT;;ACkCO,IAAM,YAAY,MAEA;IACvB,MAAM,UAA+B,CAAC,CAAA;IAEtC,MAAMc,aAA8B,SAAU,KAAA,EAAO;QACnD,CAAA,GAAA,2MAAA,CAAA,sBAAA,CAAoB;QAEpB,MAAM,UAAyB,CAAC,CAAA;wNAEhCF,OAAAA,EAAK,SAAS,CAAC,MAAM,MAAM;YACzB,gNAAIC,KAAAA,CAAG,GAAA,CAAI,KAAK,GAAG;gBACjB,QAAQ,IAAA,CAAK,KAAK,KAAA,CAAM,CAAC;YAC3B,OAAO;gBACL,MAAME,UAAS,UAAU,OAAO,MAAM,CAAC;gBACvC,IAAIA,SAAQ;oBACV,QAAQ,IAAA,CAAK,KAAK,KAAA,CAAMA,OAAM,CAAC;gBACjC;YACF;QACF,CAAC;QAED,OAAO;IACT;IAEAD,WAAU,OAAA,GAAU;IAGpBA,WAAU,GAAA,GAAM,SAAU,IAAA,EAAyB;QACjD,IAAI,CAAC,QAAQ,QAAA,CAAS,IAAI,GAAG;YAC3B,QAAQ,IAAA,CAAK,IAAI;QACnB;IACF;IAGAA,WAAU,MAAA,GAAS,SAAU,IAAA,EAAyB;QACpD,MAAM,IAAI,QAAQ,OAAA,CAAQ,IAAI;QAC9B,IAAI,CAAC,GAAG,QAAQ,MAAA,CAAO,GAAG,CAAC;IAC7B;IAGAA,WAAU,KAAA,GAAQ,WAAY;uNAC5BF,QAAAA,EAAK,SAAS,CAAA,OAAQ,KAAK,KAAA,CAAM,GAAG,SAAS,CAAC;QAC9C,OAAO,IAAA;IACT;IAGAE,WAAU,MAAA,GAAS,WAAY;SAC7BF,sNAAAA,EAAK,SAAS,CAAA,OAAQ,KAAK,MAAA,CAAO,GAAG,SAAS,CAAC;QAC/C,OAAO,IAAA;IACT;IAGAE,WAAU,GAAA,GAAM,SACd,MAAA,EAGA;QACAF,uNAAAA,EAAK,SAAS,CAAC,MAAM,MAAM;YACzB,MAAMG,sNAASF,KAAAA,CAAG,GAAA,CAAI,MAAM,IAAI,OAAO,GAAG,IAAI,IAAI;YAClD,IAAIE,SAAQ;gBACV,KAAK,GAAA,CAAIA,OAAM;YACjB;QACF,CAAC;IACH;IAEAD,WAAU,KAAA,GAAQ,SAAU,KAAA,EAA4C;QACtE,MAAM,UAAyB,CAAC,CAAA;QAEhCF,uNAAAA,EAAK,SAAS,CAAC,MAAM,MAAM;YACzB,gNAAIC,KAAAA,CAAG,GAAA,CAAI,KAAK,GAAG;gBACjB,QAAQ,IAAA,CAAK,KAAK,KAAA,CAAM,CAAC;YAC3B,OAAO;gBACL,MAAME,UAAS,IAAA,CAAK,SAAA,CAAU,OAAO,MAAM,CAAC;gBAC5C,IAAIA,SAAQ;oBACV,QAAQ,IAAA,CAAK,KAAK,KAAA,CAAMA,OAAM,CAAC;gBACjC;YACF;QACF,CAAC;QAED,OAAO;IACT;IAGAD,WAAU,IAAA,GAAO,WAAY;YAC3BF,mNAAAA,EAAK,SAAS,CAAA,OAAQ,KAAK,IAAA,CAAK,GAAG,SAAS,CAAC;QAC7C,OAAO,IAAA;IACT;IAEAE,WAAU,MAAA,GAAS,SAAU,KAAA,EAA2C;wNACtEF,OAAAA,EAAK,SAAS,CAAC,MAAM,IAAM,KAAK,MAAA,CAAO,IAAA,CAAK,SAAA,CAAU,OAAO,MAAM,CAAC,CAAC,CAAC;QACtE,OAAO,IAAA;IACT;IAGA,MAAM,YAAY,SAChB,GAAA,EACA,IAAA,EACA,KAAA,EACA;QACA,mNAAOC,KAAAA,CAAG,GAAA,CAAI,GAAG,IAAI,IAAI,OAAO,IAAI,IAAI;IAC1C;IAEAC,WAAU,SAAA,GAAY;IAEtB,OAAOA;AACT;;AZ9GO,SAAS,WACd,MAAA,EACA,KAAA,EACA,IAAA,EACK;IACL,MAAM,sNAAUE,KAAAA,CAAG,GAAA,CAAI,KAAK,KAAK;IACjC,IAAI,WAAW,CAAC,MAAM,OAAO,CAAC,CAAA;IAG9B,MAAM,OAAM,2KAAA;mCACV,IAAO,WAAW,UAAU,MAAA,IAAU,IAAI,UAAU,IAAI,KAAA;kCACxD,CAAC,CAAA;IAaH,MAAM,WAAW,2KAAA,EAAO,CAAC;IACzB,MAAM,8NAAc,iBAAA,CAAe;IAGnC,MAAM,SAAQ,2KAAA;qCACZ,IAAA,CAAc;gBACZ,OAAO,CAAC,CAAA;gBACR,OAAO,CAAC,CAAA;gBACR,OAAM,IAAA,EAAMC,QAAAA,EAAS;oBACnB,MAAMC,WAAU,WAAW,MAAMD,QAAO;oBAIxC,MAAM,eACJ,SAAS,OAAA,GAAU,KACnB,CAAC,MAAM,KAAA,CAAM,MAAA,IACb,CAAC,OAAO,IAAA,CAAKC,QAAO,EAAE,IAAA;qDAAK,CAAA,MAAO,CAAC,KAAK,OAAA,CAAQ,GAAG,CAAC;;oBAEtD,OAAO,eACH,iBAAiB,MAAMD,QAAO,IAC9B,IAAI;qDAAa,CAAA,YAAW;4BAC1B,WAAW,MAAMC,QAAO;4BACxB,MAAM,KAAA,CAAM,IAAA;6DAAK,MAAM;oCACrB,QAAQ,iBAAiB,MAAMD,QAAO,CAAC;gCACzC,CAAC;;4BACD,YAAY;wBACd,CAAC;;gBACP;YACF,CAAA;oCACA,CAAC,CAAA;IAGH,MAAM,0KAAQ,SAAA,EAAO,CAAC;WAAG,MAAM,KAAK;KAAC;IACrC,MAAM,UAAiB,CAAC,CAAA;IAGxB,MAAM,6NAAa,UAAA,EAAQ,MAAM,KAAK;IAItC,CAAA,GAAA,6JAAA,CAAA,UAAA;8BAAQ,MAAM;4NAEZE,OAAAA,EAAK,MAAM,OAAA,CAAQ,KAAA,CAAM,QAAQ,UAAU;sCAAG,CAAA,SAAQ;oBACpD,WAAW,MAAM,GAAG;oBACpB,KAAK,IAAA,CAAK,IAAI;gBAChB,CAAC;;YACD,MAAM,OAAA,CAAQ,MAAA,GAAS;YAEvB,eAAe,YAAY,MAAM;QACnC;6BAAG;QAAC,MAAM;KAAC;IAGX,CAAA,GAAA,6JAAA,CAAA,UAAA;8BAAQ,MAAM;YACZ,eAAe,GAAG,KAAK,GAAA,CAAI,YAAY,MAAM,CAAC;QAChD;6BAAG,IAAI;IAGP,SAAS,eAAe,UAAA,EAAoB,QAAA,EAAkB;QAC5D,IAAA,IAAS,IAAI,YAAY,IAAI,UAAU,IAAK;YAC1C,MAAM,OACJ,MAAM,OAAA,CAAQ,CAAC,CAAA,IAAA,CACd,MAAM,OAAA,CAAQ,CAAC,CAAA,GAAI,IAAI,WAAW,MAAM,MAAM,KAAK,CAAA;YAEtD,MAAMC,UAA8B,UAChC,QAAQ,GAAG,IAAI,IACd,KAAA,CAAc,CAAC,CAAA;YAEpB,IAAIA,SAAQ;gBACV,OAAA,CAAQ,CAAC,CAAA,GAAI,cAAcA,OAAM;YACnC;QACF;IACF;IAKA,MAAM,UAAU,MAAM,OAAA,CAAQ,GAAA,CAAI,CAAC,MAAM,IAAM,WAAW,MAAM,OAAA,CAAQ,CAAC,CAAC,CAAC;IAE3E,MAAM,4KAAUC,aAAAA,EAAW,aAAa;IACxC,MAAM,8NAAc,UAAA,EAAQ,OAAO;IACnC,MAAM,aAAa,YAAY,eAAe,SAAS,OAAO;IAE9DC,4OAAAA;iDAA0B,MAAM;YAC9B,SAAS,OAAA;YAGT,MAAM,KAAA,GAAQ,MAAM,OAAA;YAGpB,MAAM,EAAE,KAAA,CAAM,CAAA,GAAI;YAClB,IAAI,MAAM,MAAA,EAAQ;gBAChB,MAAM,KAAA,GAAQ,CAAC,CAAA;gOACfH,OAAAA,EAAK;6DAAO,CAAA,KAAM,GAAG,CAAC;;YACxB;gBAGAA,mNAAAA,EAAK,MAAM,OAAA;yDAAS,CAAC,MAAM,MAAM;oBAE/B,KAAK,IAAI,IAAI;oBAGb,IAAI,YAAY;wBACd,KAAK,KAAA,CAAM;4BAAE,SAAS;wBAAQ,CAAC;oBACjC;oBAGA,MAAMC,UAAS,OAAA,CAAQ,CAAC,CAAA;oBACxB,IAAIA,SAAQ;wBAEV,WAAW,MAAMA,QAAO,GAAG;wBAI3B,IAAI,KAAK,GAAA,EAAK;4BACZ,KAAK,KAAA,CAAM,IAAA,CAAKA,OAAM;wBACxB,OAAO;4BACL,KAAK,KAAA,CAAMA,OAAM;wBACnB;oBACF;gBACF,CAAC;;QACH,CAAC;;IAGD,CAAA,GAAA,2MAAA,CAAA,UAAA;8BAAQ;sCAAM,MAAM;oOAClBD,OAAAA,EAAK,MAAM,KAAA;8CAAO,CAAA,OAAQ,KAAK,IAAA,CAAK,IAAI,CAAC;;gBAC3C,CAAC;;;IAID,MAAM,SAAS,QAAQ,GAAA,CAAI,CAAA,IAAA,CAAM;YAAE,GAAG,CAAA;QAAE,CAAA,CAAE;IAE1C,OAAO,MAAM;QAAC;QAAQ,GAAG;KAAA,GAAI;AAC/B;;ADvKO,SAAS,UAAU,KAAA,EAAY,IAAA,EAAuB;IAC3D,MAAM,mNAAOI,KAAAA,CAAG,GAAA,CAAI,KAAK;IACzB,MAAM,CAAC,CAAC,MAAM,CAAA,EAAG,GAAG,CAAA,GAAI,WACtB,GACA,OAAO,QAAQ;QAAC,KAAK;KAAA,EACrB,OAAO,QAAQ,CAAC,CAAA,GAAI;IAEtB,OAAO,QAAQ,UAAU,MAAA,IAAU,IAAI;QAAC;QAAQ,GAAG;KAAA,GAAI;AACzD;;AcjEA,IAAM,gBAAgB,IAAM,UAAe;AAEpC,IAAM,eAAe,sKAC1B,WAAA,EAAS,aAAa,CAAA,CAAE,CAAC,CAAA;;ACgBpB,IAAM,iBAAiB,CAC5B,SACA,UACG;IACH,MAAM,8NAAc,cAAA;mDAAY,IAAM,IAAI,YAAY,SAAS,KAAK,CAAC;;oNAErEE,UAAAA;mCAAQ;2CAAM,MAAM;oBAClB,YAAY,IAAA,CAAK;gBACnB,CAAC;;;IAED,OAAO;AACT;;AC8CO,SAAS,SACd,MAAA,EACA,QAAA,EACA,IAAA,EACA;IACA,MAAM,qNAAUI,MAAAA,CAAG,GAAA,CAAI,QAAQ,KAAK;IACpC,IAAI,WAAW,CAAC,MAAM,OAAO,CAAC,CAAA;IAG9B,IAAI,UAAU;IACd,IAAI,YAAmC,KAAA;IAEvC,MAAM,SAAS,WACb;uCACA,CAAC,GAAG,SAAS;YACX,MAAM,QAAQ,UAAU,QAAQ,GAAG,IAAI,IAAI;YAC3C,YAAY,MAAM,GAAA;YAClB,UAAU,WAAW,MAAM,OAAA;YAE3B,OAAO;QACT;sCAAA,0DAAA;IAAA,yCAAA;IAGA,QAAQ;QAAC,CAAC,CAAC;KAAA;oNAGbC,4BAAAA;+CAA0B,MAAM;YAI9BC,uNAAAA,EAAK,MAAA,CAAO,CAAC,CAAA,CAAE,OAAA;uDAAS,CAAC,MAAM,MAAM;oBACnC,MAAM,SAAS,MAAA,CAAO,CAAC,CAAA,CAAE,OAAA,CAAQ,IAAA,CAAK,UAAU,IAAI,CAAA,CAAA,CAAG,CAAA;oBAKvD,WAAW,MAAM,SAAS;oBAO1B,IAAI,KAAK,GAAA,EAAK;wBACZ,IAAI,QAAQ;4BACV,KAAK,MAAA,CAAO;gCAAE,IAAI,OAAO,OAAA;4BAAQ,CAAC;wBACpC;wBAEA;oBACF;oBAEA,IAAI,QAAQ;wBACV,KAAK,KAAA,CAAM;4BAAE,IAAI,OAAO,OAAA;wBAAQ,CAAC;oBACnC,OAAO;wBACL,KAAK,KAAA,CAAM;oBACb;gBACF,CAAC;;QACH;8CAAG,IAAI;IAEP,IAAI,WAAW,UAAU,MAAA,IAAU,GAAG;QACpC,MAAM,MAAM,aAAa,MAAA,CAAO,CAAC,CAAA;QAEjC,GAAA,CAAI,WAAW,CAAA,GAAI,CAACC,WAAU,MAAM,MAAM;YACxC,MAAM,oNAAQH,KAAAA,CAAG,GAAA,CAAIG,SAAQ,IAAIA,UAAS,GAAG,IAAI,IAAIA;YACrD,IAAI,OAAO;gBACT,MAAM,SAAS,IAAI,OAAA,CAAQ,IAAA,CAAK,MAAM,OAAA,GAAU,IAAI,CAAA,CAAA,CAAG,CAAA;gBACvD,IAAI,QAAQ,MAAM,EAAA,GAAK,OAAO,OAAA;gBAC9B,OAAO;YACT;QACF;QACA,OAAO;IACT;IAEA,OAAO,MAAA,CAAO,CAAC,CAAA;AACjB;;;;ACnFO,SAAS,cACd,IAAA,EACA,KAAA,EACA,IAAA,EACK;IACL,MAAM,UAAUY,iNAAAA,CAAG,GAAA,CAAI,KAAK,KAAK;IAEjC,MAAM,EACJ,KAAA,EACA,IAAA,EACA,QAAQ,CAAA,EACR,UAAU,IAAA,EACV,kBAAkB,KAAA,EAClB,WAAA,EACA,KAAK,QAAA,EACL,QAAQ,WAAA,EACV,GAA6B,UAAU,QAAQ,IAAI;IAGnD,MAAM,wKAAMC,UAAAA;uCACV,IAAO,WAAW,UAAU,MAAA,IAAU,IAAI,UAAU,IAAI,KAAA;sCACxD,CAAC,CAAA;IAIH,MAAM,QAAQC,0NAAAA,EAAQ,IAAI;IAC1B,MAAM,cAAiC,CAAC,CAAA;IAGxC,MAAM,oLAAkBC,SAAAA,EAAiC,IAAI;IAC7D,MAAM,kBAAkB,QAAQ,OAAO,gBAAgB,OAAA;mNAEvDC,6BAAAA;oDAA0B,MAAM;YAC9B,gBAAgB,OAAA,GAAU;QAC5B,CAAC;;oNAEDC,UAAAA;kCAAQ,MAAM;aASZC,sNAAAA,EAAK;0CAAa,CAAA,MAAK;oBACrB,KAAK,IAAI,EAAE,IAAI;oBACf,EAAE,IAAA,CAAK,GAAA,GAAM;gBACf,CAAC;;YAGD;0CAAO,MAAM;oOACXA,OAAAA,EAAK,gBAAgB,OAAA;kDAAU,CAAA,MAAK;4BAClC,IAAI,EAAE,OAAA,EAAS;gCACb,aAAa,EAAE,YAAa;4BAC9B;4BACA,WAAW,EAAE,IAAA,EAAM,GAAG;4BACtB,EAAE,IAAA,CAAK,IAAA,CAAK,IAAI;wBAClB,CAAC;;gBACH;;QACF,CAAC;;IAMD,MAAM,OAAO,QAAQ,OAAO,UAAU,QAAQ,IAAI,OAAO,eAAe;IAGxE,MAAM,UAAW,SAAS,gBAAgB,OAAA,IAAY,CAAC,CAAA;KACvDF,2OAAAA;oDAA0B,oNACxBE,OAAAA,EAAK;4DAAS,CAAC,EAAE,IAAA,EAAM,IAAA,EAAM,GAAA,CAAI,CAAA,KAAM;oBACrC,WAAW,MAAM,GAAG;oBACpB,SAAS,aAAa,MAAM,GAAG;gBACjC,CAAC;;;IAIH,MAAM,SAAmB,CAAC,CAAA;IAC1B,IAAI,iOACFA,OAAAA,EAAK,iBAAiB,CAAC,GAAG,MAAM;QAE9B,IAAI,EAAE,OAAA,EAAS;YACb,aAAa,EAAE,YAAa;YAC5B,QAAQ,IAAA,CAAK,CAAC;QAChB,OAAO;YACL,IAAI,MAAA,CAAO,CAAC,CAAA,GAAI,KAAK,OAAA,CAAQ,EAAE,GAAG;YAClC,IAAI,CAAC,GAAG,WAAA,CAAY,CAAC,CAAA,GAAI;QAC3B;IACF,CAAC;KAGHA,sNAAAA,EAAK,OAAO,CAAC,MAAM,MAAM;QACvB,IAAI,CAAC,WAAA,CAAY,CAAC,CAAA,EAAG;YACnB,WAAA,CAAY,CAAC,CAAA,GAAI;gBACf,KAAK,IAAA,CAAK,CAAC,CAAA;gBACX;gBACA,OAAA,QAAA,SAAA;gBACA,MAAM,IAAI,WAAW;YACvB;YAEA,WAAA,CAAY,CAAC,CAAA,CAAE,IAAA,CAAK,IAAA,GAAO;QAC7B;IACF,CAAC;IAID,IAAI,OAAO,MAAA,EAAQ;QACjB,IAAI,IAAI,CAAA;QACR,MAAM,EAAE,KAAA,CAAM,CAAA,GAA6B,UAAU,QAAQ,IAAI;wNACjEA,OAAAA,EAAK,QAAQ,CAAC,UAAU,cAAc;YACpC,MAAM,IAAI,eAAA,CAAiB,SAAS,CAAA;YACpC,IAAI,CAAC,UAAU;gBACb,IAAI,YAAY,OAAA,CAAQ,CAAC;gBACzB,WAAA,CAAY,CAAC,CAAA,GAAI;oBAAE,GAAG,CAAA;oBAAG,MAAM,KAAA,CAAM,QAAQ,CAAA;gBAAE;YACjD,OAAA,IAAW,OAAO;gBAChB,YAAY,MAAA,CAAO,EAAE,GAAG,GAAG,CAAC;YAC9B;QACF,CAAC;IACH;IAEA,+MAAIN,MAAAA,CAAG,GAAA,CAAI,IAAI,GAAG;QAChB,YAAY,IAAA,CAAK,CAAC,GAAG,IAAM,KAAK,EAAE,IAAA,EAAM,EAAE,IAAI,CAAC;IACjD;IAGA,IAAI,QAAQ,CAAC;IAGb,MAAM,eAAcO,gOAAAA,CAAe;IAGnC,MAAM,eAAe,gBAAoC,KAAK;IAE9D,MAAM,UAAU,aAAA,GAAA,IAAI,IAA6B;IACjD,MAAM,uLAAqBJ,SAAAA,EAAO,aAAA,GAAA,IAAI,IAA6B,CAAC;IAEpE,MAAM,cAAcA,2KAAAA,EAAO,KAAK;oNAChCG,OAAAA,EAAK,aAAa,CAAC,GAAG,MAAM;QAC1B,MAAM,MAAM,EAAE,GAAA;QACd,MAAM,YAAY,EAAE,KAAA;QAEpB,MAAM,IAA6B,UAAU,QAAQ,IAAI;QAEzD,IAAIE;QACJ,IAAI;QAEJ,MAAM,aAAa,SAAS,EAAE,KAAA,IAAS,GAAG,GAAG;QAE7C,IAAI,aAAA,QAAA,SAAA,KAAoC;YACtCA,MAAK,EAAE,KAAA;YACP,QAAA,QAAA,SAAA;QACF,OAAO;YACL,MAAM,UAAU,KAAK,OAAA,CAAQ,GAAG,IAAI;YACpC,IAAI,aAAA,QAAA,SAAA,KAAoC;gBACtC,IAAI,SAAS;oBACXA,MAAK,EAAE,KAAA;oBACP,QAAA,QAAA,SAAA;gBACF,OAAA,IAAYA,MAAK,EAAE,MAAA,EAAS;oBAC1B,QAAA,SAAA,UAAA;gBACF,OAAO;YACT,OAAA,IAAW,CAAC,SAAS;gBACnBA,MAAK,EAAE,KAAA;gBACP,QAAA,QAAA,SAAA;YACF,OAAO;QACT;QAIAA,MAAK,SAASA,KAAI,EAAE,IAAA,EAAM,CAAC;QAC3BA,kNAAKR,KAAAA,CAAG,GAAA,CAAIQ,GAAE,IAAI,QAAQA,GAAE,IAAI;YAAE,IAAAA;QAAG;QAarC,IAAI,CAACA,IAAG,MAAA,EAAQ;YACd,MAAMC,UAAS,eAAe,aAAa,MAAA;YAC3CD,IAAG,MAAA,GAAS,SAASC,SAAQ,EAAE,IAAA,EAAM,GAAG,KAAK;QAC/C;QAEA,SAAS;QAGT,MAAM,UAA0C;YAC9C,GAAG,YAAA;YAAA,iDAAA;YAEH,OAAO,aAAa;YACpB,KAAK;YACL,WAAW,EAAE,SAAA;YAAA,gCAAA;YAEb,OAAO;YAAA,kCAAA;YAEP,GAAID,GAAAA;QACN;QAEA,IAAI,SAAA,QAAA,SAAA,mNAAkCR,KAAAA,CAAG,GAAA,CAAI,QAAQ,IAAI,GAAG;YAC1D,MAAMU,KAAI,UAAU,QAAQ,IAAI;YAIhC,MAAM,mNAAOV,KAAAA,CAAG,GAAA,CAAIU,GAAE,OAAO,KAAK,kBAAkBA,GAAE,IAAA,GAAOA,GAAE,OAAA;YAE/D,QAAQ,IAAA,GAAO,SAAS,MAAM,EAAE,IAAA,EAAM,CAAC;QACzC;QAEA,MAAM,EAAE,SAAA,CAAU,CAAA,GAAI;QACtB,QAAQ,SAAA,GAAY,CAAA,WAAU;YAC5B,SAAS,WAAW,MAAM;YAE1B,MAAMC,eAAc,gBAAgB,OAAA;YACpC,MAAMC,KAAID,aAAY,IAAA,CAAK,CAAAC,KAAKA,GAAE,GAAA,KAAQ,GAAG;YAC7C,IAAI,CAACA,IAAG;YAIR,IAAI,OAAO,SAAA,IAAaA,GAAE,KAAA,IAAA,SAAA,UAAA,KAAiC;gBAQzD;YACF;YAEA,IAAIA,GAAE,IAAA,CAAK,IAAA,EAAM;gBACf,MAAM,OAAOD,aAAY,KAAA,CAAM,CAAAC,KAAKA,GAAE,IAAA,CAAK,IAAI;gBAC/C,IAAIA,GAAE,KAAA,IAAA,QAAA,SAAA,KAAgC;oBACpC,MAAM,SAAS,SAAS,SAASA,GAAE,IAAI;oBACvC,IAAI,WAAW,OAAO;wBACpB,MAAM,WAAW,WAAW,OAAO,IAAI;wBACvCA,GAAE,OAAA,GAAU;wBAGZ,IAAI,CAAC,QAAQ,WAAW,GAAG;4BAEzB,IAAI,YAAY,YACdA,GAAE,YAAA,GAAe,WAAW,aAAa,QAAQ;4BACnD;wBACF;oBACF;gBACF;gBAEA,IAAI,QAAQD,aAAY,IAAA,CAAK,CAAAC,KAAKA,GAAE,OAAO,GAAG;oBAK5C,mBAAmB,OAAA,CAAQ,MAAA,CAAOA,EAAC;oBAEnC,IAAI,iBAAiB;wBAKnB,YAAY,OAAA,GAAU;oBACxB;oBAEA,YAAY;gBACd;YACF;QACF;QAEA,MAAM,UAAU,WAAW,EAAE,IAAA,EAAM,OAAO;QAK1C,IAAI,UAAA,QAAA,SAAA,OAAmC,iBAAiB;YACtD,mBAAmB,OAAA,CAAQ,GAAA,CAAI,GAAG;gBAAE;gBAAO;gBAAS;YAAQ,CAAC;QAC/D,OAAO;YACL,QAAQ,GAAA,CAAI,GAAG;gBAAE;gBAAO;gBAAS;YAAQ,CAAC;QAC5C;IACF,CAAC;IAGD,MAAM,4KAAUC,aAAAA,EAAW,aAAa;IACxC,MAAM,kBAAcC,sNAAAA,EAAQ,OAAO;IACnC,MAAM,aAAa,YAAY,eAAe,SAAS,OAAO;oNAG9DV,4BAAAA;oDAA0B,MAAM;YAC9B,IAAI,YAAY;gOACdE,OAAAA,EAAK;gEAAa,CAAA,MAAK;wBACrB,EAAE,IAAA,CAAK,KAAA,CAAM;4BAAE,SAAS;wBAAQ,CAAC;oBACnC,CAAC;;YACH;QACF;mDAAG;QAAC,OAAO;KAAC;oNAEZA,OAAAA,EAAK,SAAS,CAAC,GAAG,MAAM;QAMtB,IAAI,mBAAmB,OAAA,CAAQ,IAAA,EAAM;YACnC,MAAM,MAAM,YAAY,SAAA,CAAU,CAAA,QAAS,MAAM,GAAA,KAAQ,EAAE,GAAG;YAC9D,YAAY,MAAA,CAAO,KAAK,CAAC;QAC3B;IACF,CAAC;oNAEDF,4BAAAA;oDACE,MAAM;YAKJE,uNAAAA,EACE,mBAAmB,OAAA,CAAQ,IAAA,GAAO,mBAAmB,OAAA,GAAU;4DAC/D,CAAC,EAAE,KAAA,EAAO,OAAA,CAAQ,CAAA,EAAG,MAAM;oBACzB,MAAM,EAAE,IAAA,CAAK,CAAA,GAAI;oBAEjB,EAAE,KAAA,GAAQ;oBAGV,KAAK,IAAI,IAAI;oBAGb,IAAI,cAAc,SAAA,QAAA,SAAA,KAAgC;wBAChD,KAAK,KAAA,CAAM;4BAAE,SAAS;wBAAQ,CAAC;oBACjC;oBAEA,IAAI,SAAS;wBAEX,WAAW,MAAM,QAAQ,GAAG;wBAQ5B,IAAA,CAAK,KAAK,GAAA,IAAO,GAAA,KAAQ,CAAC,YAAY,OAAA,EAAS;4BAC7C,KAAK,MAAA,CAAO,OAAO;wBACrB,OAAO;4BACL,KAAK,KAAA,CAAM,OAAO;4BAElB,IAAI,YAAY,OAAA,EAAS;gCACvB,YAAY,OAAA,GAAU;4BACxB;wBACF;oBACF;gBACF;;QAEJ;mDACA,QAAQ,KAAA,IAAS;IAGnB,MAAM,oBAAkC,CAAA,SACtC,aAAA,GAAA,CAAA,GAAA,6JAAA,CAAA,gBAAA,EAAA,6JAAA,CAAA,WAAA,EAAA,MACG,YAAY,GAAA,CAAI,CAAC,GAAG,MAAM;YACzB,MAAM,EAAE,OAAA,CAAQ,CAAA,GAAI,QAAQ,GAAA,CAAI,CAAC,KAAK,EAAE,IAAA;YACxC,MAAM,OAAY,OAAO;gBAAE,GAAG,OAAA;YAAQ,GAAG,EAAE,IAAA,EAAM,GAAG,CAAC;YACrD,OAAO,QAAQ,KAAK,IAAA,GAClB,aAAA,GAAA,CAAA,GAAA,6JAAA,CAAA,gBAAA,EAAC,KAAK,IAAA,EAAL;gBACE,GAAG,KAAK,KAAA;gBACT,iNAAKN,KAAAA,CAAG,GAAA,CAAI,EAAE,GAAG,iNAAKA,KAAAA,CAAG,GAAA,CAAI,EAAE,GAAG,IAAI,EAAE,GAAA,GAAM,EAAE,IAAA,CAAK,EAAA;gBACrD,KAAK,KAAK,GAAA;YAAA,KAGZ;QAEJ,CAAC,CACH;IAGF,OAAO,MAAM;QAAC;QAAmB,GAAG;KAAA,GAAI;AAC1C;AAGA,IAAI,UAAU;AAEd,SAAS,QACP,KAAA,EACA,EAAE,GAAA,EAAK,OAAO,GAAA,CAAI,CAAA,EAClB,eAAA,EACgB;IAChB,IAAI,SAAS,MAAM;QACjB,MAAM,SAAS,aAAA,GAAA,IAAI,IAAI;QACvB,OAAO,MAAM,GAAA,CAAI,CAAA,SAAQ;YACvB,MAAM,IACJ,mBACA,gBAAgB,IAAA,CACd,CAAAY,KACEA,GAAE,IAAA,KAAS,QACXA,GAAE,KAAA,KAAA,QAAA,SAAA,OACF,CAAC,OAAO,GAAA,CAAIA,EAAC;YAEnB,IAAI,GAAG;gBACL,OAAO,GAAA,CAAI,CAAC;gBACZ,OAAO,EAAE,GAAA;YACX;YACA,OAAO;QACT,CAAC;IACH;IACA,mNAAOZ,KAAAA,CAAG,GAAA,CAAI,IAAI,IAAI,oNAAQA,KAAAA,CAAG,GAAA,CAAI,IAAI,IAAI,MAAM,GAAA,CAAI,IAAI,IAAIE,0NAAAA,EAAQ,IAAI;AAC7E;;ACzbO,IAAM,YAAY,CAAC,EACxB,SAAA,EACA,GAAG,eACL,GAAsB,CAAC,CAAA,KAKjB;IACJ,MAAM,CAAC,cAAc,GAAG,CAAA,GAAI;+BAC1B,IAAA,CAAO;gBACL,SAAS;gBACT,SAAS;gBACT,iBAAiB;gBACjB,iBAAiB;gBACjB,GAAG,aAAA;YACL,CAAA;8BACA,CAAC,CAAA;oNAGHe,4BAAAA;gDAA0B,MAAM;YAC9B,MAAM,gOAAgB,WAAA;sEACpB,CAAC,EAAE,CAAA,EAAG,CAAA,CAAE,CAAA,KAAM;oBACZ,IAAI,KAAA,CAAM;wBACR,SAAS,EAAE,OAAA;wBACX,iBAAiB,EAAE,QAAA;wBACnB,SAAS,EAAE,OAAA;wBACX,iBAAiB,EAAE,QAAA;oBACrB,CAAC;gBACH;qEACA;gBAAE,WAAW,WAAW,WAAW,KAAA;YAAU;YAG/C;wDAAO,MAAM;oOAIXC,OAAAA,EAAK,OAAO,MAAA,CAAO,YAAY;gEAAG,CAAA,QAAS,MAAM,IAAA,CAAK,CAAC;;oBAEvD,cAAc;gBAChB;;QACF;+CAAG,CAAC,CAAC;IAEL,OAAO;AACT;;AC5CO,IAAM,YAAY,CAAC,EACxB,SAAA,EACA,GAAG,eACL,KAGM;IACJ,MAAM,CAAC,YAAY,GAAG,CAAA,GAAI;+BACxB,IAAA,CAAO;gBACL,OAAO;gBACP,QAAQ;gBACR,GAAG,aAAA;YACL,CAAA;8BACA,CAAC,CAAA;KAGHG,2OAAAA;gDAA0B,MAAM;YAC9B,MAAM,gOAAgB,WAAA;sEACpB,CAAC,EAAE,KAAA,EAAO,MAAA,CAAO,CAAA,KAAM;oBACrB,IAAI,KAAA,CAAM;wBACR;wBACA;wBACA,WACE,WAAW,KAAA,CAAM,GAAA,CAAI,MAAM,KAAK,WAAW,MAAA,CAAO,GAAA,CAAI,MAAM;oBAChE,CAAC;gBACH;qEACA;gBAAE,WAAW,WAAW,WAAW,KAAA;YAAU;YAG/C;wDAAO,MAAM;oOAIXC,OAAAA,EAAK,OAAO,MAAA,CAAO,UAAU;gEAAG,CAAA,QAAS,MAAM,IAAA,CAAK,CAAC;;oBAErD,cAAc;gBAChB;;QACF;+CAAG,CAAC,CAAC;IAEL,OAAO;AACT;;;AC7DA,IAAM,0BAA0B;IAC9B,KAAK;IACL,KAAK;AACP;AAcO,SAAS,UACd,KAAA,EACA,IAAA,EACA;IACA,MAAM,CAAC,UAAU,WAAW,CAAA,GAAIK,6KAAAA,EAAS,KAAK;IAC9C,MAAM,wKAAMC,SAAAA,CAAiB;IAE7B,MAAM,sNAAUC,KAAAA,CAAG,GAAA,CAAI,KAAK,KAAK;IAEjC,MAAM,eAAe,UAAU,QAAQ,IAAI,CAAC;IAC5C,MAAM,EAAE,IAAAC,MAAK,CAAC,CAAA,EAAG,OAAO,CAAC,CAAA,EAAG,GAAG,gBAAgB,CAAA,GAAI;IAEnD,MAAM,wBAAwB,UAAU,OAAO;IAE/C,MAAM,CAAC,SAAS,GAAG,CAAA,GAAI;+BAAU,IAAA,CAAO;gBAAE;gBAAM,GAAG,eAAA;YAAgB,CAAA;8BAAI,CAAC,CAAC;oNAEzEC,4BAAAA;gDAA0B,MAAM;YAC9B,MAAM,UAAU,IAAI,OAAA;YACpB,MAAM,EACJ,IAAA,EACA,IAAA,EACA,SAAS,KAAA,EACT,GAAG,UACL,GAAI,yBAAyB,CAAC;YAE9B,IACE,CAAC,WACA,QAAQ,YACT,OAAO,yBAAyB,aAEhC;YAEF,MAAM,sBAAsB,aAAA,GAAA,IAAI,QAA+B;YAE/D,MAAM;gEAAU,MAAM;oBACpB,IAAID,KAAI;wBAEN,IAAI,KAAA,CAAMA,GAAE;oBACd;oBAEA,YAAY,IAAI;oBAEhB,MAAM;gFAAU,MAAM;4BACpB,IAAI,MAAM;gCACR,IAAI,KAAA,CAAM,IAAI;4BAChB;4BACA,YAAY,KAAK;wBACnB;;oBAEA,OAAO,OAAO,KAAA,IAAY;gBAC5B;;YAEA,MAAM;2EAAmD,CAAA,YAAW;oBAClE,QAAQ,OAAA;mFAAQ,CAAA,UAAS;4BACvB,MAAM,UAAU,oBAAoB,GAAA,CAAI,MAAM,MAAM;4BAEpD,IAAI,MAAM,cAAA,KAAmB,QAAQ,OAAO,GAAG;gCAC7C;4BACF;4BAEA,IAAI,MAAM,cAAA,EAAgB;gCACxB,MAAM,aAAa,QAAQ;gCAC3B,gNAAID,KAAAA,CAAG,GAAA,CAAI,UAAU,GAAG;oCACtB,oBAAoB,GAAA,CAAI,MAAM,MAAA,EAAQ,UAAU;gCAClD,OAAO;oCACL,SAAS,SAAA,CAAU,MAAM,MAAM;gCACjC;4BACF,OAAA,IAAW,SAAS;gCAClB,QAAQ;gCACR,oBAAoB,MAAA,CAAO,MAAM,MAAM;4BACzC;wBACF,CAAC;;gBACH;;YAEA,MAAM,WAAW,IAAI,qBAAqB,oBAAoB;gBAC5D,MAAO,QAAQ,KAAK,OAAA,IAAY,KAAA;gBAChC,WACE,OAAO,WAAW,YAAY,MAAM,OAAA,CAAQ,MAAM,IAC9C,SACA,uBAAA,CAAwB,MAAM,CAAA;gBACpC,GAAG,QAAA;YACL,CAAC;YAED,SAAS,OAAA,CAAQ,OAAO;YAExB;wDAAO,IAAM,SAAS,SAAA,CAAU,OAAO;;QACzC;+CAAG;QAAC,qBAAqB;KAAC;IAE1B,IAAI,SAAS;QACX,OAAO;YAAC;YAAK,OAAO;SAAA;IACtB;IAEA,OAAO;QAAC;QAAK,QAAQ;KAAA;AACvB;;ACtGO,SAAS,OAAO,EAAE,QAAA,EAAU,GAAG,MAAM,CAAA,EAAQ;IAClD,OAAO,SAAS,UAAU,KAAK,CAAC;AAClC;;ACPO,SAAS,MAAqD,EACnE,KAAA,EACA,QAAA,EACA,GAAG,OACL,EAA2D;IACzD,MAAM,SAAgB,SAAS,MAAM,MAAA,EAAQ,KAAK;IAClD,OAAO,MAAM,GAAA,CAAI,CAAC,MAAM,UAAU;QAChC,MAAM,SAAS,SAAS,MAAM,KAAK;QACnC,mNAAOI,KAAAA,CAAG,GAAA,CAAI,MAAM,IAAI,OAAO,MAAA,CAAO,KAAK,CAAC,IAAI;IAClD,CAAC;AACH;;AClBO,SAAS,WAAW,EACzB,KAAA,EACA,QAAA,EACA,GAAG,OACL,EAAkC;IAChC,OAAO,cAAc,OAAO,KAAK,EAAE,QAAQ;AAC7C;;;;AEkBO,IAAM,gBAAN,cAGG,WAAmB;IAa3B,YAEW,MAAA,EACT,IAAA,CACA;QACA,KAAA,CAAM;QAHG,IAAA,CAAA,MAAA,GAAA;QAVX,uCAAA,GAAA,IAAA,CAAA,IAAA,GAAO;QAMP,6CAAA,GAAA,IAAA,CAAU,OAAA,GAAU,aAAA,GAAA,IAAI,IAAgB;QAQtC,IAAA,CAAK,IAAA,mNAAO,qBAAA,CAAmB,IAAG,IAAI;QAEtC,MAAM,QAAQ,IAAA,CAAK,IAAA,CAAK;QACxB,MAAM,+MAAWgB,kBAAAA,EAAgB,KAAK;QAGtCD,kNAAAA,EAAY,IAAA,EAAM,SAAS,MAAA,CAAO,KAAK,CAAC;IAC1C;IAEA,QAAQ,GAAA,EAAc;QACpB,MAAM,QAAQ,IAAA,CAAK,IAAA,CAAK;QACxB,MAAM,WAAW,IAAA,CAAK,GAAA,CAAI;QAC1B,IAAI,EAACG,yNAAAA,EAAQ,OAAO,QAAQ,GAAG;gNAC7BJ,cAAAA,EAAY,IAAI,EAAG,QAAA,CAAS,KAAK;YACjC,IAAA,CAAK,SAAA,CAAU,OAAO,IAAA,CAAK,IAAI;QACjC;QAEA,IAAI,CAAC,IAAA,CAAK,IAAA,IAAQ,UAAU,IAAA,CAAK,OAAO,GAAG;YACzC,WAAW,IAAI;QACjB;IACF;IAEU,OAAO;QACf,MAAM,qNAAwBK,KAAAA,CAAG,GAAA,CAAI,IAAA,CAAK,MAAM,IAC5C,IAAA,CAAK,MAAA,CAAO,GAAA,6MAAIC,gBAAa,oNAC5BC,UAAAA,kNAAQD,gBAAAA,EAAc,IAAA,CAAK,MAAM,CAAC;QAEvC,OAAO,IAAA,CAAK,IAAA,CAAK,GAAG,MAAM;IAC5B;IAEU,SAAS;QACjB,IAAI,IAAA,CAAK,IAAA,IAAQ,CAAC,UAAU,IAAA,CAAK,OAAO,GAAG;YACzC,IAAA,CAAK,IAAA,GAAO;4NAEZE,OAAAA,sMAAKL,aAAAA,EAAW,IAAI,GAAI,CAAA,SAAQ;gBAC9B,KAAK,IAAA,GAAO;YACd,CAAC;YAED,gNAAIM,UAAAA,CAAE,aAAA,EAAe;wMACnBC,MAAAA,CAAI,cAAA,CAAe,IAAM,IAAA,CAAK,OAAA,CAAQ,CAAC;gBACvC,WAAW,IAAI;YACjB,OAAO;4NACLC,YAAAA,CAAU,KAAA,CAAM,IAAI;YACtB;QACF;IACF;IAAA,gDAAA;IAGU,UAAU;QAClB,IAAI,WAAW;QACfH,uNAAAA,kNAAKD,UAAAA,EAAQ,IAAA,CAAK,MAAM,GAAG,CAAA,WAAU;YACnC,oNAAIK,gBAAAA,EAAc,MAAM,GAAG;+NACzBC,oBAAAA,EAAiB,QAAQ,IAAI;YAC/B;YACA,IAAI,aAAa,MAAM,GAAG;gBACxB,IAAI,CAAC,OAAO,IAAA,EAAM;oBAChB,IAAA,CAAK,OAAA,CAAQ,GAAA,CAAI,MAAM;gBACzB;gBACA,WAAW,KAAK,GAAA,CAAI,UAAU,OAAO,QAAA,GAAW,CAAC;YACnD;QACF,CAAC;QACD,IAAA,CAAK,QAAA,GAAW;QAChB,IAAA,CAAK,MAAA,CAAO;IACd;IAAA,wDAAA;IAGU,UAAU;QAClBL,uNAAAA,kNAAKD,UAAAA,EAAQ,IAAA,CAAK,MAAM,GAAG,CAAA,WAAU;YACnC,oNAAIK,gBAAAA,EAAc,MAAM,GAAG;gBACzBE,sOAAAA,EAAoB,QAAQ,IAAI;YAClC;QACF,CAAC;QACD,IAAA,CAAK,OAAA,CAAQ,KAAA,CAAM;QACnB,WAAW,IAAI;IACjB;IAAA,cAAA,GAGA,cAAc,KAAA,EAAyB;QAGrC,IAAI,MAAM,IAAA,IAAQ,UAAU;YAC1B,IAAI,MAAM,IAAA,EAAM;gBACd,IAAA,CAAK,OAAA,CAAQ;YACf,OAAO;gBACL,IAAA,CAAK,OAAA,CAAQ,GAAA,CAAI,MAAM,MAAM;gBAC7B,IAAA,CAAK,MAAA,CAAO;YACd;QACF,OAAA,IAGS,MAAM,IAAA,IAAQ,QAAQ;YAC7B,IAAA,CAAK,OAAA,CAAQ,MAAA,CAAO,MAAM,MAAM;QAClC,OAAA,IAGS,MAAM,IAAA,IAAQ,YAAY;YACjC,IAAA,CAAK,QAAA,GAAWP,0NAAAA,EAAQ,IAAA,CAAK,MAAM,EAAE,MAAA,CACnC,CAAC,SAAiB,SAChB,KAAK,GAAA,CAAI,SAAA,CAAU,aAAa,MAAM,IAAI,OAAO,QAAA,GAAW,CAAA,IAAK,CAAC,GACpE;QAEJ;IACF;AACF;AAGA,SAAS,OAAO,MAAA,EAAa;IAC3B,OAAO,OAAO,IAAA,KAAS;AACzB;AAGA,SAAS,UAAU,MAAA,EAAyB;IAG1C,OAAO,CAAC,OAAO,IAAA,IAAQ,MAAM,IAAA,CAAK,MAAM,EAAE,KAAA,CAAM,MAAM;AACxD;AAGA,SAAS,WAAW,IAAA,EAAqB;IACvC,IAAI,CAAC,KAAK,IAAA,EAAM;QACd,KAAK,IAAA,GAAO;wNAEZC,OAAAA,sMAAKL,aAAAA,EAAW,IAAI,GAAI,CAAA,SAAQ;YAC9B,KAAK,IAAA,GAAO;QACd,CAAC;wNAEDY,qBAAAA,EAAmB,MAAM;YACvB,MAAM;YACN,QAAQ;QACV,CAAC;IACH;AACF;;AD/KO,IAAM,KAAmB,CAAC,QAAA,GAAgB,OAC/C,IAAI,cAAc,QAAQ,IAAI;AAGzB,IAAM,cAA4B,CAAC,QAAA,GAAgB,OAAA,iNACxDC,uBAAAA,CAAqB,IAAG,IAAI,cAAc,QAAQ,IAAI,CAAA;;4METxD,UAAA,CAAQ,MAAA,CAAO;0OACb,2BAAA;IACA,IAAI,CAAC,QAAQ,OAAS,IAAI,cAAc,QAAQ,IAAI;AACtD,CAAC;AAKM,IAAM,qNAASE,YAAAA,CAAU,OAAA", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29], "debugId": null}}]}