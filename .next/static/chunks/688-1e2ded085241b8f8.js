(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[688],{39974:function(e){e.exports={style:{fontFamily:"'__Inter_e8ce0c', '__Inter_Fallback_e8ce0c'",fontStyle:"normal"},className:"__className_e8ce0c"}},41915:function(e,t,n){"use strict";n.d(t,{Dx:function(){return Z},aU:function(){return et},dk:function(){return ee},fC:function(){return J},l_:function(){return $},x8:function(){return en},zt:function(){return Y}});var r=n(2265),i=n(54887),o=n(6741),s=n(98575),a=n(68398),l=n(73966),u=n(15278),c=n(83832),d=n(71599),f=n(66840),h=n(26606),p=n(80886),y=n(61188),m=n(95098),v=n(57437),g="ToastProvider",[w,b,x]=(0,a.B)("Toast"),[C,E]=(0,l.b)("Toast",[x]),[T,R]=C(g),P=e=>{let{__scopeToast:t,label:n="Notification",duration:i=5e3,swipeDirection:o="right",swipeThreshold:s=50,children:a}=e,[l,u]=r.useState(null),[c,d]=r.useState(0),f=r.useRef(!1),h=r.useRef(!1);return n.trim()||console.error("Invalid prop `label` supplied to `".concat(g,"`. Expected non-empty `string`.")),(0,v.jsx)(w.Provider,{scope:t,children:(0,v.jsx)(T,{scope:t,label:n,duration:i,swipeDirection:o,swipeThreshold:s,toastCount:c,viewport:l,onViewportChange:u,onToastAdd:r.useCallback(()=>d(e=>e+1),[]),onToastRemove:r.useCallback(()=>d(e=>e-1),[]),isFocusedToastEscapeKeyDownRef:f,isClosePausedRef:h,children:a})})};P.displayName=g;var O="ToastViewport",S=["F8"],A="toast.viewportPause",F="toast.viewportResume",D=r.forwardRef((e,t)=>{let{__scopeToast:n,hotkey:i=S,label:o="Notifications ({hotkey})",...a}=e,l=R(O,n),c=b(n),d=r.useRef(null),h=r.useRef(null),p=r.useRef(null),y=r.useRef(null),m=(0,s.e)(t,y,l.onViewportChange),g=i.join("+").replace(/Key/g,"").replace(/Digit/g,""),x=l.toastCount>0;r.useEffect(()=>{let e=e=>{var t;0!==i.length&&i.every(t=>e[t]||e.code===t)&&(null===(t=y.current)||void 0===t||t.focus())};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[i]),r.useEffect(()=>{let e=d.current,t=y.current;if(x&&e&&t){let n=()=>{if(!l.isClosePausedRef.current){let e=new CustomEvent(A);t.dispatchEvent(e),l.isClosePausedRef.current=!0}},r=()=>{if(l.isClosePausedRef.current){let e=new CustomEvent(F);t.dispatchEvent(e),l.isClosePausedRef.current=!1}},i=t=>{e.contains(t.relatedTarget)||r()},o=()=>{e.contains(document.activeElement)||r()};return e.addEventListener("focusin",n),e.addEventListener("focusout",i),e.addEventListener("pointermove",n),e.addEventListener("pointerleave",o),window.addEventListener("blur",n),window.addEventListener("focus",r),()=>{e.removeEventListener("focusin",n),e.removeEventListener("focusout",i),e.removeEventListener("pointermove",n),e.removeEventListener("pointerleave",o),window.removeEventListener("blur",n),window.removeEventListener("focus",r)}}},[x,l.isClosePausedRef]);let C=r.useCallback(e=>{let{tabbingDirection:t}=e,n=c().map(e=>{let n=e.ref.current,r=[n,...function(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}(n)];return"forwards"===t?r:r.reverse()});return("forwards"===t?n.reverse():n).flat()},[c]);return r.useEffect(()=>{let e=y.current;if(e){let t=t=>{let n=t.altKey||t.ctrlKey||t.metaKey;if("Tab"===t.key&&!n){var r,i,o;let n=document.activeElement,s=t.shiftKey;if(t.target===e&&s){null===(r=h.current)||void 0===r||r.focus();return}let a=C({tabbingDirection:s?"backwards":"forwards"}),l=a.findIndex(e=>e===n);X(a.slice(l+1))?t.preventDefault():s?null===(i=h.current)||void 0===i||i.focus():null===(o=p.current)||void 0===o||o.focus()}};return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)}},[c,C]),(0,v.jsxs)(u.I0,{ref:d,role:"region","aria-label":o.replace("{hotkey}",g),tabIndex:-1,style:{pointerEvents:x?void 0:"none"},children:[x&&(0,v.jsx)(j,{ref:h,onFocusFromOutsideViewport:()=>{X(C({tabbingDirection:"forwards"}))}}),(0,v.jsx)(w.Slot,{scope:n,children:(0,v.jsx)(f.WV.ol,{tabIndex:-1,...a,ref:m})}),x&&(0,v.jsx)(j,{ref:p,onFocusFromOutsideViewport:()=>{X(C({tabbingDirection:"backwards"}))}})]})});D.displayName=O;var M="ToastFocusProxy",j=r.forwardRef((e,t)=>{let{__scopeToast:n,onFocusFromOutsideViewport:r,...i}=e,o=R(M,n);return(0,v.jsx)(m.TX,{"aria-hidden":!0,tabIndex:0,...i,ref:t,style:{position:"fixed"},onFocus:e=>{var t;let n=e.relatedTarget;(null===(t=o.viewport)||void 0===t?void 0:t.contains(n))||r()}})});j.displayName=M;var L="Toast",k=r.forwardRef((e,t)=>{let{forceMount:n,open:r,defaultOpen:i,onOpenChange:s,...a}=e,[l,u]=(0,p.T)({prop:r,defaultProp:null==i||i,onChange:s,caller:L});return(0,v.jsx)(d.z,{present:n||l,children:(0,v.jsx)(I,{open:l,...a,ref:t,onClose:()=>u(!1),onPause:(0,h.W)(e.onPause),onResume:(0,h.W)(e.onResume),onSwipeStart:(0,o.M)(e.onSwipeStart,e=>{e.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:(0,o.M)(e.onSwipeMove,e=>{let{x:t,y:n}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y","".concat(n,"px"))}),onSwipeCancel:(0,o.M)(e.onSwipeCancel,e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:(0,o.M)(e.onSwipeEnd,e=>{let{x:t,y:n}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y","".concat(n,"px")),u(!1)})})})});k.displayName=L;var[q,N]=C(L,{onClose(){}}),I=r.forwardRef((e,t)=>{let{__scopeToast:n,type:a="foreground",duration:l,open:c,onClose:d,onEscapeKeyDown:p,onPause:y,onResume:m,onSwipeStart:g,onSwipeMove:b,onSwipeCancel:x,onSwipeEnd:C,...E}=e,T=R(L,n),[P,O]=r.useState(null),S=(0,s.e)(t,e=>O(e)),D=r.useRef(null),M=r.useRef(null),j=l||T.duration,k=r.useRef(0),N=r.useRef(j),I=r.useRef(0),{onToastAdd:Q,onToastRemove:_}=T,W=(0,h.W)(()=>{var e;(null==P?void 0:P.contains(document.activeElement))&&(null===(e=T.viewport)||void 0===e||e.focus()),d()}),K=r.useCallback(e=>{e&&e!==1/0&&(window.clearTimeout(I.current),k.current=new Date().getTime(),I.current=window.setTimeout(W,e))},[W]);r.useEffect(()=>{let e=T.viewport;if(e){let t=()=>{K(N.current),null==m||m()},n=()=>{let e=new Date().getTime()-k.current;N.current=N.current-e,window.clearTimeout(I.current),null==y||y()};return e.addEventListener(A,n),e.addEventListener(F,t),()=>{e.removeEventListener(A,n),e.removeEventListener(F,t)}}},[T.viewport,j,y,m,K]),r.useEffect(()=>{c&&!T.isClosePausedRef.current&&K(j)},[c,j,T.isClosePausedRef,K]),r.useEffect(()=>(Q(),()=>_()),[Q,_]);let U=r.useMemo(()=>P?function e(t){let n=[];return Array.from(t.childNodes).forEach(t=>{if(t.nodeType===t.TEXT_NODE&&t.textContent&&n.push(t.textContent),t.nodeType===t.ELEMENT_NODE){let r=t.ariaHidden||t.hidden||"none"===t.style.display,i=""===t.dataset.radixToastAnnounceExclude;if(!r){if(i){let e=t.dataset.radixToastAnnounceAlt;e&&n.push(e)}else n.push(...e(t))}}}),n}(P):null,[P]);return T.viewport?(0,v.jsxs)(v.Fragment,{children:[U&&(0,v.jsx)(H,{__scopeToast:n,role:"status","aria-live":"foreground"===a?"assertive":"polite","aria-atomic":!0,children:U}),(0,v.jsx)(q,{scope:n,onClose:W,children:i.createPortal((0,v.jsx)(w.ItemSlot,{scope:n,children:(0,v.jsx)(u.fC,{asChild:!0,onEscapeKeyDown:(0,o.M)(p,()=>{T.isFocusedToastEscapeKeyDownRef.current||W(),T.isFocusedToastEscapeKeyDownRef.current=!1}),children:(0,v.jsx)(f.WV.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":c?"open":"closed","data-swipe-direction":T.swipeDirection,...E,ref:S,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:(0,o.M)(e.onKeyDown,e=>{"Escape"!==e.key||(null==p||p(e.nativeEvent),e.nativeEvent.defaultPrevented||(T.isFocusedToastEscapeKeyDownRef.current=!0,W()))}),onPointerDown:(0,o.M)(e.onPointerDown,e=>{0===e.button&&(D.current={x:e.clientX,y:e.clientY})}),onPointerMove:(0,o.M)(e.onPointerMove,e=>{if(!D.current)return;let t=e.clientX-D.current.x,n=e.clientY-D.current.y,r=!!M.current,i=["left","right"].includes(T.swipeDirection),o=["left","up"].includes(T.swipeDirection)?Math.min:Math.max,s=i?o(0,t):0,a=i?0:o(0,n),l="touch"===e.pointerType?10:2,u={x:s,y:a},c={originalEvent:e,delta:u};r?(M.current=u,B("toast.swipeMove",b,c,{discrete:!1})):G(u,T.swipeDirection,l)?(M.current=u,B("toast.swipeStart",g,c,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>l||Math.abs(n)>l)&&(D.current=null)}),onPointerUp:(0,o.M)(e.onPointerUp,e=>{let t=M.current,n=e.target;if(n.hasPointerCapture(e.pointerId)&&n.releasePointerCapture(e.pointerId),M.current=null,D.current=null,t){let n=e.currentTarget,r={originalEvent:e,delta:t};G(t,T.swipeDirection,T.swipeThreshold)?B("toast.swipeEnd",C,r,{discrete:!0}):B("toast.swipeCancel",x,r,{discrete:!0}),n.addEventListener("click",e=>e.preventDefault(),{once:!0})}})})})}),T.viewport)})]}):null}),H=e=>{let{__scopeToast:t,children:n,...i}=e,o=R(L,t),[s,a]=r.useState(!1),[l,u]=r.useState(!1);return function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:()=>{},t=(0,h.W)(e);(0,y.b)(()=>{let e=0,n=0;return e=window.requestAnimationFrame(()=>n=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(n)}},[t])}(()=>a(!0)),r.useEffect(()=>{let e=window.setTimeout(()=>u(!0),1e3);return()=>window.clearTimeout(e)},[]),l?null:(0,v.jsx)(c.h,{asChild:!0,children:(0,v.jsx)(m.TX,{...i,children:s&&(0,v.jsxs)(v.Fragment,{children:[o.label," ",n]})})})},Q=r.forwardRef((e,t)=>{let{__scopeToast:n,...r}=e;return(0,v.jsx)(f.WV.div,{...r,ref:t})});Q.displayName="ToastTitle";var _=r.forwardRef((e,t)=>{let{__scopeToast:n,...r}=e;return(0,v.jsx)(f.WV.div,{...r,ref:t})});_.displayName="ToastDescription";var W="ToastAction",K=r.forwardRef((e,t)=>{let{altText:n,...r}=e;return n.trim()?(0,v.jsx)(z,{altText:n,asChild:!0,children:(0,v.jsx)(V,{...r,ref:t})}):(console.error("Invalid prop `altText` supplied to `".concat(W,"`. Expected non-empty `string`.")),null)});K.displayName=W;var U="ToastClose",V=r.forwardRef((e,t)=>{let{__scopeToast:n,...r}=e,i=N(U,n);return(0,v.jsx)(z,{asChild:!0,children:(0,v.jsx)(f.WV.button,{type:"button",...r,ref:t,onClick:(0,o.M)(e.onClick,i.onClose)})})});V.displayName=U;var z=r.forwardRef((e,t)=>{let{__scopeToast:n,altText:r,...i}=e;return(0,v.jsx)(f.WV.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":r||void 0,...i,ref:t})});function B(e,t,n,r){let{discrete:i}=r,o=n.originalEvent.currentTarget,s=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),i?(0,f.jH)(o,s):o.dispatchEvent(s)}var G=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=Math.abs(e.x),i=Math.abs(e.y),o=r>i;return"left"===t||"right"===t?o&&r>n:!o&&i>n};function X(e){let t=document.activeElement;return e.some(e=>e===t||(e.focus(),document.activeElement!==t))}var Y=P,$=D,J=k,Z=Q,ee=_,et=K,en=V},32696:function(e,t,n){"use strict";n.d(t,{VY:function(){return tg},zt:function(){return ty},fC:function(){return tm},xz:function(){return tv}});var r=n(2265),i=n(6741),o=n(98575),s=n(73966),a=n(15278),l=n(99255);let u=["top","right","bottom","left"],c=Math.min,d=Math.max,f=Math.round,h=Math.floor,p=e=>({x:e,y:e}),y={left:"right",right:"left",bottom:"top",top:"bottom"},m={start:"end",end:"start"};function v(e,t){return"function"==typeof e?e(t):e}function g(e){return e.split("-")[0]}function w(e){return e.split("-")[1]}function b(e){return"x"===e?"y":"x"}function x(e){return"y"===e?"height":"width"}function C(e){return["top","bottom"].includes(g(e))?"y":"x"}function E(e){return e.replace(/start|end/g,e=>m[e])}function T(e){return e.replace(/left|right|bottom|top/g,e=>y[e])}function R(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function P(e){let{x:t,y:n,width:r,height:i}=e;return{width:r,height:i,top:n,left:t,right:t+r,bottom:n+i,x:t,y:n}}function O(e,t,n){let r,{reference:i,floating:o}=e,s=C(t),a=b(C(t)),l=x(a),u=g(t),c="y"===s,d=i.x+i.width/2-o.width/2,f=i.y+i.height/2-o.height/2,h=i[l]/2-o[l]/2;switch(u){case"top":r={x:d,y:i.y-o.height};break;case"bottom":r={x:d,y:i.y+i.height};break;case"right":r={x:i.x+i.width,y:f};break;case"left":r={x:i.x-o.width,y:f};break;default:r={x:i.x,y:i.y}}switch(w(t)){case"start":r[a]-=h*(n&&c?-1:1);break;case"end":r[a]+=h*(n&&c?-1:1)}return r}let S=async(e,t,n)=>{let{placement:r="bottom",strategy:i="absolute",middleware:o=[],platform:s}=n,a=o.filter(Boolean),l=await (null==s.isRTL?void 0:s.isRTL(t)),u=await s.getElementRects({reference:e,floating:t,strategy:i}),{x:c,y:d}=O(u,r,l),f=r,h={},p=0;for(let n=0;n<a.length;n++){let{name:o,fn:y}=a[n],{x:m,y:v,data:g,reset:w}=await y({x:c,y:d,initialPlacement:r,placement:f,strategy:i,middlewareData:h,rects:u,platform:s,elements:{reference:e,floating:t}});c=null!=m?m:c,d=null!=v?v:d,h={...h,[o]:{...h[o],...g}},w&&p<=50&&(p++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(u=!0===w.rects?await s.getElementRects({reference:e,floating:t,strategy:i}):w.rects),{x:c,y:d}=O(u,f,l)),n=-1)}return{x:c,y:d,placement:f,strategy:i,middlewareData:h}};async function A(e,t){var n;void 0===t&&(t={});let{x:r,y:i,platform:o,rects:s,elements:a,strategy:l}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:f=!1,padding:h=0}=v(t,e),p=R(h),y=a[f?"floating"===d?"reference":"floating":d],m=P(await o.getClippingRect({element:null==(n=await (null==o.isElement?void 0:o.isElement(y)))||n?y:y.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(a.floating)),boundary:u,rootBoundary:c,strategy:l})),g="floating"===d?{x:r,y:i,width:s.floating.width,height:s.floating.height}:s.reference,w=await (null==o.getOffsetParent?void 0:o.getOffsetParent(a.floating)),b=await (null==o.isElement?void 0:o.isElement(w))&&await (null==o.getScale?void 0:o.getScale(w))||{x:1,y:1},x=P(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:g,offsetParent:w,strategy:l}):g);return{top:(m.top-x.top+p.top)/b.y,bottom:(x.bottom-m.bottom+p.bottom)/b.y,left:(m.left-x.left+p.left)/b.x,right:(x.right-m.right+p.right)/b.x}}function F(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function D(e){return u.some(t=>e[t]>=0)}async function M(e,t){let{placement:n,platform:r,elements:i}=e,o=await (null==r.isRTL?void 0:r.isRTL(i.floating)),s=g(n),a=w(n),l="y"===C(n),u=["left","top"].includes(s)?-1:1,c=o&&l?-1:1,d=v(t,e),{mainAxis:f,crossAxis:h,alignmentAxis:p}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return a&&"number"==typeof p&&(h="end"===a?-1*p:p),l?{x:h*c,y:f*u}:{x:f*u,y:h*c}}function j(){return"undefined"!=typeof window}function L(e){return N(e)?(e.nodeName||"").toLowerCase():"#document"}function k(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function q(e){var t;return null==(t=(N(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function N(e){return!!j()&&(e instanceof Node||e instanceof k(e).Node)}function I(e){return!!j()&&(e instanceof Element||e instanceof k(e).Element)}function H(e){return!!j()&&(e instanceof HTMLElement||e instanceof k(e).HTMLElement)}function Q(e){return!!j()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof k(e).ShadowRoot)}function _(e){let{overflow:t,overflowX:n,overflowY:r,display:i}=z(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(i)}function W(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function K(e){let t=U(),n=I(e)?z(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function U(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function V(e){return["html","body","#document"].includes(L(e))}function z(e){return k(e).getComputedStyle(e)}function B(e){return I(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function G(e){if("html"===L(e))return e;let t=e.assignedSlot||e.parentNode||Q(e)&&e.host||q(e);return Q(t)?t.host:t}function X(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let i=function e(t){let n=G(t);return V(n)?t.ownerDocument?t.ownerDocument.body:t.body:H(n)&&_(n)?n:e(n)}(e),o=i===(null==(r=e.ownerDocument)?void 0:r.body),s=k(i);if(o){let e=Y(s);return t.concat(s,s.visualViewport||[],_(i)?i:[],e&&n?X(e):[])}return t.concat(i,X(i,[],n))}function Y(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function $(e){let t=z(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,i=H(e),o=i?e.offsetWidth:n,s=i?e.offsetHeight:r,a=f(n)!==o||f(r)!==s;return a&&(n=o,r=s),{width:n,height:r,$:a}}function J(e){return I(e)?e:e.contextElement}function Z(e){let t=J(e);if(!H(t))return p(1);let n=t.getBoundingClientRect(),{width:r,height:i,$:o}=$(t),s=(o?f(n.width):n.width)/r,a=(o?f(n.height):n.height)/i;return s&&Number.isFinite(s)||(s=1),a&&Number.isFinite(a)||(a=1),{x:s,y:a}}let ee=p(0);function et(e){let t=k(e);return U()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:ee}function en(e,t,n,r){var i;void 0===t&&(t=!1),void 0===n&&(n=!1);let o=e.getBoundingClientRect(),s=J(e),a=p(1);t&&(r?I(r)&&(a=Z(r)):a=Z(e));let l=(void 0===(i=n)&&(i=!1),r&&(!i||r===k(s))&&i)?et(s):p(0),u=(o.left+l.x)/a.x,c=(o.top+l.y)/a.y,d=o.width/a.x,f=o.height/a.y;if(s){let e=k(s),t=r&&I(r)?k(r):r,n=e,i=Y(n);for(;i&&r&&t!==n;){let e=Z(i),t=i.getBoundingClientRect(),r=z(i),o=t.left+(i.clientLeft+parseFloat(r.paddingLeft))*e.x,s=t.top+(i.clientTop+parseFloat(r.paddingTop))*e.y;u*=e.x,c*=e.y,d*=e.x,f*=e.y,u+=o,c+=s,i=Y(n=k(i))}}return P({width:d,height:f,x:u,y:c})}function er(e,t){let n=B(e).scrollLeft;return t?t.left+n:en(q(e)).left+n}function ei(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:er(e,r)),y:r.top+t.scrollTop}}function eo(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=k(e),r=q(e),i=n.visualViewport,o=r.clientWidth,s=r.clientHeight,a=0,l=0;if(i){o=i.width,s=i.height;let e=U();(!e||e&&"fixed"===t)&&(a=i.offsetLeft,l=i.offsetTop)}return{width:o,height:s,x:a,y:l}}(e,n);else if("document"===t)r=function(e){let t=q(e),n=B(e),r=e.ownerDocument.body,i=d(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),o=d(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),s=-n.scrollLeft+er(e),a=-n.scrollTop;return"rtl"===z(r).direction&&(s+=d(t.clientWidth,r.clientWidth)-i),{width:i,height:o,x:s,y:a}}(q(e));else if(I(t))r=function(e,t){let n=en(e,!0,"fixed"===t),r=n.top+e.clientTop,i=n.left+e.clientLeft,o=H(e)?Z(e):p(1),s=e.clientWidth*o.x;return{width:s,height:e.clientHeight*o.y,x:i*o.x,y:r*o.y}}(t,n);else{let n=et(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return P(r)}function es(e){return"static"===z(e).position}function ea(e,t){if(!H(e)||"fixed"===z(e).position)return null;if(t)return t(e);let n=e.offsetParent;return q(e)===n&&(n=n.ownerDocument.body),n}function el(e,t){let n=k(e);if(W(e))return n;if(!H(e)){let t=G(e);for(;t&&!V(t);){if(I(t)&&!es(t))return t;t=G(t)}return n}let r=ea(e,t);for(;r&&["table","td","th"].includes(L(r))&&es(r);)r=ea(r,t);return r&&V(r)&&es(r)&&!K(r)?n:r||function(e){let t=G(e);for(;H(t)&&!V(t);){if(K(t))return t;if(W(t))break;t=G(t)}return null}(e)||n}let eu=async function(e){let t=this.getOffsetParent||el,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=H(t),i=q(t),o="fixed"===n,s=en(e,!0,o,t),a={scrollLeft:0,scrollTop:0},l=p(0);if(r||!r&&!o){if(("body"!==L(t)||_(i))&&(a=B(t)),r){let e=en(t,!0,o,t);l.x=e.x+t.clientLeft,l.y=e.y+t.clientTop}else i&&(l.x=er(i))}o&&!r&&i&&(l.x=er(i));let u=!i||r||o?p(0):ei(i,a);return{x:s.left+a.scrollLeft-l.x-u.x,y:s.top+a.scrollTop-l.y-u.y,width:s.width,height:s.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ec={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:i}=e,o="fixed"===i,s=q(r),a=!!t&&W(t.floating);if(r===s||a&&o)return n;let l={scrollLeft:0,scrollTop:0},u=p(1),c=p(0),d=H(r);if((d||!d&&!o)&&(("body"!==L(r)||_(s))&&(l=B(r)),H(r))){let e=en(r);u=Z(r),c.x=e.x+r.clientLeft,c.y=e.y+r.clientTop}let f=!s||d||o?p(0):ei(s,l,!0);return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-l.scrollLeft*u.x+c.x+f.x,y:n.y*u.y-l.scrollTop*u.y+c.y+f.y}},getDocumentElement:q,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:i}=e,o=[..."clippingAncestors"===n?W(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=X(e,[],!1).filter(e=>I(e)&&"body"!==L(e)),i=null,o="fixed"===z(e).position,s=o?G(e):e;for(;I(s)&&!V(s);){let t=z(s),n=K(s);n||"fixed"!==t.position||(i=null),(o?!n&&!i:!n&&"static"===t.position&&!!i&&["absolute","fixed"].includes(i.position)||_(s)&&!n&&function e(t,n){let r=G(t);return!(r===n||!I(r)||V(r))&&("fixed"===z(r).position||e(r,n))}(e,s))?r=r.filter(e=>e!==s):i=t,s=G(s)}return t.set(e,r),r}(t,this._c):[].concat(n),r],s=o[0],a=o.reduce((e,n)=>{let r=eo(t,n,i);return e.top=d(r.top,e.top),e.right=c(r.right,e.right),e.bottom=c(r.bottom,e.bottom),e.left=d(r.left,e.left),e},eo(t,s,i));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}},getOffsetParent:el,getElementRects:eu,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=$(e);return{width:t,height:n}},getScale:Z,isElement:I,isRTL:function(e){return"rtl"===z(e).direction}};function ed(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ef=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:i,rects:o,platform:s,elements:a,middlewareData:l}=t,{element:u,padding:f=0}=v(e,t)||{};if(null==u)return{};let h=R(f),p={x:n,y:r},y=b(C(i)),m=x(y),g=await s.getDimensions(u),E="y"===y,T=E?"clientHeight":"clientWidth",P=o.reference[m]+o.reference[y]-p[y]-o.floating[m],O=p[y]-o.reference[y],S=await (null==s.getOffsetParent?void 0:s.getOffsetParent(u)),A=S?S[T]:0;A&&await (null==s.isElement?void 0:s.isElement(S))||(A=a.floating[T]||o.floating[m]);let F=A/2-g[m]/2-1,D=c(h[E?"top":"left"],F),M=c(h[E?"bottom":"right"],F),j=A-g[m]-M,L=A/2-g[m]/2+(P/2-O/2),k=d(D,c(L,j)),q=!l.arrow&&null!=w(i)&&L!==k&&o.reference[m]/2-(L<D?D:M)-g[m]/2<0,N=q?L<D?L-D:L-j:0;return{[y]:p[y]+N,data:{[y]:k,centerOffset:L-k-N,...q&&{alignmentOffset:N}},reset:q}}}),eh=(e,t,n)=>{let r=new Map,i={platform:ec,...n},o={...i.platform,_c:r};return S(e,t,{...i,platform:o})};var ep=n(54887),ey="undefined"!=typeof document?r.useLayoutEffect:r.useEffect;function em(e,t){let n,r,i;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!em(e[r],t[r]))return!1;return!0}if((n=(i=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,i[r]))return!1;for(r=n;0!=r--;){let n=i[r];if(("_owner"!==n||!e.$$typeof)&&!em(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function ev(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eg(e,t){let n=ev(e);return Math.round(t*n)/n}function ew(e){let t=r.useRef(e);return ey(()=>{t.current=e}),t}let eb=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ef({element:n.current,padding:r}).fn(t):{}:n?ef({element:n,padding:r}).fn(t):{}}}),ex=(e,t)=>{var n;return{...(void 0===(n=e)&&(n=0),{name:"offset",options:n,async fn(e){var t,r;let{x:i,y:o,placement:s,middlewareData:a}=e,l=await M(e,n);return s===(null==(t=a.offset)?void 0:t.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:i+l.x,y:o+l.y,data:{...l,placement:s}}}}),options:[e,t]}},eC=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"shift",options:n,async fn(e){let{x:t,y:r,placement:i}=e,{mainAxis:o=!0,crossAxis:s=!1,limiter:a={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...l}=v(n,e),u={x:t,y:r},f=await A(e,l),h=C(g(i)),p=b(h),y=u[p],m=u[h];if(o){let e="y"===p?"top":"left",t="y"===p?"bottom":"right",n=y+f[e],r=y-f[t];y=d(n,c(y,r))}if(s){let e="y"===h?"top":"left",t="y"===h?"bottom":"right",n=m+f[e],r=m-f[t];m=d(n,c(m,r))}let w=a.fn({...e,[p]:y,[h]:m});return{...w,data:{x:w.x-t,y:w.y-r,enabled:{[p]:o,[h]:s}}}}}),options:[e,t]}},eE=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{options:n,fn(e){let{x:t,y:r,placement:i,rects:o,middlewareData:s}=e,{offset:a=0,mainAxis:l=!0,crossAxis:u=!0}=v(n,e),c={x:t,y:r},d=C(i),f=b(d),h=c[f],p=c[d],y=v(a,e),m="number"==typeof y?{mainAxis:y,crossAxis:0}:{mainAxis:0,crossAxis:0,...y};if(l){let e="y"===f?"height":"width",t=o.reference[f]-o.floating[e]+m.mainAxis,n=o.reference[f]+o.reference[e]-m.mainAxis;h<t?h=t:h>n&&(h=n)}if(u){var w,x;let e="y"===f?"width":"height",t=["top","left"].includes(g(i)),n=o.reference[d]-o.floating[e]+(t&&(null==(w=s.offset)?void 0:w[d])||0)+(t?0:m.crossAxis),r=o.reference[d]+o.reference[e]+(t?0:(null==(x=s.offset)?void 0:x[d])||0)-(t?m.crossAxis:0);p<n?p=n:p>r&&(p=r)}return{[f]:h,[d]:p}}}),options:[e,t]}},eT=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"flip",options:n,async fn(e){var t,r,i,o,s,a;let{placement:l,middlewareData:u,rects:c,initialPlacement:d,platform:f,elements:h}=e,{mainAxis:p=!0,crossAxis:y=!0,fallbackPlacements:m,fallbackStrategy:R="bestFit",fallbackAxisSideDirection:P="none",flipAlignment:O=!0,...S}=v(n,e);if(null!=(t=u.arrow)&&t.alignmentOffset)return{};let F=g(l),D=C(d),M=g(d)===d,j=await (null==f.isRTL?void 0:f.isRTL(h.floating)),L=m||(M||!O?[T(d)]:function(e){let t=T(e);return[E(e),t,E(t)]}(d)),k="none"!==P;!m&&k&&L.push(...function(e,t,n,r){let i=w(e),o=function(e,t,n){let r=["left","right"],i=["right","left"];switch(e){case"top":case"bottom":if(n)return t?i:r;return t?r:i;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(g(e),"start"===n,r);return i&&(o=o.map(e=>e+"-"+i),t&&(o=o.concat(o.map(E)))),o}(d,O,P,j));let q=[d,...L],N=await A(e,S),I=[],H=(null==(r=u.flip)?void 0:r.overflows)||[];if(p&&I.push(N[F]),y){let e=function(e,t,n){void 0===n&&(n=!1);let r=w(e),i=b(C(e)),o=x(i),s="x"===i?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[o]>t.floating[o]&&(s=T(s)),[s,T(s)]}(l,c,j);I.push(N[e[0]],N[e[1]])}if(H=[...H,{placement:l,overflows:I}],!I.every(e=>e<=0)){let e=((null==(i=u.flip)?void 0:i.index)||0)+1,t=q[e];if(t){let n="alignment"===y&&D!==C(t),r=(null==(s=H[0])?void 0:s.overflows[0])>0;if(!n||r)return{data:{index:e,overflows:H},reset:{placement:t}}}let n=null==(o=H.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:o.placement;if(!n)switch(R){case"bestFit":{let e=null==(a=H.filter(e=>{if(k){let t=C(e.placement);return t===D||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(n=e);break}case"initialPlacement":n=d}if(l!==n)return{reset:{placement:n}}}return{}}}),options:[e,t]}},eR=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"size",options:n,async fn(e){var t,r;let i,o;let{placement:s,rects:a,platform:l,elements:u}=e,{apply:f=()=>{},...h}=v(n,e),p=await A(e,h),y=g(s),m=w(s),b="y"===C(s),{width:x,height:E}=a.floating;"top"===y||"bottom"===y?(i=y,o=m===(await (null==l.isRTL?void 0:l.isRTL(u.floating))?"start":"end")?"left":"right"):(o=y,i="end"===m?"top":"bottom");let T=E-p.top-p.bottom,R=x-p.left-p.right,P=c(E-p[i],T),O=c(x-p[o],R),S=!e.middlewareData.shift,F=P,D=O;if(null!=(t=e.middlewareData.shift)&&t.enabled.x&&(D=R),null!=(r=e.middlewareData.shift)&&r.enabled.y&&(F=T),S&&!m){let e=d(p.left,0),t=d(p.right,0),n=d(p.top,0),r=d(p.bottom,0);b?D=x-2*(0!==e||0!==t?e+t:d(p.left,p.right)):F=E-2*(0!==n||0!==r?n+r:d(p.top,p.bottom))}await f({...e,availableWidth:D,availableHeight:F});let M=await l.getDimensions(u.floating);return x!==M.width||E!==M.height?{reset:{rects:!0}}:{}}}),options:[e,t]}},eP=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"hide",options:n,async fn(e){let{rects:t}=e,{strategy:r="referenceHidden",...i}=v(n,e);switch(r){case"referenceHidden":{let n=F(await A(e,{...i,elementContext:"reference"}),t.reference);return{data:{referenceHiddenOffsets:n,referenceHidden:D(n)}}}case"escaped":{let n=F(await A(e,{...i,altBoundary:!0}),t.floating);return{data:{escapedOffsets:n,escaped:D(n)}}}default:return{}}}}),options:[e,t]}},eO=(e,t)=>({...eb(e),options:[e,t]});var eS=n(66840),eA=n(57437),eF=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:i=5,...o}=e;return(0,eA.jsx)(eS.WV.svg,{...o,ref:t,width:r,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eA.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eF.displayName="Arrow";var eD=n(26606),eM=n(61188),ej="Popper",[eL,ek]=(0,s.b)(ej),[eq,eN]=eL(ej),eI=e=>{let{__scopePopper:t,children:n}=e,[i,o]=r.useState(null);return(0,eA.jsx)(eq,{scope:t,anchor:i,onAnchorChange:o,children:n})};eI.displayName=ej;var eH="PopperAnchor",eQ=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:i,...s}=e,a=eN(eH,n),l=r.useRef(null),u=(0,o.e)(t,l);return r.useEffect(()=>{a.onAnchorChange((null==i?void 0:i.current)||l.current)}),i?null:(0,eA.jsx)(eS.WV.div,{...s,ref:u})});eQ.displayName=eH;var e_="PopperContent",[eW,eK]=eL(e_),eU=r.forwardRef((e,t)=>{var n,i,s,a,l,u,f,p;let{__scopePopper:y,side:m="bottom",sideOffset:v=0,align:g="center",alignOffset:w=0,arrowPadding:b=0,avoidCollisions:x=!0,collisionBoundary:C=[],collisionPadding:E=0,sticky:T="partial",hideWhenDetached:R=!1,updatePositionStrategy:P="optimized",onPlaced:O,...S}=e,A=eN(e_,y),[F,D]=r.useState(null),M=(0,o.e)(t,e=>D(e)),[j,L]=r.useState(null),k=function(e){let[t,n]=r.useState(void 0);return(0,eM.b)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,i;if(!Array.isArray(t)||!t.length)return;let o=t[0];if("borderBoxSize"in o){let e=o.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,i=t.blockSize}else r=e.offsetWidth,i=e.offsetHeight;n({width:r,height:i})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(j),N=null!==(f=null==k?void 0:k.width)&&void 0!==f?f:0,I=null!==(p=null==k?void 0:k.height)&&void 0!==p?p:0,H="number"==typeof E?E:{top:0,right:0,bottom:0,left:0,...E},Q=Array.isArray(C)?C:[C],_=Q.length>0,W={padding:H,boundary:Q.filter(eG),altBoundary:_},{refs:K,floatingStyles:U,placement:V,isPositioned:z,middlewareData:B}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:i=[],platform:o,elements:{reference:s,floating:a}={},transform:l=!0,whileElementsMounted:u,open:c}=e,[d,f]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[h,p]=r.useState(i);em(h,i)||p(i);let[y,m]=r.useState(null),[v,g]=r.useState(null),w=r.useCallback(e=>{e!==E.current&&(E.current=e,m(e))},[]),b=r.useCallback(e=>{e!==T.current&&(T.current=e,g(e))},[]),x=s||y,C=a||v,E=r.useRef(null),T=r.useRef(null),R=r.useRef(d),P=null!=u,O=ew(u),S=ew(o),A=ew(c),F=r.useCallback(()=>{if(!E.current||!T.current)return;let e={placement:t,strategy:n,middleware:h};S.current&&(e.platform=S.current),eh(E.current,T.current,e).then(e=>{let t={...e,isPositioned:!1!==A.current};D.current&&!em(R.current,t)&&(R.current=t,ep.flushSync(()=>{f(t)}))})},[h,t,n,S,A]);ey(()=>{!1===c&&R.current.isPositioned&&(R.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[c]);let D=r.useRef(!1);ey(()=>(D.current=!0,()=>{D.current=!1}),[]),ey(()=>{if(x&&(E.current=x),C&&(T.current=C),x&&C){if(O.current)return O.current(x,C,F);F()}},[x,C,F,O,P]);let M=r.useMemo(()=>({reference:E,floating:T,setReference:w,setFloating:b}),[w,b]),j=r.useMemo(()=>({reference:x,floating:C}),[x,C]),L=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!j.floating)return e;let t=eg(j.floating,d.x),r=eg(j.floating,d.y);return l?{...e,transform:"translate("+t+"px, "+r+"px)",...ev(j.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,l,j.floating,d.x,d.y]);return r.useMemo(()=>({...d,update:F,refs:M,elements:j,floatingStyles:L}),[d,F,M,j,L])}({strategy:"fixed",placement:m+("center"!==g?"-"+g:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let i;void 0===r&&(r={});let{ancestorScroll:o=!0,ancestorResize:s=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:l="function"==typeof IntersectionObserver,animationFrame:u=!1}=r,f=J(e),p=o||s?[...f?X(f):[],...X(t)]:[];p.forEach(e=>{o&&e.addEventListener("scroll",n,{passive:!0}),s&&e.addEventListener("resize",n)});let y=f&&l?function(e,t){let n,r=null,i=q(e);function o(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function s(a,l){void 0===a&&(a=!1),void 0===l&&(l=1),o();let u=e.getBoundingClientRect(),{left:f,top:p,width:y,height:m}=u;if(a||t(),!y||!m)return;let v=h(p),g=h(i.clientWidth-(f+y)),w={rootMargin:-v+"px "+-g+"px "+-h(i.clientHeight-(p+m))+"px "+-h(f)+"px",threshold:d(0,c(1,l))||1},b=!0;function x(t){let r=t[0].intersectionRatio;if(r!==l){if(!b)return s();r?s(!1,r):n=setTimeout(()=>{s(!1,1e-7)},1e3)}1!==r||ed(u,e.getBoundingClientRect())||s(),b=!1}try{r=new IntersectionObserver(x,{...w,root:i.ownerDocument})}catch(e){r=new IntersectionObserver(x,w)}r.observe(e)}(!0),o}(f,n):null,m=-1,v=null;a&&(v=new ResizeObserver(e=>{let[r]=e;r&&r.target===f&&v&&(v.unobserve(t),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{var e;null==(e=v)||e.observe(t)})),n()}),f&&!u&&v.observe(f),v.observe(t));let g=u?en(e):null;return u&&function t(){let r=en(e);g&&!ed(g,r)&&n(),g=r,i=requestAnimationFrame(t)}(),n(),()=>{var e;p.forEach(e=>{o&&e.removeEventListener("scroll",n),s&&e.removeEventListener("resize",n)}),null==y||y(),null==(e=v)||e.disconnect(),v=null,u&&cancelAnimationFrame(i)}}(...t,{animationFrame:"always"===P})},elements:{reference:A.anchor},middleware:[ex({mainAxis:v+I,alignmentAxis:w}),x&&eC({mainAxis:!0,crossAxis:!1,limiter:"partial"===T?eE():void 0,...W}),x&&eT({...W}),eR({...W,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:i}=e,{width:o,height:s}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(i,"px")),a.setProperty("--radix-popper-anchor-width","".concat(o,"px")),a.setProperty("--radix-popper-anchor-height","".concat(s,"px"))}}),j&&eO({element:j,padding:b}),eX({arrowWidth:N,arrowHeight:I}),R&&eP({strategy:"referenceHidden",...W})]}),[G,Y]=eY(V),$=(0,eD.W)(O);(0,eM.b)(()=>{z&&(null==$||$())},[z,$]);let Z=null===(n=B.arrow)||void 0===n?void 0:n.x,ee=null===(i=B.arrow)||void 0===i?void 0:i.y,et=(null===(s=B.arrow)||void 0===s?void 0:s.centerOffset)!==0,[er,ei]=r.useState();return(0,eM.b)(()=>{F&&ei(window.getComputedStyle(F).zIndex)},[F]),(0,eA.jsx)("div",{ref:K.setFloating,"data-radix-popper-content-wrapper":"",style:{...U,transform:z?U.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:er,"--radix-popper-transform-origin":[null===(a=B.transformOrigin)||void 0===a?void 0:a.x,null===(l=B.transformOrigin)||void 0===l?void 0:l.y].join(" "),...(null===(u=B.hide)||void 0===u?void 0:u.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eA.jsx)(eW,{scope:y,placedSide:G,onArrowChange:L,arrowX:Z,arrowY:ee,shouldHideArrow:et,children:(0,eA.jsx)(eS.WV.div,{"data-side":G,"data-align":Y,...S,ref:M,style:{...S.style,animation:z?void 0:"none"}})})})});eU.displayName=e_;var eV="PopperArrow",ez={top:"bottom",right:"left",bottom:"top",left:"right"},eB=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,i=eK(eV,n),o=ez[i.placedSide];return(0,eA.jsx)("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:(0,eA.jsx)(eF,{...r,ref:t,style:{...r.style,display:"block"}})})});function eG(e){return null!==e}eB.displayName=eV;var eX=e=>({name:"transformOrigin",options:e,fn(t){var n,r,i,o,s;let{placement:a,rects:l,middlewareData:u}=t,c=(null===(n=u.arrow)||void 0===n?void 0:n.centerOffset)!==0,d=c?0:e.arrowWidth,f=c?0:e.arrowHeight,[h,p]=eY(a),y={start:"0%",center:"50%",end:"100%"}[p],m=(null!==(o=null===(r=u.arrow)||void 0===r?void 0:r.x)&&void 0!==o?o:0)+d/2,v=(null!==(s=null===(i=u.arrow)||void 0===i?void 0:i.y)&&void 0!==s?s:0)+f/2,g="",w="";return"bottom"===h?(g=c?y:"".concat(m,"px"),w="".concat(-f,"px")):"top"===h?(g=c?y:"".concat(m,"px"),w="".concat(l.floating.height+f,"px")):"right"===h?(g="".concat(-f,"px"),w=c?y:"".concat(v,"px")):"left"===h&&(g="".concat(l.floating.width+f,"px"),w=c?y:"".concat(v,"px")),{data:{x:g,y:w}}}});function eY(e){let[t,n="center"]=e.split("-");return[t,n]}n(83832);var e$=n(71599),eJ=n(37053),eZ=n(80886),e0=n(95098),[e1,e2]=(0,s.b)("Tooltip",[ek]),e5=ek(),e8="TooltipProvider",e6="tooltip.open",[e3,e7]=e1(e8),e9=e=>{let{__scopeTooltip:t,delayDuration:n=700,skipDelayDuration:i=300,disableHoverableContent:o=!1,children:s}=e,a=r.useRef(!0),l=r.useRef(!1),u=r.useRef(0);return r.useEffect(()=>{let e=u.current;return()=>window.clearTimeout(e)},[]),(0,eA.jsx)(e3,{scope:t,isOpenDelayedRef:a,delayDuration:n,onOpen:r.useCallback(()=>{window.clearTimeout(u.current),a.current=!1},[]),onClose:r.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>a.current=!0,i)},[i]),isPointerInTransitRef:l,onPointerInTransitChange:r.useCallback(e=>{l.current=e},[]),disableHoverableContent:o,children:s})};e9.displayName=e8;var e4="Tooltip",[te,tt]=e1(e4),tn=e=>{let{__scopeTooltip:t,children:n,open:i,defaultOpen:o,onOpenChange:s,disableHoverableContent:a,delayDuration:u}=e,c=e7(e4,e.__scopeTooltip),d=e5(t),[f,h]=r.useState(null),p=(0,l.M)(),y=r.useRef(0),m=null!=a?a:c.disableHoverableContent,v=null!=u?u:c.delayDuration,g=r.useRef(!1),[w,b]=(0,eZ.T)({prop:i,defaultProp:null!=o&&o,onChange:e=>{e?(c.onOpen(),document.dispatchEvent(new CustomEvent(e6))):c.onClose(),null==s||s(e)},caller:e4}),x=r.useMemo(()=>w?g.current?"delayed-open":"instant-open":"closed",[w]),C=r.useCallback(()=>{window.clearTimeout(y.current),y.current=0,g.current=!1,b(!0)},[b]),E=r.useCallback(()=>{window.clearTimeout(y.current),y.current=0,b(!1)},[b]),T=r.useCallback(()=>{window.clearTimeout(y.current),y.current=window.setTimeout(()=>{g.current=!0,b(!0),y.current=0},v)},[v,b]);return r.useEffect(()=>()=>{y.current&&(window.clearTimeout(y.current),y.current=0)},[]),(0,eA.jsx)(eI,{...d,children:(0,eA.jsx)(te,{scope:t,contentId:p,open:w,stateAttribute:x,trigger:f,onTriggerChange:h,onTriggerEnter:r.useCallback(()=>{c.isOpenDelayedRef.current?T():C()},[c.isOpenDelayedRef,T,C]),onTriggerLeave:r.useCallback(()=>{m?E():(window.clearTimeout(y.current),y.current=0)},[E,m]),onOpen:C,onClose:E,disableHoverableContent:m,children:n})})};tn.displayName=e4;var tr="TooltipTrigger",ti=r.forwardRef((e,t)=>{let{__scopeTooltip:n,...s}=e,a=tt(tr,n),l=e7(tr,n),u=e5(n),c=r.useRef(null),d=(0,o.e)(t,c,a.onTriggerChange),f=r.useRef(!1),h=r.useRef(!1),p=r.useCallback(()=>f.current=!1,[]);return r.useEffect(()=>()=>document.removeEventListener("pointerup",p),[p]),(0,eA.jsx)(eQ,{asChild:!0,...u,children:(0,eA.jsx)(eS.WV.button,{"aria-describedby":a.open?a.contentId:void 0,"data-state":a.stateAttribute,...s,ref:d,onPointerMove:(0,i.M)(e.onPointerMove,e=>{"touch"===e.pointerType||h.current||l.isPointerInTransitRef.current||(a.onTriggerEnter(),h.current=!0)}),onPointerLeave:(0,i.M)(e.onPointerLeave,()=>{a.onTriggerLeave(),h.current=!1}),onPointerDown:(0,i.M)(e.onPointerDown,()=>{a.open&&a.onClose(),f.current=!0,document.addEventListener("pointerup",p,{once:!0})}),onFocus:(0,i.M)(e.onFocus,()=>{f.current||a.onOpen()}),onBlur:(0,i.M)(e.onBlur,a.onClose),onClick:(0,i.M)(e.onClick,a.onClose)})})});ti.displayName=tr;var[to,ts]=e1("TooltipPortal",{forceMount:void 0}),ta="TooltipContent",tl=r.forwardRef((e,t)=>{let n=ts(ta,e.__scopeTooltip),{forceMount:r=n.forceMount,side:i="top",...o}=e,s=tt(ta,e.__scopeTooltip);return(0,eA.jsx)(e$.z,{present:r||s.open,children:s.disableHoverableContent?(0,eA.jsx)(th,{side:i,...o,ref:t}):(0,eA.jsx)(tu,{side:i,...o,ref:t})})}),tu=r.forwardRef((e,t)=>{let n=tt(ta,e.__scopeTooltip),i=e7(ta,e.__scopeTooltip),s=r.useRef(null),a=(0,o.e)(t,s),[l,u]=r.useState(null),{trigger:c,onClose:d}=n,f=s.current,{onPointerInTransitChange:h}=i,p=r.useCallback(()=>{u(null),h(!1)},[h]),y=r.useCallback((e,t)=>{let n=e.currentTarget,r={x:e.clientX,y:e.clientY},i=function(e,t){let n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),i=Math.abs(t.right-e.x),o=Math.abs(t.left-e.x);switch(Math.min(n,r,i,o)){case o:return"left";case i:return"right";case n:return"top";case r:return"bottom";default:throw Error("unreachable")}}(r,n.getBoundingClientRect());u(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:e.y>t.y?1:0),function(e){if(e.length<=1)return e.slice();let t=[];for(let n=0;n<e.length;n++){let r=e[n];for(;t.length>=2;){let e=t[t.length-1],n=t[t.length-2];if((e.x-n.x)*(r.y-n.y)>=(e.y-n.y)*(r.x-n.x))t.pop();else break}t.push(r)}t.pop();let n=[];for(let t=e.length-1;t>=0;t--){let r=e[t];for(;n.length>=2;){let e=n[n.length-1],t=n[n.length-2];if((e.x-t.x)*(r.y-t.y)>=(e.y-t.y)*(r.x-t.x))n.pop();else break}n.push(r)}return(n.pop(),1===t.length&&1===n.length&&t[0].x===n[0].x&&t[0].y===n[0].y)?t:t.concat(n)}(t)}([...function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n})}return r}(r,i),...function(e){let{top:t,right:n,bottom:r,left:i}=e;return[{x:i,y:t},{x:n,y:t},{x:n,y:r},{x:i,y:r}]}(t.getBoundingClientRect())])),h(!0)},[h]);return r.useEffect(()=>()=>p(),[p]),r.useEffect(()=>{if(c&&f){let e=e=>y(e,f),t=e=>y(e,c);return c.addEventListener("pointerleave",e),f.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),f.removeEventListener("pointerleave",t)}}},[c,f,y,p]),r.useEffect(()=>{if(l){let e=e=>{let t=e.target,n={x:e.clientX,y:e.clientY},r=(null==c?void 0:c.contains(t))||(null==f?void 0:f.contains(t)),i=!function(e,t){let{x:n,y:r}=e,i=!1;for(let e=0,o=t.length-1;e<t.length;o=e++){let s=t[e],a=t[o],l=s.x,u=s.y,c=a.x,d=a.y;u>r!=d>r&&n<(c-l)*(r-u)/(d-u)+l&&(i=!i)}return i}(n,l);r?p():i&&(p(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,f,l,d,p]),(0,eA.jsx)(th,{...e,ref:a})}),[tc,td]=e1(e4,{isInside:!1}),tf=(0,eJ.sA)("TooltipContent"),th=r.forwardRef((e,t)=>{let{__scopeTooltip:n,children:i,"aria-label":o,onEscapeKeyDown:s,onPointerDownOutside:l,...u}=e,c=tt(ta,n),d=e5(n),{onClose:f}=c;return r.useEffect(()=>(document.addEventListener(e6,f),()=>document.removeEventListener(e6,f)),[f]),r.useEffect(()=>{if(c.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(c.trigger))&&f()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[c.trigger,f]),(0,eA.jsx)(a.XB,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:s,onPointerDownOutside:l,onFocusOutside:e=>e.preventDefault(),onDismiss:f,children:(0,eA.jsxs)(eU,{"data-state":c.stateAttribute,...d,...u,ref:t,style:{...u.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,eA.jsx)(tf,{children:i}),(0,eA.jsx)(tc,{scope:n,isInside:!0,children:(0,eA.jsx)(e0.fC,{id:c.contentId,role:"tooltip",children:o||i})})]})})});tl.displayName=ta;var tp="TooltipArrow";r.forwardRef((e,t)=>{let{__scopeTooltip:n,...r}=e,i=e5(n);return td(tp,n).isInside?null:(0,eA.jsx)(eB,{...i,...r,ref:t})}).displayName=tp;var ty=e9,tm=tn,tv=ti,tg=tl},95098:function(e,t,n){"use strict";n.d(t,{TX:function(){return a},fC:function(){return l}});var r=n(2265),i=n(66840),o=n(57437),s=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),a=r.forwardRef((e,t)=>(0,o.jsx)(i.WV.span,{...e,ref:t,style:{...s,...e.style}}));a.displayName="VisuallyHidden";var l=a},31800:function(e,t,n){"use strict";n.d(t,{S:function(){return q}});var r="undefined"==typeof window||"Deno"in globalThis;function i(){}function o(e,t){return"function"==typeof e?e(t):e}function s(e,t){let{type:n="all",exact:r,fetchStatus:i,predicate:o,queryKey:s,stale:a}=e;if(s){if(r){if(t.queryHash!==l(s,t.options))return!1}else if(!c(t.queryKey,s))return!1}if("all"!==n){let e=t.isActive();if("active"===n&&!e||"inactive"===n&&e)return!1}return("boolean"!=typeof a||t.isStale()===a)&&(!i||i===t.state.fetchStatus)&&(!o||!!o(t))}function a(e,t){let{exact:n,status:r,predicate:i,mutationKey:o}=e;if(o){if(!t.options.mutationKey)return!1;if(n){if(u(t.options.mutationKey)!==u(o))return!1}else if(!c(t.options.mutationKey,o))return!1}return(!r||t.state.status===r)&&(!i||!!i(t))}function l(e,t){return(t?.queryKeyHashFn||u)(e)}function u(e){return JSON.stringify(e,(e,t)=>f(t)?Object.keys(t).sort().reduce((e,n)=>(e[n]=t[n],e),{}):t)}function c(e,t){return e===t||typeof e==typeof t&&!!e&&!!t&&"object"==typeof e&&"object"==typeof t&&Object.keys(t).every(n=>c(e[n],t[n]))}function d(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function f(e){if(!h(e))return!1;let t=e.constructor;if(void 0===t)return!0;let n=t.prototype;return!!(h(n)&&n.hasOwnProperty("isPrototypeOf"))&&Object.getPrototypeOf(e)===Object.prototype}function h(e){return"[object Object]"===Object.prototype.toString.call(e)}function p(e,t,n=0){let r=[...e,t];return n&&r.length>n?r.slice(1):r}function y(e,t,n=0){let r=[t,...e];return n&&r.length>n?r.slice(0,-1):r}var m=Symbol();function v(e,t){return!e.queryFn&&t?.initialPromise?()=>t.initialPromise:e.queryFn&&e.queryFn!==m?e.queryFn:()=>Promise.reject(Error(`Missing queryFn: '${e.queryHash}'`))}var g=e=>setTimeout(e,0),w=function(){let e=[],t=0,n=e=>{e()},r=e=>{e()},i=g,o=r=>{t?e.push(r):i(()=>{n(r)})},s=()=>{let t=e;e=[],t.length&&i(()=>{r(()=>{t.forEach(e=>{n(e)})})})};return{batch:e=>{let n;t++;try{n=e()}finally{--t||s()}return n},batchCalls:e=>(...t)=>{o(()=>{e(...t)})},schedule:o,setNotifyFunction:e=>{n=e},setBatchNotifyFunction:e=>{r=e},setScheduler:e=>{i=e}}}(),b=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},x=new class extends b{#e;#t;#n;constructor(){super(),this.#n=e=>{if(!r&&window.addEventListener){let t=()=>e();return window.addEventListener("visibilitychange",t,!1),()=>{window.removeEventListener("visibilitychange",t)}}}}onSubscribe(){this.#t||this.setEventListener(this.#n)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#n=e,this.#t?.(),this.#t=e(e=>{"boolean"==typeof e?this.setFocused(e):this.onFocus()})}setFocused(e){this.#e!==e&&(this.#e=e,this.onFocus())}onFocus(){let e=this.isFocused();this.listeners.forEach(t=>{t(e)})}isFocused(){return"boolean"==typeof this.#e?this.#e:globalThis.document?.visibilityState!=="hidden"}},C=new class extends b{#r=!0;#t;#n;constructor(){super(),this.#n=e=>{if(!r&&window.addEventListener){let t=()=>e(!0),n=()=>e(!1);return window.addEventListener("online",t,!1),window.addEventListener("offline",n,!1),()=>{window.removeEventListener("online",t),window.removeEventListener("offline",n)}}}}onSubscribe(){this.#t||this.setEventListener(this.#n)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#n=e,this.#t?.(),this.#t=e(this.setOnline.bind(this))}setOnline(e){this.#r!==e&&(this.#r=e,this.listeners.forEach(t=>{t(e)}))}isOnline(){return this.#r}};function E(e){return Math.min(1e3*2**e,3e4)}function T(e){return(e??"online")!=="online"||C.isOnline()}var R=class extends Error{constructor(e){super("CancelledError"),this.revert=e?.revert,this.silent=e?.silent}};function P(e){return e instanceof R}function O(e){let t,n=!1,i=0,o=!1,s=function(){let e,t;let n=new Promise((n,r)=>{e=n,t=r});function r(e){Object.assign(n,e),delete n.resolve,delete n.reject}return n.status="pending",n.catch(()=>{}),n.resolve=t=>{r({status:"fulfilled",value:t}),e(t)},n.reject=e=>{r({status:"rejected",reason:e}),t(e)},n}(),a=()=>x.isFocused()&&("always"===e.networkMode||C.isOnline())&&e.canRun(),l=()=>T(e.networkMode)&&e.canRun(),u=n=>{o||(o=!0,e.onSuccess?.(n),t?.(),s.resolve(n))},c=n=>{o||(o=!0,e.onError?.(n),t?.(),s.reject(n))},d=()=>new Promise(n=>{t=e=>{(o||a())&&n(e)},e.onPause?.()}).then(()=>{t=void 0,o||e.onContinue?.()}),f=()=>{let t;if(o)return;let s=0===i?e.initialPromise:void 0;try{t=s??e.fn()}catch(e){t=Promise.reject(e)}Promise.resolve(t).then(u).catch(t=>{if(o)return;let s=e.retry??(r?0:3),l=e.retryDelay??E,u="function"==typeof l?l(i,t):l,h=!0===s||"number"==typeof s&&i<s||"function"==typeof s&&s(i,t);if(n||!h){c(t);return}i++,e.onFail?.(i,t),new Promise(e=>{setTimeout(e,u)}).then(()=>a()?void 0:d()).then(()=>{n?c(t):f()})})};return{promise:s,cancel:t=>{o||(c(new R(t)),e.abort?.())},continue:()=>(t?.(),s),cancelRetry:()=>{n=!0},continueRetry:()=>{n=!1},canStart:l,start:()=>(l()?f():d().then(f),s)}}var S=class{#i;destroy(){this.clearGcTimeout()}scheduleGc(){var e;this.clearGcTimeout(),"number"==typeof(e=this.gcTime)&&e>=0&&e!==1/0&&(this.#i=setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(r?1/0:3e5))}clearGcTimeout(){this.#i&&(clearTimeout(this.#i),this.#i=void 0)}},A=class extends S{#o;#s;#a;#l;#u;#c;#d;constructor(e){super(),this.#d=!1,this.#c=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.#l=e.client,this.#a=this.#l.getQueryCache(),this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.#o=function(e){let t="function"==typeof e.initialData?e.initialData():e.initialData,n=void 0!==t,r=n?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:n?r??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:n?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=e.state??this.#o,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#u?.promise}setOptions(e){this.options={...this.#c,...e},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#a.remove(this)}setData(e,t){var n,r;let i=(n=this.state.data,"function"==typeof(r=this.options).structuralSharing?r.structuralSharing(n,e):!1!==r.structuralSharing?function e(t,n){if(t===n)return t;let r=d(t)&&d(n);if(r||f(t)&&f(n)){let i=r?t:Object.keys(t),o=i.length,s=r?n:Object.keys(n),a=s.length,l=r?[]:{},u=0;for(let o=0;o<a;o++){let a=r?o:s[o];(!r&&i.includes(a)||r)&&void 0===t[a]&&void 0===n[a]?(l[a]=void 0,u++):(l[a]=e(t[a],n[a]),l[a]===t[a]&&void 0!==t[a]&&u++)}return o===a&&u===o?t:l}return n}(n,e):e);return this.#f({data:i,type:"success",dataUpdatedAt:t?.updatedAt,manual:t?.manual}),i}setState(e,t){this.#f({type:"setState",state:e,setStateOptions:t})}cancel(e){let t=this.#u?.promise;return this.#u?.cancel(e),t?t.then(i).catch(i):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#o)}isActive(){return this.observers.some(e=>{var t;return!1!==("function"==typeof(t=e.options.enabled)?t(this):t)})}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===m||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return!!this.state.isInvalidated||(this.getObserversCount()>0?this.observers.some(e=>e.getCurrentResult().isStale):void 0===this.state.data)}isStaleByTime(e=0){return this.state.isInvalidated||void 0===this.state.data||!Math.max(this.state.dataUpdatedAt+(e||0)-Date.now(),0)}onFocus(){let e=this.observers.find(e=>e.shouldFetchOnWindowFocus());e?.refetch({cancelRefetch:!1}),this.#u?.continue()}onOnline(){let e=this.observers.find(e=>e.shouldFetchOnReconnect());e?.refetch({cancelRefetch:!1}),this.#u?.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.#a.notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter(t=>t!==e),this.observers.length||(this.#u&&(this.#d?this.#u.cancel({revert:!0}):this.#u.cancelRetry()),this.scheduleGc()),this.#a.notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#f({type:"invalidate"})}fetch(e,t){if("idle"!==this.state.fetchStatus){if(void 0!==this.state.data&&t?.cancelRefetch)this.cancel({silent:!0});else if(this.#u)return this.#u.continueRetry(),this.#u.promise}if(e&&this.setOptions(e),!this.options.queryFn){let e=this.observers.find(e=>e.options.queryFn);e&&this.setOptions(e.options)}let n=new AbortController,r=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(this.#d=!0,n.signal)})},i={fetchOptions:t,options:this.options,queryKey:this.queryKey,client:this.#l,state:this.state,fetchFn:()=>{let e=v(this.options,t),n={client:this.#l,queryKey:this.queryKey,meta:this.meta};return(r(n),this.#d=!1,this.options.persister)?this.options.persister(e,n,this):e(n)}};r(i),this.options.behavior?.onFetch(i,this),this.#s=this.state,("idle"===this.state.fetchStatus||this.state.fetchMeta!==i.fetchOptions?.meta)&&this.#f({type:"fetch",meta:i.fetchOptions?.meta});let o=e=>{P(e)&&e.silent||this.#f({type:"error",error:e}),P(e)||(this.#a.config.onError?.(e,this),this.#a.config.onSettled?.(this.state.data,e,this)),this.scheduleGc()};return this.#u=O({initialPromise:t?.initialPromise,fn:i.fetchFn,abort:n.abort.bind(n),onSuccess:e=>{if(void 0===e){o(Error(`${this.queryHash} data is undefined`));return}try{this.setData(e)}catch(e){o(e);return}this.#a.config.onSuccess?.(e,this),this.#a.config.onSettled?.(e,this.state.error,this),this.scheduleGc()},onError:o,onFail:(e,t)=>{this.#f({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#f({type:"pause"})},onContinue:()=>{this.#f({type:"continue"})},retry:i.options.retry,retryDelay:i.options.retryDelay,networkMode:i.options.networkMode,canRun:()=>!0}),this.#u.start()}#f(e){this.state=(t=>{switch(e.type){case"failed":return{...t,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...t,fetchStatus:"paused"};case"continue":return{...t,fetchStatus:"fetching"};case"fetch":var n;return{...t,...(n=t.data,{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:T(this.options.networkMode)?"fetching":"paused",...void 0===n&&{error:null,status:"pending"}}),fetchMeta:e.meta??null};case"success":return{...t,data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:e.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":let r=e.error;if(P(r)&&r.revert&&this.#s)return{...this.#s,fetchStatus:"idle"};return{...t,error:r,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,fetchFailureReason:r,fetchStatus:"idle",status:"error"};case"invalidate":return{...t,isInvalidated:!0};case"setState":return{...t,...e.state}}})(this.state),w.batch(()=>{this.observers.forEach(e=>{e.onQueryUpdate()}),this.#a.notify({query:this,type:"updated",action:e})})}},F=class extends b{constructor(e={}){super(),this.config=e,this.#h=new Map}#h;build(e,t,n){let r=t.queryKey,i=t.queryHash??l(r,t),o=this.get(i);return o||(o=new A({client:e,queryKey:r,queryHash:i,options:e.defaultQueryOptions(t),state:n,defaultOptions:e.getQueryDefaults(r)}),this.add(o)),o}add(e){this.#h.has(e.queryHash)||(this.#h.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){let t=this.#h.get(e.queryHash);t&&(e.destroy(),t===e&&this.#h.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){w.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}get(e){return this.#h.get(e)}getAll(){return[...this.#h.values()]}find(e){let t={exact:!0,...e};return this.getAll().find(e=>s(t,e))}findAll(e={}){let t=this.getAll();return Object.keys(e).length>0?t.filter(t=>s(e,t)):t}notify(e){w.batch(()=>{this.listeners.forEach(t=>{t(e)})})}onFocus(){w.batch(()=>{this.getAll().forEach(e=>{e.onFocus()})})}onOnline(){w.batch(()=>{this.getAll().forEach(e=>{e.onOnline()})})}},D=class extends S{#p;#y;#u;constructor(e){super(),this.mutationId=e.mutationId,this.#y=e.mutationCache,this.#p=[],this.state=e.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#p.includes(e)||(this.#p.push(e),this.clearGcTimeout(),this.#y.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#p=this.#p.filter(t=>t!==e),this.scheduleGc(),this.#y.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#p.length||("pending"===this.state.status?this.scheduleGc():this.#y.remove(this))}continue(){return this.#u?.continue()??this.execute(this.state.variables)}async execute(e){let t=()=>{this.#f({type:"continue"})};this.#u=O({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(Error("No mutationFn found")),onFail:(e,t)=>{this.#f({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#f({type:"pause"})},onContinue:t,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#y.canRun(this)});let n="pending"===this.state.status,r=!this.#u.canStart();try{if(n)t();else{this.#f({type:"pending",variables:e,isPaused:r}),await this.#y.config.onMutate?.(e,this);let t=await this.options.onMutate?.(e);t!==this.state.context&&this.#f({type:"pending",context:t,variables:e,isPaused:r})}let i=await this.#u.start();return await this.#y.config.onSuccess?.(i,e,this.state.context,this),await this.options.onSuccess?.(i,e,this.state.context),await this.#y.config.onSettled?.(i,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(i,null,e,this.state.context),this.#f({type:"success",data:i}),i}catch(t){try{throw await this.#y.config.onError?.(t,e,this.state.context,this),await this.options.onError?.(t,e,this.state.context),await this.#y.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,t,e,this.state.context),t}finally{this.#f({type:"error",error:t})}}finally{this.#y.runNext(this)}}#f(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),w.batch(()=>{this.#p.forEach(t=>{t.onMutationUpdate(e)}),this.#y.notify({mutation:this,type:"updated",action:e})})}},M=class extends b{constructor(e={}){super(),this.config=e,this.#m=new Set,this.#v=new Map,this.#g=0}#m;#v;#g;build(e,t,n){let r=new D({mutationCache:this,mutationId:++this.#g,options:e.defaultMutationOptions(t),state:n});return this.add(r),r}add(e){this.#m.add(e);let t=j(e);if("string"==typeof t){let n=this.#v.get(t);n?n.push(e):this.#v.set(t,[e])}this.notify({type:"added",mutation:e})}remove(e){if(this.#m.delete(e)){let t=j(e);if("string"==typeof t){let n=this.#v.get(t);if(n){if(n.length>1){let t=n.indexOf(e);-1!==t&&n.splice(t,1)}else n[0]===e&&this.#v.delete(t)}}}this.notify({type:"removed",mutation:e})}canRun(e){let t=j(e);if("string"!=typeof t)return!0;{let n=this.#v.get(t),r=n?.find(e=>"pending"===e.state.status);return!r||r===e}}runNext(e){let t=j(e);if("string"!=typeof t)return Promise.resolve();{let n=this.#v.get(t)?.find(t=>t!==e&&t.state.isPaused);return n?.continue()??Promise.resolve()}}clear(){w.batch(()=>{this.#m.forEach(e=>{this.notify({type:"removed",mutation:e})}),this.#m.clear(),this.#v.clear()})}getAll(){return Array.from(this.#m)}find(e){let t={exact:!0,...e};return this.getAll().find(e=>a(t,e))}findAll(e={}){return this.getAll().filter(t=>a(e,t))}notify(e){w.batch(()=>{this.listeners.forEach(t=>{t(e)})})}resumePausedMutations(){let e=this.getAll().filter(e=>e.state.isPaused);return w.batch(()=>Promise.all(e.map(e=>e.continue().catch(i))))}};function j(e){return e.options.scope?.id}function L(e){return{onFetch:(t,n)=>{let r=t.options,i=t.fetchOptions?.meta?.fetchMore?.direction,o=t.state.data?.pages||[],s=t.state.data?.pageParams||[],a={pages:[],pageParams:[]},l=0,u=async()=>{let n=!1,u=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(t.signal.aborted?n=!0:t.signal.addEventListener("abort",()=>{n=!0}),t.signal)})},c=v(t.options,t.fetchOptions),d=async(e,r,i)=>{if(n)return Promise.reject();if(null==r&&e.pages.length)return Promise.resolve(e);let o={client:t.client,queryKey:t.queryKey,pageParam:r,direction:i?"backward":"forward",meta:t.options.meta};u(o);let s=await c(o),{maxPages:a}=t.options,l=i?y:p;return{pages:l(e.pages,s,a),pageParams:l(e.pageParams,r,a)}};if(i&&o.length){let e="backward"===i,t={pages:o,pageParams:s},n=(e?function(e,{pages:t,pageParams:n}){return t.length>0?e.getPreviousPageParam?.(t[0],t,n[0],n):void 0}:k)(r,t);a=await d(t,n,e)}else{let t=e??o.length;do{let e=0===l?s[0]??r.initialPageParam:k(r,a);if(l>0&&null==e)break;a=await d(a,e),l++}while(l<t)}return a};t.options.persister?t.fetchFn=()=>t.options.persister?.(u,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},n):t.fetchFn=u}}}function k(e,{pages:t,pageParams:n}){let r=t.length-1;return t.length>0?e.getNextPageParam(t[r],t,n[r],n):void 0}var q=class{#w;#y;#c;#b;#x;#C;#E;#T;constructor(e={}){this.#w=e.queryCache||new F,this.#y=e.mutationCache||new M,this.#c=e.defaultOptions||{},this.#b=new Map,this.#x=new Map,this.#C=0}mount(){this.#C++,1===this.#C&&(this.#E=x.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#w.onFocus())}),this.#T=C.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#w.onOnline())}))}unmount(){this.#C--,0===this.#C&&(this.#E?.(),this.#E=void 0,this.#T?.(),this.#T=void 0)}isFetching(e){return this.#w.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#y.findAll({...e,status:"pending"}).length}getQueryData(e){let t=this.defaultQueryOptions({queryKey:e});return this.#w.get(t.queryHash)?.state.data}ensureQueryData(e){let t=this.defaultQueryOptions(e),n=this.#w.build(this,t),r=n.state.data;return void 0===r?this.fetchQuery(e):(e.revalidateIfStale&&n.isStaleByTime(o(t.staleTime,n))&&this.prefetchQuery(t),Promise.resolve(r))}getQueriesData(e){return this.#w.findAll(e).map(({queryKey:e,state:t})=>[e,t.data])}setQueryData(e,t,n){let r=this.defaultQueryOptions({queryKey:e}),i=this.#w.get(r.queryHash),o=i?.state.data,s="function"==typeof t?t(o):t;if(void 0!==s)return this.#w.build(this,r).setData(s,{...n,manual:!0})}setQueriesData(e,t,n){return w.batch(()=>this.#w.findAll(e).map(({queryKey:e})=>[e,this.setQueryData(e,t,n)]))}getQueryState(e){let t=this.defaultQueryOptions({queryKey:e});return this.#w.get(t.queryHash)?.state}removeQueries(e){let t=this.#w;w.batch(()=>{t.findAll(e).forEach(e=>{t.remove(e)})})}resetQueries(e,t){let n=this.#w;return w.batch(()=>(n.findAll(e).forEach(e=>{e.reset()}),this.refetchQueries({type:"active",...e},t)))}cancelQueries(e,t={}){let n={revert:!0,...t};return Promise.all(w.batch(()=>this.#w.findAll(e).map(e=>e.cancel(n)))).then(i).catch(i)}invalidateQueries(e,t={}){return w.batch(()=>(this.#w.findAll(e).forEach(e=>{e.invalidate()}),e?.refetchType==="none")?Promise.resolve():this.refetchQueries({...e,type:e?.refetchType??e?.type??"active"},t))}refetchQueries(e,t={}){let n={...t,cancelRefetch:t.cancelRefetch??!0};return Promise.all(w.batch(()=>this.#w.findAll(e).filter(e=>!e.isDisabled()).map(e=>{let t=e.fetch(void 0,n);return n.throwOnError||(t=t.catch(i)),"paused"===e.state.fetchStatus?Promise.resolve():t}))).then(i)}fetchQuery(e){let t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);let n=this.#w.build(this,t);return n.isStaleByTime(o(t.staleTime,n))?n.fetch(t):Promise.resolve(n.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(i).catch(i)}fetchInfiniteQuery(e){return e.behavior=L(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(i).catch(i)}ensureInfiniteQueryData(e){return e.behavior=L(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return C.isOnline()?this.#y.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#w}getMutationCache(){return this.#y}getDefaultOptions(){return this.#c}setDefaultOptions(e){this.#c=e}setQueryDefaults(e,t){this.#b.set(u(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){let t=[...this.#b.values()],n={};return t.forEach(t=>{c(e,t.queryKey)&&Object.assign(n,t.defaultOptions)}),n}setMutationDefaults(e,t){this.#x.set(u(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){let t=[...this.#x.values()],n={};return t.forEach(t=>{c(e,t.mutationKey)&&Object.assign(n,t.defaultOptions)}),n}defaultQueryOptions(e){if(e._defaulted)return e;let t={...this.#c.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=l(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===m&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#c.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#w.clear(),this.#y.clear()}}},29827:function(e,t,n){"use strict";n.d(t,{aH:function(){return s}});var r=n(2265),i=n(57437),o=r.createContext(void 0),s=e=>{let{client:t,children:n}=e;return r.useEffect(()=>(t.mount(),()=>{t.unmount()}),[t]),(0,i.jsx)(o.Provider,{value:t,children:n})}},90535:function(e,t,n){"use strict";n.d(t,{j:function(){return s}});var r=n(61994);let i=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,o=r.W,s=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return o(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:s,defaultVariants:a}=t,l=Object.keys(s).map(e=>{let t=null==n?void 0:n[e],r=null==a?void 0:a[e];if(null===t)return null;let o=i(t)||i(r);return s[e][o]}),u=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return o(e,l,null==t?void 0:null===(r=t.compoundVariants)||void 0===r?void 0:r.reduce((e,t)=>{let{class:n,className:r,...i}=t;return Object.entries(i).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...a,...u}[t]):({...a,...u})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}},48614:function(e,t,n){"use strict";n.d(t,{M:function(){return g}});var r=n(57437),i=n(2265),o=n(58881),s=n(53576),a=n(11534),l=n(64252),u=n(13537),c=n(45750);class d extends i.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,n=(0,u.R)(e)&&e.offsetWidth||0,r=this.props.sizeRef.current;r.height=t.offsetHeight||0,r.width=t.offsetWidth||0,r.top=t.offsetTop,r.left=t.offsetLeft,r.right=n-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function f(e){let{children:t,isPresent:n,anchorX:o}=e,s=(0,i.useId)(),a=(0,i.useRef)(null),l=(0,i.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:u}=(0,i.useContext)(c._);return(0,i.useInsertionEffect)(()=>{let{width:e,height:t,top:r,left:i,right:c}=l.current;if(n||!a.current||!e||!t)return;a.current.dataset.motionPopId=s;let d=document.createElement("style");return u&&(d.nonce=u),document.head.appendChild(d),d.sheet&&d.sheet.insertRule('\n          [data-motion-pop-id="'.concat(s,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            ").concat("left"===o?"left: ".concat(i):"right: ".concat(c),"px !important;\n            top: ").concat(r,"px !important;\n          }\n        ")),()=>{document.head.contains(d)&&document.head.removeChild(d)}},[n]),(0,r.jsx)(d,{isPresent:n,childRef:a,sizeRef:l,children:i.cloneElement(t,{ref:a})})}let h=e=>{let{children:t,initial:n,isPresent:o,onExitComplete:a,custom:u,presenceAffectsLayout:c,mode:d,anchorX:h}=e,y=(0,s.h)(p),m=(0,i.useId)(),v=!0,g=(0,i.useMemo)(()=>(v=!1,{id:m,initial:n,isPresent:o,custom:u,onExitComplete:e=>{for(let t of(y.set(e,!0),y.values()))if(!t)return;a&&a()},register:e=>(y.set(e,!1),()=>y.delete(e))}),[o,y,a]);return c&&v&&(g={...g}),(0,i.useMemo)(()=>{y.forEach((e,t)=>y.set(t,!1))},[o]),i.useEffect(()=>{o||y.size||!a||a()},[o]),"popLayout"===d&&(t=(0,r.jsx)(f,{isPresent:o,anchorX:h,children:t})),(0,r.jsx)(l.O.Provider,{value:g,children:t})};function p(){return new Map}var y=n(49637);let m=e=>e.key||"";function v(e){let t=[];return i.Children.forEach(e,e=>{(0,i.isValidElement)(e)&&t.push(e)}),t}let g=e=>{let{children:t,custom:n,initial:l=!0,onExitComplete:u,presenceAffectsLayout:c=!0,mode:d="sync",propagate:f=!1,anchorX:p="left"}=e,[g,w]=(0,y.oO)(f),b=(0,i.useMemo)(()=>v(t),[t]),x=f&&!g?[]:b.map(m),C=(0,i.useRef)(!0),E=(0,i.useRef)(b),T=(0,s.h)(()=>new Map),[R,P]=(0,i.useState)(b),[O,S]=(0,i.useState)(b);(0,a.L)(()=>{C.current=!1,E.current=b;for(let e=0;e<O.length;e++){let t=m(O[e]);x.includes(t)?T.delete(t):!0!==T.get(t)&&T.set(t,!1)}},[O,x.length,x.join("-")]);let A=[];if(b!==R){let e=[...b];for(let t=0;t<O.length;t++){let n=O[t],r=m(n);x.includes(r)||(e.splice(t,0,n),A.push(n))}return"wait"===d&&A.length&&(e=A),S(v(e)),P(b),null}let{forceRender:F}=(0,i.useContext)(o.p);return(0,r.jsx)(r.Fragment,{children:O.map(e=>{let t=m(e),i=(!f||!!g)&&(b===O||x.includes(t));return(0,r.jsx)(h,{isPresent:i,initial:(!C.current||!!l)&&void 0,custom:n,presenceAffectsLayout:c,mode:d,onExitComplete:i?void 0:()=>{if(!T.has(t))return;T.set(t,!0);let e=!0;T.forEach(t=>{t||(e=!1)}),e&&(null==F||F(),S(E.current),f&&(null==w||w()),u&&u())},anchorX:p,children:e},t)})})}},25922:function(e,t,n){"use strict";n.d(t,{F:function(){return s}});var r=n(2265),i=r.createContext(void 0),o={setTheme:e=>{},themes:[]},s=()=>{var e;return null!=(e=r.useContext(i))?e:o}}}]);