"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[306],{23306:(e,t,n)=>{n.r(t),n.d(t,{default:()=>c});var o=n(95155),i=n(12115),a=n(10697),r=n(97431),s=n(28099);let l=(e,t)=>({uniforms:{pointTexture:{value:t||new r.TextureLoader().load("/images/particle.png")},time:{value:0},mousePosition:{value:new r.Vector2(0,0)},color:{value:new r.Color(e)}},vertexShader:"\n      uniform float time;\n      uniform vec2 mousePosition;\n      \n      attribute float size;\n      varying vec3 vColor;\n      \n      void main() {\n        // Add subtle variation to color based on position\n        vColor = vec3(".concat(e.replace("#","0x"),") * (1.0 + sin(position.x * 0.5 + time * 0.2) * 0.2);\n        \n        // Apply time-based animation\n        vec4 mvPosition = modelViewMatrix * vec4(position, 1.0);\n        \n        // Apply subtle wave effect\n        float wave = sin(position.x * 1.5 + time) * 0.1 + \n                     sin(position.y * 1.5 + time * 0.8) * 0.1;\n        mvPosition.z += wave;\n        \n        // Mouse interaction\n        float dist = length(mousePosition - vec2(position.xy));\n        float mouseEffect = max(0.0, 1.0 - dist / 5.0);\n        mvPosition.z += mouseEffect * 0.5;\n        \n        gl_Position = projectionMatrix * mvPosition;\n        \n        // Vary size slightly based on position for more natural look\n        float sizeFactor = 1.0 + sin(position.x * 3.0 + position.y * 2.0) * 0.3;\n        gl_PointSize = size * sizeFactor * (300.0 / -mvPosition.z);\n      }\n    "),fragmentShader:"\n      uniform sampler2D pointTexture;\n      varying vec3 vColor;\n      \n      void main() {\n        vec4 texColor = texture2D(pointTexture, gl_PointCoord);\n        gl_FragColor = vec4(vColor, 1.0) * texColor;\n        \n        // Enhance the alpha blending to match the nebula effect\n        if (gl_FragColor.a < 0.05) discard;\n      }\n    "}),c=e=>{let{color:t="#3fb950",count:n=2e3,size:c=.06,mouseInfluence:u=.05}=e,m=(0,i.useRef)(null),{mouse:p,viewport:f}=(0,a.D)(),[d,h]=(0,i.useState)(null),[v,x]=(0,i.useState)(null);(0,i.useEffect)(()=>{new r.TextureLoader().load("/images/particle.png",e=>{e.premultiplyAlpha=!0,x(e)})},[]);let[{cameraX:g,cameraY:y}]=(0,s.zh)(()=>({cameraX:0,cameraY:0,config:{mass:1,tension:100,friction:30}}));(0,i.useEffect)(()=>{let e=new Float32Array(3*n);for(let t=0;t<n;t++){let n=5*Math.random()+.5,o=Math.acos(2*Math.random()-1),i=Math.random()*Math.PI*2;e[3*t]=n*Math.sin(o)*Math.cos(i),e[3*t+1]=n*Math.sin(o)*Math.sin(i),e[3*t+2]=n*Math.cos(o)*.5}h(e)},[n]),(0,a.F)(e=>{m.current&&d&&(m.current.rotation.y+=8e-4,e.camera.position.x=r.MathUtils.lerp(e.camera.position.x,p.x*u,.05),e.camera.position.y=r.MathUtils.lerp(e.camera.position.y,p.y*u,.05),e.camera.lookAt(0,0,0),m.current.material instanceof r.ShaderMaterial&&(m.current.material.uniforms.time.value=e.clock.getElapsedTime(),m.current.material.uniforms.mousePosition.value.set(p.x*f.width/2,p.y*f.height/2)))});let M=l(t,v);return d?(0,o.jsx)("group",{children:(0,o.jsxs)("points",{ref:m,children:[(0,o.jsx)("bufferGeometry",{children:(0,o.jsx)("bufferAttribute",{attach:"attributes-position",count:n,array:d,itemSize:3})}),(0,o.jsx)("shaderMaterial",{attach:"material",args:[M],transparent:!0,depthWrite:!1,blending:r.AdditiveBlending})]})}):null}}}]);