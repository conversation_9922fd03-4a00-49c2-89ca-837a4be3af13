(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@react-three/fiber/dist/events-776716bd.esm.js [app-client] (ecmascript) <export F as useFrame>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useFrame": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$776716bd$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["F"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$776716bd$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-three/fiber/dist/events-776716bd.esm.js [app-client] (ecmascript)");
}}),
"[project]/node_modules/@react-three/fiber/dist/events-776716bd.esm.js [app-client] (ecmascript) <export D as useThree>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useThree": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$776716bd$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["D"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$776716bd$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-three/fiber/dist/events-776716bd.esm.js [app-client] (ecmascript)");
}}),
"[project]/node_modules/@react-spring/rafz/dist/react-spring_rafz.modern.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// src/index.ts
__turbopack_context__.s({
    "__raf": (()=>__raf),
    "raf": (()=>raf)
});
var updateQueue = makeQueue();
var raf = (fn)=>schedule(fn, updateQueue);
var writeQueue = makeQueue();
raf.write = (fn)=>schedule(fn, writeQueue);
var onStartQueue = makeQueue();
raf.onStart = (fn)=>schedule(fn, onStartQueue);
var onFrameQueue = makeQueue();
raf.onFrame = (fn)=>schedule(fn, onFrameQueue);
var onFinishQueue = makeQueue();
raf.onFinish = (fn)=>schedule(fn, onFinishQueue);
var timeouts = [];
raf.setTimeout = (handler, ms)=>{
    const time = raf.now() + ms;
    const cancel = ()=>{
        const i = timeouts.findIndex((t)=>t.cancel == cancel);
        if (~i) timeouts.splice(i, 1);
        pendingCount -= ~i ? 1 : 0;
    };
    const timeout = {
        time,
        handler,
        cancel
    };
    timeouts.splice(findTimeout(time), 0, timeout);
    pendingCount += 1;
    start();
    return timeout;
};
var findTimeout = (time)=>~(~timeouts.findIndex((t)=>t.time > time) || ~timeouts.length);
raf.cancel = (fn)=>{
    onStartQueue.delete(fn);
    onFrameQueue.delete(fn);
    onFinishQueue.delete(fn);
    updateQueue.delete(fn);
    writeQueue.delete(fn);
};
raf.sync = (fn)=>{
    sync = true;
    raf.batchedUpdates(fn);
    sync = false;
};
raf.throttle = (fn)=>{
    let lastArgs;
    function queuedFn() {
        try {
            fn(...lastArgs);
        } finally{
            lastArgs = null;
        }
    }
    function throttled(...args) {
        lastArgs = args;
        raf.onStart(queuedFn);
    }
    throttled.handler = fn;
    throttled.cancel = ()=>{
        onStartQueue.delete(queuedFn);
        lastArgs = null;
    };
    return throttled;
};
var nativeRaf = typeof window != "undefined" ? window.requestAnimationFrame : // eslint-disable-next-line @typescript-eslint/no-empty-function
()=>{};
raf.use = (impl)=>nativeRaf = impl;
raf.now = typeof performance != "undefined" ? ()=>performance.now() : Date.now;
raf.batchedUpdates = (fn)=>fn();
raf.catch = console.error;
raf.frameLoop = "always";
raf.advance = ()=>{
    if (raf.frameLoop !== "demand") {
        console.warn("Cannot call the manual advancement of rafz whilst frameLoop is not set as demand");
    } else {
        update();
    }
};
var ts = -1;
var pendingCount = 0;
var sync = false;
function schedule(fn, queue) {
    if (sync) {
        queue.delete(fn);
        fn(0);
    } else {
        queue.add(fn);
        start();
    }
}
function start() {
    if (ts < 0) {
        ts = 0;
        if (raf.frameLoop !== "demand") {
            nativeRaf(loop);
        }
    }
}
function stop() {
    ts = -1;
}
function loop() {
    if (~ts) {
        nativeRaf(loop);
        raf.batchedUpdates(update);
    }
}
function update() {
    const prevTs = ts;
    ts = raf.now();
    const count = findTimeout(ts);
    if (count) {
        eachSafely(timeouts.splice(0, count), (t)=>t.handler());
        pendingCount -= count;
    }
    if (!pendingCount) {
        stop();
        return;
    }
    onStartQueue.flush();
    updateQueue.flush(prevTs ? Math.min(64, ts - prevTs) : 16.667);
    onFrameQueue.flush();
    writeQueue.flush();
    onFinishQueue.flush();
}
function makeQueue() {
    let next = /* @__PURE__ */ new Set();
    let current = next;
    return {
        add (fn) {
            pendingCount += current == next && !next.has(fn) ? 1 : 0;
            next.add(fn);
        },
        delete (fn) {
            pendingCount -= current == next && next.has(fn) ? 1 : 0;
            return next.delete(fn);
        },
        flush (arg) {
            if (current.size) {
                next = /* @__PURE__ */ new Set();
                pendingCount -= current.size;
                eachSafely(current, (fn)=>fn(arg) && next.add(fn));
                pendingCount += next.size;
                current = next;
            }
        }
    };
}
function eachSafely(values, each) {
    values.forEach((value)=>{
        try {
            each(value);
        } catch (e) {
            raf.catch(e);
        }
    });
}
var __raf = {
    /** The number of pending tasks */ count () {
        return pendingCount;
    },
    /** Whether there's a raf update loop running */ isRunning () {
        return ts >= 0;
    },
    /** Clear internal state. Never call from update loop! */ clear () {
        ts = -1;
        timeouts = [];
        onStartQueue = makeQueue();
        updateQueue = makeQueue();
        onFrameQueue = makeQueue();
        writeQueue = makeQueue();
        onFinishQueue = makeQueue();
        pendingCount = 0;
    }
};
;
 //# sourceMappingURL=react-spring_rafz.modern.mjs.map
}}),
"[project]/node_modules/@react-spring/shared/dist/react-spring_shared.modern.mjs [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "FluidValue": (()=>FluidValue),
    "Globals": (()=>globals_exports),
    "addFluidObserver": (()=>addFluidObserver),
    "callFluidObserver": (()=>callFluidObserver),
    "callFluidObservers": (()=>callFluidObservers),
    "clamp": (()=>clamp),
    "colorToRgba": (()=>colorToRgba),
    "colors": (()=>colors2),
    "createInterpolator": (()=>createInterpolator),
    "createStringInterpolator": (()=>createStringInterpolator2),
    "defineHidden": (()=>defineHidden),
    "deprecateDirectCall": (()=>deprecateDirectCall),
    "deprecateInterpolate": (()=>deprecateInterpolate),
    "each": (()=>each),
    "eachProp": (()=>eachProp),
    "easings": (()=>easings),
    "flush": (()=>flush),
    "flushCalls": (()=>flushCalls),
    "frameLoop": (()=>frameLoop),
    "getFluidObservers": (()=>getFluidObservers),
    "getFluidValue": (()=>getFluidValue),
    "hasFluidValue": (()=>hasFluidValue),
    "hex3": (()=>hex3),
    "hex4": (()=>hex4),
    "hex6": (()=>hex6),
    "hex8": (()=>hex8),
    "hsl": (()=>hsl),
    "hsla": (()=>hsla),
    "is": (()=>is),
    "isAnimatedString": (()=>isAnimatedString),
    "isEqual": (()=>isEqual),
    "isSSR": (()=>isSSR),
    "noop": (()=>noop),
    "onResize": (()=>onResize),
    "onScroll": (()=>onScroll),
    "once": (()=>once),
    "prefix": (()=>prefix),
    "removeFluidObserver": (()=>removeFluidObserver),
    "rgb": (()=>rgb),
    "rgba": (()=>rgba),
    "setFluidGetter": (()=>setFluidGetter),
    "toArray": (()=>toArray),
    "useConstant": (()=>useConstant),
    "useForceUpdate": (()=>useForceUpdate),
    "useIsomorphicLayoutEffect": (()=>useIsomorphicLayoutEffect),
    "useMemoOne": (()=>useMemoOne),
    "useOnce": (()=>useOnce),
    "usePrev": (()=>usePrev),
    "useReducedMotion": (()=>useReducedMotion)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$rafz$2f$dist$2f$react$2d$spring_rafz$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-spring/rafz/dist/react-spring_rafz.modern.mjs [app-client] (ecmascript)");
// src/hooks/useConstant.ts
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __defProp = Object.defineProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
// src/globals.ts
var globals_exports = {};
__export(globals_exports, {
    assign: ()=>assign,
    colors: ()=>colors,
    createStringInterpolator: ()=>createStringInterpolator,
    skipAnimation: ()=>skipAnimation,
    to: ()=>to,
    willAdvance: ()=>willAdvance
});
;
// src/helpers.ts
function noop() {}
var defineHidden = (obj, key, value)=>Object.defineProperty(obj, key, {
        value,
        writable: true,
        configurable: true
    });
var is = {
    arr: Array.isArray,
    obj: (a)=>!!a && a.constructor.name === "Object",
    fun: (a)=>typeof a === "function",
    str: (a)=>typeof a === "string",
    num: (a)=>typeof a === "number",
    und: (a)=>a === void 0
};
function isEqual(a, b) {
    if (is.arr(a)) {
        if (!is.arr(b) || a.length !== b.length) return false;
        for(let i = 0; i < a.length; i++){
            if (a[i] !== b[i]) return false;
        }
        return true;
    }
    return a === b;
}
var each = (obj, fn)=>obj.forEach(fn);
function eachProp(obj, fn, ctx) {
    if (is.arr(obj)) {
        for(let i = 0; i < obj.length; i++){
            fn.call(ctx, obj[i], `${i}`);
        }
        return;
    }
    for(const key in obj){
        if (obj.hasOwnProperty(key)) {
            fn.call(ctx, obj[key], key);
        }
    }
}
var toArray = (a)=>is.und(a) ? [] : is.arr(a) ? a : [
        a
    ];
function flush(queue, iterator) {
    if (queue.size) {
        const items = Array.from(queue);
        queue.clear();
        each(items, iterator);
    }
}
var flushCalls = (queue, ...args)=>flush(queue, (fn)=>fn(...args));
var isSSR = ()=>typeof window === "undefined" || !window.navigator || /ServerSideRendering|^Deno\//.test(window.navigator.userAgent);
// src/globals.ts
var createStringInterpolator;
var to;
var colors = null;
var skipAnimation = false;
var willAdvance = noop;
var assign = (globals)=>{
    if (globals.to) to = globals.to;
    if (globals.now) __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$rafz$2f$dist$2f$react$2d$spring_rafz$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["raf"].now = globals.now;
    if (globals.colors !== void 0) colors = globals.colors;
    if (globals.skipAnimation != null) skipAnimation = globals.skipAnimation;
    if (globals.createStringInterpolator) createStringInterpolator = globals.createStringInterpolator;
    if (globals.requestAnimationFrame) __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$rafz$2f$dist$2f$react$2d$spring_rafz$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["raf"].use(globals.requestAnimationFrame);
    if (globals.batchedUpdates) __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$rafz$2f$dist$2f$react$2d$spring_rafz$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["raf"].batchedUpdates = globals.batchedUpdates;
    if (globals.willAdvance) willAdvance = globals.willAdvance;
    if (globals.frameLoop) __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$rafz$2f$dist$2f$react$2d$spring_rafz$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["raf"].frameLoop = globals.frameLoop;
};
;
var startQueue = /* @__PURE__ */ new Set();
var currentFrame = [];
var prevFrame = [];
var priority = 0;
var frameLoop = {
    get idle () {
        return !startQueue.size && !currentFrame.length;
    },
    /** Advance the given animation on every frame until idle. */ start (animation) {
        if (priority > animation.priority) {
            startQueue.add(animation);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$rafz$2f$dist$2f$react$2d$spring_rafz$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["raf"].onStart(flushStartQueue);
        } else {
            startSafely(animation);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$rafz$2f$dist$2f$react$2d$spring_rafz$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["raf"])(advance);
        }
    },
    /** Advance all animations by the given time. */ advance,
    /** Call this when an animation's priority changes. */ sort (animation) {
        if (priority) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$rafz$2f$dist$2f$react$2d$spring_rafz$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["raf"].onFrame(()=>frameLoop.sort(animation));
        } else {
            const prevIndex = currentFrame.indexOf(animation);
            if (~prevIndex) {
                currentFrame.splice(prevIndex, 1);
                startUnsafely(animation);
            }
        }
    },
    /**
   * Clear all animations. For testing purposes.
   *
   * ☠️ Never call this from within the frameloop.
   */ clear () {
        currentFrame = [];
        startQueue.clear();
    }
};
function flushStartQueue() {
    startQueue.forEach(startSafely);
    startQueue.clear();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$rafz$2f$dist$2f$react$2d$spring_rafz$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["raf"])(advance);
}
function startSafely(animation) {
    if (!currentFrame.includes(animation)) startUnsafely(animation);
}
function startUnsafely(animation) {
    currentFrame.splice(findIndex(currentFrame, (other)=>other.priority > animation.priority), 0, animation);
}
function advance(dt) {
    const nextFrame = prevFrame;
    for(let i = 0; i < currentFrame.length; i++){
        const animation = currentFrame[i];
        priority = animation.priority;
        if (!animation.idle) {
            willAdvance(animation);
            animation.advance(dt);
            if (!animation.idle) {
                nextFrame.push(animation);
            }
        }
    }
    priority = 0;
    prevFrame = currentFrame;
    prevFrame.length = 0;
    currentFrame = nextFrame;
    return currentFrame.length > 0;
}
function findIndex(arr, test) {
    const index = arr.findIndex(test);
    return index < 0 ? arr.length : index;
}
// src/clamp.ts
var clamp = (min, max, v)=>Math.min(Math.max(v, min), max);
// src/colors.ts
var colors2 = {
    transparent: 0,
    aliceblue: 4042850303,
    antiquewhite: 4209760255,
    aqua: 16777215,
    aquamarine: 2147472639,
    azure: 4043309055,
    beige: 4126530815,
    bisque: 4293182719,
    black: 255,
    blanchedalmond: 4293643775,
    blue: 65535,
    blueviolet: 2318131967,
    brown: 2771004159,
    burlywood: 3736635391,
    burntsienna: 3934150143,
    cadetblue: 1604231423,
    chartreuse: 2147418367,
    chocolate: 3530104575,
    coral: 4286533887,
    cornflowerblue: 1687547391,
    cornsilk: 4294499583,
    crimson: 3692313855,
    cyan: 16777215,
    darkblue: 35839,
    darkcyan: 9145343,
    darkgoldenrod: 3095792639,
    darkgray: 2846468607,
    darkgreen: 6553855,
    darkgrey: 2846468607,
    darkkhaki: 3182914559,
    darkmagenta: 2332068863,
    darkolivegreen: 1433087999,
    darkorange: 4287365375,
    darkorchid: 2570243327,
    darkred: 2332033279,
    darksalmon: 3918953215,
    darkseagreen: 2411499519,
    darkslateblue: 1211993087,
    darkslategray: 793726975,
    darkslategrey: 793726975,
    darkturquoise: 13554175,
    darkviolet: 2483082239,
    deeppink: 4279538687,
    deepskyblue: 12582911,
    dimgray: 1768516095,
    dimgrey: 1768516095,
    dodgerblue: 512819199,
    firebrick: 2988581631,
    floralwhite: 4294635775,
    forestgreen: 579543807,
    fuchsia: 4278255615,
    gainsboro: 3705462015,
    ghostwhite: 4177068031,
    gold: 4292280575,
    goldenrod: 3668254975,
    gray: 2155905279,
    green: 8388863,
    greenyellow: 2919182335,
    grey: 2155905279,
    honeydew: 4043305215,
    hotpink: 4285117695,
    indianred: 3445382399,
    indigo: 1258324735,
    ivory: 4294963455,
    khaki: 4041641215,
    lavender: 3873897215,
    lavenderblush: 4293981695,
    lawngreen: 2096890111,
    lemonchiffon: 4294626815,
    lightblue: 2916673279,
    lightcoral: 4034953471,
    lightcyan: 3774873599,
    lightgoldenrodyellow: 4210742015,
    lightgray: 3553874943,
    lightgreen: 2431553791,
    lightgrey: 3553874943,
    lightpink: 4290167295,
    lightsalmon: 4288707327,
    lightseagreen: 548580095,
    lightskyblue: 2278488831,
    lightslategray: 2005441023,
    lightslategrey: 2005441023,
    lightsteelblue: 2965692159,
    lightyellow: 4294959359,
    lime: 16711935,
    limegreen: 852308735,
    linen: 4210091775,
    magenta: 4278255615,
    maroon: 2147483903,
    mediumaquamarine: 1724754687,
    mediumblue: 52735,
    mediumorchid: 3126187007,
    mediumpurple: 2473647103,
    mediumseagreen: 1018393087,
    mediumslateblue: 2070474495,
    mediumspringgreen: 16423679,
    mediumturquoise: 1221709055,
    mediumvioletred: 3340076543,
    midnightblue: 421097727,
    mintcream: 4127193855,
    mistyrose: 4293190143,
    moccasin: 4293178879,
    navajowhite: 4292783615,
    navy: 33023,
    oldlace: 4260751103,
    olive: 2155872511,
    olivedrab: 1804477439,
    orange: 4289003775,
    orangered: 4282712319,
    orchid: 3664828159,
    palegoldenrod: 4008225535,
    palegreen: 2566625535,
    paleturquoise: 2951671551,
    palevioletred: 3681588223,
    papayawhip: 4293907967,
    peachpuff: 4292524543,
    peru: 3448061951,
    pink: 4290825215,
    plum: 3718307327,
    powderblue: 2967529215,
    purple: 2147516671,
    rebeccapurple: 1714657791,
    red: 4278190335,
    rosybrown: 3163525119,
    royalblue: 1097458175,
    saddlebrown: 2336560127,
    salmon: 4202722047,
    sandybrown: 4104413439,
    seagreen: 780883967,
    seashell: 4294307583,
    sienna: 2689740287,
    silver: 3233857791,
    skyblue: 2278484991,
    slateblue: 1784335871,
    slategray: 1887473919,
    slategrey: 1887473919,
    snow: 4294638335,
    springgreen: 16744447,
    steelblue: 1182971135,
    tan: 3535047935,
    teal: 8421631,
    thistle: 3636451583,
    tomato: 4284696575,
    turquoise: 1088475391,
    violet: 4001558271,
    wheat: 4125012991,
    white: 4294967295,
    whitesmoke: 4126537215,
    yellow: 4294902015,
    yellowgreen: 2597139199
};
// src/colorMatchers.ts
var NUMBER = "[-+]?\\d*\\.?\\d+";
var PERCENTAGE = NUMBER + "%";
function call(...parts) {
    return "\\(\\s*(" + parts.join(")\\s*,\\s*(") + ")\\s*\\)";
}
var rgb = new RegExp("rgb" + call(NUMBER, NUMBER, NUMBER));
var rgba = new RegExp("rgba" + call(NUMBER, NUMBER, NUMBER, NUMBER));
var hsl = new RegExp("hsl" + call(NUMBER, PERCENTAGE, PERCENTAGE));
var hsla = new RegExp("hsla" + call(NUMBER, PERCENTAGE, PERCENTAGE, NUMBER));
var hex3 = /^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/;
var hex4 = /^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/;
var hex6 = /^#([0-9a-fA-F]{6})$/;
var hex8 = /^#([0-9a-fA-F]{8})$/;
// src/normalizeColor.ts
function normalizeColor(color) {
    let match;
    if (typeof color === "number") {
        return color >>> 0 === color && color >= 0 && color <= 4294967295 ? color : null;
    }
    if (match = hex6.exec(color)) return parseInt(match[1] + "ff", 16) >>> 0;
    if (colors && colors[color] !== void 0) {
        return colors[color];
    }
    if (match = rgb.exec(color)) {
        return (parse255(match[1]) << 24 | // r
        parse255(match[2]) << 16 | // g
        parse255(match[3]) << 8 | // b
        255) >>> // a
        0;
    }
    if (match = rgba.exec(color)) {
        return (parse255(match[1]) << 24 | // r
        parse255(match[2]) << 16 | // g
        parse255(match[3]) << 8 | // b
        parse1(match[4])) >>> // a
        0;
    }
    if (match = hex3.exec(color)) {
        return parseInt(match[1] + match[1] + // r
        match[2] + match[2] + // g
        match[3] + match[3] + // b
        "ff", // a
        16) >>> 0;
    }
    if (match = hex8.exec(color)) return parseInt(match[1], 16) >>> 0;
    if (match = hex4.exec(color)) {
        return parseInt(match[1] + match[1] + // r
        match[2] + match[2] + // g
        match[3] + match[3] + // b
        match[4] + match[4], // a
        16) >>> 0;
    }
    if (match = hsl.exec(color)) {
        return (hslToRgb(parse360(match[1]), // h
        parsePercentage(match[2]), // s
        parsePercentage(match[3])) | 255) >>> // a
        0;
    }
    if (match = hsla.exec(color)) {
        return (hslToRgb(parse360(match[1]), // h
        parsePercentage(match[2]), // s
        parsePercentage(match[3])) | parse1(match[4])) >>> // a
        0;
    }
    return null;
}
function hue2rgb(p, q, t) {
    if (t < 0) t += 1;
    if (t > 1) t -= 1;
    if (t < 1 / 6) return p + (q - p) * 6 * t;
    if (t < 1 / 2) return q;
    if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;
    return p;
}
function hslToRgb(h, s, l) {
    const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
    const p = 2 * l - q;
    const r = hue2rgb(p, q, h + 1 / 3);
    const g = hue2rgb(p, q, h);
    const b = hue2rgb(p, q, h - 1 / 3);
    return Math.round(r * 255) << 24 | Math.round(g * 255) << 16 | Math.round(b * 255) << 8;
}
function parse255(str) {
    const int = parseInt(str, 10);
    if (int < 0) return 0;
    if (int > 255) return 255;
    return int;
}
function parse360(str) {
    const int = parseFloat(str);
    return (int % 360 + 360) % 360 / 360;
}
function parse1(str) {
    const num = parseFloat(str);
    if (num < 0) return 0;
    if (num > 1) return 255;
    return Math.round(num * 255);
}
function parsePercentage(str) {
    const int = parseFloat(str);
    if (int < 0) return 0;
    if (int > 100) return 1;
    return int / 100;
}
// src/colorToRgba.ts
function colorToRgba(input) {
    let int32Color = normalizeColor(input);
    if (int32Color === null) return input;
    int32Color = int32Color || 0;
    const r = (int32Color & 4278190080) >>> 24;
    const g = (int32Color & 16711680) >>> 16;
    const b = (int32Color & 65280) >>> 8;
    const a = (int32Color & 255) / 255;
    return `rgba(${r}, ${g}, ${b}, ${a})`;
}
// src/createInterpolator.ts
var createInterpolator = (range, output, extrapolate)=>{
    if (is.fun(range)) {
        return range;
    }
    if (is.arr(range)) {
        return createInterpolator({
            range,
            output,
            extrapolate
        });
    }
    if (is.str(range.output[0])) {
        return createStringInterpolator(range);
    }
    const config = range;
    const outputRange = config.output;
    const inputRange = config.range || [
        0,
        1
    ];
    const extrapolateLeft = config.extrapolateLeft || config.extrapolate || "extend";
    const extrapolateRight = config.extrapolateRight || config.extrapolate || "extend";
    const easing = config.easing || ((t)=>t);
    return (input)=>{
        const range2 = findRange(input, inputRange);
        return interpolate(input, inputRange[range2], inputRange[range2 + 1], outputRange[range2], outputRange[range2 + 1], easing, extrapolateLeft, extrapolateRight, config.map);
    };
};
function interpolate(input, inputMin, inputMax, outputMin, outputMax, easing, extrapolateLeft, extrapolateRight, map) {
    let result = map ? map(input) : input;
    if (result < inputMin) {
        if (extrapolateLeft === "identity") return result;
        else if (extrapolateLeft === "clamp") result = inputMin;
    }
    if (result > inputMax) {
        if (extrapolateRight === "identity") return result;
        else if (extrapolateRight === "clamp") result = inputMax;
    }
    if (outputMin === outputMax) return outputMin;
    if (inputMin === inputMax) return input <= inputMin ? outputMin : outputMax;
    if (inputMin === -Infinity) result = -result;
    else if (inputMax === Infinity) result = result - inputMin;
    else result = (result - inputMin) / (inputMax - inputMin);
    result = easing(result);
    if (outputMin === -Infinity) result = -result;
    else if (outputMax === Infinity) result = result + outputMin;
    else result = result * (outputMax - outputMin) + outputMin;
    return result;
}
function findRange(input, inputRange) {
    for(var i = 1; i < inputRange.length - 1; ++i)if (inputRange[i] >= input) break;
    return i - 1;
}
// src/easings.ts
var steps = (steps2, direction = "end")=>(progress2)=>{
        progress2 = direction === "end" ? Math.min(progress2, 0.999) : Math.max(progress2, 1e-3);
        const expanded = progress2 * steps2;
        const rounded = direction === "end" ? Math.floor(expanded) : Math.ceil(expanded);
        return clamp(0, 1, rounded / steps2);
    };
var c1 = 1.70158;
var c2 = c1 * 1.525;
var c3 = c1 + 1;
var c4 = 2 * Math.PI / 3;
var c5 = 2 * Math.PI / 4.5;
var bounceOut = (x)=>{
    const n1 = 7.5625;
    const d1 = 2.75;
    if (x < 1 / d1) {
        return n1 * x * x;
    } else if (x < 2 / d1) {
        return n1 * (x -= 1.5 / d1) * x + 0.75;
    } else if (x < 2.5 / d1) {
        return n1 * (x -= 2.25 / d1) * x + 0.9375;
    } else {
        return n1 * (x -= 2.625 / d1) * x + 0.984375;
    }
};
var easings = {
    linear: (x)=>x,
    easeInQuad: (x)=>x * x,
    easeOutQuad: (x)=>1 - (1 - x) * (1 - x),
    easeInOutQuad: (x)=>x < 0.5 ? 2 * x * x : 1 - Math.pow(-2 * x + 2, 2) / 2,
    easeInCubic: (x)=>x * x * x,
    easeOutCubic: (x)=>1 - Math.pow(1 - x, 3),
    easeInOutCubic: (x)=>x < 0.5 ? 4 * x * x * x : 1 - Math.pow(-2 * x + 2, 3) / 2,
    easeInQuart: (x)=>x * x * x * x,
    easeOutQuart: (x)=>1 - Math.pow(1 - x, 4),
    easeInOutQuart: (x)=>x < 0.5 ? 8 * x * x * x * x : 1 - Math.pow(-2 * x + 2, 4) / 2,
    easeInQuint: (x)=>x * x * x * x * x,
    easeOutQuint: (x)=>1 - Math.pow(1 - x, 5),
    easeInOutQuint: (x)=>x < 0.5 ? 16 * x * x * x * x * x : 1 - Math.pow(-2 * x + 2, 5) / 2,
    easeInSine: (x)=>1 - Math.cos(x * Math.PI / 2),
    easeOutSine: (x)=>Math.sin(x * Math.PI / 2),
    easeInOutSine: (x)=>-(Math.cos(Math.PI * x) - 1) / 2,
    easeInExpo: (x)=>x === 0 ? 0 : Math.pow(2, 10 * x - 10),
    easeOutExpo: (x)=>x === 1 ? 1 : 1 - Math.pow(2, -10 * x),
    easeInOutExpo: (x)=>x === 0 ? 0 : x === 1 ? 1 : x < 0.5 ? Math.pow(2, 20 * x - 10) / 2 : (2 - Math.pow(2, -20 * x + 10)) / 2,
    easeInCirc: (x)=>1 - Math.sqrt(1 - Math.pow(x, 2)),
    easeOutCirc: (x)=>Math.sqrt(1 - Math.pow(x - 1, 2)),
    easeInOutCirc: (x)=>x < 0.5 ? (1 - Math.sqrt(1 - Math.pow(2 * x, 2))) / 2 : (Math.sqrt(1 - Math.pow(-2 * x + 2, 2)) + 1) / 2,
    easeInBack: (x)=>c3 * x * x * x - c1 * x * x,
    easeOutBack: (x)=>1 + c3 * Math.pow(x - 1, 3) + c1 * Math.pow(x - 1, 2),
    easeInOutBack: (x)=>x < 0.5 ? Math.pow(2 * x, 2) * ((c2 + 1) * 2 * x - c2) / 2 : (Math.pow(2 * x - 2, 2) * ((c2 + 1) * (x * 2 - 2) + c2) + 2) / 2,
    easeInElastic: (x)=>x === 0 ? 0 : x === 1 ? 1 : -Math.pow(2, 10 * x - 10) * Math.sin((x * 10 - 10.75) * c4),
    easeOutElastic: (x)=>x === 0 ? 0 : x === 1 ? 1 : Math.pow(2, -10 * x) * Math.sin((x * 10 - 0.75) * c4) + 1,
    easeInOutElastic: (x)=>x === 0 ? 0 : x === 1 ? 1 : x < 0.5 ? -(Math.pow(2, 20 * x - 10) * Math.sin((20 * x - 11.125) * c5)) / 2 : Math.pow(2, -20 * x + 10) * Math.sin((20 * x - 11.125) * c5) / 2 + 1,
    easeInBounce: (x)=>1 - bounceOut(1 - x),
    easeOutBounce: bounceOut,
    easeInOutBounce: (x)=>x < 0.5 ? (1 - bounceOut(1 - 2 * x)) / 2 : (1 + bounceOut(2 * x - 1)) / 2,
    steps
};
// src/fluids.ts
var $get = Symbol.for("FluidValue.get");
var $observers = Symbol.for("FluidValue.observers");
var hasFluidValue = (arg)=>Boolean(arg && arg[$get]);
var getFluidValue = (arg)=>arg && arg[$get] ? arg[$get]() : arg;
var getFluidObservers = (target)=>target[$observers] || null;
function callFluidObserver(observer2, event) {
    if (observer2.eventObserved) {
        observer2.eventObserved(event);
    } else {
        observer2(event);
    }
}
function callFluidObservers(target, event) {
    const observers = target[$observers];
    if (observers) {
        observers.forEach((observer2)=>{
            callFluidObserver(observer2, event);
        });
    }
}
var FluidValue = class {
    constructor(get){
        if (!get && !(get = this.get)) {
            throw Error("Unknown getter");
        }
        setFluidGetter(this, get);
    }
};
$get, $observers;
var setFluidGetter = (target, get)=>setHidden(target, $get, get);
function addFluidObserver(target, observer2) {
    if (target[$get]) {
        let observers = target[$observers];
        if (!observers) {
            setHidden(target, $observers, observers = /* @__PURE__ */ new Set());
        }
        if (!observers.has(observer2)) {
            observers.add(observer2);
            if (target.observerAdded) {
                target.observerAdded(observers.size, observer2);
            }
        }
    }
    return observer2;
}
function removeFluidObserver(target, observer2) {
    const observers = target[$observers];
    if (observers && observers.has(observer2)) {
        const count = observers.size - 1;
        if (count) {
            observers.delete(observer2);
        } else {
            target[$observers] = null;
        }
        if (target.observerRemoved) {
            target.observerRemoved(count, observer2);
        }
    }
}
var setHidden = (target, key, value)=>Object.defineProperty(target, key, {
        value,
        writable: true,
        configurable: true
    });
// src/regexs.ts
var numberRegex = /[+\-]?(?:0|[1-9]\d*)(?:\.\d*)?(?:[eE][+\-]?\d+)?/g;
var colorRegex = /(#(?:[0-9a-f]{2}){2,4}|(#[0-9a-f]{3})|(rgb|hsl)a?\((-?\d+%?[,\s]+){2,3}\s*[\d\.]+%?\))/gi;
var unitRegex = new RegExp(`(${numberRegex.source})(%|[a-z]+)`, "i");
var rgbaRegex = /rgba\(([0-9\.-]+), ([0-9\.-]+), ([0-9\.-]+), ([0-9\.-]+)\)/gi;
var cssVariableRegex = /var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;
// src/variableToRgba.ts
var variableToRgba = (input)=>{
    const [token, fallback] = parseCSSVariable(input);
    if (!token || isSSR()) {
        return input;
    }
    const value = window.getComputedStyle(document.documentElement).getPropertyValue(token);
    if (value) {
        return value.trim();
    } else if (fallback && fallback.startsWith("--")) {
        const value2 = window.getComputedStyle(document.documentElement).getPropertyValue(fallback);
        if (value2) {
            return value2;
        } else {
            return input;
        }
    } else if (fallback && cssVariableRegex.test(fallback)) {
        return variableToRgba(fallback);
    } else if (fallback) {
        return fallback;
    }
    return input;
};
var parseCSSVariable = (current)=>{
    const match = cssVariableRegex.exec(current);
    if (!match) return [
        , 
    ];
    const [, token, fallback] = match;
    return [
        token,
        fallback
    ];
};
// src/stringInterpolation.ts
var namedColorRegex;
var rgbaRound = (_, p1, p2, p3, p4)=>`rgba(${Math.round(p1)}, ${Math.round(p2)}, ${Math.round(p3)}, ${p4})`;
var createStringInterpolator2 = (config)=>{
    if (!namedColorRegex) namedColorRegex = colors ? // match color names, ignore partial matches
    new RegExp(`(${Object.keys(colors).join("|")})(?!\\w)`, "g") : // never match
    /^\b$/;
    const output = config.output.map((value)=>{
        return getFluidValue(value).replace(cssVariableRegex, variableToRgba).replace(colorRegex, colorToRgba).replace(namedColorRegex, colorToRgba);
    });
    const keyframes = output.map((value)=>value.match(numberRegex).map(Number));
    const outputRanges = keyframes[0].map((_, i)=>keyframes.map((values)=>{
            if (!(i in values)) {
                throw Error('The arity of each "output" value must be equal');
            }
            return values[i];
        }));
    const interpolators = outputRanges.map((output2)=>createInterpolator({
            ...config,
            output: output2
        }));
    return (input)=>{
        const missingUnit = !unitRegex.test(output[0]) && output.find((value)=>unitRegex.test(value))?.replace(numberRegex, "");
        let i = 0;
        return output[0].replace(numberRegex, ()=>`${interpolators[i++](input)}${missingUnit || ""}`).replace(rgbaRegex, rgbaRound);
    };
};
// src/deprecations.ts
var prefix = "react-spring: ";
var once = (fn)=>{
    const func = fn;
    let called = false;
    if (typeof func != "function") {
        throw new TypeError(`${prefix}once requires a function parameter`);
    }
    return (...args)=>{
        if (!called) {
            func(...args);
            called = true;
        }
    };
};
var warnInterpolate = once(console.warn);
function deprecateInterpolate() {
    warnInterpolate(`${prefix}The "interpolate" function is deprecated in v9 (use "to" instead)`);
}
var warnDirectCall = once(console.warn);
function deprecateDirectCall() {
    warnDirectCall(`${prefix}Directly calling start instead of using the api object is deprecated in v9 (use ".start" instead), this will be removed in later 0.X.0 versions`);
}
// src/isAnimatedString.ts
function isAnimatedString(value) {
    return is.str(value) && (value[0] == "#" || /\d/.test(value) || // Do not identify a CSS variable as an AnimatedString if its SSR
    !isSSR() && cssVariableRegex.test(value) || value in (colors || {}));
}
;
// src/dom-events/resize/resizeElement.ts
var observer;
var resizeHandlers = /* @__PURE__ */ new WeakMap();
var handleObservation = (entries)=>entries.forEach(({ target, contentRect })=>{
        return resizeHandlers.get(target)?.forEach((handler)=>handler(contentRect));
    });
function resizeElement(handler, target) {
    if (!observer) {
        if (typeof ResizeObserver !== "undefined") {
            observer = new ResizeObserver(handleObservation);
        }
    }
    let elementHandlers = resizeHandlers.get(target);
    if (!elementHandlers) {
        elementHandlers = /* @__PURE__ */ new Set();
        resizeHandlers.set(target, elementHandlers);
    }
    elementHandlers.add(handler);
    if (observer) {
        observer.observe(target);
    }
    return ()=>{
        const elementHandlers2 = resizeHandlers.get(target);
        if (!elementHandlers2) return;
        elementHandlers2.delete(handler);
        if (!elementHandlers2.size && observer) {
            observer.unobserve(target);
        }
    };
}
// src/dom-events/resize/resizeWindow.ts
var listeners = /* @__PURE__ */ new Set();
var cleanupWindowResizeHandler;
var createResizeHandler = ()=>{
    const handleResize = ()=>{
        listeners.forEach((callback)=>callback({
                width: window.innerWidth,
                height: window.innerHeight
            }));
    };
    window.addEventListener("resize", handleResize);
    return ()=>{
        window.removeEventListener("resize", handleResize);
    };
};
var resizeWindow = (callback)=>{
    listeners.add(callback);
    if (!cleanupWindowResizeHandler) {
        cleanupWindowResizeHandler = createResizeHandler();
    }
    return ()=>{
        listeners.delete(callback);
        if (!listeners.size && cleanupWindowResizeHandler) {
            cleanupWindowResizeHandler();
            cleanupWindowResizeHandler = void 0;
        }
    };
};
// src/dom-events/resize/index.ts
var onResize = (callback, { container = document.documentElement } = {})=>{
    if (container === document.documentElement) {
        return resizeWindow(callback);
    } else {
        return resizeElement(callback, container);
    }
};
// src/progress.ts
var progress = (min, max, value)=>max - min === 0 ? 1 : (value - min) / (max - min);
// src/dom-events/scroll/ScrollHandler.ts
var SCROLL_KEYS = {
    x: {
        length: "Width",
        position: "Left"
    },
    y: {
        length: "Height",
        position: "Top"
    }
};
var ScrollHandler = class {
    constructor(callback, container){
        this.createAxis = ()=>({
                current: 0,
                progress: 0,
                scrollLength: 0
            });
        this.updateAxis = (axisName)=>{
            const axis = this.info[axisName];
            const { length, position } = SCROLL_KEYS[axisName];
            axis.current = this.container[`scroll${position}`];
            axis.scrollLength = this.container[`scroll${length}`] - this.container[`client${length}`];
            axis.progress = progress(0, axis.scrollLength, axis.current);
        };
        this.update = ()=>{
            this.updateAxis("x");
            this.updateAxis("y");
        };
        this.sendEvent = ()=>{
            this.callback(this.info);
        };
        this.advance = ()=>{
            this.update();
            this.sendEvent();
        };
        this.callback = callback;
        this.container = container;
        this.info = {
            time: 0,
            x: this.createAxis(),
            y: this.createAxis()
        };
    }
};
// src/dom-events/scroll/index.ts
var scrollListeners = /* @__PURE__ */ new WeakMap();
var resizeListeners = /* @__PURE__ */ new WeakMap();
var onScrollHandlers = /* @__PURE__ */ new WeakMap();
var getTarget = (container)=>container === document.documentElement ? window : container;
var onScroll = (callback, { container = document.documentElement } = {})=>{
    let containerHandlers = onScrollHandlers.get(container);
    if (!containerHandlers) {
        containerHandlers = /* @__PURE__ */ new Set();
        onScrollHandlers.set(container, containerHandlers);
    }
    const containerHandler = new ScrollHandler(callback, container);
    containerHandlers.add(containerHandler);
    if (!scrollListeners.has(container)) {
        const listener = ()=>{
            containerHandlers?.forEach((handler)=>handler.advance());
            return true;
        };
        scrollListeners.set(container, listener);
        const target = getTarget(container);
        window.addEventListener("resize", listener, {
            passive: true
        });
        if (container !== document.documentElement) {
            resizeListeners.set(container, onResize(listener, {
                container
            }));
        }
        target.addEventListener("scroll", listener, {
            passive: true
        });
    }
    const animateScroll = scrollListeners.get(container);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$rafz$2f$dist$2f$react$2d$spring_rafz$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["raf"])(animateScroll);
    return ()=>{
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$rafz$2f$dist$2f$react$2d$spring_rafz$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["raf"].cancel(animateScroll);
        const containerHandlers2 = onScrollHandlers.get(container);
        if (!containerHandlers2) return;
        containerHandlers2.delete(containerHandler);
        if (containerHandlers2.size) return;
        const listener = scrollListeners.get(container);
        scrollListeners.delete(container);
        if (listener) {
            getTarget(container).removeEventListener("scroll", listener);
            window.removeEventListener("resize", listener);
            resizeListeners.get(container)?.();
        }
    };
};
;
function useConstant(init) {
    const ref = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    if (ref.current === null) {
        ref.current = init();
    }
    return ref.current;
}
;
;
;
var useIsomorphicLayoutEffect = isSSR() ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"] : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLayoutEffect"];
// src/hooks/useIsMounted.ts
var useIsMounted = ()=>{
    const isMounted = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(false);
    useIsomorphicLayoutEffect({
        "useIsMounted.useIsomorphicLayoutEffect": ()=>{
            isMounted.current = true;
            return ({
                "useIsMounted.useIsomorphicLayoutEffect": ()=>{
                    isMounted.current = false;
                }
            })["useIsMounted.useIsomorphicLayoutEffect"];
        }
    }["useIsMounted.useIsomorphicLayoutEffect"], []);
    return isMounted;
};
// src/hooks/useForceUpdate.ts
function useForceUpdate() {
    const update = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])()[1];
    const isMounted = useIsMounted();
    return ()=>{
        if (isMounted.current) {
            update(Math.random());
        }
    };
}
;
function useMemoOne(getResult, inputs) {
    const [initial] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        "useMemoOne.useState2": ()=>({
                inputs,
                result: getResult()
            })
    }["useMemoOne.useState2"]);
    const committed = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])();
    const prevCache = committed.current;
    let cache = prevCache;
    if (cache) {
        const useCache = Boolean(inputs && cache.inputs && areInputsEqual(inputs, cache.inputs));
        if (!useCache) {
            cache = {
                inputs,
                result: getResult()
            };
        }
    } else {
        cache = initial;
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useMemoOne.useEffect2": ()=>{
            committed.current = cache;
            if (prevCache == initial) {
                initial.inputs = initial.result = void 0;
            }
        }
    }["useMemoOne.useEffect2"], [
        cache
    ]);
    return cache.result;
}
function areInputsEqual(next, prev) {
    if (next.length !== prev.length) {
        return false;
    }
    for(let i = 0; i < next.length; i++){
        if (next[i] !== prev[i]) {
            return false;
        }
    }
    return true;
}
;
var useOnce = (effect)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(effect, emptyDeps);
var emptyDeps = [];
;
function usePrev(value) {
    const prevRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "usePrev.useEffect4": ()=>{
            prevRef.current = value;
        }
    }["usePrev.useEffect4"]);
    return prevRef.current;
}
;
var useReducedMotion = ()=>{
    const [reducedMotion, setReducedMotion] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    useIsomorphicLayoutEffect({
        "useReducedMotion.useIsomorphicLayoutEffect": ()=>{
            const mql = window.matchMedia("(prefers-reduced-motion)");
            const handleMediaChange = {
                "useReducedMotion.useIsomorphicLayoutEffect.handleMediaChange": (e)=>{
                    setReducedMotion(e.matches);
                    assign({
                        skipAnimation: e.matches
                    });
                }
            }["useReducedMotion.useIsomorphicLayoutEffect.handleMediaChange"];
            handleMediaChange(mql);
            if (mql.addEventListener) {
                mql.addEventListener("change", handleMediaChange);
            } else {
                mql.addListener(handleMediaChange);
            }
            return ({
                "useReducedMotion.useIsomorphicLayoutEffect": ()=>{
                    if (mql.removeEventListener) {
                        mql.removeEventListener("change", handleMediaChange);
                    } else {
                        mql.removeListener(handleMediaChange);
                    }
                }
            })["useReducedMotion.useIsomorphicLayoutEffect"];
        }
    }["useReducedMotion.useIsomorphicLayoutEffect"], []);
    return reducedMotion;
};
;
;
 //# sourceMappingURL=react-spring_shared.modern.mjs.map
}}),
"[project]/node_modules/@react-spring/shared/dist/react-spring_shared.modern.mjs [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$rafz$2f$dist$2f$react$2d$spring_rafz$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-spring/rafz/dist/react-spring_rafz.modern.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@react-spring/shared/dist/react-spring_shared.modern.mjs [app-client] (ecmascript) <locals>");
}}),
"[project]/node_modules/@react-spring/animated/dist/react-spring_animated.modern.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// src/Animated.ts
__turbopack_context__.s({
    "Animated": (()=>Animated),
    "AnimatedArray": (()=>AnimatedArray),
    "AnimatedObject": (()=>AnimatedObject),
    "AnimatedString": (()=>AnimatedString),
    "AnimatedValue": (()=>AnimatedValue),
    "createHost": (()=>createHost),
    "getAnimated": (()=>getAnimated),
    "getAnimatedType": (()=>getAnimatedType),
    "getPayload": (()=>getPayload),
    "isAnimated": (()=>isAnimated),
    "setAnimated": (()=>setAnimated)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@react-spring/shared/dist/react-spring_shared.modern.mjs [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@react-spring/shared/dist/react-spring_shared.modern.mjs [app-client] (ecmascript) <locals>");
// src/withAnimated.tsx
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$rafz$2f$dist$2f$react$2d$spring_rafz$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-spring/rafz/dist/react-spring_rafz.modern.mjs [app-client] (ecmascript)");
;
var $node = Symbol.for("Animated:node");
var isAnimated = (value)=>!!value && value[$node] === value;
var getAnimated = (owner)=>owner && owner[$node];
var setAnimated = (owner, node)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["defineHidden"])(owner, $node, node);
var getPayload = (owner)=>owner && owner[$node] && owner[$node].getPayload();
var Animated = class {
    constructor(){
        setAnimated(this, this);
    }
    /** Get every `AnimatedValue` used by this node. */ getPayload() {
        return this.payload || [];
    }
};
;
var AnimatedValue = class extends Animated {
    constructor(_value){
        super();
        this._value = _value;
        this.done = true;
        this.durationProgress = 0;
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].num(this._value)) {
            this.lastPosition = this._value;
        }
    }
    /** @internal */ static create(value) {
        return new AnimatedValue(value);
    }
    getPayload() {
        return [
            this
        ];
    }
    getValue() {
        return this._value;
    }
    setValue(value, step) {
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].num(value)) {
            this.lastPosition = value;
            if (step) {
                value = Math.round(value / step) * step;
                if (this.done) {
                    this.lastPosition = value;
                }
            }
        }
        if (this._value === value) {
            return false;
        }
        this._value = value;
        return true;
    }
    reset() {
        const { done } = this;
        this.done = false;
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].num(this._value)) {
            this.elapsedTime = 0;
            this.durationProgress = 0;
            this.lastPosition = this._value;
            if (done) this.lastVelocity = null;
            this.v0 = null;
        }
    }
};
;
var AnimatedString = class extends AnimatedValue {
    constructor(value){
        super(0);
        this._string = null;
        this._toString = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createInterpolator"])({
            output: [
                value,
                value
            ]
        });
    }
    /** @internal */ static create(value) {
        return new AnimatedString(value);
    }
    getValue() {
        const value = this._string;
        return value == null ? this._string = this._toString(this._value) : value;
    }
    setValue(value) {
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].str(value)) {
            if (value == this._string) {
                return false;
            }
            this._string = value;
            this._value = 1;
        } else if (super.setValue(value)) {
            this._string = null;
        } else {
            return false;
        }
        return true;
    }
    reset(goal) {
        if (goal) {
            this._toString = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createInterpolator"])({
                output: [
                    this.getValue(),
                    goal
                ]
            });
        }
        this._value = 0;
        super.reset();
    }
};
;
;
// src/context.ts
var TreeContext = {
    dependencies: null
};
// src/AnimatedObject.ts
var AnimatedObject = class extends Animated {
    constructor(source){
        super();
        this.source = source;
        this.setValue(source);
    }
    getValue(animated) {
        const values = {};
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["eachProp"])(this.source, (source, key)=>{
            if (isAnimated(source)) {
                values[key] = source.getValue(animated);
            } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["hasFluidValue"])(source)) {
                values[key] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getFluidValue"])(source);
            } else if (!animated) {
                values[key] = source;
            }
        });
        return values;
    }
    /** Replace the raw object data */ setValue(source) {
        this.source = source;
        this.payload = this._makePayload(source);
    }
    reset() {
        if (this.payload) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])(this.payload, (node)=>node.reset());
        }
    }
    /** Create a payload set. */ _makePayload(source) {
        if (source) {
            const payload = /* @__PURE__ */ new Set();
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["eachProp"])(source, this._addToPayload, payload);
            return Array.from(payload);
        }
    }
    /** Add to a payload set. */ _addToPayload(source) {
        if (TreeContext.dependencies && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["hasFluidValue"])(source)) {
            TreeContext.dependencies.add(source);
        }
        const payload = getPayload(source);
        if (payload) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])(payload, (node)=>this.add(node));
        }
    }
};
// src/AnimatedArray.ts
var AnimatedArray = class extends AnimatedObject {
    constructor(source){
        super(source);
    }
    /** @internal */ static create(source) {
        return new AnimatedArray(source);
    }
    getValue() {
        return this.source.map((node)=>node.getValue());
    }
    setValue(source) {
        const payload = this.getPayload();
        if (source.length == payload.length) {
            return payload.map((node, i)=>node.setValue(source[i])).some(Boolean);
        }
        super.setValue(source.map(makeAnimated));
        return true;
    }
};
function makeAnimated(value) {
    const nodeType = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["isAnimatedString"])(value) ? AnimatedString : AnimatedValue;
    return nodeType.create(value);
}
;
function getAnimatedType(value) {
    const parentNode = getAnimated(value);
    return parentNode ? parentNode.constructor : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].arr(value) ? AnimatedArray : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["isAnimatedString"])(value) ? AnimatedString : AnimatedValue;
}
;
;
;
;
var withAnimated = (Component, host)=>{
    const hasInstance = // Function components must use "forwardRef" to avoid being
    // re-rendered on every animation frame.
    !__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].fun(Component) || Component.prototype && Component.prototype.isReactComponent;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((givenProps, givenRef)=>{
        const instanceRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
        const ref = hasInstance && // eslint-disable-next-line react-hooks/rules-of-hooks
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
            "withAnimated.useCallback": (value)=>{
                instanceRef.current = updateRef(givenRef, value);
            }
        }["withAnimated.useCallback"], [
            givenRef
        ]);
        const [props, deps] = getAnimatedState(givenProps, host);
        const forceUpdate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useForceUpdate"])();
        const callback = ()=>{
            const instance = instanceRef.current;
            if (hasInstance && !instance) {
                return;
            }
            const didUpdate = instance ? host.applyAnimatedValues(instance, props.getValue(true)) : false;
            if (didUpdate === false) {
                forceUpdate();
            }
        };
        const observer = new PropsObserver(callback, deps);
        const observerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])();
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useIsomorphicLayoutEffect"])({
            "withAnimated.useIsomorphicLayoutEffect": ()=>{
                observerRef.current = observer;
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])(deps, {
                    "withAnimated.useIsomorphicLayoutEffect": (dep)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["addFluidObserver"])(dep, observer)
                }["withAnimated.useIsomorphicLayoutEffect"]);
                return ({
                    "withAnimated.useIsomorphicLayoutEffect": ()=>{
                        if (observerRef.current) {
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])(observerRef.current.deps, {
                                "withAnimated.useIsomorphicLayoutEffect": (dep)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["removeFluidObserver"])(dep, observerRef.current)
                            }["withAnimated.useIsomorphicLayoutEffect"]);
                            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$rafz$2f$dist$2f$react$2d$spring_rafz$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["raf"].cancel(observerRef.current.update);
                        }
                    }
                })["withAnimated.useIsomorphicLayoutEffect"];
            }
        }["withAnimated.useIsomorphicLayoutEffect"]);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])(callback, []);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useOnce"])({
            "withAnimated.useOnce": ()=>({
                    "withAnimated.useOnce": ()=>{
                        const observer2 = observerRef.current;
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])(observer2.deps, {
                            "withAnimated.useOnce": (dep)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["removeFluidObserver"])(dep, observer2)
                        }["withAnimated.useOnce"]);
                    }
                })["withAnimated.useOnce"]
        }["withAnimated.useOnce"]);
        const usedProps = host.getComponentProps(props.getValue());
        return /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(Component, {
            ...usedProps,
            ref
        });
    });
};
var PropsObserver = class {
    constructor(update, deps){
        this.update = update;
        this.deps = deps;
    }
    eventObserved(event) {
        if (event.type == "change") {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$rafz$2f$dist$2f$react$2d$spring_rafz$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["raf"].write(this.update);
        }
    }
};
function getAnimatedState(props, host) {
    const dependencies = /* @__PURE__ */ new Set();
    TreeContext.dependencies = dependencies;
    if (props.style) props = {
        ...props,
        style: host.createAnimatedStyle(props.style)
    };
    props = new AnimatedObject(props);
    TreeContext.dependencies = null;
    return [
        props,
        dependencies
    ];
}
function updateRef(ref, value) {
    if (ref) {
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].fun(ref)) ref(value);
        else ref.current = value;
    }
    return value;
}
// src/createHost.ts
var cacheKey = Symbol.for("AnimatedComponent");
var createHost = (components, { applyAnimatedValues = ()=>false, createAnimatedStyle = (style)=>new AnimatedObject(style), getComponentProps = (props)=>props } = {})=>{
    const hostConfig = {
        applyAnimatedValues,
        createAnimatedStyle,
        getComponentProps
    };
    const animated = (Component)=>{
        const displayName = getDisplayName(Component) || "Anonymous";
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].str(Component)) {
            Component = animated[Component] || (animated[Component] = withAnimated(Component, hostConfig));
        } else {
            Component = Component[cacheKey] || (Component[cacheKey] = withAnimated(Component, hostConfig));
        }
        Component.displayName = `Animated(${displayName})`;
        return Component;
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["eachProp"])(components, (Component, key)=>{
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].arr(components)) {
            key = getDisplayName(Component);
        }
        animated[key] = animated(Component);
    });
    return {
        animated
    };
};
var getDisplayName = (arg)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].str(arg) ? arg : arg && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].str(arg.displayName) ? arg.displayName : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].fun(arg) && arg.name || null;
;
 //# sourceMappingURL=react-spring_animated.modern.mjs.map
}}),
"[project]/node_modules/@react-spring/types/dist/react-spring_types.modern.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// src/utils.ts
__turbopack_context__.s({
    "Any": (()=>Any)
});
var Any = class {
};
;
 //# sourceMappingURL=react-spring_types.modern.mjs.map
}}),
"[project]/node_modules/@react-spring/core/dist/react-spring_core.modern.mjs [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// src/hooks/useChain.ts
__turbopack_context__.s({
    "BailSignal": (()=>BailSignal),
    "Controller": (()=>Controller),
    "FrameValue": (()=>FrameValue),
    "Interpolation": (()=>Interpolation),
    "Spring": (()=>Spring),
    "SpringContext": (()=>SpringContext),
    "SpringRef": (()=>SpringRef),
    "SpringValue": (()=>SpringValue),
    "Trail": (()=>Trail),
    "Transition": (()=>Transition),
    "config": (()=>config),
    "inferTo": (()=>inferTo),
    "interpolate": (()=>interpolate),
    "to": (()=>to),
    "update": (()=>update),
    "useChain": (()=>useChain),
    "useInView": (()=>useInView),
    "useResize": (()=>useResize),
    "useScroll": (()=>useScroll),
    "useSpring": (()=>useSpring),
    "useSpringRef": (()=>useSpringRef),
    "useSpringValue": (()=>useSpringValue),
    "useSprings": (()=>useSprings),
    "useTrail": (()=>useTrail),
    "useTransition": (()=>useTransition)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@react-spring/shared/dist/react-spring_shared.modern.mjs [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@react-spring/shared/dist/react-spring_shared.modern.mjs [app-client] (ecmascript) <locals>");
// src/hooks/useSprings.ts
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
// src/SpringValue.ts
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$rafz$2f$dist$2f$react$2d$spring_rafz$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-spring/rafz/dist/react-spring_rafz.modern.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$animated$2f$dist$2f$react$2d$spring_animated$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-spring/animated/dist/react-spring_animated.modern.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$types$2f$dist$2f$react$2d$spring_types$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-spring/types/dist/react-spring_types.modern.mjs [app-client] (ecmascript)");
;
;
function callProp(value, ...args) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].fun(value) ? value(...args) : value;
}
var matchProp = (value, key)=>value === true || !!(key && value && (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].fun(value) ? value(key) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["toArray"])(value).includes(key)));
var resolveProp = (prop, key)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].obj(prop) ? key && prop[key] : prop;
var getDefaultProp = (props, key)=>props.default === true ? props[key] : props.default ? props.default[key] : void 0;
var noopTransform = (value)=>value;
var getDefaultProps = (props, transform = noopTransform)=>{
    let keys = DEFAULT_PROPS;
    if (props.default && props.default !== true) {
        props = props.default;
        keys = Object.keys(props);
    }
    const defaults2 = {};
    for (const key of keys){
        const value = transform(props[key], key);
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].und(value)) {
            defaults2[key] = value;
        }
    }
    return defaults2;
};
var DEFAULT_PROPS = [
    "config",
    "onProps",
    "onStart",
    "onChange",
    "onPause",
    "onResume",
    "onRest"
];
var RESERVED_PROPS = {
    config: 1,
    from: 1,
    to: 1,
    ref: 1,
    loop: 1,
    reset: 1,
    pause: 1,
    cancel: 1,
    reverse: 1,
    immediate: 1,
    default: 1,
    delay: 1,
    onProps: 1,
    onStart: 1,
    onChange: 1,
    onPause: 1,
    onResume: 1,
    onRest: 1,
    onResolve: 1,
    // Transition props
    items: 1,
    trail: 1,
    sort: 1,
    expires: 1,
    initial: 1,
    enter: 1,
    update: 1,
    leave: 1,
    children: 1,
    onDestroyed: 1,
    // Internal props
    keys: 1,
    callId: 1,
    parentId: 1
};
function getForwardProps(props) {
    const forward = {};
    let count = 0;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["eachProp"])(props, (value, prop)=>{
        if (!RESERVED_PROPS[prop]) {
            forward[prop] = value;
            count++;
        }
    });
    if (count) {
        return forward;
    }
}
function inferTo(props) {
    const to2 = getForwardProps(props);
    if (to2) {
        const out = {
            to: to2
        };
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["eachProp"])(props, (val, key)=>key in to2 || (out[key] = val));
        return out;
    }
    return {
        ...props
    };
}
function computeGoal(value) {
    value = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getFluidValue"])(value);
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].arr(value) ? value.map(computeGoal) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["isAnimatedString"])(value) ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["Globals"].createStringInterpolator({
        range: [
            0,
            1
        ],
        output: [
            value,
            value
        ]
    })(1) : value;
}
function hasProps(props) {
    for(const _ in props)return true;
    return false;
}
function isAsyncTo(to2) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].fun(to2) || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].arr(to2) && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].obj(to2[0]);
}
function detachRefs(ctrl, ref) {
    ctrl.ref?.delete(ctrl);
    ref?.delete(ctrl);
}
function replaceRef(ctrl, ref) {
    if (ref && ctrl.ref !== ref) {
        ctrl.ref?.delete(ctrl);
        ref.add(ctrl);
        ctrl.ref = ref;
    }
}
// src/hooks/useChain.ts
function useChain(refs, timeSteps, timeFrame = 1e3) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useIsomorphicLayoutEffect"])({
        "useChain.useIsomorphicLayoutEffect": ()=>{
            if (timeSteps) {
                let prevDelay = 0;
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])(refs, {
                    "useChain.useIsomorphicLayoutEffect": (ref, i)=>{
                        const controllers = ref.current;
                        if (controllers.length) {
                            let delay = timeFrame * timeSteps[i];
                            if (isNaN(delay)) delay = prevDelay;
                            else prevDelay = delay;
                            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])(controllers, {
                                "useChain.useIsomorphicLayoutEffect": (ctrl)=>{
                                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])(ctrl.queue, {
                                        "useChain.useIsomorphicLayoutEffect": (props)=>{
                                            const memoizedDelayProp = props.delay;
                                            props.delay = ({
                                                "useChain.useIsomorphicLayoutEffect": (key)=>delay + callProp(memoizedDelayProp || 0, key)
                                            })["useChain.useIsomorphicLayoutEffect"];
                                        }
                                    }["useChain.useIsomorphicLayoutEffect"]);
                                }
                            }["useChain.useIsomorphicLayoutEffect"]);
                            ref.start();
                        }
                    }
                }["useChain.useIsomorphicLayoutEffect"]);
            } else {
                let p = Promise.resolve();
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])(refs, {
                    "useChain.useIsomorphicLayoutEffect": (ref)=>{
                        const controllers = ref.current;
                        if (controllers.length) {
                            const queues = controllers.map({
                                "useChain.useIsomorphicLayoutEffect.queues": (ctrl)=>{
                                    const q = ctrl.queue;
                                    ctrl.queue = [];
                                    return q;
                                }
                            }["useChain.useIsomorphicLayoutEffect.queues"]);
                            p = p.then({
                                "useChain.useIsomorphicLayoutEffect": ()=>{
                                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])(controllers, {
                                        "useChain.useIsomorphicLayoutEffect": (ctrl, i)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])(queues[i] || [], {
                                                "useChain.useIsomorphicLayoutEffect": (update2)=>ctrl.queue.push(update2)
                                            }["useChain.useIsomorphicLayoutEffect"])
                                    }["useChain.useIsomorphicLayoutEffect"]);
                                    return Promise.all(ref.start());
                                }
                            }["useChain.useIsomorphicLayoutEffect"]);
                        }
                    }
                }["useChain.useIsomorphicLayoutEffect"]);
            }
        }
    }["useChain.useIsomorphicLayoutEffect"]);
}
;
;
;
;
;
;
// src/constants.ts
var config = {
    default: {
        tension: 170,
        friction: 26
    },
    gentle: {
        tension: 120,
        friction: 14
    },
    wobbly: {
        tension: 180,
        friction: 12
    },
    stiff: {
        tension: 210,
        friction: 20
    },
    slow: {
        tension: 280,
        friction: 60
    },
    molasses: {
        tension: 280,
        friction: 120
    }
};
// src/AnimationConfig.ts
var defaults = {
    ...config.default,
    mass: 1,
    damping: 1,
    easing: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["easings"].linear,
    clamp: false
};
var AnimationConfig = class {
    constructor(){
        /**
     * The initial velocity of one or more values.
     *
     * @default 0
     */ this.velocity = 0;
        Object.assign(this, defaults);
    }
};
function mergeConfig(config2, newConfig, defaultConfig) {
    if (defaultConfig) {
        defaultConfig = {
            ...defaultConfig
        };
        sanitizeConfig(defaultConfig, newConfig);
        newConfig = {
            ...defaultConfig,
            ...newConfig
        };
    }
    sanitizeConfig(config2, newConfig);
    Object.assign(config2, newConfig);
    for(const key in defaults){
        if (config2[key] == null) {
            config2[key] = defaults[key];
        }
    }
    let { frequency, damping } = config2;
    const { mass } = config2;
    if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].und(frequency)) {
        if (frequency < 0.01) frequency = 0.01;
        if (damping < 0) damping = 0;
        config2.tension = Math.pow(2 * Math.PI / frequency, 2) * mass;
        config2.friction = 4 * Math.PI * damping * mass / frequency;
    }
    return config2;
}
function sanitizeConfig(config2, props) {
    if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].und(props.decay)) {
        config2.duration = void 0;
    } else {
        const isTensionConfig = !__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].und(props.tension) || !__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].und(props.friction);
        if (isTensionConfig || !__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].und(props.frequency) || !__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].und(props.damping) || !__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].und(props.mass)) {
            config2.duration = void 0;
            config2.decay = void 0;
        }
        if (isTensionConfig) {
            config2.frequency = void 0;
        }
    }
}
// src/Animation.ts
var emptyArray = [];
var Animation = class {
    constructor(){
        this.changed = false;
        this.values = emptyArray;
        this.toValues = null;
        this.fromValues = emptyArray;
        this.config = new AnimationConfig();
        this.immediate = false;
    }
};
;
function scheduleProps(callId, { key, props, defaultProps, state, actions }) {
    return new Promise((resolve, reject)=>{
        let delay;
        let timeout;
        let cancel = matchProp(props.cancel ?? defaultProps?.cancel, key);
        if (cancel) {
            onStart();
        } else {
            if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].und(props.pause)) {
                state.paused = matchProp(props.pause, key);
            }
            let pause = defaultProps?.pause;
            if (pause !== true) {
                pause = state.paused || matchProp(pause, key);
            }
            delay = callProp(props.delay || 0, key);
            if (pause) {
                state.resumeQueue.add(onResume);
                actions.pause();
            } else {
                actions.resume();
                onResume();
            }
        }
        function onPause() {
            state.resumeQueue.add(onResume);
            state.timeouts.delete(timeout);
            timeout.cancel();
            delay = timeout.time - __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$rafz$2f$dist$2f$react$2d$spring_rafz$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["raf"].now();
        }
        function onResume() {
            if (delay > 0 && !__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["Globals"].skipAnimation) {
                state.delayed = true;
                timeout = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$rafz$2f$dist$2f$react$2d$spring_rafz$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["raf"].setTimeout(onStart, delay);
                state.pauseQueue.add(onPause);
                state.timeouts.add(timeout);
            } else {
                onStart();
            }
        }
        function onStart() {
            if (state.delayed) {
                state.delayed = false;
            }
            state.pauseQueue.delete(onPause);
            state.timeouts.delete(timeout);
            if (callId <= (state.cancelId || 0)) {
                cancel = true;
            }
            try {
                actions.start({
                    ...props,
                    callId,
                    cancel
                }, resolve);
            } catch (err) {
                reject(err);
            }
        }
    });
}
;
// src/AnimationResult.ts
var getCombinedResult = (target, results)=>results.length == 1 ? results[0] : results.some((result)=>result.cancelled) ? getCancelledResult(target.get()) : results.every((result)=>result.noop) ? getNoopResult(target.get()) : getFinishedResult(target.get(), results.every((result)=>result.finished));
var getNoopResult = (value)=>({
        value,
        noop: true,
        finished: true,
        cancelled: false
    });
var getFinishedResult = (value, finished, cancelled = false)=>({
        value,
        finished,
        cancelled
    });
var getCancelledResult = (value)=>({
        value,
        cancelled: true,
        finished: false
    });
// src/runAsync.ts
function runAsync(to2, props, state, target) {
    const { callId, parentId, onRest } = props;
    const { asyncTo: prevTo, promise: prevPromise } = state;
    if (!parentId && to2 === prevTo && !props.reset) {
        return prevPromise;
    }
    return state.promise = (async ()=>{
        state.asyncId = callId;
        state.asyncTo = to2;
        const defaultProps = getDefaultProps(props, (value, key)=>// The `onRest` prop is only called when the `runAsync` promise is resolved.
            key === "onRest" ? void 0 : value);
        let preventBail;
        let bail;
        const bailPromise = new Promise((resolve, reject)=>(preventBail = resolve, bail = reject));
        const bailIfEnded = (bailSignal)=>{
            const bailResult = // The `cancel` prop or `stop` method was used.
            callId <= (state.cancelId || 0) && getCancelledResult(target) || // The async `to` prop was replaced.
            callId !== state.asyncId && getFinishedResult(target, false);
            if (bailResult) {
                bailSignal.result = bailResult;
                bail(bailSignal);
                throw bailSignal;
            }
        };
        const animate = (arg1, arg2)=>{
            const bailSignal = new BailSignal();
            const skipAnimationSignal = new SkipAnimationSignal();
            return (async ()=>{
                if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["Globals"].skipAnimation) {
                    stopAsync(state);
                    skipAnimationSignal.result = getFinishedResult(target, false);
                    bail(skipAnimationSignal);
                    throw skipAnimationSignal;
                }
                bailIfEnded(bailSignal);
                const props2 = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].obj(arg1) ? {
                    ...arg1
                } : {
                    ...arg2,
                    to: arg1
                };
                props2.parentId = callId;
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["eachProp"])(defaultProps, (value, key)=>{
                    if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].und(props2[key])) {
                        props2[key] = value;
                    }
                });
                const result2 = await target.start(props2);
                bailIfEnded(bailSignal);
                if (state.paused) {
                    await new Promise((resume)=>{
                        state.resumeQueue.add(resume);
                    });
                }
                return result2;
            })();
        };
        let result;
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["Globals"].skipAnimation) {
            stopAsync(state);
            return getFinishedResult(target, false);
        }
        try {
            let animating;
            if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].arr(to2)) {
                animating = (async (queue)=>{
                    for (const props2 of queue){
                        await animate(props2);
                    }
                })(to2);
            } else {
                animating = Promise.resolve(to2(animate, target.stop.bind(target)));
            }
            await Promise.all([
                animating.then(preventBail),
                bailPromise
            ]);
            result = getFinishedResult(target.get(), true, false);
        } catch (err) {
            if (err instanceof BailSignal) {
                result = err.result;
            } else if (err instanceof SkipAnimationSignal) {
                result = err.result;
            } else {
                throw err;
            }
        } finally{
            if (callId == state.asyncId) {
                state.asyncId = parentId;
                state.asyncTo = parentId ? prevTo : void 0;
                state.promise = parentId ? prevPromise : void 0;
            }
        }
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].fun(onRest)) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$rafz$2f$dist$2f$react$2d$spring_rafz$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["raf"].batchedUpdates(()=>{
                onRest(result, target, target.item);
            });
        }
        return result;
    })();
}
function stopAsync(state, cancelId) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["flush"])(state.timeouts, (t)=>t.cancel());
    state.pauseQueue.clear();
    state.resumeQueue.clear();
    state.asyncId = state.asyncTo = state.promise = void 0;
    if (cancelId) state.cancelId = cancelId;
}
var BailSignal = class extends Error {
    constructor(){
        super("An async animation has been interrupted. You see this error because you forgot to use `await` or `.catch(...)` on its returned promise.");
    }
};
var SkipAnimationSignal = class extends Error {
    constructor(){
        super("SkipAnimationSignal");
    }
};
;
;
var isFrameValue = (value)=>value instanceof FrameValue;
var nextId = 1;
var FrameValue = class extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["FluidValue"] {
    constructor(){
        super(...arguments);
        this.id = nextId++;
        this._priority = 0;
    }
    get priority() {
        return this._priority;
    }
    set priority(priority) {
        if (this._priority != priority) {
            this._priority = priority;
            this._onPriorityChange(priority);
        }
    }
    /** Get the current value */ get() {
        const node = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$animated$2f$dist$2f$react$2d$spring_animated$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAnimated"])(this);
        return node && node.getValue();
    }
    /** Create a spring that maps our value to another value */ to(...args) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["Globals"].to(this, args);
    }
    /** @deprecated Use the `to` method instead. */ interpolate(...args) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["deprecateInterpolate"])();
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["Globals"].to(this, args);
    }
    toJSON() {
        return this.get();
    }
    observerAdded(count) {
        if (count == 1) this._attach();
    }
    observerRemoved(count) {
        if (count == 0) this._detach();
    }
    /** Called when the first child is added. */ _attach() {}
    /** Called when the last child is removed. */ _detach() {}
    /** Tell our children about our new value */ _onChange(value, idle = false) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["callFluidObservers"])(this, {
            type: "change",
            parent: this,
            value,
            idle
        });
    }
    /** Tell our children about our new priority */ _onPriorityChange(priority) {
        if (!this.idle) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["frameLoop"].sort(this);
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["callFluidObservers"])(this, {
            type: "priority",
            parent: this,
            priority
        });
    }
};
// src/SpringPhase.ts
var $P = Symbol.for("SpringPhase");
var HAS_ANIMATED = 1;
var IS_ANIMATING = 2;
var IS_PAUSED = 4;
var hasAnimated = (target)=>(target[$P] & HAS_ANIMATED) > 0;
var isAnimating = (target)=>(target[$P] & IS_ANIMATING) > 0;
var isPaused = (target)=>(target[$P] & IS_PAUSED) > 0;
var setActiveBit = (target, active)=>active ? target[$P] |= IS_ANIMATING | HAS_ANIMATED : target[$P] &= ~IS_ANIMATING;
var setPausedBit = (target, paused)=>paused ? target[$P] |= IS_PAUSED : target[$P] &= ~IS_PAUSED;
// src/SpringValue.ts
var SpringValue = class extends FrameValue {
    constructor(arg1, arg2){
        super();
        /** The animation state */ this.animation = new Animation();
        /** Some props have customizable default values */ this.defaultProps = {};
        /** The state for `runAsync` calls */ this._state = {
            paused: false,
            delayed: false,
            pauseQueue: /* @__PURE__ */ new Set(),
            resumeQueue: /* @__PURE__ */ new Set(),
            timeouts: /* @__PURE__ */ new Set()
        };
        /** The promise resolvers of pending `start` calls */ this._pendingCalls = /* @__PURE__ */ new Set();
        /** The counter for tracking `scheduleProps` calls */ this._lastCallId = 0;
        /** The last `scheduleProps` call that changed the `to` prop */ this._lastToId = 0;
        this._memoizedDuration = 0;
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].und(arg1) || !__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].und(arg2)) {
            const props = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].obj(arg1) ? {
                ...arg1
            } : {
                ...arg2,
                from: arg1
            };
            if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].und(props.default)) {
                props.default = true;
            }
            this.start(props);
        }
    }
    /** Equals true when not advancing on each frame. */ get idle() {
        return !(isAnimating(this) || this._state.asyncTo) || isPaused(this);
    }
    get goal() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getFluidValue"])(this.animation.to);
    }
    get velocity() {
        const node = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$animated$2f$dist$2f$react$2d$spring_animated$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAnimated"])(this);
        return node instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$animated$2f$dist$2f$react$2d$spring_animated$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AnimatedValue"] ? node.lastVelocity || 0 : node.getPayload().map((node2)=>node2.lastVelocity || 0);
    }
    /**
   * When true, this value has been animated at least once.
   */ get hasAnimated() {
        return hasAnimated(this);
    }
    /**
   * When true, this value has an unfinished animation,
   * which is either active or paused.
   */ get isAnimating() {
        return isAnimating(this);
    }
    /**
   * When true, all current and future animations are paused.
   */ get isPaused() {
        return isPaused(this);
    }
    /**
   *
   *
   */ get isDelayed() {
        return this._state.delayed;
    }
    /** Advance the current animation by a number of milliseconds */ advance(dt) {
        let idle = true;
        let changed = false;
        const anim = this.animation;
        let { toValues } = anim;
        const { config: config2 } = anim;
        const payload = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$animated$2f$dist$2f$react$2d$spring_animated$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getPayload"])(anim.to);
        if (!payload && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["hasFluidValue"])(anim.to)) {
            toValues = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["toArray"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getFluidValue"])(anim.to));
        }
        anim.values.forEach((node2, i)=>{
            if (node2.done) return;
            const to2 = // Animated strings always go from 0 to 1.
            node2.constructor == __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$animated$2f$dist$2f$react$2d$spring_animated$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AnimatedString"] ? 1 : payload ? payload[i].lastPosition : toValues[i];
            let finished = anim.immediate;
            let position = to2;
            if (!finished) {
                position = node2.lastPosition;
                if (config2.tension <= 0) {
                    node2.done = true;
                    return;
                }
                let elapsed = node2.elapsedTime += dt;
                const from = anim.fromValues[i];
                const v0 = node2.v0 != null ? node2.v0 : node2.v0 = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].arr(config2.velocity) ? config2.velocity[i] : config2.velocity;
                let velocity;
                const precision = config2.precision || (from == to2 ? 5e-3 : Math.min(1, Math.abs(to2 - from) * 1e-3));
                if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].und(config2.duration)) {
                    let p = 1;
                    if (config2.duration > 0) {
                        if (this._memoizedDuration !== config2.duration) {
                            this._memoizedDuration = config2.duration;
                            if (node2.durationProgress > 0) {
                                node2.elapsedTime = config2.duration * node2.durationProgress;
                                elapsed = node2.elapsedTime += dt;
                            }
                        }
                        p = (config2.progress || 0) + elapsed / this._memoizedDuration;
                        p = p > 1 ? 1 : p < 0 ? 0 : p;
                        node2.durationProgress = p;
                    }
                    position = from + config2.easing(p) * (to2 - from);
                    velocity = (position - node2.lastPosition) / dt;
                    finished = p == 1;
                } else if (config2.decay) {
                    const decay = config2.decay === true ? 0.998 : config2.decay;
                    const e = Math.exp(-(1 - decay) * elapsed);
                    position = from + v0 / (1 - decay) * (1 - e);
                    finished = Math.abs(node2.lastPosition - position) <= precision;
                    velocity = v0 * e;
                } else {
                    velocity = node2.lastVelocity == null ? v0 : node2.lastVelocity;
                    const restVelocity = config2.restVelocity || precision / 10;
                    const bounceFactor = config2.clamp ? 0 : config2.bounce;
                    const canBounce = !__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].und(bounceFactor);
                    const isGrowing = from == to2 ? node2.v0 > 0 : from < to2;
                    let isMoving;
                    let isBouncing = false;
                    const step = 1;
                    const numSteps = Math.ceil(dt / step);
                    for(let n = 0; n < numSteps; ++n){
                        isMoving = Math.abs(velocity) > restVelocity;
                        if (!isMoving) {
                            finished = Math.abs(to2 - position) <= precision;
                            if (finished) {
                                break;
                            }
                        }
                        if (canBounce) {
                            isBouncing = position == to2 || position > to2 == isGrowing;
                            if (isBouncing) {
                                velocity = -velocity * bounceFactor;
                                position = to2;
                            }
                        }
                        const springForce = -config2.tension * 1e-6 * (position - to2);
                        const dampingForce = -config2.friction * 1e-3 * velocity;
                        const acceleration = (springForce + dampingForce) / config2.mass;
                        velocity = velocity + acceleration * step;
                        position = position + velocity * step;
                    }
                }
                node2.lastVelocity = velocity;
                if (Number.isNaN(position)) {
                    console.warn(`Got NaN while animating:`, this);
                    finished = true;
                }
            }
            if (payload && !payload[i].done) {
                finished = false;
            }
            if (finished) {
                node2.done = true;
            } else {
                idle = false;
            }
            if (node2.setValue(position, config2.round)) {
                changed = true;
            }
        });
        const node = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$animated$2f$dist$2f$react$2d$spring_animated$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAnimated"])(this);
        const currVal = node.getValue();
        if (idle) {
            const finalVal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getFluidValue"])(anim.to);
            if ((currVal !== finalVal || changed) && !config2.decay) {
                node.setValue(finalVal);
                this._onChange(finalVal);
            } else if (changed && config2.decay) {
                this._onChange(currVal);
            }
            this._stop();
        } else if (changed) {
            this._onChange(currVal);
        }
    }
    /** Set the current value, while stopping the current animation */ set(value) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$rafz$2f$dist$2f$react$2d$spring_rafz$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["raf"].batchedUpdates(()=>{
            this._stop();
            this._focus(value);
            this._set(value);
        });
        return this;
    }
    /**
   * Freeze the active animation in time, as well as any updates merged
   * before `resume` is called.
   */ pause() {
        this._update({
            pause: true
        });
    }
    /** Resume the animation if paused. */ resume() {
        this._update({
            pause: false
        });
    }
    /** Skip to the end of the current animation. */ finish() {
        if (isAnimating(this)) {
            const { to: to2, config: config2 } = this.animation;
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$rafz$2f$dist$2f$react$2d$spring_rafz$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["raf"].batchedUpdates(()=>{
                this._onStart();
                if (!config2.decay) {
                    this._set(to2, false);
                }
                this._stop();
            });
        }
        return this;
    }
    /** Push props into the pending queue. */ update(props) {
        const queue = this.queue || (this.queue = []);
        queue.push(props);
        return this;
    }
    start(to2, arg2) {
        let queue;
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].und(to2)) {
            queue = [
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].obj(to2) ? to2 : {
                    ...arg2,
                    to: to2
                }
            ];
        } else {
            queue = this.queue || [];
            this.queue = [];
        }
        return Promise.all(queue.map((props)=>{
            const up = this._update(props);
            return up;
        })).then((results)=>getCombinedResult(this, results));
    }
    /**
   * Stop the current animation, and cancel any delayed updates.
   *
   * Pass `true` to call `onRest` with `cancelled: true`.
   */ stop(cancel) {
        const { to: to2 } = this.animation;
        this._focus(this.get());
        stopAsync(this._state, cancel && this._lastCallId);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$rafz$2f$dist$2f$react$2d$spring_rafz$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["raf"].batchedUpdates(()=>this._stop(to2, cancel));
        return this;
    }
    /** Restart the animation. */ reset() {
        this._update({
            reset: true
        });
    }
    /** @internal */ eventObserved(event) {
        if (event.type == "change") {
            this._start();
        } else if (event.type == "priority") {
            this.priority = event.priority + 1;
        }
    }
    /**
   * Parse the `to` and `from` range from the given `props` object.
   *
   * This also ensures the initial value is available to animated components
   * during the render phase.
   */ _prepareNode(props) {
        const key = this.key || "";
        let { to: to2, from } = props;
        to2 = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].obj(to2) ? to2[key] : to2;
        if (to2 == null || isAsyncTo(to2)) {
            to2 = void 0;
        }
        from = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].obj(from) ? from[key] : from;
        if (from == null) {
            from = void 0;
        }
        const range = {
            to: to2,
            from
        };
        if (!hasAnimated(this)) {
            if (props.reverse) [to2, from] = [
                from,
                to2
            ];
            from = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getFluidValue"])(from);
            if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].und(from)) {
                this._set(from);
            } else if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$animated$2f$dist$2f$react$2d$spring_animated$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAnimated"])(this)) {
                this._set(to2);
            }
        }
        return range;
    }
    /** Every update is processed by this method before merging. */ _update({ ...props }, isLoop) {
        const { key, defaultProps } = this;
        if (props.default) Object.assign(defaultProps, getDefaultProps(props, (value, prop)=>/^on/.test(prop) ? resolveProp(value, key) : value));
        mergeActiveFn(this, props, "onProps");
        sendEvent(this, "onProps", props, this);
        const range = this._prepareNode(props);
        if (Object.isFrozen(this)) {
            throw Error("Cannot animate a `SpringValue` object that is frozen. Did you forget to pass your component to `animated(...)` before animating its props?");
        }
        const state = this._state;
        return scheduleProps(++this._lastCallId, {
            key,
            props,
            defaultProps,
            state,
            actions: {
                pause: ()=>{
                    if (!isPaused(this)) {
                        setPausedBit(this, true);
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["flushCalls"])(state.pauseQueue);
                        sendEvent(this, "onPause", getFinishedResult(this, checkFinished(this, this.animation.to)), this);
                    }
                },
                resume: ()=>{
                    if (isPaused(this)) {
                        setPausedBit(this, false);
                        if (isAnimating(this)) {
                            this._resume();
                        }
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["flushCalls"])(state.resumeQueue);
                        sendEvent(this, "onResume", getFinishedResult(this, checkFinished(this, this.animation.to)), this);
                    }
                },
                start: this._merge.bind(this, range)
            }
        }).then((result)=>{
            if (props.loop && result.finished && !(isLoop && result.noop)) {
                const nextProps = createLoopUpdate(props);
                if (nextProps) {
                    return this._update(nextProps, true);
                }
            }
            return result;
        });
    }
    /** Merge props into the current animation */ _merge(range, props, resolve) {
        if (props.cancel) {
            this.stop(true);
            return resolve(getCancelledResult(this));
        }
        const hasToProp = !__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].und(range.to);
        const hasFromProp = !__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].und(range.from);
        if (hasToProp || hasFromProp) {
            if (props.callId > this._lastToId) {
                this._lastToId = props.callId;
            } else {
                return resolve(getCancelledResult(this));
            }
        }
        const { key, defaultProps, animation: anim } = this;
        const { to: prevTo, from: prevFrom } = anim;
        let { to: to2 = prevTo, from = prevFrom } = range;
        if (hasFromProp && !hasToProp && (!props.default || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].und(to2))) {
            to2 = from;
        }
        if (props.reverse) [to2, from] = [
            from,
            to2
        ];
        const hasFromChanged = !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["isEqual"])(from, prevFrom);
        if (hasFromChanged) {
            anim.from = from;
        }
        from = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getFluidValue"])(from);
        const hasToChanged = !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["isEqual"])(to2, prevTo);
        if (hasToChanged) {
            this._focus(to2);
        }
        const hasAsyncTo = isAsyncTo(props.to);
        const { config: config2 } = anim;
        const { decay, velocity } = config2;
        if (hasToProp || hasFromProp) {
            config2.velocity = 0;
        }
        if (props.config && !hasAsyncTo) {
            mergeConfig(config2, callProp(props.config, key), // Avoid calling the same "config" prop twice.
            props.config !== defaultProps.config ? callProp(defaultProps.config, key) : void 0);
        }
        let node = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$animated$2f$dist$2f$react$2d$spring_animated$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAnimated"])(this);
        if (!node || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].und(to2)) {
            return resolve(getFinishedResult(this, true));
        }
        const reset = // When `reset` is undefined, the `from` prop implies `reset: true`,
        // except for declarative updates. When `reset` is defined, there
        // must exist a value to animate from.
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].und(props.reset) ? hasFromProp && !props.default : !__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].und(from) && matchProp(props.reset, key);
        const value = reset ? from : this.get();
        const goal = computeGoal(to2);
        const isAnimatable = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].num(goal) || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].arr(goal) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["isAnimatedString"])(goal);
        const immediate = !hasAsyncTo && (!isAnimatable || matchProp(defaultProps.immediate || props.immediate, key));
        if (hasToChanged) {
            const nodeType = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$animated$2f$dist$2f$react$2d$spring_animated$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAnimatedType"])(to2);
            if (nodeType !== node.constructor) {
                if (immediate) {
                    node = this._set(goal);
                } else throw Error(`Cannot animate between ${node.constructor.name} and ${nodeType.name}, as the "to" prop suggests`);
            }
        }
        const goalType = node.constructor;
        let started = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["hasFluidValue"])(to2);
        let finished = false;
        if (!started) {
            const hasValueChanged = reset || !hasAnimated(this) && hasFromChanged;
            if (hasToChanged || hasValueChanged) {
                finished = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["isEqual"])(computeGoal(value), goal);
                started = !finished;
            }
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["isEqual"])(anim.immediate, immediate) && !immediate || !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["isEqual"])(config2.decay, decay) || !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["isEqual"])(config2.velocity, velocity)) {
                started = true;
            }
        }
        if (finished && isAnimating(this)) {
            if (anim.changed && !reset) {
                started = true;
            } else if (!started) {
                this._stop(prevTo);
            }
        }
        if (!hasAsyncTo) {
            if (started || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["hasFluidValue"])(prevTo)) {
                anim.values = node.getPayload();
                anim.toValues = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["hasFluidValue"])(to2) ? null : goalType == __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$animated$2f$dist$2f$react$2d$spring_animated$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AnimatedString"] ? [
                    1
                ] : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["toArray"])(goal);
            }
            if (anim.immediate != immediate) {
                anim.immediate = immediate;
                if (!immediate && !reset) {
                    this._set(prevTo);
                }
            }
            if (started) {
                const { onRest } = anim;
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])(ACTIVE_EVENTS, (type)=>mergeActiveFn(this, props, type));
                const result = getFinishedResult(this, checkFinished(this, prevTo));
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["flushCalls"])(this._pendingCalls, result);
                this._pendingCalls.add(resolve);
                if (anim.changed) __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$rafz$2f$dist$2f$react$2d$spring_rafz$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["raf"].batchedUpdates(()=>{
                    anim.changed = !reset;
                    onRest?.(result, this);
                    if (reset) {
                        callProp(defaultProps.onRest, result);
                    } else {
                        anim.onStart?.(result, this);
                    }
                });
            }
        }
        if (reset) {
            this._set(value);
        }
        if (hasAsyncTo) {
            resolve(runAsync(props.to, props, this._state, this));
        } else if (started) {
            this._start();
        } else if (isAnimating(this) && !hasToChanged) {
            this._pendingCalls.add(resolve);
        } else {
            resolve(getNoopResult(value));
        }
    }
    /** Update the `animation.to` value, which might be a `FluidValue` */ _focus(value) {
        const anim = this.animation;
        if (value !== anim.to) {
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getFluidObservers"])(this)) {
                this._detach();
            }
            anim.to = value;
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getFluidObservers"])(this)) {
                this._attach();
            }
        }
    }
    _attach() {
        let priority = 0;
        const { to: to2 } = this.animation;
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["hasFluidValue"])(to2)) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["addFluidObserver"])(to2, this);
            if (isFrameValue(to2)) {
                priority = to2.priority + 1;
            }
        }
        this.priority = priority;
    }
    _detach() {
        const { to: to2 } = this.animation;
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["hasFluidValue"])(to2)) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["removeFluidObserver"])(to2, this);
        }
    }
    /**
   * Update the current value from outside the frameloop,
   * and return the `Animated` node.
   */ _set(arg, idle = true) {
        const value = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getFluidValue"])(arg);
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].und(value)) {
            const oldNode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$animated$2f$dist$2f$react$2d$spring_animated$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAnimated"])(this);
            if (!oldNode || !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["isEqual"])(value, oldNode.getValue())) {
                const nodeType = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$animated$2f$dist$2f$react$2d$spring_animated$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAnimatedType"])(value);
                if (!oldNode || oldNode.constructor != nodeType) {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$animated$2f$dist$2f$react$2d$spring_animated$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setAnimated"])(this, nodeType.create(value));
                } else {
                    oldNode.setValue(value);
                }
                if (oldNode) {
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$rafz$2f$dist$2f$react$2d$spring_rafz$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["raf"].batchedUpdates(()=>{
                        this._onChange(value, idle);
                    });
                }
            }
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$animated$2f$dist$2f$react$2d$spring_animated$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAnimated"])(this);
    }
    _onStart() {
        const anim = this.animation;
        if (!anim.changed) {
            anim.changed = true;
            sendEvent(this, "onStart", getFinishedResult(this, checkFinished(this, anim.to)), this);
        }
    }
    _onChange(value, idle) {
        if (!idle) {
            this._onStart();
            callProp(this.animation.onChange, value, this);
        }
        callProp(this.defaultProps.onChange, value, this);
        super._onChange(value, idle);
    }
    // This method resets the animation state (even if already animating) to
    // ensure the latest from/to range is used, and it also ensures this spring
    // is added to the frameloop.
    _start() {
        const anim = this.animation;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$animated$2f$dist$2f$react$2d$spring_animated$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAnimated"])(this).reset((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getFluidValue"])(anim.to));
        if (!anim.immediate) {
            anim.fromValues = anim.values.map((node)=>node.lastPosition);
        }
        if (!isAnimating(this)) {
            setActiveBit(this, true);
            if (!isPaused(this)) {
                this._resume();
            }
        }
    }
    _resume() {
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["Globals"].skipAnimation) {
            this.finish();
        } else {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["frameLoop"].start(this);
        }
    }
    /**
   * Exit the frameloop and notify `onRest` listeners.
   *
   * Always wrap `_stop` calls with `batchedUpdates`.
   */ _stop(goal, cancel) {
        if (isAnimating(this)) {
            setActiveBit(this, false);
            const anim = this.animation;
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])(anim.values, (node)=>{
                node.done = true;
            });
            if (anim.toValues) {
                anim.onChange = anim.onPause = anim.onResume = void 0;
            }
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["callFluidObservers"])(this, {
                type: "idle",
                parent: this
            });
            const result = cancel ? getCancelledResult(this.get()) : getFinishedResult(this.get(), checkFinished(this, goal ?? anim.to));
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["flushCalls"])(this._pendingCalls, result);
            if (anim.changed) {
                anim.changed = false;
                sendEvent(this, "onRest", result, this);
            }
        }
    }
};
function checkFinished(target, to2) {
    const goal = computeGoal(to2);
    const value = computeGoal(target.get());
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["isEqual"])(value, goal);
}
function createLoopUpdate(props, loop = props.loop, to2 = props.to) {
    const loopRet = callProp(loop);
    if (loopRet) {
        const overrides = loopRet !== true && inferTo(loopRet);
        const reverse = (overrides || props).reverse;
        const reset = !overrides || overrides.reset;
        return createUpdate({
            ...props,
            loop,
            // Avoid updating default props when looping.
            default: false,
            // Never loop the `pause` prop.
            pause: void 0,
            // For the "reverse" prop to loop as expected, the "to" prop
            // must be undefined. The "reverse" prop is ignored when the
            // "to" prop is an array or function.
            to: !reverse || isAsyncTo(to2) ? to2 : void 0,
            // Ignore the "from" prop except on reset.
            from: reset ? props.from : void 0,
            reset,
            // The "loop" prop can return a "useSpring" props object to
            // override any of the original props.
            ...overrides
        });
    }
}
function createUpdate(props) {
    const { to: to2, from } = props = inferTo(props);
    const keys = /* @__PURE__ */ new Set();
    if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].obj(to2)) findDefined(to2, keys);
    if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].obj(from)) findDefined(from, keys);
    props.keys = keys.size ? Array.from(keys) : null;
    return props;
}
function declareUpdate(props) {
    const update2 = createUpdate(props);
    if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].und(update2.default)) {
        update2.default = getDefaultProps(update2);
    }
    return update2;
}
function findDefined(values, keys) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["eachProp"])(values, (value, key)=>value != null && keys.add(key));
}
var ACTIVE_EVENTS = [
    "onStart",
    "onRest",
    "onChange",
    "onPause",
    "onResume"
];
function mergeActiveFn(target, props, type) {
    target.animation[type] = props[type] !== getDefaultProp(props, type) ? resolveProp(props[type], target.key) : void 0;
}
function sendEvent(target, type, ...args) {
    target.animation[type]?.(...args);
    target.defaultProps[type]?.(...args);
}
;
var BATCHED_EVENTS = [
    "onStart",
    "onChange",
    "onRest"
];
var nextId2 = 1;
var Controller = class {
    constructor(props, flush3){
        this.id = nextId2++;
        /** The animated values */ this.springs = {};
        /** The queue of props passed to the `update` method. */ this.queue = [];
        /** The counter for tracking `scheduleProps` calls */ this._lastAsyncId = 0;
        /** The values currently being animated */ this._active = /* @__PURE__ */ new Set();
        /** The values that changed recently */ this._changed = /* @__PURE__ */ new Set();
        /** Equals false when `onStart` listeners can be called */ this._started = false;
        /** State used by the `runAsync` function */ this._state = {
            paused: false,
            pauseQueue: /* @__PURE__ */ new Set(),
            resumeQueue: /* @__PURE__ */ new Set(),
            timeouts: /* @__PURE__ */ new Set()
        };
        /** The event queues that are flushed once per frame maximum */ this._events = {
            onStart: /* @__PURE__ */ new Map(),
            onChange: /* @__PURE__ */ new Map(),
            onRest: /* @__PURE__ */ new Map()
        };
        this._onFrame = this._onFrame.bind(this);
        if (flush3) {
            this._flush = flush3;
        }
        if (props) {
            this.start({
                default: true,
                ...props
            });
        }
    }
    /**
   * Equals `true` when no spring values are in the frameloop, and
   * no async animation is currently active.
   */ get idle() {
        return !this._state.asyncTo && Object.values(this.springs).every((spring)=>{
            return spring.idle && !spring.isDelayed && !spring.isPaused;
        });
    }
    get item() {
        return this._item;
    }
    set item(item) {
        this._item = item;
    }
    /** Get the current values of our springs */ get() {
        const values = {};
        this.each((spring, key)=>values[key] = spring.get());
        return values;
    }
    /** Set the current values without animating. */ set(values) {
        for(const key in values){
            const value = values[key];
            if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].und(value)) {
                this.springs[key].set(value);
            }
        }
    }
    /** Push an update onto the queue of each value. */ update(props) {
        if (props) {
            this.queue.push(createUpdate(props));
        }
        return this;
    }
    /**
   * Start the queued animations for every spring, and resolve the returned
   * promise once all queued animations have finished or been cancelled.
   *
   * When you pass a queue (instead of nothing), that queue is used instead of
   * the queued animations added with the `update` method, which are left alone.
   */ start(props) {
        let { queue } = this;
        if (props) {
            queue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["toArray"])(props).map(createUpdate);
        } else {
            this.queue = [];
        }
        if (this._flush) {
            return this._flush(this, queue);
        }
        prepareKeys(this, queue);
        return flushUpdateQueue(this, queue);
    }
    /** @internal */ stop(arg, keys) {
        if (arg !== !!arg) {
            keys = arg;
        }
        if (keys) {
            const springs = this.springs;
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["toArray"])(keys), (key)=>springs[key].stop(!!arg));
        } else {
            stopAsync(this._state, this._lastAsyncId);
            this.each((spring)=>spring.stop(!!arg));
        }
        return this;
    }
    /** Freeze the active animation in time */ pause(keys) {
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].und(keys)) {
            this.start({
                pause: true
            });
        } else {
            const springs = this.springs;
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["toArray"])(keys), (key)=>springs[key].pause());
        }
        return this;
    }
    /** Resume the animation if paused. */ resume(keys) {
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].und(keys)) {
            this.start({
                pause: false
            });
        } else {
            const springs = this.springs;
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["toArray"])(keys), (key)=>springs[key].resume());
        }
        return this;
    }
    /** Call a function once per spring value */ each(iterator) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["eachProp"])(this.springs, iterator);
    }
    /** @internal Called at the end of every animation frame */ _onFrame() {
        const { onStart, onChange, onRest } = this._events;
        const active = this._active.size > 0;
        const changed = this._changed.size > 0;
        if (active && !this._started || changed && !this._started) {
            this._started = true;
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["flush"])(onStart, ([onStart2, result])=>{
                result.value = this.get();
                onStart2(result, this, this._item);
            });
        }
        const idle = !active && this._started;
        const values = changed || idle && onRest.size ? this.get() : null;
        if (changed && onChange.size) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["flush"])(onChange, ([onChange2, result])=>{
                result.value = values;
                onChange2(result, this, this._item);
            });
        }
        if (idle) {
            this._started = false;
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["flush"])(onRest, ([onRest2, result])=>{
                result.value = values;
                onRest2(result, this, this._item);
            });
        }
    }
    /** @internal */ eventObserved(event) {
        if (event.type == "change") {
            this._changed.add(event.parent);
            if (!event.idle) {
                this._active.add(event.parent);
            }
        } else if (event.type == "idle") {
            this._active.delete(event.parent);
        } else return;
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$rafz$2f$dist$2f$react$2d$spring_rafz$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["raf"].onFrame(this._onFrame);
    }
};
function flushUpdateQueue(ctrl, queue) {
    return Promise.all(queue.map((props)=>flushUpdate(ctrl, props))).then((results)=>getCombinedResult(ctrl, results));
}
async function flushUpdate(ctrl, props, isLoop) {
    const { keys, to: to2, from, loop, onRest, onResolve } = props;
    const defaults2 = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].obj(props.default) && props.default;
    if (loop) {
        props.loop = false;
    }
    if (to2 === false) props.to = null;
    if (from === false) props.from = null;
    const asyncTo = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].arr(to2) || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].fun(to2) ? to2 : void 0;
    if (asyncTo) {
        props.to = void 0;
        props.onRest = void 0;
        if (defaults2) {
            defaults2.onRest = void 0;
        }
    } else {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])(BATCHED_EVENTS, (key)=>{
            const handler = props[key];
            if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].fun(handler)) {
                const queue = ctrl["_events"][key];
                props[key] = ({ finished, cancelled })=>{
                    const result2 = queue.get(handler);
                    if (result2) {
                        if (!finished) result2.finished = false;
                        if (cancelled) result2.cancelled = true;
                    } else {
                        queue.set(handler, {
                            value: null,
                            finished: finished || false,
                            cancelled: cancelled || false
                        });
                    }
                };
                if (defaults2) {
                    defaults2[key] = props[key];
                }
            }
        });
    }
    const state = ctrl["_state"];
    if (props.pause === !state.paused) {
        state.paused = props.pause;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["flushCalls"])(props.pause ? state.pauseQueue : state.resumeQueue);
    } else if (state.paused) {
        props.pause = true;
    }
    const promises = (keys || Object.keys(ctrl.springs)).map((key)=>ctrl.springs[key].start(props));
    const cancel = props.cancel === true || getDefaultProp(props, "cancel") === true;
    if (asyncTo || cancel && state.asyncId) {
        promises.push(scheduleProps(++ctrl["_lastAsyncId"], {
            props,
            state,
            actions: {
                pause: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["noop"],
                resume: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["noop"],
                start (props2, resolve) {
                    if (cancel) {
                        stopAsync(state, ctrl["_lastAsyncId"]);
                        resolve(getCancelledResult(ctrl));
                    } else {
                        props2.onRest = onRest;
                        resolve(runAsync(asyncTo, props2, state, ctrl));
                    }
                }
            }
        }));
    }
    if (state.paused) {
        await new Promise((resume)=>{
            state.resumeQueue.add(resume);
        });
    }
    const result = getCombinedResult(ctrl, await Promise.all(promises));
    if (loop && result.finished && !(isLoop && result.noop)) {
        const nextProps = createLoopUpdate(props, loop, to2);
        if (nextProps) {
            prepareKeys(ctrl, [
                nextProps
            ]);
            return flushUpdate(ctrl, nextProps, true);
        }
    }
    if (onResolve) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$rafz$2f$dist$2f$react$2d$spring_rafz$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["raf"].batchedUpdates(()=>onResolve(result, ctrl, ctrl.item));
    }
    return result;
}
function getSprings(ctrl, props) {
    const springs = {
        ...ctrl.springs
    };
    if (props) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["toArray"])(props), (props2)=>{
            if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].und(props2.keys)) {
                props2 = createUpdate(props2);
            }
            if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].obj(props2.to)) {
                props2 = {
                    ...props2,
                    to: void 0
                };
            }
            prepareSprings(springs, props2, (key)=>{
                return createSpring(key);
            });
        });
    }
    setSprings(ctrl, springs);
    return springs;
}
function setSprings(ctrl, springs) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["eachProp"])(springs, (spring, key)=>{
        if (!ctrl.springs[key]) {
            ctrl.springs[key] = spring;
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["addFluidObserver"])(spring, ctrl);
        }
    });
}
function createSpring(key, observer) {
    const spring = new SpringValue();
    spring.key = key;
    if (observer) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["addFluidObserver"])(spring, observer);
    }
    return spring;
}
function prepareSprings(springs, props, create) {
    if (props.keys) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])(props.keys, (key)=>{
            const spring = springs[key] || (springs[key] = create(key));
            spring["_prepareNode"](props);
        });
    }
}
function prepareKeys(ctrl, queue) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])(queue, (props)=>{
        prepareSprings(ctrl.springs, props, (key)=>{
            return createSpring(key, ctrl);
        });
    });
}
;
;
;
var SpringContext = ({ children, ...props })=>{
    const inherited = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(ctx);
    const pause = props.pause || !!inherited.pause, immediate = props.immediate || !!inherited.immediate;
    props = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useMemoOne"])({
        "SpringContext.useMemoOne": ()=>({
                pause,
                immediate
            })
    }["SpringContext.useMemoOne"], [
        pause,
        immediate
    ]);
    const { Provider } = ctx;
    return /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(Provider, {
        value: props
    }, children);
};
var ctx = makeContext(SpringContext, {});
SpringContext.Provider = ctx.Provider;
SpringContext.Consumer = ctx.Consumer;
function makeContext(target, init) {
    Object.assign(target, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(init));
    target.Provider._context = target;
    target.Consumer._context = target;
    return target;
}
;
var SpringRef = ()=>{
    const current = [];
    const SpringRef2 = function(props) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["deprecateDirectCall"])();
        const results = [];
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])(current, (ctrl, i)=>{
            if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].und(props)) {
                results.push(ctrl.start());
            } else {
                const update2 = _getProps(props, ctrl, i);
                if (update2) {
                    results.push(ctrl.start(update2));
                }
            }
        });
        return results;
    };
    SpringRef2.current = current;
    SpringRef2.add = function(ctrl) {
        if (!current.includes(ctrl)) {
            current.push(ctrl);
        }
    };
    SpringRef2.delete = function(ctrl) {
        const i = current.indexOf(ctrl);
        if (~i) current.splice(i, 1);
    };
    SpringRef2.pause = function() {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])(current, (ctrl)=>ctrl.pause(...arguments));
        return this;
    };
    SpringRef2.resume = function() {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])(current, (ctrl)=>ctrl.resume(...arguments));
        return this;
    };
    SpringRef2.set = function(values) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])(current, (ctrl, i)=>{
            const update2 = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].fun(values) ? values(i, ctrl) : values;
            if (update2) {
                ctrl.set(update2);
            }
        });
    };
    SpringRef2.start = function(props) {
        const results = [];
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])(current, (ctrl, i)=>{
            if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].und(props)) {
                results.push(ctrl.start());
            } else {
                const update2 = this._getProps(props, ctrl, i);
                if (update2) {
                    results.push(ctrl.start(update2));
                }
            }
        });
        return results;
    };
    SpringRef2.stop = function() {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])(current, (ctrl)=>ctrl.stop(...arguments));
        return this;
    };
    SpringRef2.update = function(props) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])(current, (ctrl, i)=>ctrl.update(this._getProps(props, ctrl, i)));
        return this;
    };
    const _getProps = function(arg, ctrl, index) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].fun(arg) ? arg(index, ctrl) : arg;
    };
    SpringRef2._getProps = _getProps;
    return SpringRef2;
};
// src/hooks/useSprings.ts
function useSprings(length, props, deps) {
    const propsFn = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].fun(props) && props;
    if (propsFn && !deps) deps = [];
    const ref = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useSprings.useMemo[ref]": ()=>propsFn || arguments.length == 3 ? SpringRef() : void 0
    }["useSprings.useMemo[ref]"], []);
    const layoutId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(0);
    const forceUpdate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useForceUpdate"])();
    const state = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useSprings.useMemo[state]": ()=>({
                ctrls: [],
                queue: [],
                flush (ctrl, updates2) {
                    const springs2 = getSprings(ctrl, updates2);
                    const canFlushSync = layoutId.current > 0 && !state.queue.length && !Object.keys(springs2).some({
                        "useSprings.useMemo[state]": (key)=>!ctrl.springs[key]
                    }["useSprings.useMemo[state]"]);
                    return canFlushSync ? flushUpdateQueue(ctrl, updates2) : new Promise({
                        "useSprings.useMemo[state]": (resolve)=>{
                            setSprings(ctrl, springs2);
                            state.queue.push({
                                "useSprings.useMemo[state]": ()=>{
                                    resolve(flushUpdateQueue(ctrl, updates2));
                                }
                            }["useSprings.useMemo[state]"]);
                            forceUpdate();
                        }
                    }["useSprings.useMemo[state]"]);
                }
            })
    }["useSprings.useMemo[state]"], []);
    const ctrls = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])([
        ...state.ctrls
    ]);
    const updates = [];
    const prevLength = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["usePrev"])(length) || 0;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useSprings.useMemo": ()=>{
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])(ctrls.current.slice(length, prevLength), {
                "useSprings.useMemo": (ctrl)=>{
                    detachRefs(ctrl, ref);
                    ctrl.stop(true);
                }
            }["useSprings.useMemo"]);
            ctrls.current.length = length;
            declareUpdates(prevLength, length);
        }
    }["useSprings.useMemo"], [
        length
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useSprings.useMemo": ()=>{
            declareUpdates(0, Math.min(prevLength, length));
        }
    }["useSprings.useMemo"], deps);
    function declareUpdates(startIndex, endIndex) {
        for(let i = startIndex; i < endIndex; i++){
            const ctrl = ctrls.current[i] || (ctrls.current[i] = new Controller(null, state.flush));
            const update2 = propsFn ? propsFn(i, ctrl) : props[i];
            if (update2) {
                updates[i] = declareUpdate(update2);
            }
        }
    }
    const springs = ctrls.current.map((ctrl, i)=>getSprings(ctrl, updates[i]));
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(SpringContext);
    const prevContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["usePrev"])(context);
    const hasContext = context !== prevContext && hasProps(context);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useIsomorphicLayoutEffect"])({
        "useSprings.useIsomorphicLayoutEffect2": ()=>{
            layoutId.current++;
            state.ctrls = ctrls.current;
            const { queue } = state;
            if (queue.length) {
                state.queue = [];
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])(queue, {
                    "useSprings.useIsomorphicLayoutEffect2": (cb)=>cb()
                }["useSprings.useIsomorphicLayoutEffect2"]);
            }
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])(ctrls.current, {
                "useSprings.useIsomorphicLayoutEffect2": (ctrl, i)=>{
                    ref?.add(ctrl);
                    if (hasContext) {
                        ctrl.start({
                            default: context
                        });
                    }
                    const update2 = updates[i];
                    if (update2) {
                        replaceRef(ctrl, update2.ref);
                        if (ctrl.ref) {
                            ctrl.queue.push(update2);
                        } else {
                            ctrl.start(update2);
                        }
                    }
                }
            }["useSprings.useIsomorphicLayoutEffect2"]);
        }
    }["useSprings.useIsomorphicLayoutEffect2"]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useOnce"])({
        "useSprings.useOnce": ()=>({
                "useSprings.useOnce": ()=>{
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])(state.ctrls, {
                        "useSprings.useOnce": (ctrl)=>ctrl.stop(true)
                    }["useSprings.useOnce"]);
                }
            })["useSprings.useOnce"]
    }["useSprings.useOnce"]);
    const values = springs.map((x)=>({
            ...x
        }));
    return ref ? [
        values,
        ref
    ] : values;
}
// src/hooks/useSpring.ts
function useSpring(props, deps) {
    const isFn = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].fun(props);
    const [[values], ref] = useSprings(1, isFn ? props : [
        props
    ], isFn ? deps || [] : deps);
    return isFn || arguments.length == 2 ? [
        values,
        ref
    ] : values;
}
;
var initSpringRef = ()=>SpringRef();
var useSpringRef = ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(initSpringRef)[0];
;
var useSpringValue = (initial, props)=>{
    const springValue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useConstant"])({
        "useSpringValue.useConstant[springValue]": ()=>new SpringValue(initial, props)
    }["useSpringValue.useConstant[springValue]"]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useOnce"])({
        "useSpringValue.useOnce2": ()=>({
                "useSpringValue.useOnce2": ()=>{
                    springValue.stop();
                }
            })["useSpringValue.useOnce2"]
    }["useSpringValue.useOnce2"]);
    return springValue;
};
;
function useTrail(length, propsArg, deps) {
    const propsFn = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].fun(propsArg) && propsArg;
    if (propsFn && !deps) deps = [];
    let reverse = true;
    let passedRef = void 0;
    const result = useSprings(length, {
        "useTrail.useSprings[result]": (i, ctrl)=>{
            const props = propsFn ? propsFn(i, ctrl) : propsArg;
            passedRef = props.ref;
            reverse = reverse && props.reverse;
            return props;
        }
    }["useTrail.useSprings[result]"], // Ensure the props function is called when no deps exist.
    // This works around the 3 argument rule.
    deps || [
        {}
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useIsomorphicLayoutEffect"])({
        "useTrail.useIsomorphicLayoutEffect3": ()=>{
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])(result[1].current, {
                "useTrail.useIsomorphicLayoutEffect3": (ctrl, i)=>{
                    const parent = result[1].current[i + (reverse ? 1 : -1)];
                    replaceRef(ctrl, passedRef);
                    if (ctrl.ref) {
                        if (parent) {
                            ctrl.update({
                                to: parent.springs
                            });
                        }
                        return;
                    }
                    if (parent) {
                        ctrl.start({
                            to: parent.springs
                        });
                    } else {
                        ctrl.start();
                    }
                }
            }["useTrail.useIsomorphicLayoutEffect3"]);
        }
    }["useTrail.useIsomorphicLayoutEffect3"], deps);
    if (propsFn || arguments.length == 3) {
        const ref = passedRef ?? result[1];
        ref["_getProps"] = (propsArg2, ctrl, i)=>{
            const props = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].fun(propsArg2) ? propsArg2(i, ctrl) : propsArg2;
            if (props) {
                const parent = ref.current[i + (props.reverse ? 1 : -1)];
                if (parent) props.to = parent.springs;
                return props;
            }
        };
        return result;
    }
    return result[0];
}
;
;
;
function useTransition(data, props, deps) {
    const propsFn = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].fun(props) && props;
    const { reset, sort, trail = 0, expires = true, exitBeforeEnter = false, onDestroyed, ref: propsRef, config: propsConfig } = propsFn ? propsFn() : props;
    const ref = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "useTransition.useMemo2[ref]": ()=>propsFn || arguments.length == 3 ? SpringRef() : void 0
    }["useTransition.useMemo2[ref]"], []);
    const items = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["toArray"])(data);
    const transitions = [];
    const usedTransitions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const prevTransitions = reset ? null : usedTransitions.current;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useIsomorphicLayoutEffect"])({
        "useTransition.useIsomorphicLayoutEffect4": ()=>{
            usedTransitions.current = transitions;
        }
    }["useTransition.useIsomorphicLayoutEffect4"]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useOnce"])({
        "useTransition.useOnce3": ()=>{
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])(transitions, {
                "useTransition.useOnce3": (t)=>{
                    ref?.add(t.ctrl);
                    t.ctrl.ref = ref;
                }
            }["useTransition.useOnce3"]);
            return ({
                "useTransition.useOnce3": ()=>{
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])(usedTransitions.current, {
                        "useTransition.useOnce3": (t)=>{
                            if (t.expired) {
                                clearTimeout(t.expirationId);
                            }
                            detachRefs(t.ctrl, ref);
                            t.ctrl.stop(true);
                        }
                    }["useTransition.useOnce3"]);
                }
            })["useTransition.useOnce3"];
        }
    }["useTransition.useOnce3"]);
    const keys = getKeys(items, propsFn ? propsFn() : props, prevTransitions);
    const expired = reset && usedTransitions.current || [];
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useIsomorphicLayoutEffect"])({
        "useTransition.useIsomorphicLayoutEffect4": ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])(expired, {
                "useTransition.useIsomorphicLayoutEffect4": ({ ctrl, item, key })=>{
                    detachRefs(ctrl, ref);
                    callProp(onDestroyed, item, key);
                }
            }["useTransition.useIsomorphicLayoutEffect4"])
    }["useTransition.useIsomorphicLayoutEffect4"]);
    const reused = [];
    if (prevTransitions) (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])(prevTransitions, (t, i)=>{
        if (t.expired) {
            clearTimeout(t.expirationId);
            expired.push(t);
        } else {
            i = reused[i] = keys.indexOf(t.key);
            if (~i) transitions[i] = t;
        }
    });
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])(items, (item, i)=>{
        if (!transitions[i]) {
            transitions[i] = {
                key: keys[i],
                item,
                phase: "mount" /* MOUNT */ ,
                ctrl: new Controller()
            };
            transitions[i].ctrl.item = item;
        }
    });
    if (reused.length) {
        let i = -1;
        const { leave } = propsFn ? propsFn() : props;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])(reused, (keyIndex, prevIndex)=>{
            const t = prevTransitions[prevIndex];
            if (~keyIndex) {
                i = transitions.indexOf(t);
                transitions[i] = {
                    ...t,
                    item: items[keyIndex]
                };
            } else if (leave) {
                transitions.splice(++i, 0, t);
            }
        });
    }
    if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].fun(sort)) {
        transitions.sort((a, b)=>sort(a.item, b.item));
    }
    let delay = -trail;
    const forceUpdate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useForceUpdate"])();
    const defaultProps = getDefaultProps(props);
    const changes = /* @__PURE__ */ new Map();
    const exitingTransitions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(/* @__PURE__ */ new Map());
    const forceChange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])(transitions, (t, i)=>{
        const key = t.key;
        const prevPhase = t.phase;
        const p = propsFn ? propsFn() : props;
        let to2;
        let phase;
        const propsDelay = callProp(p.delay || 0, key);
        if (prevPhase == "mount" /* MOUNT */ ) {
            to2 = p.enter;
            phase = "enter" /* ENTER */ ;
        } else {
            const isLeave = keys.indexOf(key) < 0;
            if (prevPhase != "leave" /* LEAVE */ ) {
                if (isLeave) {
                    to2 = p.leave;
                    phase = "leave" /* LEAVE */ ;
                } else if (to2 = p.update) {
                    phase = "update" /* UPDATE */ ;
                } else return;
            } else if (!isLeave) {
                to2 = p.enter;
                phase = "enter" /* ENTER */ ;
            } else return;
        }
        to2 = callProp(to2, t.item, i);
        to2 = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].obj(to2) ? inferTo(to2) : {
            to: to2
        };
        if (!to2.config) {
            const config2 = propsConfig || defaultProps.config;
            to2.config = callProp(config2, t.item, i, phase);
        }
        delay += trail;
        const payload = {
            ...defaultProps,
            // we need to add our props.delay value you here.
            delay: propsDelay + delay,
            ref: propsRef,
            immediate: p.immediate,
            // This prevents implied resets.
            reset: false,
            // Merge any phase-specific props.
            ...to2
        };
        if (phase == "enter" /* ENTER */  && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].und(payload.from)) {
            const p2 = propsFn ? propsFn() : props;
            const from = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].und(p2.initial) || prevTransitions ? p2.from : p2.initial;
            payload.from = callProp(from, t.item, i);
        }
        const { onResolve } = payload;
        payload.onResolve = (result)=>{
            callProp(onResolve, result);
            const transitions2 = usedTransitions.current;
            const t2 = transitions2.find((t3)=>t3.key === key);
            if (!t2) return;
            if (result.cancelled && t2.phase != "update" /* UPDATE */ ) {
                return;
            }
            if (t2.ctrl.idle) {
                const idle = transitions2.every((t3)=>t3.ctrl.idle);
                if (t2.phase == "leave" /* LEAVE */ ) {
                    const expiry = callProp(expires, t2.item);
                    if (expiry !== false) {
                        const expiryMs = expiry === true ? 0 : expiry;
                        t2.expired = true;
                        if (!idle && expiryMs > 0) {
                            if (expiryMs <= 2147483647) t2.expirationId = setTimeout(forceUpdate, expiryMs);
                            return;
                        }
                    }
                }
                if (idle && transitions2.some((t3)=>t3.expired)) {
                    exitingTransitions.current.delete(t2);
                    if (exitBeforeEnter) {
                        forceChange.current = true;
                    }
                    forceUpdate();
                }
            }
        };
        const springs = getSprings(t.ctrl, payload);
        if (phase === "leave" /* LEAVE */  && exitBeforeEnter) {
            exitingTransitions.current.set(t, {
                phase,
                springs,
                payload
            });
        } else {
            changes.set(t, {
                phase,
                springs,
                payload
            });
        }
    });
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(SpringContext);
    const prevContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["usePrev"])(context);
    const hasContext = context !== prevContext && hasProps(context);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useIsomorphicLayoutEffect"])({
        "useTransition.useIsomorphicLayoutEffect4": ()=>{
            if (hasContext) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])(transitions, {
                    "useTransition.useIsomorphicLayoutEffect4": (t)=>{
                        t.ctrl.start({
                            default: context
                        });
                    }
                }["useTransition.useIsomorphicLayoutEffect4"]);
            }
        }
    }["useTransition.useIsomorphicLayoutEffect4"], [
        context
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])(changes, (_, t)=>{
        if (exitingTransitions.current.size) {
            const ind = transitions.findIndex((state)=>state.key === t.key);
            transitions.splice(ind, 1);
        }
    });
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useIsomorphicLayoutEffect"])({
        "useTransition.useIsomorphicLayoutEffect4": ()=>{
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])(exitingTransitions.current.size ? exitingTransitions.current : changes, {
                "useTransition.useIsomorphicLayoutEffect4": ({ phase, payload }, t)=>{
                    const { ctrl } = t;
                    t.phase = phase;
                    ref?.add(ctrl);
                    if (hasContext && phase == "enter" /* ENTER */ ) {
                        ctrl.start({
                            default: context
                        });
                    }
                    if (payload) {
                        replaceRef(ctrl, payload.ref);
                        if ((ctrl.ref || ref) && !forceChange.current) {
                            ctrl.update(payload);
                        } else {
                            ctrl.start(payload);
                            if (forceChange.current) {
                                forceChange.current = false;
                            }
                        }
                    }
                }
            }["useTransition.useIsomorphicLayoutEffect4"]);
        }
    }["useTransition.useIsomorphicLayoutEffect4"], reset ? void 0 : deps);
    const renderTransitions = (render)=>/* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], null, transitions.map((t, i)=>{
            const { springs } = changes.get(t) || t.ctrl;
            const elem = render({
                ...springs
            }, t.item, t, i);
            return elem && elem.type ? /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"])(elem.type, {
                ...elem.props,
                key: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].str(t.key) || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].num(t.key) ? t.key : t.ctrl.id,
                ref: elem.ref
            }) : elem;
        }));
    return ref ? [
        renderTransitions,
        ref
    ] : renderTransitions;
}
var nextKey = 1;
function getKeys(items, { key, keys = key }, prevTransitions) {
    if (keys === null) {
        const reused = /* @__PURE__ */ new Set();
        return items.map((item)=>{
            const t = prevTransitions && prevTransitions.find((t2)=>t2.item === item && t2.phase !== "leave" /* LEAVE */  && !reused.has(t2));
            if (t) {
                reused.add(t);
                return t.key;
            }
            return nextKey++;
        });
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].und(keys) ? items : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].fun(keys) ? items.map(keys) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["toArray"])(keys);
}
;
var useScroll = ({ container, ...springOptions } = {})=>{
    const [scrollValues, api] = useSpring({
        "useScroll.useSpring": ()=>({
                scrollX: 0,
                scrollY: 0,
                scrollXProgress: 0,
                scrollYProgress: 0,
                ...springOptions
            })
    }["useScroll.useSpring"], []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useIsomorphicLayoutEffect"])({
        "useScroll.useIsomorphicLayoutEffect5": ()=>{
            const cleanupScroll = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["onScroll"])({
                "useScroll.useIsomorphicLayoutEffect5.cleanupScroll": ({ x, y })=>{
                    api.start({
                        scrollX: x.current,
                        scrollXProgress: x.progress,
                        scrollY: y.current,
                        scrollYProgress: y.progress
                    });
                }
            }["useScroll.useIsomorphicLayoutEffect5.cleanupScroll"], {
                container: container?.current || void 0
            });
            return ({
                "useScroll.useIsomorphicLayoutEffect5": ()=>{
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])(Object.values(scrollValues), {
                        "useScroll.useIsomorphicLayoutEffect5": (value)=>value.stop()
                    }["useScroll.useIsomorphicLayoutEffect5"]);
                    cleanupScroll();
                }
            })["useScroll.useIsomorphicLayoutEffect5"];
        }
    }["useScroll.useIsomorphicLayoutEffect5"], []);
    return scrollValues;
};
;
var useResize = ({ container, ...springOptions })=>{
    const [sizeValues, api] = useSpring({
        "useResize.useSpring": ()=>({
                width: 0,
                height: 0,
                ...springOptions
            })
    }["useResize.useSpring"], []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useIsomorphicLayoutEffect"])({
        "useResize.useIsomorphicLayoutEffect6": ()=>{
            const cleanupScroll = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["onResize"])({
                "useResize.useIsomorphicLayoutEffect6.cleanupScroll": ({ width, height })=>{
                    api.start({
                        width,
                        height,
                        immediate: sizeValues.width.get() === 0 || sizeValues.height.get() === 0
                    });
                }
            }["useResize.useIsomorphicLayoutEffect6.cleanupScroll"], {
                container: container?.current || void 0
            });
            return ({
                "useResize.useIsomorphicLayoutEffect6": ()=>{
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])(Object.values(sizeValues), {
                        "useResize.useIsomorphicLayoutEffect6": (value)=>value.stop()
                    }["useResize.useIsomorphicLayoutEffect6"]);
                    cleanupScroll();
                }
            })["useResize.useIsomorphicLayoutEffect6"];
        }
    }["useResize.useIsomorphicLayoutEffect6"], []);
    return sizeValues;
};
;
;
var defaultThresholdOptions = {
    any: 0,
    all: 1
};
function useInView(props, args) {
    const [isInView, setIsInView] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const ref = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])();
    const propsFn = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].fun(props) && props;
    const springsProps = propsFn ? propsFn() : {};
    const { to: to2 = {}, from = {}, ...restSpringProps } = springsProps;
    const intersectionArguments = propsFn ? args : props;
    const [springs, api] = useSpring({
        "useInView.useSpring": ()=>({
                from,
                ...restSpringProps
            })
    }["useInView.useSpring"], []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useIsomorphicLayoutEffect"])({
        "useInView.useIsomorphicLayoutEffect7": ()=>{
            const element = ref.current;
            const { root, once, amount = "any", ...restArgs } = intersectionArguments ?? {};
            if (!element || once && isInView || typeof IntersectionObserver === "undefined") return;
            const activeIntersections = /* @__PURE__ */ new WeakMap();
            const onEnter = {
                "useInView.useIsomorphicLayoutEffect7.onEnter": ()=>{
                    if (to2) {
                        api.start(to2);
                    }
                    setIsInView(true);
                    const cleanup = {
                        "useInView.useIsomorphicLayoutEffect7.onEnter.cleanup": ()=>{
                            if (from) {
                                api.start(from);
                            }
                            setIsInView(false);
                        }
                    }["useInView.useIsomorphicLayoutEffect7.onEnter.cleanup"];
                    return once ? void 0 : cleanup;
                }
            }["useInView.useIsomorphicLayoutEffect7.onEnter"];
            const handleIntersection = {
                "useInView.useIsomorphicLayoutEffect7.handleIntersection": (entries)=>{
                    entries.forEach({
                        "useInView.useIsomorphicLayoutEffect7.handleIntersection": (entry)=>{
                            const onLeave = activeIntersections.get(entry.target);
                            if (entry.isIntersecting === Boolean(onLeave)) {
                                return;
                            }
                            if (entry.isIntersecting) {
                                const newOnLeave = onEnter();
                                if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].fun(newOnLeave)) {
                                    activeIntersections.set(entry.target, newOnLeave);
                                } else {
                                    observer.unobserve(entry.target);
                                }
                            } else if (onLeave) {
                                onLeave();
                                activeIntersections.delete(entry.target);
                            }
                        }
                    }["useInView.useIsomorphicLayoutEffect7.handleIntersection"]);
                }
            }["useInView.useIsomorphicLayoutEffect7.handleIntersection"];
            const observer = new IntersectionObserver(handleIntersection, {
                root: root && root.current || void 0,
                threshold: typeof amount === "number" || Array.isArray(amount) ? amount : defaultThresholdOptions[amount],
                ...restArgs
            });
            observer.observe(element);
            return ({
                "useInView.useIsomorphicLayoutEffect7": ()=>observer.unobserve(element)
            })["useInView.useIsomorphicLayoutEffect7"];
        }
    }["useInView.useIsomorphicLayoutEffect7"], [
        intersectionArguments
    ]);
    if (propsFn) {
        return [
            ref,
            springs
        ];
    }
    return [
        ref,
        isInView
    ];
}
// src/components/Spring.tsx
function Spring({ children, ...props }) {
    return children(useSpring(props));
}
;
function Trail({ items, children, ...props }) {
    const trails = useTrail(items.length, props);
    return items.map((item, index)=>{
        const result = children(item, index);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].fun(result) ? result(trails[index]) : result;
    });
}
// src/components/Transition.tsx
function Transition({ items, children, ...props }) {
    return useTransition(items, props)(children);
}
;
;
;
var Interpolation = class extends FrameValue {
    constructor(source, args){
        super();
        this.source = source;
        /** Equals false when in the frameloop */ this.idle = true;
        /** The inputs which are currently animating */ this._active = /* @__PURE__ */ new Set();
        this.calc = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createInterpolator"])(...args);
        const value = this._get();
        const nodeType = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$animated$2f$dist$2f$react$2d$spring_animated$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAnimatedType"])(value);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$animated$2f$dist$2f$react$2d$spring_animated$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setAnimated"])(this, nodeType.create(value));
    }
    advance(_dt) {
        const value = this._get();
        const oldValue = this.get();
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["isEqual"])(value, oldValue)) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$animated$2f$dist$2f$react$2d$spring_animated$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAnimated"])(this).setValue(value);
            this._onChange(value, this.idle);
        }
        if (!this.idle && checkIdle(this._active)) {
            becomeIdle(this);
        }
    }
    _get() {
        const inputs = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["is"].arr(this.source) ? this.source.map(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getFluidValue"]) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["toArray"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["getFluidValue"])(this.source));
        return this.calc(...inputs);
    }
    _start() {
        if (this.idle && !checkIdle(this._active)) {
            this.idle = false;
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$animated$2f$dist$2f$react$2d$spring_animated$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getPayload"])(this), (node)=>{
                node.done = false;
            });
            if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["Globals"].skipAnimation) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$rafz$2f$dist$2f$react$2d$spring_rafz$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["raf"].batchedUpdates(()=>this.advance());
                becomeIdle(this);
            } else {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["frameLoop"].start(this);
            }
        }
    }
    // Observe our sources only when we're observed.
    _attach() {
        let priority = 1;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["toArray"])(this.source), (source)=>{
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["hasFluidValue"])(source)) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["addFluidObserver"])(source, this);
            }
            if (isFrameValue(source)) {
                if (!source.idle) {
                    this._active.add(source);
                }
                priority = Math.max(priority, source.priority + 1);
            }
        });
        this.priority = priority;
        this._start();
    }
    // Stop observing our sources once we have no observers.
    _detach() {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["toArray"])(this.source), (source)=>{
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["hasFluidValue"])(source)) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["removeFluidObserver"])(source, this);
            }
        });
        this._active.clear();
        becomeIdle(this);
    }
    /** @internal */ eventObserved(event) {
        if (event.type == "change") {
            if (event.idle) {
                this.advance();
            } else {
                this._active.add(event.parent);
                this._start();
            }
        } else if (event.type == "idle") {
            this._active.delete(event.parent);
        } else if (event.type == "priority") {
            this.priority = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["toArray"])(this.source).reduce((highest, parent)=>Math.max(highest, (isFrameValue(parent) ? parent.priority : 0) + 1), 0);
        }
    }
};
function isIdle(source) {
    return source.idle !== false;
}
function checkIdle(active) {
    return !active.size || Array.from(active).every(isIdle);
}
function becomeIdle(self) {
    if (!self.idle) {
        self.idle = true;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["each"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$animated$2f$dist$2f$react$2d$spring_animated$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getPayload"])(self), (node)=>{
            node.done = true;
        });
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["callFluidObservers"])(self, {
            type: "idle",
            parent: self
        });
    }
}
// src/interpolate.ts
var to = (source, ...args)=>new Interpolation(source, args);
var interpolate = (source, ...args)=>((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["deprecateInterpolate"])(), new Interpolation(source, args));
;
__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["Globals"].assign({
    createStringInterpolator: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createStringInterpolator"],
    to: (source, args)=>new Interpolation(source, args)
});
var update = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["frameLoop"].advance;
;
;
;
 //# sourceMappingURL=react-spring_core.modern.mjs.map
}}),
"[project]/node_modules/@react-spring/core/dist/react-spring_core.modern.mjs [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@react-spring/shared/dist/react-spring_shared.modern.mjs [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$animated$2f$dist$2f$react$2d$spring_animated$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-spring/animated/dist/react-spring_animated.modern.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$types$2f$dist$2f$react$2d$spring_types$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-spring/types/dist/react-spring_types.modern.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$core$2f$dist$2f$react$2d$spring_core$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@react-spring/core/dist/react-spring_core.modern.mjs [app-client] (ecmascript) <locals>");
}}),
"[project]/node_modules/@react-three/fiber/dist/events-776716bd.esm.js [app-client] (ecmascript) <export k as applyProps>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "applyProps": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$776716bd$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["k"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$776716bd$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-three/fiber/dist/events-776716bd.esm.js [app-client] (ecmascript)");
}}),
"[project]/node_modules/@react-three/fiber/dist/events-776716bd.esm.js [app-client] (ecmascript) <export o as addEffect>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "addEffect": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$776716bd$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["o"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$776716bd$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-three/fiber/dist/events-776716bd.esm.js [app-client] (ecmascript)");
}}),
"[project]/node_modules/@react-spring/three/dist/react-spring_three.modern.mjs [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// src/index.ts
__turbopack_context__.s({
    "a": (()=>animated),
    "animated": (()=>animated)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$776716bd$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__k__as__applyProps$3e$__ = __turbopack_context__.i("[project]/node_modules/@react-three/fiber/dist/events-776716bd.esm.js [app-client] (ecmascript) <export k as applyProps>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$776716bd$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__o__as__addEffect$3e$__ = __turbopack_context__.i("[project]/node_modules/@react-three/fiber/dist/events-776716bd.esm.js [app-client] (ecmascript) <export o as addEffect>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$core$2f$dist$2f$react$2d$spring_core$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@react-spring/core/dist/react-spring_core.modern.mjs [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@react-spring/shared/dist/react-spring_shared.modern.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@react-spring/shared/dist/react-spring_shared.modern.mjs [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$rafz$2f$dist$2f$react$2d$spring_rafz$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-spring/rafz/dist/react-spring_rafz.modern.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$animated$2f$dist$2f$react$2d$spring_animated$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-spring/animated/dist/react-spring_animated.modern.mjs [app-client] (ecmascript)");
// src/primitives.ts
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$three$2f$build$2f$three$2e$module$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/three/build/three.module.js [app-client] (ecmascript)");
;
;
;
;
;
;
var primitives = [
    "primitive"
].concat(Object.keys(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$three$2f$build$2f$three$2e$module$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__).filter((key)=>/^[A-Z]/.test(key)).map((key)=>key[0].toLowerCase() + key.slice(1)));
;
__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["Globals"].assign({
    createStringInterpolator: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createStringInterpolator"],
    colors: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["colors"],
    frameLoop: "demand"
});
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$776716bd$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__o__as__addEffect$3e$__["addEffect"])(()=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$rafz$2f$dist$2f$react$2d$spring_rafz$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["raf"].advance();
});
var host = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$animated$2f$dist$2f$react$2d$spring_animated$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createHost"])(primitives, {
    // @ts-expect-error r3f related
    applyAnimatedValues: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$three$2f$fiber$2f$dist$2f$events$2d$776716bd$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__k__as__applyProps$3e$__["applyProps"]
});
var animated = host.animated;
;
 //# sourceMappingURL=react-spring_three.modern.mjs.map
}}),
"[project]/node_modules/@react-spring/three/dist/react-spring_three.modern.mjs [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$core$2f$dist$2f$react$2d$spring_core$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@react-spring/core/dist/react-spring_core.modern.mjs [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$shared$2f$dist$2f$react$2d$spring_shared$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@react-spring/shared/dist/react-spring_shared.modern.mjs [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$animated$2f$dist$2f$react$2d$spring_animated$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@react-spring/animated/dist/react-spring_animated.modern.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$react$2d$spring$2f$three$2f$dist$2f$react$2d$spring_three$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@react-spring/three/dist/react-spring_three.modern.mjs [app-client] (ecmascript) <locals>");
}}),
}]);

//# sourceMappingURL=node_modules_8ba9ff21._.js.map