"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[244],{92244:(t,e,a)=>{a.r(e),a.d(e,{default:()=>s});var r=a(95155),i=a(12115),n=a(10697),o=a(97431);let s=t=>{let{color:e="#3fb950",count:a=2e3,size:s=.06,mouseInfluence:l=.05}=t,u=(0,i.useRef)(null),{mouse:c,viewport:h}=(0,n.D)(),[m,f]=(0,i.useState)(null);if((0,i.useEffect)(()=>{let t=new Float32Array(3*a);for(let e=0;e<a;e++){let a=5*Math.random()+.5,r=Math.acos(2*Math.random()-1),i=Math.random()*Math.PI*2;t[3*e]=a*Math.sin(r)*Math.cos(i),t[3*e+1]=a*Math.sin(r)*Math.sin(i),t[3*e+2]=a*Math.cos(r)*.5}f(t)},[a]),(0,n.F)(t=>{u.current&&m&&(u.current.rotation.y+=8e-4,t.camera.position.x=o.MathUtils.lerp(t.camera.position.x,c.x*l,.05),t.camera.position.y=o.MathUtils.lerp(t.camera.position.y,c.y*l,.05),t.camera.lookAt(0,0,0),u.current.material instanceof o.ShaderMaterial&&(u.current.material.uniforms.time.value=t.clock.getElapsedTime(),u.current.material.uniforms.mousePosition.value.set(c.x*h.width/2,c.y*h.height/2)))}),!m)return null;let p=new o.BufferGeometry;p.setAttribute("position",new o.BufferAttribute(m,3));let M=new o.PointsMaterial({color:e,size:s,transparent:!0,opacity:.8,sizeAttenuation:!0});return(0,r.jsx)("points",{ref:u,geometry:p,material:M})}}}]);