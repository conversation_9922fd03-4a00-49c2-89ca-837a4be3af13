{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/app/api/projects/route.ts"], "sourcesContent": ["import { NextResponse } from 'next/server';\n\nexport async function GET(request: Request) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const category = searchParams.get('category');\n    const featured = searchParams.get('featured') === 'true';\n    \n    let projects = getAllProjects();\n    \n    // Filter by category if specified\n    if (category && category !== 'all') {\n      projects = projects.filter(project => \n        project.category.toLowerCase() === category.toLowerCase()\n      );\n    }\n    \n    // Filter by featured if specified\n    if (featured) {\n      projects = projects.filter(project => project.featured);\n    }\n    \n    return NextResponse.json({\n      projects,\n      total: projects.length,\n      categories: getCategories(),\n    });\n\n  } catch (error) {\n    console.error('Projects API error:', error);\n    return NextResponse.json(\n      { error: 'Failed to fetch projects' },\n      { status: 500 }\n    );\n  }\n}\n\nfunction getAllProjects() {\n  return [\n    {\n      id: 1,\n      title: 'AI-Powered Portfolio',\n      description: 'Modern portfolio website with AI integration, 3D components, and real-time GitHub data visualization.',\n      longDescription: 'A cutting-edge portfolio website built with Next.js 15, featuring AI-powered content generation, interactive 3D components using Three.js and Spline, real-time GitHub API integration, and a responsive design optimized for performance.',\n      category: 'Web Development',\n      technologies: ['Next.js', 'TypeScript', 'Three.js', 'Spline', 'Tailwind CSS', 'Gemini AI'],\n      featured: true,\n      status: 'completed',\n      github_url: 'https://github.com/GreenHacker420/portfolio-nextjs',\n      live_url: 'https://greenhacker420.vercel.app',\n      image_url: '/images/projects/portfolio.jpg',\n      screenshots: [\n        '/images/projects/portfolio-1.jpg',\n        '/images/projects/portfolio-2.jpg',\n        '/images/projects/portfolio-3.jpg'\n      ],\n      created_at: '2024-01-15T00:00:00Z',\n      updated_at: new Date().toISOString(),\n      highlights: [\n        'Real-time GitHub API integration',\n        'Interactive 3D keyboard component',\n        'AI-powered content generation',\n        'Responsive design with dark theme',\n        'Performance optimized with Next.js 15'\n      ]\n    },\n    {\n      id: 2,\n      title: 'Intelligent Chat Assistant',\n      description: 'Advanced AI chatbot with natural language processing and context-aware responses.',\n      longDescription: 'A sophisticated chat assistant powered by Google\\'s Gemini AI, featuring advanced conversation capabilities, context awareness, and integration with various APIs for enhanced functionality.',\n      category: 'AI/ML',\n      technologies: ['Python', 'Gemini AI', 'FastAPI', 'React', 'WebSocket', 'Docker'],\n      featured: true,\n      status: 'completed',\n      github_url: 'https://github.com/GreenHacker420/ai-chat-assistant',\n      live_url: 'https://ai-chat-demo.vercel.app',\n      image_url: '/images/projects/ai-chat.jpg',\n      screenshots: [\n        '/images/projects/ai-chat-1.jpg',\n        '/images/projects/ai-chat-2.jpg'\n      ],\n      created_at: '2024-02-01T00:00:00Z',\n      updated_at: '2024-03-15T00:00:00Z',\n      highlights: [\n        'Natural language understanding',\n        'Context-aware conversations',\n        'Real-time messaging with WebSocket',\n        'Multi-language support',\n        'Customizable personality settings'\n      ]\n    },\n    {\n      id: 3,\n      title: 'React 3D Component Library',\n      description: 'Reusable 3D React components for modern web applications.',\n      longDescription: 'A comprehensive library of 3D React components built with Three.js and React Three Fiber, providing developers with easy-to-use, performant 3D elements for web applications.',\n      category: 'Open Source',\n      technologies: ['React', 'Three.js', 'TypeScript', 'Storybook', 'Jest', 'Rollup'],\n      featured: false,\n      status: 'in-progress',\n      github_url: 'https://github.com/GreenHacker420/react-3d-components',\n      live_url: 'https://react-3d-components.vercel.app',\n      image_url: '/images/projects/3d-components.jpg',\n      screenshots: [\n        '/images/projects/3d-components-1.jpg',\n        '/images/projects/3d-components-2.jpg'\n      ],\n      created_at: '2023-11-20T00:00:00Z',\n      updated_at: '2024-01-10T00:00:00Z',\n      highlights: [\n        'Performance optimized components',\n        'TypeScript support',\n        'Comprehensive documentation',\n        'Interactive Storybook demos',\n        'Tree-shakable exports'\n      ]\n    },\n    {\n      id: 4,\n      title: 'E-Commerce Platform',\n      description: 'Full-stack e-commerce solution with modern payment integration.',\n      longDescription: 'A complete e-commerce platform built with modern technologies, featuring user authentication, product management, shopping cart, payment processing, and admin dashboard.',\n      category: 'Web Development',\n      technologies: ['Next.js', 'Node.js', 'PostgreSQL', 'Stripe', 'Redis', 'Docker'],\n      featured: false,\n      status: 'completed',\n      github_url: 'https://github.com/GreenHacker420/ecommerce-platform',\n      live_url: 'https://ecommerce-demo.vercel.app',\n      image_url: '/images/projects/ecommerce.jpg',\n      screenshots: [\n        '/images/projects/ecommerce-1.jpg',\n        '/images/projects/ecommerce-2.jpg',\n        '/images/projects/ecommerce-3.jpg'\n      ],\n      created_at: '2023-08-15T00:00:00Z',\n      updated_at: '2023-12-01T00:00:00Z',\n      highlights: [\n        'Secure payment processing',\n        'Real-time inventory management',\n        'Admin dashboard with analytics',\n        'Mobile-responsive design',\n        'SEO optimized product pages'\n      ]\n    },\n    {\n      id: 5,\n      title: 'Data Visualization Dashboard',\n      description: 'Interactive dashboard for complex data analysis and visualization.',\n      longDescription: 'A powerful data visualization dashboard built with D3.js and React, providing interactive charts, real-time data updates, and customizable visualization options for business intelligence.',\n      category: 'Data Science',\n      technologies: ['React', 'D3.js', 'Python', 'FastAPI', 'PostgreSQL', 'Chart.js'],\n      featured: false,\n      status: 'completed',\n      github_url: 'https://github.com/GreenHacker420/data-dashboard',\n      live_url: 'https://data-viz-dashboard.vercel.app',\n      image_url: '/images/projects/dashboard.jpg',\n      screenshots: [\n        '/images/projects/dashboard-1.jpg',\n        '/images/projects/dashboard-2.jpg'\n      ],\n      created_at: '2023-06-01T00:00:00Z',\n      updated_at: '2023-09-15T00:00:00Z',\n      highlights: [\n        'Interactive data visualizations',\n        'Real-time data updates',\n        'Customizable chart types',\n        'Export functionality',\n        'Responsive design'\n      ]\n    }\n  ];\n}\n\nfunction getCategories() {\n  const projects = getAllProjects();\n  const categories = [...new Set(projects.map(project => project.category))];\n  return ['all', ...categories];\n}\n\nexport async function POST() {\n  return NextResponse.json(\n    { error: 'Method not allowed' },\n    { status: 405 }\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEO,eAAe,IAAI,OAAgB;IACxC,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,WAAW,aAAa,GAAG,CAAC,gBAAgB;QAElD,IAAI,WAAW;QAEf,kCAAkC;QAClC,IAAI,YAAY,aAAa,OAAO;YAClC,WAAW,SAAS,MAAM,CAAC,CAAA,UACzB,QAAQ,QAAQ,CAAC,WAAW,OAAO,SAAS,WAAW;QAE3D;QAEA,kCAAkC;QAClC,IAAI,UAAU;YACZ,WAAW,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ;QACxD;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB;YACA,OAAO,SAAS,MAAM;YACtB,YAAY;QACd;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA2B,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,SAAS;IACP,OAAO;QACL;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,iBAAiB;YACjB,UAAU;YACV,cAAc;gBAAC;gBAAW;gBAAc;gBAAY;gBAAU;gBAAgB;aAAY;YAC1F,UAAU;YACV,QAAQ;YACR,YAAY;YACZ,UAAU;YACV,WAAW;YACX,aAAa;gBACX;gBACA;gBACA;aACD;YACD,YAAY;YACZ,YAAY,IAAI,OAAO,WAAW;YAClC,YAAY;gBACV;gBACA;gBACA;gBACA;gBACA;aACD;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,iBAAiB;YACjB,UAAU;YACV,cAAc;gBAAC;gBAAU;gBAAa;gBAAW;gBAAS;gBAAa;aAAS;YAChF,UAAU;YACV,QAAQ;YACR,YAAY;YACZ,UAAU;YACV,WAAW;YACX,aAAa;gBACX;gBACA;aACD;YACD,YAAY;YACZ,YAAY;YACZ,YAAY;gBACV;gBACA;gBACA;gBACA;gBACA;aACD;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,iBAAiB;YACjB,UAAU;YACV,cAAc;gBAAC;gBAAS;gBAAY;gBAAc;gBAAa;gBAAQ;aAAS;YAChF,UAAU;YACV,QAAQ;YACR,YAAY;YACZ,UAAU;YACV,WAAW;YACX,aAAa;gBACX;gBACA;aACD;YACD,YAAY;YACZ,YAAY;YACZ,YAAY;gBACV;gBACA;gBACA;gBACA;gBACA;aACD;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,iBAAiB;YACjB,UAAU;YACV,cAAc;gBAAC;gBAAW;gBAAW;gBAAc;gBAAU;gBAAS;aAAS;YAC/E,UAAU;YACV,QAAQ;YACR,YAAY;YACZ,UAAU;YACV,WAAW;YACX,aAAa;gBACX;gBACA;gBACA;aACD;YACD,YAAY;YACZ,YAAY;YACZ,YAAY;gBACV;gBACA;gBACA;gBACA;gBACA;aACD;QACH;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,iBAAiB;YACjB,UAAU;YACV,cAAc;gBAAC;gBAAS;gBAAS;gBAAU;gBAAW;gBAAc;aAAW;YAC/E,UAAU;YACV,QAAQ;YACR,YAAY;YACZ,UAAU;YACV,WAAW;YACX,aAAa;gBACX;gBACA;aACD;YACD,YAAY;YACZ,YAAY;YACZ,YAAY;gBACV;gBACA;gBACA;gBACA;gBACA;aACD;QACH;KACD;AACH;AAEA,SAAS;IACP,MAAM,WAAW;IACjB,MAAM,aAAa;WAAI,IAAI,IAAI,SAAS,GAAG,CAAC,CAAA,UAAW,QAAQ,QAAQ;KAAG;IAC1E,OAAO;QAAC;WAAU;KAAW;AAC/B;AAEO,eAAe;IACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;QAAE,OAAO;IAAqB,GAC9B;QAAE,QAAQ;IAAI;AAElB", "debugId": null}}]}