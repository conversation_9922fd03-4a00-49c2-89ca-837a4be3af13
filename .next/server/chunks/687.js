"use strict";exports.id=687,exports.ids=[687],exports.modules={52687:(e,t,i)=>{i.r(t),i.d(t,{default:()=>ir});var n,r,s,a,o=i(10326),u=i(17577),l=i(12533),d=i(95797),h=C(),c=e=>x(e,h),p=C();c.write=e=>x(e,p);var f=C();c.onStart=e=>x(e,f);var m=C();c.onFrame=e=>x(e,m);var g=C();c.onFinish=e=>x(e,g);var y=[];c.setTimeout=(e,t)=>{let i=c.now()+t,n=()=>{let e=y.findIndex(e=>e.cancel==n);~e&&y.splice(e,1),b-=~e?1:0},r={time:i,handler:e,cancel:n};return y.splice(v(i),0,r),b+=1,k(),r};var v=e=>~(~y.findIndex(t=>t.time>e)||~y.length);c.cancel=e=>{f.delete(e),m.delete(e),g.delete(e),h.delete(e),p.delete(e)},c.sync=e=>{P=!0,c.batchedUpdates(e),P=!1},c.throttle=e=>{let t;function i(){try{e(...t)}finally{t=null}}function n(...e){t=e,c.onStart(i)}return n.handler=e,n.cancel=()=>{f.delete(i),t=null},n};var _="undefined"!=typeof window?window.requestAnimationFrame:()=>{};c.use=e=>_=e,c.now="undefined"!=typeof performance?()=>performance.now():Date.now,c.batchedUpdates=e=>e(),c.catch=console.error,c.frameLoop="always",c.advance=()=>{"demand"!==c.frameLoop?console.warn("Cannot call the manual advancement of rafz whilst frameLoop is not set as demand"):A()};var w=-1,b=0,P=!1;function x(e,t){P?(t.delete(e),e(0)):(t.add(e),k())}function k(){w<0&&(w=0,"demand"!==c.frameLoop&&_(S))}function S(){~w&&(_(S),c.batchedUpdates(A))}function A(){let e=w,t=v(w=c.now());if(t&&(M(y.splice(0,t),e=>e.handler()),b-=t),!b){w=-1;return}f.flush(),h.flush(e?Math.min(64,w-e):16.667),m.flush(),p.flush(),g.flush()}function C(){let e=new Set,t=e;return{add(i){b+=t!=e||e.has(i)?0:1,e.add(i)},delete:i=>(b-=t==e&&e.has(i)?1:0,e.delete(i)),flush(i){t.size&&(e=new Set,b-=t.size,M(t,t=>t(i)&&e.add(t)),b+=e.size,t=e)}}}function M(e,t){e.forEach(e=>{try{t(e)}catch(e){c.catch(e)}})}var V=Object.defineProperty,I={};function j(){}((e,t)=>{for(var i in t)V(e,i,{get:t[i],enumerable:!0})})(I,{assign:()=>N,colors:()=>Q,createStringInterpolator:()=>n,skipAnimation:()=>L,to:()=>r,willAdvance:()=>D});var E=(e,t,i)=>Object.defineProperty(e,t,{value:i,writable:!0,configurable:!0}),R={arr:Array.isArray,obj:e=>!!e&&"Object"===e.constructor.name,fun:e=>"function"==typeof e,str:e=>"string"==typeof e,num:e=>"number"==typeof e,und:e=>void 0===e};function z(e,t){if(R.arr(e)){if(!R.arr(t)||e.length!==t.length)return!1;for(let i=0;i<e.length;i++)if(e[i]!==t[i])return!1;return!0}return e===t}var F=(e,t)=>e.forEach(t);function q(e,t,i){if(R.arr(e)){for(let n=0;n<e.length;n++)t.call(i,e[n],`${n}`);return}for(let n in e)e.hasOwnProperty(n)&&t.call(i,e[n],n)}var T=e=>R.und(e)?[]:R.arr(e)?e:[e];function O(e,t){if(e.size){let i=Array.from(e);e.clear(),F(i,t)}}var $=(e,...t)=>O(e,e=>e(...t)),U=()=>"undefined"==typeof window||!window.navigator||/ServerSideRendering|^Deno\//.test(window.navigator.userAgent),Q=null,L=!1,D=j,N=e=>{e.to&&(r=e.to),e.now&&(c.now=e.now),void 0!==e.colors&&(Q=e.colors),null!=e.skipAnimation&&(L=e.skipAnimation),e.createStringInterpolator&&(n=e.createStringInterpolator),e.requestAnimationFrame&&c.use(e.requestAnimationFrame),e.batchedUpdates&&(c.batchedUpdates=e.batchedUpdates),e.willAdvance&&(D=e.willAdvance),e.frameLoop&&(c.frameLoop=e.frameLoop)},W=new Set,Z=[],B=[],G=0,X={get idle(){return!W.size&&!Z.length},start(e){G>e.priority?(W.add(e),c.onStart(Y)):(H(e),c(K))},advance:K,sort(e){if(G)c.onFrame(()=>X.sort(e));else{let t=Z.indexOf(e);~t&&(Z.splice(t,1),J(e))}},clear(){Z=[],W.clear()}};function Y(){W.forEach(H),W.clear(),c(K)}function H(e){Z.includes(e)||J(e)}function J(e){Z.splice(function(e,t){let i=e.findIndex(t);return i<0?e.length:i}(Z,t=>t.priority>e.priority),0,e)}function K(e){let t=B;for(let i=0;i<Z.length;i++){let n=Z[i];G=n.priority,n.idle||(D(n),n.advance(e),n.idle||t.push(n))}return G=0,(B=Z).length=0,(Z=t).length>0}var ee="[-+]?\\d*\\.?\\d+",et=ee+"%";function ei(...e){return"\\(\\s*("+e.join(")\\s*,\\s*(")+")\\s*\\)"}var en=RegExp("rgb"+ei(ee,ee,ee)),er=RegExp("rgba"+ei(ee,ee,ee,ee)),es=RegExp("hsl"+ei(ee,et,et)),ea=RegExp("hsla"+ei(ee,et,et,ee)),eo=/^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,eu=/^#([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,el=/^#([0-9a-fA-F]{6})$/,ed=/^#([0-9a-fA-F]{8})$/;function eh(e,t,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?e+(t-e)*6*i:i<.5?t:i<2/3?e+(t-e)*(2/3-i)*6:e}function ec(e,t,i){let n=i<.5?i*(1+t):i+t-i*t,r=2*i-n;return Math.round(255*eh(r,n,e+1/3))<<24|Math.round(255*eh(r,n,e))<<16|Math.round(255*eh(r,n,e-1/3))<<8}function ep(e){let t=parseInt(e,10);return t<0?0:t>255?255:t}function ef(e){return(parseFloat(e)%360+360)%360/360}function em(e){let t=parseFloat(e);return t<0?0:t>1?255:Math.round(255*t)}function eg(e){let t=parseFloat(e);return t<0?0:t>100?1:t/100}function ey(e){let t;let i="number"==typeof e?e>>>0===e&&e>=0&&e<=4294967295?e:null:(t=el.exec(e))?parseInt(t[1]+"ff",16)>>>0:Q&&void 0!==Q[e]?Q[e]:(t=en.exec(e))?(ep(t[1])<<24|ep(t[2])<<16|ep(t[3])<<8|255)>>>0:(t=er.exec(e))?(ep(t[1])<<24|ep(t[2])<<16|ep(t[3])<<8|em(t[4]))>>>0:(t=eo.exec(e))?parseInt(t[1]+t[1]+t[2]+t[2]+t[3]+t[3]+"ff",16)>>>0:(t=ed.exec(e))?parseInt(t[1],16)>>>0:(t=eu.exec(e))?parseInt(t[1]+t[1]+t[2]+t[2]+t[3]+t[3]+t[4]+t[4],16)>>>0:(t=es.exec(e))?(255|ec(ef(t[1]),eg(t[2]),eg(t[3])))>>>0:(t=ea.exec(e))?(ec(ef(t[1]),eg(t[2]),eg(t[3]))|em(t[4]))>>>0:null;if(null===i)return e;let n=(4278190080&(i=i||0))>>>24,r=(16711680&i)>>>16,s=(65280&i)>>>8,a=(255&i)/255;return`rgba(${n}, ${r}, ${s}, ${a})`}var ev=(e,t,i)=>{if(R.fun(e))return e;if(R.arr(e))return ev({range:e,output:t,extrapolate:i});if(R.str(e.output[0]))return n(e);let r=e.output,s=e.range||[0,1],a=e.extrapolateLeft||e.extrapolate||"extend",o=e.extrapolateRight||e.extrapolate||"extend",u=e.easing||(e=>e);return t=>{let i=function(e,t){for(var i=1;i<t.length-1&&!(t[i]>=e);++i);return i-1}(t,s);return function(e,t,i,n,r,s,a,o,u){let l=u?u(e):e;if(l<t){if("identity"===a)return l;"clamp"===a&&(l=t)}if(l>i){if("identity"===o)return l;"clamp"===o&&(l=i)}return n===r?n:t===i?e<=t?n:r:(t===-1/0?l=-l:i===1/0?l-=t:l=(l-t)/(i-t),l=s(l),n===-1/0?l=-l:r===1/0?l+=n:l=l*(r-n)+n,l)}(t,s[i],s[i+1],r[i],r[i+1],u,a,o,e.map)}},e_=Symbol.for("FluidValue.get"),ew=Symbol.for("FluidValue.observers"),eb=e=>!!(e&&e[e_]),eP=e=>e&&e[e_]?e[e_]():e,ex=e=>e[ew]||null;function ek(e,t){let i=e[ew];i&&i.forEach(e=>{e.eventObserved?e.eventObserved(t):e(t)})}var eS=class{constructor(e){if(!e&&!(e=this.get))throw Error("Unknown getter");eA(this,e)}},eA=(e,t)=>eV(e,e_,t);function eC(e,t){if(e[e_]){let i=e[ew];i||eV(e,ew,i=new Set),!i.has(t)&&(i.add(t),e.observerAdded&&e.observerAdded(i.size,t))}return t}function eM(e,t){let i=e[ew];if(i&&i.has(t)){let n=i.size-1;n?i.delete(t):e[ew]=null,e.observerRemoved&&e.observerRemoved(n,t)}}var eV=(e,t,i)=>Object.defineProperty(e,t,{value:i,writable:!0,configurable:!0}),eI=/[+\-]?(?:0|[1-9]\d*)(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,ej=/(#(?:[0-9a-f]{2}){2,4}|(#[0-9a-f]{3})|(rgb|hsl)a?\((-?\d+%?[,\s]+){2,3}\s*[\d\.]+%?\))/gi,eE=RegExp(`(${eI.source})(%|[a-z]+)`,"i"),eR=/rgba\(([0-9\.-]+), ([0-9\.-]+), ([0-9\.-]+), ([0-9\.-]+)\)/gi,ez=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/,eF=e=>{let[t,i]=eq(e);if(!t||U())return e;let n=window.getComputedStyle(document.documentElement).getPropertyValue(t);if(n)return n.trim();if(i&&i.startsWith("--")){let e=window.getComputedStyle(document.documentElement).getPropertyValue(i);if(e)return e}else if(i&&ez.test(i))return eF(i);else if(i)return i;return e},eq=e=>{let t=ez.exec(e);if(!t)return[,];let[,i,n]=t;return[i,n]},eT=(e,t,i,n,r)=>`rgba(${Math.round(t)}, ${Math.round(i)}, ${Math.round(n)}, ${r})`,eO=e=>{s||(s=Q?RegExp(`(${Object.keys(Q).join("|")})(?!\\w)`,"g"):/^\b$/);let t=e.output.map(e=>eP(e).replace(ez,eF).replace(ej,ey).replace(s,ey)),i=t.map(e=>e.match(eI).map(Number)),n=i[0].map((e,t)=>i.map(e=>{if(!(t in e))throw Error('The arity of each "output" value must be equal');return e[t]})).map(t=>ev({...e,output:t}));return e=>{let i=!eE.test(t[0])&&t.find(e=>eE.test(e))?.replace(eI,""),r=0;return t[0].replace(eI,()=>`${n[r++](e)}${i||""}`).replace(eR,eT)}},e$="react-spring: ",eU=e=>{let t=!1;if("function"!=typeof e)throw TypeError(`${e$}once requires a function parameter`);return(...i)=>{t||(e(...i),t=!0)}},eQ=eU(console.warn),eL=eU(console.warn);function eD(e){return R.str(e)&&("#"==e[0]||/\d/.test(e)||!U()&&ez.test(e)||e in(Q||{}))}var eN=new WeakMap,eW=new Set,eZ=()=>{let e=()=>{eW.forEach(e=>e({width:window.innerWidth,height:window.innerHeight}))};return window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}},eB=U()?u.useEffect:u.useLayoutEffect,eG=()=>{let e=(0,u.useRef)(!1);return eB(()=>(e.current=!0,()=>{e.current=!1}),[]),e};function eX(){let e=(0,u.useState)()[1],t=eG();return()=>{t.current&&e(Math.random())}}var eY=e=>(0,u.useEffect)(e,eH),eH=[];function eJ(e){let t=(0,u.useRef)();return(0,u.useEffect)(()=>{t.current=e}),t.current}var eK=Symbol.for("Animated:node"),e0=e=>!!e&&e[eK]===e,e1=e=>e&&e[eK],e2=(e,t)=>E(e,eK,t),e5=e=>e&&e[eK]&&e[eK].getPayload(),e3=class{constructor(){e2(this,this)}getPayload(){return this.payload||[]}},e4=class extends e3{constructor(e){super(),this._value=e,this.done=!0,this.durationProgress=0,R.num(this._value)&&(this.lastPosition=this._value)}static create(e){return new e4(e)}getPayload(){return[this]}getValue(){return this._value}setValue(e,t){return R.num(e)&&(this.lastPosition=e,t&&(e=Math.round(e/t)*t,this.done&&(this.lastPosition=e))),this._value!==e&&(this._value=e,!0)}reset(){let{done:e}=this;this.done=!1,R.num(this._value)&&(this.elapsedTime=0,this.durationProgress=0,this.lastPosition=this._value,e&&(this.lastVelocity=null),this.v0=null)}},e9=class extends e4{constructor(e){super(0),this._string=null,this._toString=ev({output:[e,e]})}static create(e){return new e9(e)}getValue(){let e=this._string;return null==e?this._string=this._toString(this._value):e}setValue(e){if(R.str(e)){if(e==this._string)return!1;this._string=e,this._value=1}else{if(!super.setValue(e))return!1;this._string=null}return!0}reset(e){e&&(this._toString=ev({output:[this.getValue(),e]})),this._value=0,super.reset()}},e7={dependencies:null},e8=class extends e3{constructor(e){super(),this.source=e,this.setValue(e)}getValue(e){let t={};return q(this.source,(i,n)=>{e0(i)?t[n]=i.getValue(e):eb(i)?t[n]=eP(i):e||(t[n]=i)}),t}setValue(e){this.source=e,this.payload=this._makePayload(e)}reset(){this.payload&&F(this.payload,e=>e.reset())}_makePayload(e){if(e){let t=new Set;return q(e,this._addToPayload,t),Array.from(t)}}_addToPayload(e){e7.dependencies&&eb(e)&&e7.dependencies.add(e);let t=e5(e);t&&F(t,e=>this.add(e))}},e6=class extends e8{constructor(e){super(e)}static create(e){return new e6(e)}getValue(){return this.source.map(e=>e.getValue())}setValue(e){let t=this.getPayload();return e.length==t.length?t.map((t,i)=>t.setValue(e[i])).some(Boolean):(super.setValue(e.map(te)),!0)}};function te(e){return(eD(e)?e9:e4).create(e)}function tt(e){let t=e1(e);return t?t.constructor:R.arr(e)?e6:eD(e)?e9:e4}var ti=(e,t)=>{let i=!R.fun(e)||e.prototype&&e.prototype.isReactComponent;return(0,u.forwardRef)((n,r)=>{let s=(0,u.useRef)(null),a=i&&(0,u.useCallback)(e=>{s.current=function(e,t){return e&&(R.fun(e)?e(t):e.current=t),t}(r,e)},[r]),[o,l]=function(e,t){let i=new Set;return e7.dependencies=i,e.style&&(e={...e,style:t.createAnimatedStyle(e.style)}),e=new e8(e),e7.dependencies=null,[e,i]}(n,t),d=eX(),h=()=>{let e=s.current;(!i||e)&&!1===(!!e&&t.applyAnimatedValues(e,o.getValue(!0)))&&d()},p=new tn(h,l),f=(0,u.useRef)();eB(()=>(f.current=p,F(l,e=>eC(e,p)),()=>{f.current&&(F(f.current.deps,e=>eM(e,f.current)),c.cancel(f.current.update))})),(0,u.useEffect)(h,[]),eY(()=>()=>{let e=f.current;F(e.deps,t=>eM(t,e))});let m=t.getComponentProps(o.getValue());return u.createElement(e,{...m,ref:a})})},tn=class{constructor(e,t){this.update=e,this.deps=t}eventObserved(e){"change"==e.type&&c.write(this.update)}},tr=Symbol.for("AnimatedComponent"),ts=e=>R.str(e)?e:e&&R.str(e.displayName)?e.displayName:R.fun(e)&&e.name||null;function ta(e,...t){return R.fun(e)?e(...t):e}var to=(e,t)=>!0===e||!!(t&&e&&(R.fun(e)?e(t):T(e).includes(t))),tu=(e,t)=>R.obj(e)?t&&e[t]:e,tl=(e,t)=>!0===e.default?e[t]:e.default?e.default[t]:void 0,td=e=>e,th=(e,t=td)=>{let i=tc;e.default&&!0!==e.default&&(i=Object.keys(e=e.default));let n={};for(let r of i){let i=t(e[r],r);R.und(i)||(n[r]=i)}return n},tc=["config","onProps","onStart","onChange","onPause","onResume","onRest"],tp={config:1,from:1,to:1,ref:1,loop:1,reset:1,pause:1,cancel:1,reverse:1,immediate:1,default:1,delay:1,onProps:1,onStart:1,onChange:1,onPause:1,onResume:1,onRest:1,onResolve:1,items:1,trail:1,sort:1,expires:1,initial:1,enter:1,update:1,leave:1,children:1,onDestroyed:1,keys:1,callId:1,parentId:1};function tf(e){let t=function(e){let t={},i=0;if(q(e,(e,n)=>{!tp[n]&&(t[n]=e,i++)}),i)return t}(e);if(t){let i={to:t};return q(e,(e,n)=>n in t||(i[n]=e)),i}return{...e}}function tm(e){return e=eP(e),R.arr(e)?e.map(tm):eD(e)?I.createStringInterpolator({range:[0,1],output:[e,e]})(1):e}function tg(e){return R.fun(e)||R.arr(e)&&R.obj(e[0])}var ty={tension:170,friction:26,mass:1,damping:1,easing:e=>e,clamp:!1},tv=class{constructor(){this.velocity=0,Object.assign(this,ty)}};function t_(e,t){if(R.und(t.decay)){let i=!R.und(t.tension)||!R.und(t.friction);!i&&R.und(t.frequency)&&R.und(t.damping)&&R.und(t.mass)||(e.duration=void 0,e.decay=void 0),i&&(e.frequency=void 0)}else e.duration=void 0}var tw=[],tb=class{constructor(){this.changed=!1,this.values=tw,this.toValues=null,this.fromValues=tw,this.config=new tv,this.immediate=!1}};function tP(e,{key:t,props:i,defaultProps:n,state:r,actions:s}){return new Promise((a,o)=>{let u,l;let d=to(i.cancel??n?.cancel,t);if(d)f();else{R.und(i.pause)||(r.paused=to(i.pause,t));let e=n?.pause;!0!==e&&(e=r.paused||to(e,t)),u=ta(i.delay||0,t),e?(r.resumeQueue.add(p),s.pause()):(s.resume(),p())}function h(){r.resumeQueue.add(p),r.timeouts.delete(l),l.cancel(),u=l.time-c.now()}function p(){u>0&&!I.skipAnimation?(r.delayed=!0,l=c.setTimeout(f,u),r.pauseQueue.add(h),r.timeouts.add(l)):f()}function f(){r.delayed&&(r.delayed=!1),r.pauseQueue.delete(h),r.timeouts.delete(l),e<=(r.cancelId||0)&&(d=!0);try{s.start({...i,callId:e,cancel:d},a)}catch(e){o(e)}}})}var tx=(e,t)=>1==t.length?t[0]:t.some(e=>e.cancelled)?tA(e.get()):t.every(e=>e.noop)?tk(e.get()):tS(e.get(),t.every(e=>e.finished)),tk=e=>({value:e,noop:!0,finished:!0,cancelled:!1}),tS=(e,t,i=!1)=>({value:e,finished:t,cancelled:i}),tA=e=>({value:e,cancelled:!0,finished:!1});function tC(e,t,i,n){let{callId:r,parentId:s,onRest:a}=t,{asyncTo:o,promise:u}=i;return s||e!==o||t.reset?i.promise=(async()=>{let l,d,h;i.asyncId=r,i.asyncTo=e;let p=th(t,(e,t)=>"onRest"===t?void 0:e),f=new Promise((e,t)=>(l=e,d=t)),m=e=>{let t=r<=(i.cancelId||0)&&tA(n)||r!==i.asyncId&&tS(n,!1);if(t)throw e.result=t,d(e),e},g=(e,t)=>{let s=new tV,a=new tI;return(async()=>{if(I.skipAnimation)throw tM(i),a.result=tS(n,!1),d(a),a;m(s);let o=R.obj(e)?{...e}:{...t,to:e};o.parentId=r,q(p,(e,t)=>{R.und(o[t])&&(o[t]=e)});let u=await n.start(o);return m(s),i.paused&&await new Promise(e=>{i.resumeQueue.add(e)}),u})()};if(I.skipAnimation)return tM(i),tS(n,!1);try{let t;t=R.arr(e)?(async e=>{for(let t of e)await g(t)})(e):Promise.resolve(e(g,n.stop.bind(n))),await Promise.all([t.then(l),f]),h=tS(n.get(),!0,!1)}catch(e){if(e instanceof tV)h=e.result;else if(e instanceof tI)h=e.result;else throw e}finally{r==i.asyncId&&(i.asyncId=s,i.asyncTo=s?o:void 0,i.promise=s?u:void 0)}return R.fun(a)&&c.batchedUpdates(()=>{a(h,n,n.item)}),h})():u}function tM(e,t){O(e.timeouts,e=>e.cancel()),e.pauseQueue.clear(),e.resumeQueue.clear(),e.asyncId=e.asyncTo=e.promise=void 0,t&&(e.cancelId=t)}var tV=class extends Error{constructor(){super("An async animation has been interrupted. You see this error because you forgot to use `await` or `.catch(...)` on its returned promise.")}},tI=class extends Error{constructor(){super("SkipAnimationSignal")}},tj=e=>e instanceof tR,tE=1,tR=class extends eS{constructor(){super(...arguments),this.id=tE++,this._priority=0}get priority(){return this._priority}set priority(e){this._priority!=e&&(this._priority=e,this._onPriorityChange(e))}get(){let e=e1(this);return e&&e.getValue()}to(...e){return I.to(this,e)}interpolate(...e){return eQ(`${e$}The "interpolate" function is deprecated in v9 (use "to" instead)`),I.to(this,e)}toJSON(){return this.get()}observerAdded(e){1==e&&this._attach()}observerRemoved(e){0==e&&this._detach()}_attach(){}_detach(){}_onChange(e,t=!1){ek(this,{type:"change",parent:this,value:e,idle:t})}_onPriorityChange(e){this.idle||X.sort(this),ek(this,{type:"priority",parent:this,priority:e})}},tz=Symbol.for("SpringPhase"),tF=e=>(1&e[tz])>0,tq=e=>(2&e[tz])>0,tT=e=>(4&e[tz])>0,tO=(e,t)=>t?e[tz]|=3:e[tz]&=-3,t$=(e,t)=>t?e[tz]|=4:e[tz]&=-5,tU=class extends tR{constructor(e,t){if(super(),this.animation=new tb,this.defaultProps={},this._state={paused:!1,delayed:!1,pauseQueue:new Set,resumeQueue:new Set,timeouts:new Set},this._pendingCalls=new Set,this._lastCallId=0,this._lastToId=0,this._memoizedDuration=0,!R.und(e)||!R.und(t)){let i=R.obj(e)?{...e}:{...t,from:e};R.und(i.default)&&(i.default=!0),this.start(i)}}get idle(){return!(tq(this)||this._state.asyncTo)||tT(this)}get goal(){return eP(this.animation.to)}get velocity(){let e=e1(this);return e instanceof e4?e.lastVelocity||0:e.getPayload().map(e=>e.lastVelocity||0)}get hasAnimated(){return tF(this)}get isAnimating(){return tq(this)}get isPaused(){return tT(this)}get isDelayed(){return this._state.delayed}advance(e){let t=!0,i=!1,n=this.animation,{toValues:r}=n,{config:s}=n,a=e5(n.to);!a&&eb(n.to)&&(r=T(eP(n.to))),n.values.forEach((o,u)=>{if(o.done)return;let l=o.constructor==e9?1:a?a[u].lastPosition:r[u],d=n.immediate,h=l;if(!d){let t;if(h=o.lastPosition,s.tension<=0){o.done=!0;return}let i=o.elapsedTime+=e,r=n.fromValues[u],a=null!=o.v0?o.v0:o.v0=R.arr(s.velocity)?s.velocity[u]:s.velocity,c=s.precision||(r==l?.005:Math.min(1,.001*Math.abs(l-r)));if(R.und(s.duration)){if(s.decay){let e=!0===s.decay?.998:s.decay,n=Math.exp(-(1-e)*i);h=r+a/(1-e)*(1-n),d=Math.abs(o.lastPosition-h)<=c,t=a*n}else{t=null==o.lastVelocity?a:o.lastVelocity;let i=s.restVelocity||c/10,n=s.clamp?0:s.bounce,u=!R.und(n),p=r==l?o.v0>0:r<l,f=Math.ceil(e/1);for(let e=0;e<f&&!(!(Math.abs(t)>i)&&(d=Math.abs(l-h)<=c));++e){u&&(h==l||h>l==p)&&(t=-t*n,h=l);let e=(-(1e-6*s.tension)*(h-l)+-(.001*s.friction)*t)/s.mass;t+=1*e,h+=1*t}}}else{let n=1;s.duration>0&&(this._memoizedDuration!==s.duration&&(this._memoizedDuration=s.duration,o.durationProgress>0&&(o.elapsedTime=s.duration*o.durationProgress,i=o.elapsedTime+=e)),n=(n=(s.progress||0)+i/this._memoizedDuration)>1?1:n<0?0:n,o.durationProgress=n),t=((h=r+s.easing(n)*(l-r))-o.lastPosition)/e,d=1==n}o.lastVelocity=t,Number.isNaN(h)&&(console.warn("Got NaN while animating:",this),d=!0)}a&&!a[u].done&&(d=!1),d?o.done=!0:t=!1,o.setValue(h,s.round)&&(i=!0)});let o=e1(this),u=o.getValue();if(t){let e=eP(n.to);(u!==e||i)&&!s.decay?(o.setValue(e),this._onChange(e)):i&&s.decay&&this._onChange(u),this._stop()}else i&&this._onChange(u)}set(e){return c.batchedUpdates(()=>{this._stop(),this._focus(e),this._set(e)}),this}pause(){this._update({pause:!0})}resume(){this._update({pause:!1})}finish(){if(tq(this)){let{to:e,config:t}=this.animation;c.batchedUpdates(()=>{this._onStart(),t.decay||this._set(e,!1),this._stop()})}return this}update(e){return(this.queue||(this.queue=[])).push(e),this}start(e,t){let i;return R.und(e)?(i=this.queue||[],this.queue=[]):i=[R.obj(e)?e:{...t,to:e}],Promise.all(i.map(e=>this._update(e))).then(e=>tx(this,e))}stop(e){let{to:t}=this.animation;return this._focus(this.get()),tM(this._state,e&&this._lastCallId),c.batchedUpdates(()=>this._stop(t,e)),this}reset(){this._update({reset:!0})}eventObserved(e){"change"==e.type?this._start():"priority"==e.type&&(this.priority=e.priority+1)}_prepareNode(e){let t=this.key||"",{to:i,from:n}=e;(null==(i=R.obj(i)?i[t]:i)||tg(i))&&(i=void 0),null==(n=R.obj(n)?n[t]:n)&&(n=void 0);let r={to:i,from:n};return tF(this)||(e.reverse&&([i,n]=[n,i]),n=eP(n),R.und(n)?e1(this)||this._set(i):this._set(n)),r}_update({...e},t){let{key:i,defaultProps:n}=this;e.default&&Object.assign(n,th(e,(e,t)=>/^on/.test(t)?tu(e,i):e)),tZ(this,e,"onProps"),tB(this,"onProps",e,this);let r=this._prepareNode(e);if(Object.isFrozen(this))throw Error("Cannot animate a `SpringValue` object that is frozen. Did you forget to pass your component to `animated(...)` before animating its props?");let s=this._state;return tP(++this._lastCallId,{key:i,props:e,defaultProps:n,state:s,actions:{pause:()=>{tT(this)||(t$(this,!0),$(s.pauseQueue),tB(this,"onPause",tS(this,tQ(this,this.animation.to)),this))},resume:()=>{tT(this)&&(t$(this,!1),tq(this)&&this._resume(),$(s.resumeQueue),tB(this,"onResume",tS(this,tQ(this,this.animation.to)),this))},start:this._merge.bind(this,r)}}).then(i=>{if(e.loop&&i.finished&&!(t&&i.noop)){let t=tL(e);if(t)return this._update(t,!0)}return i})}_merge(e,t,i){if(t.cancel)return this.stop(!0),i(tA(this));let n=!R.und(e.to),r=!R.und(e.from);if(n||r){if(!(t.callId>this._lastToId))return i(tA(this));this._lastToId=t.callId}let{key:s,defaultProps:a,animation:o}=this,{to:u,from:l}=o,{to:d=u,from:h=l}=e;r&&!n&&(!t.default||R.und(d))&&(d=h),t.reverse&&([d,h]=[h,d]);let p=!z(h,l);p&&(o.from=h),h=eP(h);let f=!z(d,u);f&&this._focus(d);let m=tg(t.to),{config:g}=o,{decay:y,velocity:v}=g;(n||r)&&(g.velocity=0),t.config&&!m&&function(e,t,i){for(let n in i&&(t_(i={...i},t),t={...i,...t}),t_(e,t),Object.assign(e,t),ty)null==e[n]&&(e[n]=ty[n]);let{frequency:n,damping:r}=e,{mass:s}=e;R.und(n)||(n<.01&&(n=.01),r<0&&(r=0),e.tension=Math.pow(2*Math.PI/n,2)*s,e.friction=4*Math.PI*r*s/n)}(g,ta(t.config,s),t.config!==a.config?ta(a.config,s):void 0);let _=e1(this);if(!_||R.und(d))return i(tS(this,!0));let w=R.und(t.reset)?r&&!t.default:!R.und(h)&&to(t.reset,s),b=w?h:this.get(),P=tm(d),x=R.num(P)||R.arr(P)||eD(P),k=!m&&(!x||to(a.immediate||t.immediate,s));if(f){let e=tt(d);if(e!==_.constructor){if(k)_=this._set(P);else throw Error(`Cannot animate between ${_.constructor.name} and ${e.name}, as the "to" prop suggests`)}}let S=_.constructor,A=eb(d),C=!1;if(!A){let e=w||!tF(this)&&p;(f||e)&&(A=!(C=z(tm(b),P))),(z(o.immediate,k)||k)&&z(g.decay,y)&&z(g.velocity,v)||(A=!0)}if(C&&tq(this)&&(o.changed&&!w?A=!0:A||this._stop(u)),!m&&((A||eb(u))&&(o.values=_.getPayload(),o.toValues=eb(d)?null:S==e9?[1]:T(P)),o.immediate==k||(o.immediate=k,k||w||this._set(u)),A)){let{onRest:e}=o;F(tW,e=>tZ(this,t,e));let n=tS(this,tQ(this,u));$(this._pendingCalls,n),this._pendingCalls.add(i),o.changed&&c.batchedUpdates(()=>{o.changed=!w,e?.(n,this),w?ta(a.onRest,n):o.onStart?.(n,this)})}w&&this._set(b),m?i(tC(t.to,t,this._state,this)):A?this._start():tq(this)&&!f?this._pendingCalls.add(i):i(tk(b))}_focus(e){let t=this.animation;e!==t.to&&(ex(this)&&this._detach(),t.to=e,ex(this)&&this._attach())}_attach(){let e=0,{to:t}=this.animation;eb(t)&&(eC(t,this),tj(t)&&(e=t.priority+1)),this.priority=e}_detach(){let{to:e}=this.animation;eb(e)&&eM(e,this)}_set(e,t=!0){let i=eP(e);if(!R.und(i)){let e=e1(this);if(!e||!z(i,e.getValue())){let n=tt(i);e&&e.constructor==n?e.setValue(i):e2(this,n.create(i)),e&&c.batchedUpdates(()=>{this._onChange(i,t)})}}return e1(this)}_onStart(){let e=this.animation;e.changed||(e.changed=!0,tB(this,"onStart",tS(this,tQ(this,e.to)),this))}_onChange(e,t){t||(this._onStart(),ta(this.animation.onChange,e,this)),ta(this.defaultProps.onChange,e,this),super._onChange(e,t)}_start(){let e=this.animation;e1(this).reset(eP(e.to)),e.immediate||(e.fromValues=e.values.map(e=>e.lastPosition)),tq(this)||(tO(this,!0),tT(this)||this._resume())}_resume(){I.skipAnimation?this.finish():X.start(this)}_stop(e,t){if(tq(this)){tO(this,!1);let i=this.animation;F(i.values,e=>{e.done=!0}),i.toValues&&(i.onChange=i.onPause=i.onResume=void 0),ek(this,{type:"idle",parent:this});let n=t?tA(this.get()):tS(this.get(),tQ(this,e??i.to));$(this._pendingCalls,n),i.changed&&(i.changed=!1,tB(this,"onRest",n,this))}}};function tQ(e,t){let i=tm(t);return z(tm(e.get()),i)}function tL(e,t=e.loop,i=e.to){let n=ta(t);if(n){let r=!0!==n&&tf(n),s=(r||e).reverse,a=!r||r.reset;return tD({...e,loop:t,default:!1,pause:void 0,to:!s||tg(i)?i:void 0,from:a?e.from:void 0,reset:a,...r})}}function tD(e){let{to:t,from:i}=e=tf(e),n=new Set;return R.obj(t)&&tN(t,n),R.obj(i)&&tN(i,n),e.keys=n.size?Array.from(n):null,e}function tN(e,t){q(e,(e,i)=>null!=e&&t.add(i))}var tW=["onStart","onRest","onChange","onPause","onResume"];function tZ(e,t,i){e.animation[i]=t[i]!==tl(t,i)?tu(t[i],e.key):void 0}function tB(e,t,...i){e.animation[t]?.(...i),e.defaultProps[t]?.(...i)}var tG=["onStart","onChange","onRest"],tX=1,tY=class{constructor(e,t){this.id=tX++,this.springs={},this.queue=[],this._lastAsyncId=0,this._active=new Set,this._changed=new Set,this._started=!1,this._state={paused:!1,pauseQueue:new Set,resumeQueue:new Set,timeouts:new Set},this._events={onStart:new Map,onChange:new Map,onRest:new Map},this._onFrame=this._onFrame.bind(this),t&&(this._flush=t),e&&this.start({default:!0,...e})}get idle(){return!this._state.asyncTo&&Object.values(this.springs).every(e=>e.idle&&!e.isDelayed&&!e.isPaused)}get item(){return this._item}set item(e){this._item=e}get(){let e={};return this.each((t,i)=>e[i]=t.get()),e}set(e){for(let t in e){let i=e[t];R.und(i)||this.springs[t].set(i)}}update(e){return e&&this.queue.push(tD(e)),this}start(e){let{queue:t}=this;return(e?t=T(e).map(tD):this.queue=[],this._flush)?this._flush(this,t):(t5(this,t),tH(this,t))}stop(e,t){if(!!e!==e&&(t=e),t){let i=this.springs;F(T(t),t=>i[t].stop(!!e))}else tM(this._state,this._lastAsyncId),this.each(t=>t.stop(!!e));return this}pause(e){if(R.und(e))this.start({pause:!0});else{let t=this.springs;F(T(e),e=>t[e].pause())}return this}resume(e){if(R.und(e))this.start({pause:!1});else{let t=this.springs;F(T(e),e=>t[e].resume())}return this}each(e){q(this.springs,e)}_onFrame(){let{onStart:e,onChange:t,onRest:i}=this._events,n=this._active.size>0,r=this._changed.size>0;(n&&!this._started||r&&!this._started)&&(this._started=!0,O(e,([e,t])=>{t.value=this.get(),e(t,this,this._item)}));let s=!n&&this._started,a=r||s&&i.size?this.get():null;r&&t.size&&O(t,([e,t])=>{t.value=a,e(t,this,this._item)}),s&&(this._started=!1,O(i,([e,t])=>{t.value=a,e(t,this,this._item)}))}eventObserved(e){if("change"==e.type)this._changed.add(e.parent),e.idle||this._active.add(e.parent);else{if("idle"!=e.type)return;this._active.delete(e.parent)}c.onFrame(this._onFrame)}};function tH(e,t){return Promise.all(t.map(t=>tJ(e,t))).then(t=>tx(e,t))}async function tJ(e,t,i){let{keys:n,to:r,from:s,loop:a,onRest:o,onResolve:u}=t,l=R.obj(t.default)&&t.default;a&&(t.loop=!1),!1===r&&(t.to=null),!1===s&&(t.from=null);let d=R.arr(r)||R.fun(r)?r:void 0;d?(t.to=void 0,t.onRest=void 0,l&&(l.onRest=void 0)):F(tG,i=>{let n=t[i];if(R.fun(n)){let r=e._events[i];t[i]=({finished:e,cancelled:t})=>{let i=r.get(n);i?(e||(i.finished=!1),t&&(i.cancelled=!0)):r.set(n,{value:null,finished:e||!1,cancelled:t||!1})},l&&(l[i]=t[i])}});let h=e._state;!h.paused===t.pause?(h.paused=t.pause,$(t.pause?h.pauseQueue:h.resumeQueue)):h.paused&&(t.pause=!0);let p=(n||Object.keys(e.springs)).map(i=>e.springs[i].start(t)),f=!0===t.cancel||!0===tl(t,"cancel");(d||f&&h.asyncId)&&p.push(tP(++e._lastAsyncId,{props:t,state:h,actions:{pause:j,resume:j,start(t,i){f?(tM(h,e._lastAsyncId),i(tA(e))):(t.onRest=o,i(tC(d,t,h,e)))}}})),h.paused&&await new Promise(e=>{h.resumeQueue.add(e)});let m=tx(e,await Promise.all(p));if(a&&m.finished&&!(i&&m.noop)){let i=tL(t,a,r);if(i)return t5(e,[i]),tJ(e,i,!0)}return u&&c.batchedUpdates(()=>u(m,e,e.item)),m}function tK(e,t){let i={...e.springs};return t&&F(T(t),e=>{R.und(e.keys)&&(e=tD(e)),R.obj(e.to)||(e={...e,to:void 0}),t2(i,e,e=>t1(e))}),t0(e,i),i}function t0(e,t){q(t,(t,i)=>{e.springs[i]||(e.springs[i]=t,eC(t,e))})}function t1(e,t){let i=new tU;return i.key=e,t&&eC(i,t),i}function t2(e,t,i){t.keys&&F(t.keys,n=>{(e[n]||(e[n]=i(n)))._prepareNode(t)})}function t5(e,t){F(t,t=>{t2(e.springs,t,t=>t1(t,e))})}var t3=({children:e,...t})=>{let i=(0,u.useContext)(t4),n=t.pause||!!i.pause,r=t.immediate||!!i.immediate;t=function(e,t){let[i]=(0,u.useState)(()=>({inputs:t,result:e()})),n=(0,u.useRef)(),r=n.current,s=r;return s?t&&s.inputs&&function(e,t){if(e.length!==t.length)return!1;for(let i=0;i<e.length;i++)if(e[i]!==t[i])return!1;return!0}(t,s.inputs)||(s={inputs:t,result:e()}):s=i,(0,u.useEffect)(()=>{n.current=s,r==i&&(i.inputs=i.result=void 0)},[s]),s.result}(()=>({pause:n,immediate:r}),[n,r]);let{Provider:s}=t4;return u.createElement(s,{value:t},e)},t4=function(e,t){return Object.assign(e,u.createContext(t)),e.Provider._context=e,e.Consumer._context=e,e}(t3,{});t3.Provider=t4.Provider,t3.Consumer=t4.Consumer;var t9=()=>{let e=[],t=function(t){eL(`${e$}Directly calling start instead of using the api object is deprecated in v9 (use ".start" instead), this will be removed in later 0.X.0 versions`);let n=[];return F(e,(e,r)=>{if(R.und(t))n.push(e.start());else{let s=i(t,e,r);s&&n.push(e.start(s))}}),n};t.current=e,t.add=function(t){e.includes(t)||e.push(t)},t.delete=function(t){let i=e.indexOf(t);~i&&e.splice(i,1)},t.pause=function(){return F(e,e=>e.pause(...arguments)),this},t.resume=function(){return F(e,e=>e.resume(...arguments)),this},t.set=function(t){F(e,(e,i)=>{let n=R.fun(t)?t(i,e):t;n&&e.set(n)})},t.start=function(t){let i=[];return F(e,(e,n)=>{if(R.und(t))i.push(e.start());else{let r=this._getProps(t,e,n);r&&i.push(e.start(r))}}),i},t.stop=function(){return F(e,e=>e.stop(...arguments)),this},t.update=function(t){return F(e,(e,i)=>e.update(this._getProps(t,e,i))),this};let i=function(e,t,i){return R.fun(e)?e(i,t):e};return t._getProps=i,t},t7=class extends tR{constructor(e,t){super(),this.source=e,this.idle=!0,this._active=new Set,this.calc=ev(...t);let i=this._get();e2(this,tt(i).create(i))}advance(e){let t=this._get();z(t,this.get())||(e1(this).setValue(t),this._onChange(t,this.idle)),!this.idle&&t6(this._active)&&ie(this)}_get(){let e=R.arr(this.source)?this.source.map(eP):T(eP(this.source));return this.calc(...e)}_start(){this.idle&&!t6(this._active)&&(this.idle=!1,F(e5(this),e=>{e.done=!1}),I.skipAnimation?(c.batchedUpdates(()=>this.advance()),ie(this)):X.start(this))}_attach(){let e=1;F(T(this.source),t=>{eb(t)&&eC(t,this),tj(t)&&(t.idle||this._active.add(t),e=Math.max(e,t.priority+1))}),this.priority=e,this._start()}_detach(){F(T(this.source),e=>{eb(e)&&eM(e,this)}),this._active.clear(),ie(this)}eventObserved(e){"change"==e.type?e.idle?this.advance():(this._active.add(e.parent),this._start()):"idle"==e.type?this._active.delete(e.parent):"priority"==e.type&&(this.priority=T(this.source).reduce((e,t)=>Math.max(e,(tj(t)?t.priority:0)+1),0))}};function t8(e){return!1!==e.idle}function t6(e){return!e.size||Array.from(e).every(t8)}function ie(e){e.idle||(e.idle=!0,F(e5(e),e=>{e.done=!0}),ek(e,{type:"idle",parent:e}))}I.assign({createStringInterpolator:eO,to:(e,t)=>new t7(e,t)}),X.advance;var it=["primitive"].concat(Object.keys(d).filter(e=>/^[A-Z]/.test(e)).map(e=>e[0].toLowerCase()+e.slice(1)));I.assign({createStringInterpolator:eO,colors:{transparent:0,aliceblue:4042850303,antiquewhite:4209760255,aqua:16777215,aquamarine:2147472639,azure:4043309055,beige:4126530815,bisque:4293182719,black:255,blanchedalmond:4293643775,blue:65535,blueviolet:2318131967,brown:2771004159,burlywood:3736635391,burntsienna:3934150143,cadetblue:1604231423,chartreuse:2147418367,chocolate:3530104575,coral:4286533887,cornflowerblue:1687547391,cornsilk:4294499583,crimson:3692313855,cyan:16777215,darkblue:35839,darkcyan:9145343,darkgoldenrod:3095792639,darkgray:2846468607,darkgreen:6553855,darkgrey:2846468607,darkkhaki:3182914559,darkmagenta:2332068863,darkolivegreen:1433087999,darkorange:4287365375,darkorchid:2570243327,darkred:2332033279,darksalmon:3918953215,darkseagreen:2411499519,darkslateblue:1211993087,darkslategray:793726975,darkslategrey:793726975,darkturquoise:13554175,darkviolet:2483082239,deeppink:4279538687,deepskyblue:12582911,dimgray:1768516095,dimgrey:1768516095,dodgerblue:512819199,firebrick:2988581631,floralwhite:4294635775,forestgreen:579543807,fuchsia:4278255615,gainsboro:3705462015,ghostwhite:4177068031,gold:4292280575,goldenrod:3668254975,gray:2155905279,green:8388863,greenyellow:2919182335,grey:2155905279,honeydew:4043305215,hotpink:4285117695,indianred:3445382399,indigo:1258324735,ivory:4294963455,khaki:4041641215,lavender:3873897215,lavenderblush:4293981695,lawngreen:2096890111,lemonchiffon:4294626815,lightblue:2916673279,lightcoral:4034953471,lightcyan:3774873599,lightgoldenrodyellow:4210742015,lightgray:3553874943,lightgreen:2431553791,lightgrey:3553874943,lightpink:4290167295,lightsalmon:4288707327,lightseagreen:548580095,lightskyblue:2278488831,lightslategray:2005441023,lightslategrey:2005441023,lightsteelblue:2965692159,lightyellow:4294959359,lime:16711935,limegreen:852308735,linen:4210091775,magenta:4278255615,maroon:2147483903,mediumaquamarine:1724754687,mediumblue:52735,mediumorchid:3126187007,mediumpurple:2473647103,mediumseagreen:1018393087,mediumslateblue:2070474495,mediumspringgreen:16423679,mediumturquoise:1221709055,mediumvioletred:3340076543,midnightblue:421097727,mintcream:4127193855,mistyrose:4293190143,moccasin:4293178879,navajowhite:4292783615,navy:33023,oldlace:4260751103,olive:2155872511,olivedrab:1804477439,orange:4289003775,orangered:4282712319,orchid:3664828159,palegoldenrod:4008225535,palegreen:2566625535,paleturquoise:2951671551,palevioletred:3681588223,papayawhip:4293907967,peachpuff:4292524543,peru:3448061951,pink:4290825215,plum:3718307327,powderblue:2967529215,purple:2147516671,rebeccapurple:1714657791,red:4278190335,rosybrown:3163525119,royalblue:1097458175,saddlebrown:2336560127,salmon:4202722047,sandybrown:4104413439,seagreen:780883967,seashell:4294307583,sienna:2689740287,silver:3233857791,skyblue:2278484991,slateblue:1784335871,slategray:1887473919,slategrey:1887473919,snow:4294638335,springgreen:16744447,steelblue:1182971135,tan:3535047935,teal:8421631,thistle:3636451583,tomato:4284696575,turquoise:1088475391,violet:4001558271,wheat:4125012991,white:4294967295,whitesmoke:4126537215,yellow:4294902015,yellowgreen:2597139199},frameLoop:"demand"}),(0,l.o)(()=>{c.advance()}),((e,{applyAnimatedValues:t=()=>!1,createAnimatedStyle:i=e=>new e8(e),getComponentProps:n=e=>e}={})=>{let r={applyAnimatedValues:t,createAnimatedStyle:i,getComponentProps:n},s=e=>{let t=ts(e)||"Anonymous";return(e=R.str(e)?s[e]||(s[e]=ti(e,r)):e[tr]||(e[tr]=ti(e,r))).displayName=`Animated(${t})`,e};return q(e,(t,i)=>{R.arr(e)&&(i=ts(t)),s[i]=s(t)}),{animated:s}})(it,{applyAnimatedValues:l.k}).animated;let ii=(e,t)=>({uniforms:{pointTexture:{value:t||new d.TextureLoader().load("/images/particle.png")},time:{value:0},mousePosition:{value:new d.Vector2(0,0)},color:{value:new d.Color(e)}},vertexShader:`
      uniform float time;
      uniform vec2 mousePosition;
      
      attribute float size;
      varying vec3 vColor;
      
      void main() {
        // Add subtle variation to color based on position
        vColor = vec3(${e.replace("#","0x")}) * (1.0 + sin(position.x * 0.5 + time * 0.2) * 0.2);
        
        // Apply time-based animation
        vec4 mvPosition = modelViewMatrix * vec4(position, 1.0);
        
        // Apply subtle wave effect
        float wave = sin(position.x * 1.5 + time) * 0.1 + 
                     sin(position.y * 1.5 + time * 0.8) * 0.1;
        mvPosition.z += wave;
        
        // Mouse interaction
        float dist = length(mousePosition - vec2(position.xy));
        float mouseEffect = max(0.0, 1.0 - dist / 5.0);
        mvPosition.z += mouseEffect * 0.5;
        
        gl_Position = projectionMatrix * mvPosition;
        
        // Vary size slightly based on position for more natural look
        float sizeFactor = 1.0 + sin(position.x * 3.0 + position.y * 2.0) * 0.3;
        gl_PointSize = size * sizeFactor * (300.0 / -mvPosition.z);
      }
    `,fragmentShader:`
      uniform sampler2D pointTexture;
      varying vec3 vColor;
      
      void main() {
        vec4 texColor = texture2D(pointTexture, gl_PointCoord);
        gl_FragColor = vec4(vColor, 1.0) * texColor;
        
        // Enhance the alpha blending to match the nebula effect
        if (gl_FragColor.a < 0.05) discard;
      }
    `}),ir=({color:e="#3fb950",count:t=2e3,size:i=.06,mouseInfluence:n=.05})=>{let r=(0,u.useRef)(null),{mouse:s,viewport:a}=(0,l.D)(),[h,c]=(0,u.useState)(null),[p,f]=(0,u.useState)(null);(0,u.useEffect)(()=>{new d.TextureLoader().load("/images/particle.png",e=>{e.premultiplyAlpha=!0,f(e)})},[]);let[{cameraX:m,cameraY:g}]=function(e,t){let i=R.fun(e),[[n],r]=function(e,t,i){let n=R.fun(t)&&t;n&&!i&&(i=[]);let r=(0,u.useMemo)(()=>n||3==arguments.length?t9():void 0,[]),s=(0,u.useRef)(0),a=eX(),o=(0,u.useMemo)(()=>({ctrls:[],queue:[],flush(e,t){let i=tK(e,t);return!(s.current>0)||o.queue.length||Object.keys(i).some(t=>!e.springs[t])?new Promise(n=>{t0(e,i),o.queue.push(()=>{n(tH(e,t))}),a()}):tH(e,t)}}),[]),l=(0,u.useRef)([...o.ctrls]),d=[],h=eJ(e)||0;function c(e,i){for(let r=e;r<i;r++){let e=l.current[r]||(l.current[r]=new tY(null,o.flush)),i=n?n(r,e):t[r];i&&(d[r]=function(e){let t=tD(e);return R.und(t.default)&&(t.default=th(t)),t}(i))}}(0,u.useMemo)(()=>{F(l.current.slice(e,h),e=>{e.ref?.delete(e),r?.delete(e),e.stop(!0)}),l.current.length=e,c(h,e)},[e]),(0,u.useMemo)(()=>{c(0,Math.min(h,e))},i);let p=l.current.map((e,t)=>tK(e,d[t])),f=(0,u.useContext)(t3),m=eJ(f),g=f!==m&&function(e){for(let t in e)return!0;return!1}(f);eB(()=>{s.current++,o.ctrls=l.current;let{queue:e}=o;e.length&&(o.queue=[],F(e,e=>e())),F(l.current,(e,t)=>{var i;r?.add(e),g&&e.start({default:f});let n=d[t];n&&((i=n.ref)&&e.ref!==i&&(e.ref?.delete(e),i.add(e),e.ref=i),e.ref?e.queue.push(n):e.start(n))})}),eY(()=>()=>{F(o.ctrls,e=>e.stop(!0))});let y=p.map(e=>({...e}));return r?[y,r]:y}(1,i?e:[e],i?t||[]:t);return i||2==arguments.length?[n,r]:n}(()=>({cameraX:0,cameraY:0,config:{mass:1,tension:100,friction:30}}));(0,u.useEffect)(()=>{let e=new Float32Array(3*t);for(let i=0;i<t;i++){let t=5*Math.random()+.5,n=Math.acos(2*Math.random()-1),r=Math.random()*Math.PI*2;e[3*i]=t*Math.sin(n)*Math.cos(r),e[3*i+1]=t*Math.sin(n)*Math.sin(r),e[3*i+2]=t*Math.cos(n)*.5}c(e)},[t]),(0,l.F)(e=>{r.current&&h&&(r.current.rotation.y+=8e-4,e.camera.position.x=d.MathUtils.lerp(e.camera.position.x,s.x*n,.05),e.camera.position.y=d.MathUtils.lerp(e.camera.position.y,s.y*n,.05),e.camera.lookAt(0,0,0),r.current.material instanceof d.ShaderMaterial&&(r.current.material.uniforms.time.value=e.clock.getElapsedTime(),r.current.material.uniforms.mousePosition.value.set(s.x*a.width/2,s.y*a.height/2)))});let y=ii(e,p);return h?o.jsx("group",{children:(0,o.jsxs)("points",{ref:r,children:[o.jsx("bufferGeometry",{children:o.jsx("bufferAttribute",{attach:"attributes-position",count:t,array:h,itemSize:3})}),o.jsx("shaderMaterial",{attach:"material",args:[y],transparent:!0,depthWrite:!1,blending:d.AdditiveBlending})]})}):null}}};