exports.id=438,exports.ids=[438],exports.modules={45317:e=>{e.exports={style:{fontFamily:"'__Inter_e8ce0c', '__Inter_Fallback_e8ce0c'",fontStyle:"normal"},className:"__className_e8ce0c"}},99479:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});var n=r(17577),i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"},o=r(3789);let a=(0,n.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:a,className:s="",children:l,iconNode:u,...c},d)=>(0,n.createElement)("svg",{ref:d,...i,width:t,height:t,stroke:e,strokeWidth:a?24*Number(r)/Number(t):r,className:(0,o.z)("lucide",s),...c},[...u.map(([e,t])=>(0,n.createElement)(e,t)),...Array.isArray(l)?l:[l]]))},68784:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});var n=r(17577),i=r(3789),o=r(99479);let a=(e,t)=>{let r=(0,n.forwardRef)(({className:r,...a},s)=>(0,n.createElement)(o.Z,{ref:s,iconNode:t,className:(0,i.z)(`lucide-${(0,i.m)(e)}`,r),...a}));return r.displayName=`${e}`,r}},86333:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(68784).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29798:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(68784).Z)("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},70715:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(68784).Z)("Maximize2",[["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["polyline",{points:"9 21 3 21 3 15",key:"1avn1i"}],["line",{x1:"21",x2:"14",y1:"3",y2:"10",key:"ota7mn"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]])},11692:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(68784).Z)("Minimize2",[["polyline",{points:"4 14 10 14 10 20",key:"11kfnr"}],["polyline",{points:"20 10 14 10 14 4",key:"rlmsce"}],["line",{x1:"14",x2:"21",y1:"10",y2:"3",key:"o5lafz"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]])},88307:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(68784).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},69436:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(68784).Z)("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},32130:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(68784).Z)("Terminal",[["polyline",{points:"4 17 10 11 4 5",key:"akl6gq"}],["line",{x1:"12",x2:"20",y1:"19",y2:"19",key:"q2wloq"}]])},94019:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(68784).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},3789:(e,t,r)=>{"use strict";r.d(t,{m:()=>n,z:()=>i});let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},90434:(e,t,r)=>{"use strict";r.d(t,{default:()=>i.a});var n=r(79404),i=r.n(n)},3486:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addBasePath",{enumerable:!0,get:function(){return o}});let n=r(8974),i=r(23658);function o(e,t){return(0,i.normalizePathTrailingSlash)((0,n.addPathPrefix)(e,""))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},53416:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addLocale",{enumerable:!0,get:function(){return n}}),r(23658);let n=function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},15424:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"callServer",{enumerable:!0,get:function(){return i}});let n=r(12994);async function i(e,t){let r=(0,n.getServerActionDispatcher)();if(!r)throw Error("Invariant: missing action dispatcher.");return new Promise((n,i)=>{r({actionId:e,actionArgs:t,resolve:n,reject:i})})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},68038:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AppRouterAnnouncer",{enumerable:!0,get:function(){return a}});let n=r(17577),i=r(60962),o="next-route-announcer";function a(e){let{tree:t}=e,[r,a]=(0,n.useState)(null);(0,n.useEffect)(()=>(a(function(){var e;let t=document.getElementsByName(o)[0];if(null==t?void 0:null==(e=t.shadowRoot)?void 0:e.childNodes[0])return t.shadowRoot.childNodes[0];{let e=document.createElement(o);e.style.cssText="position:absolute";let t=document.createElement("div");return t.ariaLive="assertive",t.id="__next-route-announcer__",t.role="alert",t.style.cssText="position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal",e.attachShadow({mode:"open"}).appendChild(t),document.body.appendChild(e),t}}()),()=>{let e=document.getElementsByTagName(o)[0];(null==e?void 0:e.isConnected)&&document.body.removeChild(e)}),[]);let[s,l]=(0,n.useState)(""),u=(0,n.useRef)();return(0,n.useEffect)(()=>{let e="";if(document.title)e=document.title;else{let t=document.querySelector("h1");t&&(e=t.innerText||t.textContent||"")}void 0!==u.current&&u.current!==e&&l(e),u.current=e},[t]),r?(0,i.createPortal)(s,r):null}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5138:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ACTION:function(){return n},FLIGHT_PARAMETERS:function(){return l},NEXT_DID_POSTPONE_HEADER:function(){return c},NEXT_ROUTER_PREFETCH_HEADER:function(){return o},NEXT_ROUTER_STATE_TREE:function(){return i},NEXT_RSC_UNION_QUERY:function(){return u},NEXT_URL:function(){return a},RSC_CONTENT_TYPE_HEADER:function(){return s},RSC_HEADER:function(){return r}});let r="RSC",n="Next-Action",i="Next-Router-State-Tree",o="Next-Router-Prefetch",a="Next-Url",s="text/x-component",l=[[r],[i],[o]],u="_rsc",c="x-nextjs-postponed";("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},12994:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createEmptyCacheNode:function(){return C},default:function(){return k},getServerActionDispatcher:function(){return _},urlToUrlWithoutFlightMarker:function(){return T}});let n=r(31667),i=r(10326),o=n._(r(17577)),a=r(52413),s=r(57767),l=r(17584),u=r(97008),c=r(77326),d=r(9727),f=r(6199),h=r(32148),p=r(3486),m=r(68038),y=r(46265),g=r(22492),v=r(39519),b=r(5138),x=r(74237),w=r(37929),P=r(68071),E=null,R=null;function _(){return R}let S={};function T(e){let t=new URL(e,location.origin);return t.searchParams.delete(b.NEXT_RSC_UNION_QUERY),t}function j(e){return e.origin!==window.location.origin}function O(e){let{appRouterState:t,sync:r}=e;return(0,o.useInsertionEffect)(()=>{let{tree:e,pushRef:n,canonicalUrl:i}=t,o={...n.preserveCustomHistoryState?window.history.state:{},__NA:!0,__PRIVATE_NEXTJS_INTERNALS_TREE:e};n.pendingPush&&(0,l.createHrefFromUrl)(new URL(window.location.href))!==i?(n.pendingPush=!1,window.history.pushState(o,"",i)):window.history.replaceState(o,"",i),r(t)},[t,r]),null}function C(){return{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,lazyDataResolved:!1,loading:null}}function M(e){null==e&&(e={});let t=window.history.state,r=null==t?void 0:t.__NA;r&&(e.__NA=r);let n=null==t?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;return n&&(e.__PRIVATE_NEXTJS_INTERNALS_TREE=n),e}function A(e){let{headCacheNode:t}=e,r=null!==t?t.head:null,n=null!==t?t.prefetchHead:null,i=null!==n?n:r;return(0,o.useDeferredValue)(r,i)}function D(e){let t,{buildId:r,initialHead:n,initialTree:l,urlParts:d,initialSeedData:b,couldBeIntercepted:_,assetPrefix:T,missingSlots:C}=e,D=(0,o.useMemo)(()=>(0,f.createInitialRouterState)({buildId:r,initialSeedData:b,urlParts:d,initialTree:l,initialParallelRoutes:E,location:null,initialHead:n,couldBeIntercepted:_}),[r,b,d,l,n,_]),[k,N,L]=(0,c.useReducerWithReduxDevtools)(D);(0,o.useEffect)(()=>{E=null},[]);let{canonicalUrl:I}=(0,c.useUnwrapState)(k),{searchParams:F,pathname:U}=(0,o.useMemo)(()=>{let e=new URL(I,"http://n");return{searchParams:e.searchParams,pathname:(0,w.hasBasePath)(e.pathname)?(0,x.removeBasePath)(e.pathname):e.pathname}},[I]),V=(0,o.useCallback)(e=>{let{previousTree:t,serverResponse:r}=e;(0,o.startTransition)(()=>{N({type:s.ACTION_SERVER_PATCH,previousTree:t,serverResponse:r})})},[N]),B=(0,o.useCallback)((e,t,r)=>{let n=new URL((0,p.addBasePath)(e),location.href);return N({type:s.ACTION_NAVIGATE,url:n,isExternalUrl:j(n),locationSearch:location.search,shouldScroll:null==r||r,navigateType:t})},[N]);R=(0,o.useCallback)(e=>{(0,o.startTransition)(()=>{N({...e,type:s.ACTION_SERVER_ACTION})})},[N]);let W=(0,o.useMemo)(()=>({back:()=>window.history.back(),forward:()=>window.history.forward(),prefetch:(e,t)=>{let r;if(!(0,h.isBot)(window.navigator.userAgent)){try{r=new URL((0,p.addBasePath)(e),window.location.href)}catch(t){throw Error("Cannot prefetch '"+e+"' because it cannot be converted to a URL.")}j(r)||(0,o.startTransition)(()=>{var e;N({type:s.ACTION_PREFETCH,url:r,kind:null!=(e=null==t?void 0:t.kind)?e:s.PrefetchKind.FULL})})}},replace:(e,t)=>{void 0===t&&(t={}),(0,o.startTransition)(()=>{var r;B(e,"replace",null==(r=t.scroll)||r)})},push:(e,t)=>{void 0===t&&(t={}),(0,o.startTransition)(()=>{var r;B(e,"push",null==(r=t.scroll)||r)})},refresh:()=>{(0,o.startTransition)(()=>{N({type:s.ACTION_REFRESH,origin:window.location.origin})})},fastRefresh:()=>{throw Error("fastRefresh can only be used in development mode. Please use refresh instead.")}}),[N,B]);(0,o.useEffect)(()=>{window.next&&(window.next.router=W)},[W]),(0,o.useEffect)(()=>{function e(e){var t;e.persisted&&(null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE)&&(S.pendingMpaPath=void 0,N({type:s.ACTION_RESTORE,url:new URL(window.location.href),tree:window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE}))}return window.addEventListener("pageshow",e),()=>{window.removeEventListener("pageshow",e)}},[N]);let{pushRef:H}=(0,c.useUnwrapState)(k);if(H.mpaNavigation){if(S.pendingMpaPath!==I){let e=window.location;H.pendingPush?e.assign(I):e.replace(I),S.pendingMpaPath=I}(0,o.use)(v.unresolvedThenable)}(0,o.useEffect)(()=>{let e=window.history.pushState.bind(window.history),t=window.history.replaceState.bind(window.history),r=e=>{var t;let r=window.location.href,n=null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;(0,o.startTransition)(()=>{N({type:s.ACTION_RESTORE,url:new URL(null!=e?e:r,r),tree:n})})};window.history.pushState=function(t,n,i){return(null==t?void 0:t.__NA)||(null==t?void 0:t._N)||(t=M(t),i&&r(i)),e(t,n,i)},window.history.replaceState=function(e,n,i){return(null==e?void 0:e.__NA)||(null==e?void 0:e._N)||(e=M(e),i&&r(i)),t(e,n,i)};let n=e=>{let{state:t}=e;if(t){if(!t.__NA){window.location.reload();return}(0,o.startTransition)(()=>{N({type:s.ACTION_RESTORE,url:new URL(window.location.href),tree:t.__PRIVATE_NEXTJS_INTERNALS_TREE})})}};return window.addEventListener("popstate",n),()=>{window.history.pushState=e,window.history.replaceState=t,window.removeEventListener("popstate",n)}},[N]);let{cache:z,tree:$,nextUrl:X,focusAndScrollRef:q}=(0,c.useUnwrapState)(k),K=(0,o.useMemo)(()=>(0,g.findHeadInCache)(z,$[1]),[z,$]),G=(0,o.useMemo)(()=>(function e(t,r){for(let n of(void 0===r&&(r={}),Object.values(t[1]))){let t=n[0],i=Array.isArray(t),o=i?t[1]:t;!o||o.startsWith(P.PAGE_SEGMENT_KEY)||(i&&("c"===t[2]||"oc"===t[2])?r[t[0]]=t[1].split("/"):i&&(r[t[0]]=t[1]),r=e(n,r))}return r})($),[$]);if(null!==K){let[e,r]=K;t=(0,i.jsx)(A,{headCacheNode:e},r)}else t=null;let Y=(0,i.jsxs)(y.RedirectBoundary,{children:[t,z.rsc,(0,i.jsx)(m.AppRouterAnnouncer,{tree:$})]});return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(O,{appRouterState:(0,c.useUnwrapState)(k),sync:L}),(0,i.jsx)(u.PathParamsContext.Provider,{value:G,children:(0,i.jsx)(u.PathnameContext.Provider,{value:U,children:(0,i.jsx)(u.SearchParamsContext.Provider,{value:F,children:(0,i.jsx)(a.GlobalLayoutRouterContext.Provider,{value:{buildId:r,changeByServerResponse:V,tree:$,focusAndScrollRef:q,nextUrl:X},children:(0,i.jsx)(a.AppRouterContext.Provider,{value:W,children:(0,i.jsx)(a.LayoutRouterContext.Provider,{value:{childNodes:z.parallelRoutes,tree:$,url:I,loading:z.loading},children:Y})})})})})})]})}function k(e){let{globalErrorComponent:t,...r}=e;return(0,i.jsx)(d.ErrorBoundary,{errorComponent:t,children:(0,i.jsx)(D,{...r})})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},16136:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"bailoutToClientRendering",{enumerable:!0,get:function(){return o}});let n=r(94129),i=r(45869);function o(e){let t=i.staticGenerationAsyncStorage.getStore();if((null==t||!t.forceStatic)&&(null==t?void 0:t.isStaticGeneration))throw new n.BailoutToCSRError(e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},96114:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ClientPageRoot",{enumerable:!0,get:function(){return o}});let n=r(10326),i=r(23325);function o(e){let{Component:t,props:r}=e;return r.searchParams=(0,i.createDynamicallyTrackedSearchParams)(r.searchParams||{}),(0,n.jsx)(t,{...r})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9727:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ErrorBoundary:function(){return p},ErrorBoundaryHandler:function(){return d},GlobalError:function(){return f},default:function(){return h}});let n=r(98052),i=r(10326),o=n._(r(17577)),a=r(77389),s=r(37313),l=r(45869),u={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},text:{fontSize:"14px",fontWeight:400,lineHeight:"28px",margin:"0 8px"}};function c(e){let{error:t}=e,r=l.staticGenerationAsyncStorage.getStore();if((null==r?void 0:r.isRevalidate)||(null==r?void 0:r.isStaticGeneration))throw console.error(t),t;return null}class d extends o.default.Component{static getDerivedStateFromError(e){if((0,s.isNextRouterError)(e))throw e;return{error:e}}static getDerivedStateFromProps(e,t){return e.pathname!==t.previousPathname&&t.error?{error:null,previousPathname:e.pathname}:{error:t.error,previousPathname:e.pathname}}render(){return this.state.error?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(c,{error:this.state.error}),this.props.errorStyles,this.props.errorScripts,(0,i.jsx)(this.props.errorComponent,{error:this.state.error,reset:this.reset})]}):this.props.children}constructor(e){super(e),this.reset=()=>{this.setState({error:null})},this.state={error:null,previousPathname:this.props.pathname}}}function f(e){let{error:t}=e,r=null==t?void 0:t.digest;return(0,i.jsxs)("html",{id:"__next_error__",children:[(0,i.jsx)("head",{}),(0,i.jsxs)("body",{children:[(0,i.jsx)(c,{error:t}),(0,i.jsx)("div",{style:u.error,children:(0,i.jsxs)("div",{children:[(0,i.jsx)("h2",{style:u.text,children:"Application error: a "+(r?"server":"client")+"-side exception has occurred (see the "+(r?"server logs":"browser console")+" for more information)."}),r?(0,i.jsx)("p",{style:u.text,children:"Digest: "+r}):null]})})]})]})}let h=f;function p(e){let{errorComponent:t,errorStyles:r,errorScripts:n,children:o}=e,s=(0,a.usePathname)();return t?(0,i.jsx)(d,{pathname:s,errorComponent:t,errorStyles:r,errorScripts:n,children:o}):(0,i.jsx)(i.Fragment,{children:o})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},70442:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DynamicServerError:function(){return n},isDynamicServerError:function(){return i}});let r="DYNAMIC_SERVER_USAGE";class n extends Error{constructor(e){super("Dynamic server usage: "+e),this.description=e,this.digest=r}}function i(e){return"object"==typeof e&&null!==e&&"digest"in e&&"string"==typeof e.digest&&e.digest===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},37313:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNextRouterError",{enumerable:!0,get:function(){return o}});let n=r(50706),i=r(62747);function o(e){return e&&e.digest&&((0,i.isRedirectError)(e)||(0,n.isNotFoundError)(e))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},79671:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return E}}),r(98052);let n=r(31667),i=r(10326),o=n._(r(17577));r(60962);let a=r(52413),s=r(9009),l=r(39519),u=r(9727),c=r(70455),d=r(79976),f=r(46265),h=r(41868),p=r(62162),m=r(39886),y=r(45262),g=["bottom","height","left","right","top","width","x","y"];function v(e,t){let r=e.getBoundingClientRect();return r.top>=0&&r.top<=t}class b extends o.default.Component{componentDidMount(){this.handlePotentialScroll()}componentDidUpdate(){this.props.focusAndScrollRef.apply&&this.handlePotentialScroll()}render(){return this.props.children}constructor(...e){super(...e),this.handlePotentialScroll=()=>{let{focusAndScrollRef:e,segmentPath:t}=this.props;if(e.apply){if(0!==e.segmentPaths.length&&!e.segmentPaths.some(e=>t.every((t,r)=>(0,c.matchSegment)(t,e[r]))))return;let r=null,n=e.hashFragment;if(n&&(r=function(e){var t;return"top"===e?document.body:null!=(t=document.getElementById(e))?t:document.getElementsByName(e)[0]}(n)),!r&&(r=null),!(r instanceof Element))return;for(;!(r instanceof HTMLElement)||function(e){if(["sticky","fixed"].includes(getComputedStyle(e).position))return!0;let t=e.getBoundingClientRect();return g.every(e=>0===t[e])}(r);){if(null===r.nextElementSibling)return;r=r.nextElementSibling}e.apply=!1,e.hashFragment=null,e.segmentPaths=[],(0,d.handleSmoothScroll)(()=>{if(n){r.scrollIntoView();return}let e=document.documentElement,t=e.clientHeight;!v(r,t)&&(e.scrollTop=0,v(r,t)||r.scrollIntoView())},{dontForceLayout:!0,onlyHashChange:e.onlyHashChange}),e.onlyHashChange=!1,r.focus()}}}}function x(e){let{segmentPath:t,children:r}=e,n=(0,o.useContext)(a.GlobalLayoutRouterContext);if(!n)throw Error("invariant global layout router not mounted");return(0,i.jsx)(b,{segmentPath:t,focusAndScrollRef:n.focusAndScrollRef,children:r})}function w(e){let{parallelRouterKey:t,url:r,childNodes:n,segmentPath:u,tree:d,cacheKey:f}=e,h=(0,o.useContext)(a.GlobalLayoutRouterContext);if(!h)throw Error("invariant global layout router not mounted");let{buildId:p,changeByServerResponse:m,tree:g}=h,v=n.get(f);if(void 0===v){let e={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,lazyDataResolved:!1,loading:null};v=e,n.set(f,e)}let b=null!==v.prefetchRsc?v.prefetchRsc:v.rsc,x=(0,o.useDeferredValue)(v.rsc,b),w="object"==typeof x&&null!==x&&"function"==typeof x.then?(0,o.use)(x):x;if(!w){let e=v.lazyData;if(null===e){let t=function e(t,r){if(t){let[n,i]=t,o=2===t.length;if((0,c.matchSegment)(r[0],n)&&r[1].hasOwnProperty(i)){if(o){let t=e(void 0,r[1][i]);return[r[0],{...r[1],[i]:[t[0],t[1],t[2],"refetch"]}]}return[r[0],{...r[1],[i]:e(t.slice(2),r[1][i])}]}}return r}(["",...u],g),n=(0,y.hasInterceptionRouteInCurrentTree)(g);v.lazyData=e=(0,s.fetchServerResponse)(new URL(r,location.origin),t,n?h.nextUrl:null,p),v.lazyDataResolved=!1}let t=(0,o.use)(e);v.lazyDataResolved||(setTimeout(()=>{(0,o.startTransition)(()=>{m({previousTree:g,serverResponse:t})})}),v.lazyDataResolved=!0),(0,o.use)(l.unresolvedThenable)}return(0,i.jsx)(a.LayoutRouterContext.Provider,{value:{tree:d[1][t],childNodes:v.parallelRoutes,url:r,loading:v.loading},children:w})}function P(e){let{children:t,hasLoading:r,loading:n,loadingStyles:a,loadingScripts:s}=e;return r?(0,i.jsx)(o.Suspense,{fallback:(0,i.jsxs)(i.Fragment,{children:[a,s,n]}),children:t}):(0,i.jsx)(i.Fragment,{children:t})}function E(e){let{parallelRouterKey:t,segmentPath:r,error:n,errorStyles:s,errorScripts:l,templateStyles:c,templateScripts:d,template:y,notFound:g,notFoundStyles:v}=e,b=(0,o.useContext)(a.LayoutRouterContext);if(!b)throw Error("invariant expected layout router to be mounted");let{childNodes:E,tree:R,url:_,loading:S}=b,T=E.get(t);T||(T=new Map,E.set(t,T));let j=R[1][t][0],O=(0,p.getSegmentValue)(j),C=[j];return(0,i.jsx)(i.Fragment,{children:C.map(e=>{let o=(0,p.getSegmentValue)(e),b=(0,m.createRouterCacheKey)(e);return(0,i.jsxs)(a.TemplateContext.Provider,{value:(0,i.jsx)(x,{segmentPath:r,children:(0,i.jsx)(u.ErrorBoundary,{errorComponent:n,errorStyles:s,errorScripts:l,children:(0,i.jsx)(P,{hasLoading:!!S,loading:null==S?void 0:S[0],loadingStyles:null==S?void 0:S[1],loadingScripts:null==S?void 0:S[2],children:(0,i.jsx)(h.NotFoundBoundary,{notFound:g,notFoundStyles:v,children:(0,i.jsx)(f.RedirectBoundary,{children:(0,i.jsx)(w,{parallelRouterKey:t,url:_,tree:R,childNodes:T,segmentPath:r,cacheKey:b,isActive:O===o})})})})})}),children:[c,d,y]},(0,m.createRouterCacheKey)(e,!0))})})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},70455:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{canSegmentBeOverridden:function(){return o},matchSegment:function(){return i}});let n=r(92357),i=(e,t)=>"string"==typeof e?"string"==typeof t&&e===t:"string"!=typeof t&&e[0]===t[0]&&e[1]===t[1],o=(e,t)=>{var r;return!Array.isArray(e)&&!!Array.isArray(t)&&(null==(r=(0,n.getSegmentParam)(e))?void 0:r.param)===t[0]};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},77389:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return l.ReadonlyURLSearchParams},RedirectType:function(){return l.RedirectType},ServerInsertedHTMLContext:function(){return u.ServerInsertedHTMLContext},notFound:function(){return l.notFound},permanentRedirect:function(){return l.permanentRedirect},redirect:function(){return l.redirect},useParams:function(){return h},usePathname:function(){return d},useRouter:function(){return f},useSearchParams:function(){return c},useSelectedLayoutSegment:function(){return m},useSelectedLayoutSegments:function(){return p},useServerInsertedHTML:function(){return u.useServerInsertedHTML}});let n=r(17577),i=r(52413),o=r(97008),a=r(62162),s=r(68071),l=r(97375),u=r(93347);function c(){let e=(0,n.useContext)(o.SearchParamsContext),t=(0,n.useMemo)(()=>e?new l.ReadonlyURLSearchParams(e):null,[e]);{let{bailoutToClientRendering:e}=r(16136);e("useSearchParams()")}return t}function d(){return(0,n.useContext)(o.PathnameContext)}function f(){let e=(0,n.useContext)(i.AppRouterContext);if(null===e)throw Error("invariant expected app router to be mounted");return e}function h(){return(0,n.useContext)(o.PathParamsContext)}function p(e){void 0===e&&(e="children");let t=(0,n.useContext)(i.LayoutRouterContext);return t?function e(t,r,n,i){let o;if(void 0===n&&(n=!0),void 0===i&&(i=[]),n)o=t[1][r];else{var l;let e=t[1];o=null!=(l=e.children)?l:Object.values(e)[0]}if(!o)return i;let u=o[0],c=(0,a.getSegmentValue)(u);return!c||c.startsWith(s.PAGE_SEGMENT_KEY)?i:(i.push(c),e(o,r,!1,i))}(t.tree,e):null}function m(e){void 0===e&&(e="children");let t=p(e);if(!t||0===t.length)return null;let r="children"===e?t[0]:t[t.length-1];return r===s.DEFAULT_SEGMENT_KEY?null:r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},97375:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return a},RedirectType:function(){return n.RedirectType},notFound:function(){return i.notFound},permanentRedirect:function(){return n.permanentRedirect},redirect:function(){return n.redirect}});let n=r(62747),i=r(50706);class o extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class a extends URLSearchParams{append(){throw new o}delete(){throw new o}set(){throw new o}sort(){throw new o}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},41868:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"NotFoundBoundary",{enumerable:!0,get:function(){return c}});let n=r(31667),i=r(10326),o=n._(r(17577)),a=r(77389),s=r(50706);r(576);let l=r(52413);class u extends o.default.Component{componentDidCatch(){}static getDerivedStateFromError(e){if((0,s.isNotFoundError)(e))return{notFoundTriggered:!0};throw e}static getDerivedStateFromProps(e,t){return e.pathname!==t.previousPathname&&t.notFoundTriggered?{notFoundTriggered:!1,previousPathname:e.pathname}:{notFoundTriggered:t.notFoundTriggered,previousPathname:e.pathname}}render(){return this.state.notFoundTriggered?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("meta",{name:"robots",content:"noindex"}),!1,this.props.notFoundStyles,this.props.notFound]}):this.props.children}constructor(e){super(e),this.state={notFoundTriggered:!!e.asNotFound,previousPathname:e.pathname}}}function c(e){let{notFound:t,notFoundStyles:r,asNotFound:n,children:s}=e,c=(0,a.usePathname)(),d=(0,o.useContext)(l.MissingSlotContext);return t?(0,i.jsx)(u,{pathname:c,notFound:t,notFoundStyles:r,asNotFound:n,missingSlots:d,children:s}):(0,i.jsx)(i.Fragment,{children:s})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},50706:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isNotFoundError:function(){return i},notFound:function(){return n}});let r="NEXT_NOT_FOUND";function n(){let e=Error(r);throw e.digest=r,e}function i(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},77815:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PromiseQueue",{enumerable:!0,get:function(){return u}});let n=r(11726),i=r(48610);var o=i._("_maxConcurrency"),a=i._("_runningCount"),s=i._("_queue"),l=i._("_processNext");class u{enqueue(e){let t,r;let i=new Promise((e,n)=>{t=e,r=n}),o=async()=>{try{n._(this,a)[a]++;let r=await e();t(r)}catch(e){r(e)}finally{n._(this,a)[a]--,n._(this,l)[l]()}};return n._(this,s)[s].push({promiseFn:i,task:o}),n._(this,l)[l](),i}bump(e){let t=n._(this,s)[s].findIndex(t=>t.promiseFn===e);if(t>-1){let e=n._(this,s)[s].splice(t,1)[0];n._(this,s)[s].unshift(e),n._(this,l)[l](!0)}}constructor(e=5){Object.defineProperty(this,l,{value:c}),Object.defineProperty(this,o,{writable:!0,value:void 0}),Object.defineProperty(this,a,{writable:!0,value:void 0}),Object.defineProperty(this,s,{writable:!0,value:void 0}),n._(this,o)[o]=e,n._(this,a)[a]=0,n._(this,s)[s]=[]}}function c(e){if(void 0===e&&(e=!1),(n._(this,a)[a]<n._(this,o)[o]||e)&&n._(this,s)[s].length>0){var t;null==(t=n._(this,s)[s].shift())||t.task()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},46265:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RedirectBoundary:function(){return c},RedirectErrorBoundary:function(){return u}});let n=r(31667),i=r(10326),o=n._(r(17577)),a=r(77389),s=r(62747);function l(e){let{redirect:t,reset:r,redirectType:n}=e,i=(0,a.useRouter)();return(0,o.useEffect)(()=>{o.default.startTransition(()=>{n===s.RedirectType.push?i.push(t,{}):i.replace(t,{}),r()})},[t,n,r,i]),null}class u extends o.default.Component{static getDerivedStateFromError(e){if((0,s.isRedirectError)(e))return{redirect:(0,s.getURLFromRedirectError)(e),redirectType:(0,s.getRedirectTypeFromError)(e)};throw e}render(){let{redirect:e,redirectType:t}=this.state;return null!==e&&null!==t?(0,i.jsx)(l,{redirect:e,redirectType:t,reset:()=>this.setState({redirect:null})}):this.props.children}constructor(e){super(e),this.state={redirect:null,redirectType:null}}}function c(e){let{children:t}=e,r=(0,a.useRouter)();return(0,i.jsx)(u,{router:r,children:t})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},28778:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}}),function(e){e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect"}(r||(r={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},62747:(e,t,r)=>{"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RedirectType:function(){return n},getRedirectError:function(){return l},getRedirectStatusCodeFromError:function(){return p},getRedirectTypeFromError:function(){return h},getURLFromRedirectError:function(){return f},isRedirectError:function(){return d},permanentRedirect:function(){return c},redirect:function(){return u}});let i=r(54580),o=r(72934),a=r(28778),s="NEXT_REDIRECT";function l(e,t,r){void 0===r&&(r=a.RedirectStatusCode.TemporaryRedirect);let n=Error(s);n.digest=s+";"+t+";"+e+";"+r+";";let o=i.requestAsyncStorage.getStore();return o&&(n.mutableCookies=o.mutableCookies),n}function u(e,t){void 0===t&&(t="replace");let r=o.actionAsyncStorage.getStore();throw l(e,t,(null==r?void 0:r.isAction)?a.RedirectStatusCode.SeeOther:a.RedirectStatusCode.TemporaryRedirect)}function c(e,t){void 0===t&&(t="replace");let r=o.actionAsyncStorage.getStore();throw l(e,t,(null==r?void 0:r.isAction)?a.RedirectStatusCode.SeeOther:a.RedirectStatusCode.PermanentRedirect)}function d(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r,n,i]=e.digest.split(";",4),o=Number(i);return t===s&&("replace"===r||"push"===r)&&"string"==typeof n&&!isNaN(o)&&o in a.RedirectStatusCode}function f(e){return d(e)?e.digest.split(";",3)[2]:null}function h(e){if(!d(e))throw Error("Not a redirect error");return e.digest.split(";",2)[1]}function p(e){if(!d(e))throw Error("Not a redirect error");return Number(e.digest.split(";",4)[3])}(function(e){e.push="push",e.replace="replace"})(n||(n={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},84759:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s}});let n=r(31667),i=r(10326),o=n._(r(17577)),a=r(52413);function s(){let e=(0,o.useContext)(a.TemplateContext);return(0,i.jsx)(i.Fragment,{children:e})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9894:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyFlightData",{enumerable:!0,get:function(){return o}});let n=r(114),i=r(19056);function o(e,t,r,o){let[a,s,l]=r.slice(-3);if(null===s)return!1;if(3===r.length){let r=s[2],i=s[3];t.loading=i,t.rsc=r,t.prefetchRsc=null,(0,n.fillLazyItemsTillLeafWithHead)(t,e,a,s,l,o)}else t.rsc=e.rsc,t.prefetchRsc=e.prefetchRsc,t.parallelRoutes=new Map(e.parallelRoutes),t.loading=e.loading,(0,i.fillCacheWithNewSubTreeData)(t,e,r,o);return!0}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},95166:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyRouterStatePatchToTree",{enumerable:!0,get:function(){return function e(t,r,n,s){let l;let[u,c,d,f,h]=r;if(1===t.length){let e=a(r,n,t);return(0,o.addRefreshMarkerToActiveParallelSegments)(e,s),e}let[p,m]=t;if(!(0,i.matchSegment)(p,u))return null;if(2===t.length)l=a(c[m],n,t);else if(null===(l=e(t.slice(2),c[m],n,s)))return null;let y=[t[0],{...c,[m]:l},d,f];return h&&(y[4]=!0),(0,o.addRefreshMarkerToActiveParallelSegments)(y,s),y}}});let n=r(68071),i=r(70455),o=r(84158);function a(e,t,r){let[o,s]=e,[l,u]=t;if(l===n.DEFAULT_SEGMENT_KEY&&o!==n.DEFAULT_SEGMENT_KEY)return e;if((0,i.matchSegment)(o,l)){let t={};for(let e in s)void 0!==u[e]?t[e]=a(s[e],u[e],r):t[e]=s[e];for(let e in u)t[e]||(t[e]=u[e]);let n=[o,t];return e[2]&&(n[2]=e[2]),e[3]&&(n[3]=e[3]),e[4]&&(n[4]=e[4]),n}return t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},12895:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"clearCacheNodeDataForSegmentPath",{enumerable:!0,get:function(){return function e(t,r,i){let o=i.length<=2,[a,s]=i,l=(0,n.createRouterCacheKey)(s),u=r.parallelRoutes.get(a),c=t.parallelRoutes.get(a);c&&c!==u||(c=new Map(u),t.parallelRoutes.set(a,c));let d=null==u?void 0:u.get(l),f=c.get(l);if(o){f&&f.lazyData&&f!==d||c.set(l,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,lazyDataResolved:!1,loading:null});return}if(!f||!d){f||c.set(l,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,lazyDataResolved:!1,loading:null});return}return f===d&&(f={lazyData:f.lazyData,rsc:f.rsc,prefetchRsc:f.prefetchRsc,head:f.head,prefetchHead:f.prefetchHead,parallelRoutes:new Map(f.parallelRoutes),lazyDataResolved:f.lazyDataResolved,loading:f.loading},c.set(l,f)),e(f,d,i.slice(2))}}});let n=r(39886);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},47326:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{computeChangedPath:function(){return c},extractPathFromFlightRouterState:function(){return u}});let n=r(87356),i=r(68071),o=r(70455),a=e=>"/"===e[0]?e.slice(1):e,s=e=>"string"==typeof e?"children"===e?"":e:e[1];function l(e){return e.reduce((e,t)=>""===(t=a(t))||(0,i.isGroupSegment)(t)?e:e+"/"+t,"")||"/"}function u(e){var t;let r=Array.isArray(e[0])?e[0][1]:e[0];if(r===i.DEFAULT_SEGMENT_KEY||n.INTERCEPTION_ROUTE_MARKERS.some(e=>r.startsWith(e)))return;if(r.startsWith(i.PAGE_SEGMENT_KEY))return"";let o=[s(r)],a=null!=(t=e[1])?t:{},c=a.children?u(a.children):void 0;if(void 0!==c)o.push(c);else for(let[e,t]of Object.entries(a)){if("children"===e)continue;let r=u(t);void 0!==r&&o.push(r)}return l(o)}function c(e,t){let r=function e(t,r){let[i,a]=t,[l,c]=r,d=s(i),f=s(l);if(n.INTERCEPTION_ROUTE_MARKERS.some(e=>d.startsWith(e)||f.startsWith(e)))return"";if(!(0,o.matchSegment)(i,l)){var h;return null!=(h=u(r))?h:""}for(let t in a)if(c[t]){let r=e(a[t],c[t]);if(null!==r)return s(l)+"/"+r}return null}(e,t);return null==r||"/"===r?r:l(r.split("/"))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},17584:(e,t)=>{"use strict";function r(e,t){return void 0===t&&(t=!0),e.pathname+e.search+(t?e.hash:"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createHrefFromUrl",{enumerable:!0,get:function(){return r}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6199:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createInitialRouterState",{enumerable:!0,get:function(){return u}});let n=r(17584),i=r(114),o=r(47326),a=r(79373),s=r(57767),l=r(84158);function u(e){var t;let{buildId:r,initialTree:u,initialSeedData:c,urlParts:d,initialParallelRoutes:f,location:h,initialHead:p,couldBeIntercepted:m}=e,y=d.join("/"),g=!h,v={lazyData:null,rsc:c[2],prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:g?new Map:f,lazyDataResolved:!1,loading:c[3]},b=h?(0,n.createHrefFromUrl)(h):y;(0,l.addRefreshMarkerToActiveParallelSegments)(u,b);let x=new Map;(null===f||0===f.size)&&(0,i.fillLazyItemsTillLeafWithHead)(v,void 0,u,c,p);let w={buildId:r,tree:u,cache:v,prefetchCache:x,pushRef:{pendingPush:!1,mpaNavigation:!1,preserveCustomHistoryState:!0},focusAndScrollRef:{apply:!1,onlyHashChange:!1,hashFragment:null,segmentPaths:[]},canonicalUrl:b,nextUrl:null!=(t=(0,o.extractPathFromFlightRouterState)(u)||(null==h?void 0:h.pathname))?t:null};if(h){let e=new URL(""+h.pathname+h.search,h.origin),t=[["",u,null,null]];(0,a.createPrefetchCacheEntryForInitialLoad)({url:e,kind:s.PrefetchKind.AUTO,data:[t,void 0,!1,m],tree:w.tree,prefetchCache:w.prefetchCache,nextUrl:w.nextUrl})}return w}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},39886:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createRouterCacheKey",{enumerable:!0,get:function(){return i}});let n=r(68071);function i(e,t){return(void 0===t&&(t=!1),Array.isArray(e))?e[0]+"|"+e[1]+"|"+e[2]:t&&e.startsWith(n.PAGE_SEGMENT_KEY)?n.PAGE_SEGMENT_KEY:e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9009:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fetchServerResponse",{enumerable:!0,get:function(){return c}});let n=r(5138),i=r(12994),o=r(15424),a=r(57767),s=r(92165),{createFromFetch:l}=r(56493);function u(e){return[(0,i.urlToUrlWithoutFlightMarker)(e).toString(),void 0,!1,!1]}async function c(e,t,r,c,d){let f={[n.RSC_HEADER]:"1",[n.NEXT_ROUTER_STATE_TREE]:encodeURIComponent(JSON.stringify(t))};d===a.PrefetchKind.AUTO&&(f[n.NEXT_ROUTER_PREFETCH_HEADER]="1"),r&&(f[n.NEXT_URL]=r);let h=(0,s.hexHash)([f[n.NEXT_ROUTER_PREFETCH_HEADER]||"0",f[n.NEXT_ROUTER_STATE_TREE],f[n.NEXT_URL]].join(","));try{var p;let t=new URL(e);t.searchParams.set(n.NEXT_RSC_UNION_QUERY,h);let r=await fetch(t,{credentials:"same-origin",headers:f}),a=(0,i.urlToUrlWithoutFlightMarker)(r.url),s=r.redirected?a:void 0,d=r.headers.get("content-type")||"",m=!!r.headers.get(n.NEXT_DID_POSTPONE_HEADER),y=!!(null==(p=r.headers.get("vary"))?void 0:p.includes(n.NEXT_URL));if(d!==n.RSC_CONTENT_TYPE_HEADER||!r.ok)return e.hash&&(a.hash=e.hash),u(a.toString());let[g,v]=await l(Promise.resolve(r),{callServer:o.callServer});if(c!==g)return u(r.url);return[v,s,m,y]}catch(t){return console.error("Failed to fetch RSC payload for "+e+". Falling back to browser navigation.",t),[e.toString(),void 0,!1,!1]}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},19056:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fillCacheWithNewSubTreeData",{enumerable:!0,get:function(){return function e(t,r,a,s){let l=a.length<=5,[u,c]=a,d=(0,o.createRouterCacheKey)(c),f=r.parallelRoutes.get(u);if(!f)return;let h=t.parallelRoutes.get(u);h&&h!==f||(h=new Map(f),t.parallelRoutes.set(u,h));let p=f.get(d),m=h.get(d);if(l){if(!m||!m.lazyData||m===p){let e=a[3];m={lazyData:null,rsc:e[2],prefetchRsc:null,head:null,prefetchHead:null,loading:e[3],parallelRoutes:p?new Map(p.parallelRoutes):new Map,lazyDataResolved:!1},p&&(0,n.invalidateCacheByRouterState)(m,p,a[2]),(0,i.fillLazyItemsTillLeafWithHead)(m,p,a[2],e,a[4],s),h.set(d,m)}return}m&&p&&(m===p&&(m={lazyData:m.lazyData,rsc:m.rsc,prefetchRsc:m.prefetchRsc,head:m.head,prefetchHead:m.prefetchHead,parallelRoutes:new Map(m.parallelRoutes),lazyDataResolved:!1,loading:m.loading},h.set(d,m)),e(m,p,a.slice(2),s))}}});let n=r(2498),i=r(114),o=r(39886);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},114:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fillLazyItemsTillLeafWithHead",{enumerable:!0,get:function(){return function e(t,r,o,a,s,l){if(0===Object.keys(o[1]).length){t.head=s;return}for(let u in o[1]){let c;let d=o[1][u],f=d[0],h=(0,n.createRouterCacheKey)(f),p=null!==a&&void 0!==a[1][u]?a[1][u]:null;if(r){let n=r.parallelRoutes.get(u);if(n){let r;let o=(null==l?void 0:l.kind)==="auto"&&l.status===i.PrefetchCacheEntryStatus.reusable,a=new Map(n),c=a.get(h);r=null!==p?{lazyData:null,rsc:p[2],prefetchRsc:null,head:null,prefetchHead:null,loading:p[3],parallelRoutes:new Map(null==c?void 0:c.parallelRoutes),lazyDataResolved:!1}:o&&c?{lazyData:c.lazyData,rsc:c.rsc,prefetchRsc:c.prefetchRsc,head:c.head,prefetchHead:c.prefetchHead,parallelRoutes:new Map(c.parallelRoutes),lazyDataResolved:c.lazyDataResolved,loading:c.loading}:{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map(null==c?void 0:c.parallelRoutes),lazyDataResolved:!1,loading:null},a.set(h,r),e(r,c,d,p||null,s,l),t.parallelRoutes.set(u,a);continue}}if(null!==p){let e=p[2],t=p[3];c={lazyData:null,rsc:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,lazyDataResolved:!1,loading:t}}else c={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,lazyDataResolved:!1,loading:null};let m=t.parallelRoutes.get(u);m?m.set(h,c):t.parallelRoutes.set(u,new Map([[h,c]])),e(c,void 0,d,p,s,l)}}}});let n=r(39886),i=r(57767);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},17252:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleMutable",{enumerable:!0,get:function(){return o}});let n=r(47326);function i(e){return void 0!==e}function o(e,t){var r,o,a;let s=null==(o=t.shouldScroll)||o,l=e.nextUrl;if(i(t.patchedTree)){let r=(0,n.computeChangedPath)(e.tree,t.patchedTree);r?l=r:l||(l=e.canonicalUrl)}return{buildId:e.buildId,canonicalUrl:i(t.canonicalUrl)?t.canonicalUrl===e.canonicalUrl?e.canonicalUrl:t.canonicalUrl:e.canonicalUrl,pushRef:{pendingPush:i(t.pendingPush)?t.pendingPush:e.pushRef.pendingPush,mpaNavigation:i(t.mpaNavigation)?t.mpaNavigation:e.pushRef.mpaNavigation,preserveCustomHistoryState:i(t.preserveCustomHistoryState)?t.preserveCustomHistoryState:e.pushRef.preserveCustomHistoryState},focusAndScrollRef:{apply:!!s&&(!!i(null==t?void 0:t.scrollableSegments)||e.focusAndScrollRef.apply),onlyHashChange:!!t.hashFragment&&e.canonicalUrl.split("#",1)[0]===(null==(r=t.canonicalUrl)?void 0:r.split("#",1)[0]),hashFragment:s?t.hashFragment&&""!==t.hashFragment?decodeURIComponent(t.hashFragment.slice(1)):e.focusAndScrollRef.hashFragment:null,segmentPaths:s?null!=(a=null==t?void 0:t.scrollableSegments)?a:e.focusAndScrollRef.segmentPaths:[]},cache:t.cache?t.cache:e.cache,prefetchCache:t.prefetchCache?t.prefetchCache:e.prefetchCache,tree:i(t.patchedTree)?t.patchedTree:e.tree,nextUrl:l}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},65652:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleSegmentMismatch",{enumerable:!0,get:function(){return i}});let n=r(20941);function i(e,t,r){return(0,n.handleExternalUrl)(e,{},e.canonicalUrl,!0)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},43193:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheBelowFlightSegmentPath",{enumerable:!0,get:function(){return function e(t,r,i){let o=i.length<=2,[a,s]=i,l=(0,n.createRouterCacheKey)(s),u=r.parallelRoutes.get(a);if(!u)return;let c=t.parallelRoutes.get(a);if(c&&c!==u||(c=new Map(u),t.parallelRoutes.set(a,c)),o){c.delete(l);return}let d=u.get(l),f=c.get(l);f&&d&&(f===d&&(f={lazyData:f.lazyData,rsc:f.rsc,prefetchRsc:f.prefetchRsc,head:f.head,prefetchHead:f.prefetchHead,parallelRoutes:new Map(f.parallelRoutes),lazyDataResolved:f.lazyDataResolved},c.set(l,f)),e(f,d,i.slice(2)))}}});let n=r(39886);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2498:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheByRouterState",{enumerable:!0,get:function(){return i}});let n=r(39886);function i(e,t,r){for(let i in r[1]){let o=r[1][i][0],a=(0,n.createRouterCacheKey)(o),s=t.parallelRoutes.get(i);if(s){let t=new Map(s);t.delete(a),e.parallelRoutes.set(i,t)}}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},23772:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNavigatingToNewRootLayout",{enumerable:!0,get:function(){return function e(t,r){let n=t[0],i=r[0];if(Array.isArray(n)&&Array.isArray(i)){if(n[0]!==i[0]||n[2]!==i[2])return!0}else if(n!==i)return!0;if(t[4])return!r[4];if(r[4])return!0;let o=Object.values(t[1])[0],a=Object.values(r[1])[0];return!o||!a||e(o,a)}}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},68831:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{abortTask:function(){return u},listenForDynamicRequest:function(){return s},updateCacheNodeOnNavigation:function(){return function e(t,r,s,u,c){let d=r[1],f=s[1],h=u[1],p=t.parallelRoutes,m=new Map(p),y={},g=null;for(let t in f){let r;let s=f[t],u=d[t],v=p.get(t),b=h[t],x=s[0],w=(0,o.createRouterCacheKey)(x),P=void 0!==u?u[0]:void 0,E=void 0!==v?v.get(w):void 0;if(null!==(r=x===n.PAGE_SEGMENT_KEY?a(s,void 0!==b?b:null,c):x===n.DEFAULT_SEGMENT_KEY?void 0!==u?{route:u,node:null,children:null}:a(s,void 0!==b?b:null,c):void 0!==P&&(0,i.matchSegment)(x,P)&&void 0!==E&&void 0!==u?null!=b?e(E,u,s,b,c):function(e){let t=l(e,null,null);return{route:e,node:t,children:null}}(s):a(s,void 0!==b?b:null,c))){null===g&&(g=new Map),g.set(t,r);let e=r.node;if(null!==e){let r=new Map(v);r.set(w,e),m.set(t,r)}y[t]=r.route}else y[t]=s}if(null===g)return null;let v={lazyData:null,rsc:t.rsc,prefetchRsc:t.prefetchRsc,head:t.head,prefetchHead:t.prefetchHead,loading:t.loading,parallelRoutes:m,lazyDataResolved:!1};return{route:function(e,t){let r=[e[0],t];return 2 in e&&(r[2]=e[2]),3 in e&&(r[3]=e[3]),4 in e&&(r[4]=e[4]),r}(s,y),node:v,children:g}}},updateCacheNodeOnPopstateRestoration:function(){return function e(t,r){let n=r[1],i=t.parallelRoutes,a=new Map(i);for(let t in n){let r=n[t],s=r[0],l=(0,o.createRouterCacheKey)(s),u=i.get(t);if(void 0!==u){let n=u.get(l);if(void 0!==n){let i=e(n,r),o=new Map(u);o.set(l,i),a.set(t,o)}}}let s=t.rsc,l=f(s)&&"pending"===s.status;return{lazyData:null,rsc:s,head:t.head,prefetchHead:l?t.prefetchHead:null,prefetchRsc:l?t.prefetchRsc:null,loading:l?t.loading:null,parallelRoutes:a,lazyDataResolved:!1}}}});let n=r(68071),i=r(70455),o=r(39886);function a(e,t,r){let n=l(e,t,r);return{route:e,node:n,children:null}}function s(e,t){t.then(t=>{for(let r of t[0]){let t=r.slice(0,-3),n=r[r.length-3],a=r[r.length-2],s=r[r.length-1];"string"!=typeof t&&function(e,t,r,n,a){let s=e;for(let e=0;e<t.length;e+=2){let r=t[e],n=t[e+1],o=s.children;if(null!==o){let e=o.get(r);if(void 0!==e){let t=e.route[0];if((0,i.matchSegment)(n,t)){s=e;continue}}}return}(function e(t,r,n,a){let s=t.children,l=t.node;if(null===s){null!==l&&(function e(t,r,n,a,s){let l=r[1],u=n[1],d=a[1],h=t.parallelRoutes;for(let t in l){let r=l[t],n=u[t],a=d[t],f=h.get(t),p=r[0],m=(0,o.createRouterCacheKey)(p),y=void 0!==f?f.get(m):void 0;void 0!==y&&(void 0!==n&&(0,i.matchSegment)(p,n[0])&&null!=a?e(y,r,n,a,s):c(r,y,null))}let p=t.rsc,m=a[2];null===p?t.rsc=m:f(p)&&p.resolve(m);let y=t.head;f(y)&&y.resolve(s)}(l,t.route,r,n,a),t.node=null);return}let u=r[1],d=n[1];for(let t in r){let r=u[t],n=d[t],o=s.get(t);if(void 0!==o){let t=o.route[0];if((0,i.matchSegment)(r[0],t)&&null!=n)return e(o,r,n,a)}}})(s,r,n,a)}(e,t,n,a,s)}u(e,null)},t=>{u(e,t)})}function l(e,t,r){let n=e[1],i=null!==t?t[1]:null,a=new Map;for(let e in n){let t=n[e],s=null!==i?i[e]:null,u=t[0],c=(0,o.createRouterCacheKey)(u),d=l(t,void 0===s?null:s,r),f=new Map;f.set(c,d),a.set(e,f)}let s=0===a.size,u=null!==t?t[2]:null,c=null!==t?t[3]:null;return{lazyData:null,parallelRoutes:a,prefetchRsc:void 0!==u?u:null,prefetchHead:s?r:null,loading:void 0!==c?c:null,rsc:h(),head:s?h():null,lazyDataResolved:!1}}function u(e,t){let r=e.node;if(null===r)return;let n=e.children;if(null===n)c(e.route,r,t);else for(let e of n.values())u(e,t);e.node=null}function c(e,t,r){let n=e[1],i=t.parallelRoutes;for(let e in n){let t=n[e],a=i.get(e);if(void 0===a)continue;let s=t[0],l=(0,o.createRouterCacheKey)(s),u=a.get(l);void 0!==u&&c(t,u,r)}let a=t.rsc;f(a)&&(null===r?a.resolve(null):a.reject(r));let s=t.head;f(s)&&s.resolve(null)}let d=Symbol();function f(e){return e&&e.tag===d}function h(){let e,t;let r=new Promise((r,n)=>{e=r,t=n});return r.status="pending",r.resolve=t=>{"pending"===r.status&&(r.status="fulfilled",r.value=t,e(t))},r.reject=e=>{"pending"===r.status&&(r.status="rejected",r.reason=e,t(e))},r.tag=d,r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},79373:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createPrefetchCacheEntryForInitialLoad:function(){return u},getOrCreatePrefetchCacheEntry:function(){return l},prunePrefetchCache:function(){return d}});let n=r(17584),i=r(9009),o=r(57767),a=r(61156);function s(e,t){let r=(0,n.createHrefFromUrl)(e,!1);return t?t+"%"+r:r}function l(e){let t,{url:r,nextUrl:n,tree:i,buildId:a,prefetchCache:l,kind:u}=e,d=s(r,n),f=l.get(d);if(f)t=f;else{let e=s(r),n=l.get(e);n&&(t=n)}return t?(t.status=p(t),t.kind!==o.PrefetchKind.FULL&&u===o.PrefetchKind.FULL)?c({tree:i,url:r,buildId:a,nextUrl:n,prefetchCache:l,kind:null!=u?u:o.PrefetchKind.TEMPORARY}):(u&&t.kind===o.PrefetchKind.TEMPORARY&&(t.kind=u),t):c({tree:i,url:r,buildId:a,nextUrl:n,prefetchCache:l,kind:u||o.PrefetchKind.TEMPORARY})}function u(e){let{nextUrl:t,tree:r,prefetchCache:n,url:i,kind:a,data:l}=e,[,,,u]=l,c=u?s(i,t):s(i),d={treeAtTimeOfPrefetch:r,data:Promise.resolve(l),kind:a,prefetchTime:Date.now(),lastUsedTime:Date.now(),key:c,status:o.PrefetchCacheEntryStatus.fresh};return n.set(c,d),d}function c(e){let{url:t,kind:r,tree:n,nextUrl:l,buildId:u,prefetchCache:c}=e,d=s(t),f=a.prefetchQueue.enqueue(()=>(0,i.fetchServerResponse)(t,n,l,u,r).then(e=>{let[,,,r]=e;return r&&function(e){let{url:t,nextUrl:r,prefetchCache:n}=e,i=s(t),o=n.get(i);if(!o)return;let a=s(t,r);n.set(a,o),n.delete(i)}({url:t,nextUrl:l,prefetchCache:c}),e})),h={treeAtTimeOfPrefetch:n,data:f,kind:r,prefetchTime:Date.now(),lastUsedTime:null,key:d,status:o.PrefetchCacheEntryStatus.fresh};return c.set(d,h),h}function d(e){for(let[t,r]of e)p(r)===o.PrefetchCacheEntryStatus.expired&&e.delete(t)}let f=1e3*Number("30"),h=1e3*Number("300");function p(e){let{kind:t,prefetchTime:r,lastUsedTime:n}=e;return Date.now()<(null!=n?n:r)+f?n?o.PrefetchCacheEntryStatus.reusable:o.PrefetchCacheEntryStatus.fresh:"auto"===t&&Date.now()<r+h?o.PrefetchCacheEntryStatus.stale:"full"===t&&Date.now()<r+h?o.PrefetchCacheEntryStatus.reusable:o.PrefetchCacheEntryStatus.expired}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},95703:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fastRefreshReducer",{enumerable:!0,get:function(){return n}}),r(9009),r(17584),r(95166),r(23772),r(20941),r(17252),r(9894),r(12994),r(65652),r(45262);let n=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},22492:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"findHeadInCache",{enumerable:!0,get:function(){return i}});let n=r(39886);function i(e,t){return function e(t,r,i){if(0===Object.keys(r).length)return[t,i];for(let o in r){let[a,s]=r[o],l=t.parallelRoutes.get(o);if(!l)continue;let u=(0,n.createRouterCacheKey)(a),c=l.get(u);if(!c)continue;let d=e(c,s,i+"/"+u);if(d)return d}return null}(e,t,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},62162:(e,t)=>{"use strict";function r(e){return Array.isArray(e)?e[1]:e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getSegmentValue",{enumerable:!0,get:function(){return r}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},45262:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hasInterceptionRouteInCurrentTree",{enumerable:!0,get:function(){return function e(t){let[r,i]=t;if(Array.isArray(r)&&("di"===r[2]||"ci"===r[2])||"string"==typeof r&&(0,n.isInterceptionRouteAppPath)(r))return!0;if(i){for(let t in i)if(e(i[t]))return!0}return!1}}});let n=r(87356);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},20941:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{handleExternalUrl:function(){return y},navigateReducer:function(){return v}}),r(9009);let n=r(17584),i=r(43193),o=r(95166),a=r(54614),s=r(23772),l=r(57767),u=r(17252),c=r(9894),d=r(61156),f=r(12994),h=r(68071),p=(r(68831),r(79373)),m=r(12895);function y(e,t,r,n){return t.mpaNavigation=!0,t.canonicalUrl=r,t.pendingPush=n,t.scrollableSegments=void 0,(0,u.handleMutable)(e,t)}function g(e){let t=[],[r,n]=e;if(0===Object.keys(n).length)return[[r]];for(let[e,i]of Object.entries(n))for(let n of g(i))""===r?t.push([e,...n]):t.push([r,e,...n]);return t}let v=function(e,t){let{url:r,isExternalUrl:v,navigateType:b,shouldScroll:x}=t,w={},{hash:P}=r,E=(0,n.createHrefFromUrl)(r),R="push"===b;if((0,p.prunePrefetchCache)(e.prefetchCache),w.preserveCustomHistoryState=!1,v)return y(e,w,r.toString(),R);let _=(0,p.getOrCreatePrefetchCacheEntry)({url:r,nextUrl:e.nextUrl,tree:e.tree,buildId:e.buildId,prefetchCache:e.prefetchCache}),{treeAtTimeOfPrefetch:S,data:T}=_;return d.prefetchQueue.bump(T),T.then(t=>{let[r,d]=t,p=!1;if(_.lastUsedTime||(_.lastUsedTime=Date.now(),p=!0),"string"==typeof r)return y(e,w,r,R);if(document.getElementById("__next-page-redirect"))return y(e,w,E,R);let v=e.tree,b=e.cache,T=[];for(let t of r){let r=t.slice(0,-4),n=t.slice(-3)[0],u=["",...r],d=(0,o.applyRouterStatePatchToTree)(u,v,n,E);if(null===d&&(d=(0,o.applyRouterStatePatchToTree)(u,S,n,E)),null!==d){if((0,s.isNavigatingToNewRootLayout)(v,d))return y(e,w,E,R);let o=(0,f.createEmptyCacheNode)(),x=!1;for(let e of(_.status!==l.PrefetchCacheEntryStatus.stale||p?x=(0,c.applyFlightData)(b,o,t,_):(x=function(e,t,r,n){let i=!1;for(let o of(e.rsc=t.rsc,e.prefetchRsc=t.prefetchRsc,e.loading=t.loading,e.parallelRoutes=new Map(t.parallelRoutes),g(n).map(e=>[...r,...e])))(0,m.clearCacheNodeDataForSegmentPath)(e,t,o),i=!0;return i}(o,b,r,n),_.lastUsedTime=Date.now()),(0,a.shouldHardNavigate)(u,v)?(o.rsc=b.rsc,o.prefetchRsc=b.prefetchRsc,(0,i.invalidateCacheBelowFlightSegmentPath)(o,b,r),w.cache=o):x&&(w.cache=o,b=o),v=d,g(n))){let t=[...r,...e];t[t.length-1]!==h.DEFAULT_SEGMENT_KEY&&T.push(t)}}}return w.patchedTree=v,w.canonicalUrl=d?(0,n.createHrefFromUrl)(d):E,w.pendingPush=R,w.scrollableSegments=T,w.hashFragment=P,w.shouldScroll=x,(0,u.handleMutable)(e,w)},()=>e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},61156:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{prefetchQueue:function(){return a},prefetchReducer:function(){return s}});let n=r(5138),i=r(77815),o=r(79373),a=new i.PromiseQueue(5);function s(e,t){(0,o.prunePrefetchCache)(e.prefetchCache);let{url:r}=t;return r.searchParams.delete(n.NEXT_RSC_UNION_QUERY),(0,o.getOrCreatePrefetchCacheEntry)({url:r,nextUrl:e.nextUrl,prefetchCache:e.prefetchCache,kind:t.kind,tree:e.tree,buildId:e.buildId}),e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},69809:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"refreshReducer",{enumerable:!0,get:function(){return p}});let n=r(9009),i=r(17584),o=r(95166),a=r(23772),s=r(20941),l=r(17252),u=r(114),c=r(12994),d=r(65652),f=r(45262),h=r(84158);function p(e,t){let{origin:r}=t,p={},m=e.canonicalUrl,y=e.tree;p.preserveCustomHistoryState=!1;let g=(0,c.createEmptyCacheNode)(),v=(0,f.hasInterceptionRouteInCurrentTree)(e.tree);return g.lazyData=(0,n.fetchServerResponse)(new URL(m,r),[y[0],y[1],y[2],"refetch"],v?e.nextUrl:null,e.buildId),g.lazyData.then(async r=>{let[n,c]=r;if("string"==typeof n)return(0,s.handleExternalUrl)(e,p,n,e.pushRef.pendingPush);for(let r of(g.lazyData=null,n)){if(3!==r.length)return console.log("REFRESH FAILED"),e;let[n]=r,l=(0,o.applyRouterStatePatchToTree)([""],y,n,e.canonicalUrl);if(null===l)return(0,d.handleSegmentMismatch)(e,t,n);if((0,a.isNavigatingToNewRootLayout)(y,l))return(0,s.handleExternalUrl)(e,p,m,e.pushRef.pendingPush);let f=c?(0,i.createHrefFromUrl)(c):void 0;c&&(p.canonicalUrl=f);let[b,x]=r.slice(-2);if(null!==b){let e=b[2];g.rsc=e,g.prefetchRsc=null,(0,u.fillLazyItemsTillLeafWithHead)(g,void 0,n,b,x),p.prefetchCache=new Map}await (0,h.refreshInactiveParallelSegments)({state:e,updatedTree:l,updatedCache:g,includeNextUrl:v,canonicalUrl:p.canonicalUrl||e.canonicalUrl}),p.cache=g,p.patchedTree=l,p.canonicalUrl=m,y=l}return(0,l.handleMutable)(e,p)},()=>e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},85608:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"restoreReducer",{enumerable:!0,get:function(){return o}});let n=r(17584),i=r(47326);function o(e,t){var r;let{url:o,tree:a}=t,s=(0,n.createHrefFromUrl)(o),l=a||e.tree,u=e.cache;return{buildId:e.buildId,canonicalUrl:s,pushRef:{pendingPush:!1,mpaNavigation:!1,preserveCustomHistoryState:!0},focusAndScrollRef:e.focusAndScrollRef,cache:u,prefetchCache:e.prefetchCache,tree:l,nextUrl:null!=(r=(0,i.extractPathFromFlightRouterState)(l))?r:o.pathname}}r(68831),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},25240:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverActionReducer",{enumerable:!0,get:function(){return b}});let n=r(15424),i=r(5138),o=r(3486),a=r(17584),s=r(20941),l=r(95166),u=r(23772),c=r(17252),d=r(114),f=r(12994),h=r(45262),p=r(65652),m=r(84158),{createFromFetch:y,encodeReply:g}=r(56493);async function v(e,t,r){let a,{actionId:s,actionArgs:l}=r,u=await g(l),c=await fetch("",{method:"POST",headers:{Accept:i.RSC_CONTENT_TYPE_HEADER,[i.ACTION]:s,[i.NEXT_ROUTER_STATE_TREE]:encodeURIComponent(JSON.stringify(e.tree)),...t?{[i.NEXT_URL]:t}:{}},body:u}),d=c.headers.get("x-action-redirect");try{let e=JSON.parse(c.headers.get("x-action-revalidated")||"[[],0,0]");a={paths:e[0]||[],tag:!!e[1],cookie:e[2]}}catch(e){a={paths:[],tag:!1,cookie:!1}}let f=d?new URL((0,o.addBasePath)(d),new URL(e.canonicalUrl,window.location.href)):void 0;if(c.headers.get("content-type")===i.RSC_CONTENT_TYPE_HEADER){let e=await y(Promise.resolve(c),{callServer:n.callServer});if(d){let[,t]=null!=e?e:[];return{actionFlightData:t,redirectLocation:f,revalidatedParts:a}}let[t,[,r]]=null!=e?e:[];return{actionResult:t,actionFlightData:r,redirectLocation:f,revalidatedParts:a}}return{redirectLocation:f,revalidatedParts:a}}function b(e,t){let{resolve:r,reject:n}=t,i={},o=e.canonicalUrl,y=e.tree;i.preserveCustomHistoryState=!1;let g=e.nextUrl&&(0,h.hasInterceptionRouteInCurrentTree)(e.tree)?e.nextUrl:null;return i.inFlightServerAction=v(e,g,t),i.inFlightServerAction.then(async n=>{let{actionResult:h,actionFlightData:v,redirectLocation:b}=n;if(b&&(e.pushRef.pendingPush=!0,i.pendingPush=!0),!v)return(r(h),b)?(0,s.handleExternalUrl)(e,i,b.href,e.pushRef.pendingPush):e;if("string"==typeof v)return(0,s.handleExternalUrl)(e,i,v,e.pushRef.pendingPush);if(i.inFlightServerAction=null,b){let e=(0,a.createHrefFromUrl)(b,!1);i.canonicalUrl=e}for(let r of v){if(3!==r.length)return console.log("SERVER ACTION APPLY FAILED"),e;let[n]=r,c=(0,l.applyRouterStatePatchToTree)([""],y,n,b?(0,a.createHrefFromUrl)(b):e.canonicalUrl);if(null===c)return(0,p.handleSegmentMismatch)(e,t,n);if((0,u.isNavigatingToNewRootLayout)(y,c))return(0,s.handleExternalUrl)(e,i,o,e.pushRef.pendingPush);let[h,v]=r.slice(-2),x=null!==h?h[2]:null;if(null!==x){let t=(0,f.createEmptyCacheNode)();t.rsc=x,t.prefetchRsc=null,(0,d.fillLazyItemsTillLeafWithHead)(t,void 0,n,h,v),await (0,m.refreshInactiveParallelSegments)({state:e,updatedTree:c,updatedCache:t,includeNextUrl:!!g,canonicalUrl:i.canonicalUrl||e.canonicalUrl}),i.cache=t,i.prefetchCache=new Map}i.patchedTree=c,y=c}return r(h),(0,c.handleMutable)(e,i)},t=>(n(t),e))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},14025:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverPatchReducer",{enumerable:!0,get:function(){return d}});let n=r(17584),i=r(95166),o=r(23772),a=r(20941),s=r(9894),l=r(17252),u=r(12994),c=r(65652);function d(e,t){let{serverResponse:r}=t,[d,f]=r,h={};if(h.preserveCustomHistoryState=!1,"string"==typeof d)return(0,a.handleExternalUrl)(e,h,d,e.pushRef.pendingPush);let p=e.tree,m=e.cache;for(let r of d){let l=r.slice(0,-4),[d]=r.slice(-3,-2),y=(0,i.applyRouterStatePatchToTree)(["",...l],p,d,e.canonicalUrl);if(null===y)return(0,c.handleSegmentMismatch)(e,t,d);if((0,o.isNavigatingToNewRootLayout)(p,y))return(0,a.handleExternalUrl)(e,h,e.canonicalUrl,e.pushRef.pendingPush);let g=f?(0,n.createHrefFromUrl)(f):void 0;g&&(h.canonicalUrl=g);let v=(0,u.createEmptyCacheNode)();(0,s.applyFlightData)(m,v,r),h.patchedTree=y,h.cache=v,m=v,p=y}return(0,l.handleMutable)(e,h)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},84158:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{addRefreshMarkerToActiveParallelSegments:function(){return function e(t,r){let[n,i,,a]=t;for(let s in n.includes(o.PAGE_SEGMENT_KEY)&&"refresh"!==a&&(t[2]=r,t[3]="refresh"),i)e(i[s],r)}},refreshInactiveParallelSegments:function(){return a}});let n=r(9894),i=r(9009),o=r(68071);async function a(e){let t=new Set;await s({...e,rootTree:e.updatedTree,fetchedSegments:t})}async function s(e){let{state:t,updatedTree:r,updatedCache:o,includeNextUrl:a,fetchedSegments:l,rootTree:u=r,canonicalUrl:c}=e,[,d,f,h]=r,p=[];if(f&&f!==c&&"refresh"===h&&!l.has(f)){l.add(f);let e=(0,i.fetchServerResponse)(new URL(f,location.origin),[u[0],u[1],u[2],"refetch"],a?t.nextUrl:null,t.buildId).then(e=>{let t=e[0];if("string"!=typeof t)for(let e of t)(0,n.applyFlightData)(o,o,e)});p.push(e)}for(let e in d){let r=s({state:t,updatedTree:d[e],updatedCache:o,includeNextUrl:a,fetchedSegments:l,rootTree:u,canonicalUrl:c});p.push(r)}await Promise.all(p)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},57767:(e,t)=>{"use strict";var r,n;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ACTION_FAST_REFRESH:function(){return u},ACTION_NAVIGATE:function(){return o},ACTION_PREFETCH:function(){return l},ACTION_REFRESH:function(){return i},ACTION_RESTORE:function(){return a},ACTION_SERVER_ACTION:function(){return c},ACTION_SERVER_PATCH:function(){return s},PrefetchCacheEntryStatus:function(){return n},PrefetchKind:function(){return r},isThenable:function(){return d}});let i="refresh",o="navigate",a="restore",s="server-patch",l="prefetch",u="fast-refresh",c="server-action";function d(e){return e&&("object"==typeof e||"function"==typeof e)&&"function"==typeof e.then}(function(e){e.AUTO="auto",e.FULL="full",e.TEMPORARY="temporary"})(r||(r={})),function(e){e.fresh="fresh",e.reusable="reusable",e.expired="expired",e.stale="stale"}(n||(n={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},83860:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"reducer",{enumerable:!0,get:function(){return n}}),r(57767),r(20941),r(14025),r(85608),r(69809),r(61156),r(95703),r(25240);let n=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},54614:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"shouldHardNavigate",{enumerable:!0,get:function(){return function e(t,r){let[i,o]=r,[a,s]=t;return(0,n.matchSegment)(a,i)?!(t.length<=2)&&e(t.slice(2),o[s]):!!Array.isArray(a)}}});let n=r(70455);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},23325:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createDynamicallyTrackedSearchParams:function(){return s},createUntrackedSearchParams:function(){return a}});let n=r(45869),i=r(52846),o=r(22255);function a(e){let t=n.staticGenerationAsyncStorage.getStore();return t&&t.forceStatic?{}:e}function s(e){let t=n.staticGenerationAsyncStorage.getStore();return t?t.forceStatic?{}:t.isStaticGeneration||t.dynamicShouldError?new Proxy({},{get:(e,r,n)=>("string"==typeof r&&(0,i.trackDynamicDataAccessed)(t,"searchParams."+r),o.ReflectAdapter.get(e,r,n)),has:(e,r)=>("string"==typeof r&&(0,i.trackDynamicDataAccessed)(t,"searchParams."+r),Reflect.has(e,r)),ownKeys:e=>((0,i.trackDynamicDataAccessed)(t,"searchParams"),Reflect.ownKeys(e))}):e:e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},86488:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{StaticGenBailoutError:function(){return n},isStaticGenBailoutError:function(){return i}});let r="NEXT_STATIC_GEN_BAILOUT";class n extends Error{constructor(...e){super(...e),this.code=r}}function i(e){return"object"==typeof e&&null!==e&&"code"in e&&e.code===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},39519:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unresolvedThenable",{enumerable:!0,get:function(){return r}});let r={then:()=>{}};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},77326:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{useReducerWithReduxDevtools:function(){return s},useUnwrapState:function(){return a}});let n=r(31667)._(r(17577)),i=r(57767);function o(e){if(e instanceof Map){let t={};for(let[r,n]of e.entries()){if("function"==typeof n){t[r]="fn()";continue}if("object"==typeof n&&null!==n){if(n.$$typeof){t[r]=n.$$typeof.toString();continue}if(n._bundlerConfig){t[r]="FlightData";continue}}t[r]=o(n)}return t}if("object"==typeof e&&null!==e){let t={};for(let r in e){let n=e[r];if("function"==typeof n){t[r]="fn()";continue}if("object"==typeof n&&null!==n){if(n.$$typeof){t[r]=n.$$typeof.toString();continue}if(n.hasOwnProperty("_bundlerConfig")){t[r]="FlightData";continue}}t[r]=o(n)}return t}return Array.isArray(e)?e.map(o):e}function a(e){return(0,i.isThenable)(e)?(0,n.use)(e):e}r(33879);let s=function(e){return[e,()=>{},()=>{}]};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},39683:(e,t,r)=>{"use strict";function n(e,t,r,n){return!1}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getDomainLocale",{enumerable:!0,get:function(){return n}}),r(23658),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},37929:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hasBasePath",{enumerable:!0,get:function(){return i}});let n=r(34655);function i(e){return(0,n.pathHasPrefix)(e,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},79404:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return v}});let n=r(98052),i=r(10326),o=n._(r(17577)),a=r(25619),s=r(60944),l=r(43071),u=r(51348),c=r(53416),d=r(50131),f=r(52413),h=r(49408),p=r(39683),m=r(3486),y=r(57767);function g(e){return"string"==typeof e?e:(0,l.formatUrl)(e)}let v=o.default.forwardRef(function(e,t){let r,n;let{href:l,as:v,children:b,prefetch:x=null,passHref:w,replace:P,shallow:E,scroll:R,locale:_,onClick:S,onMouseEnter:T,onTouchStart:j,legacyBehavior:O=!1,...C}=e;r=b,O&&("string"==typeof r||"number"==typeof r)&&(r=(0,i.jsx)("a",{children:r}));let M=o.default.useContext(d.RouterContext),A=o.default.useContext(f.AppRouterContext),D=null!=M?M:A,k=!M,N=!1!==x,L=null===x?y.PrefetchKind.AUTO:y.PrefetchKind.FULL,{href:I,as:F}=o.default.useMemo(()=>{if(!M){let e=g(l);return{href:e,as:v?g(v):e}}let[e,t]=(0,a.resolveHref)(M,l,!0);return{href:e,as:v?(0,a.resolveHref)(M,v):t||e}},[M,l,v]),U=o.default.useRef(I),V=o.default.useRef(F);O&&(n=o.default.Children.only(r));let B=O?n&&"object"==typeof n&&n.ref:t,[W,H,z]=(0,h.useIntersection)({rootMargin:"200px"}),$=o.default.useCallback(e=>{(V.current!==F||U.current!==I)&&(z(),V.current=F,U.current=I),W(e),B&&("function"==typeof B?B(e):"object"==typeof B&&(B.current=e))},[F,B,I,z,W]);o.default.useEffect(()=>{},[F,I,H,_,N,null==M?void 0:M.locale,D,k,L]);let X={ref:$,onClick(e){O||"function"!=typeof S||S(e),O&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),D&&!e.defaultPrevented&&function(e,t,r,n,i,a,l,u,c){let{nodeName:d}=e.currentTarget;if("A"===d.toUpperCase()&&(function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||!c&&!(0,s.isLocalURL)(r)))return;e.preventDefault();let f=()=>{let e=null==l||l;"beforePopState"in t?t[i?"replace":"push"](r,n,{shallow:a,locale:u,scroll:e}):t[i?"replace":"push"](n||r,{scroll:e})};c?o.default.startTransition(f):f()}(e,D,I,F,P,E,R,_,k)},onMouseEnter(e){O||"function"!=typeof T||T(e),O&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e)},onTouchStart:function(e){O||"function"!=typeof j||j(e),O&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e)}};if((0,u.isAbsoluteUrl)(F))X.href=F;else if(!O||w||"a"===n.type&&!("href"in n.props)){let e=void 0!==_?_:null==M?void 0:M.locale,t=(null==M?void 0:M.isLocaleDomain)&&(0,p.getDomainLocale)(F,e,null==M?void 0:M.locales,null==M?void 0:M.domainLocales);X.href=t||(0,m.addBasePath)((0,c.addLocale)(F,e,null==M?void 0:M.defaultLocale))}return O?o.default.cloneElement(n,X):(0,i.jsx)("a",{...C,...X,children:r})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},23658:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return o}});let n=r(83236),i=r(93067),o=e=>{if(!e.startsWith("/"))return e;let{pathname:t,query:r,hash:o}=(0,i.parsePath)(e);return""+(0,n.removeTrailingSlash)(t)+r+o};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},74237:(e,t,r)=>{"use strict";function n(e){return e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeBasePath",{enumerable:!0,get:function(){return n}}),r(37929),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},10956:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{cancelIdleCallback:function(){return n},requestIdleCallback:function(){return r}});let r="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let t=Date.now();return self.setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})},1)},n="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},25619:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"resolveHref",{enumerable:!0,get:function(){return d}});let n=r(72149),i=r(43071),o=r(20757),a=r(51348),s=r(23658),l=r(60944),u=r(94903),c=r(81394);function d(e,t,r){let d;let f="string"==typeof t?t:(0,i.formatWithValidation)(t),h=f.match(/^[a-zA-Z]{1,}:\/\//),p=h?f.slice(h[0].length):f;if((p.split("?",1)[0]||"").match(/(\/\/|\\)/)){console.error("Invalid href '"+f+"' passed to next/router in page: '"+e.pathname+"'. Repeated forward-slashes (//) or backslashes \\ are not valid in the href.");let t=(0,a.normalizeRepeatedSlashes)(p);f=(h?h[0]:"")+t}if(!(0,l.isLocalURL)(f))return r?[f]:f;try{d=new URL(f.startsWith("#")?e.asPath:e.pathname,"http://n")}catch(e){d=new URL("/","http://n")}try{let e=new URL(f,d);e.pathname=(0,s.normalizePathTrailingSlash)(e.pathname);let t="";if((0,u.isDynamicRoute)(e.pathname)&&e.searchParams&&r){let r=(0,n.searchParamsToUrlQuery)(e.searchParams),{result:a,params:s}=(0,c.interpolateAs)(e.pathname,e.pathname,r);a&&(t=(0,i.formatWithValidation)({pathname:a,hash:e.hash,query:(0,o.omit)(r,s)}))}let a=e.origin===d.origin?e.href.slice(e.origin.length):e.href;return r?[a,t||a]:a}catch(e){return r?[f]:f}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},49408:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useIntersection",{enumerable:!0,get:function(){return l}});let n=r(17577),i=r(10956),o="function"==typeof IntersectionObserver,a=new Map,s=[];function l(e){let{rootRef:t,rootMargin:r,disabled:l}=e,u=l||!o,[c,d]=(0,n.useState)(!1),f=(0,n.useRef)(null),h=(0,n.useCallback)(e=>{f.current=e},[]);return(0,n.useEffect)(()=>{if(o){if(u||c)return;let e=f.current;if(e&&e.tagName)return function(e,t,r){let{id:n,observer:i,elements:o}=function(e){let t;let r={root:e.root||null,margin:e.rootMargin||""},n=s.find(e=>e.root===r.root&&e.margin===r.margin);if(n&&(t=a.get(n)))return t;let i=new Map;return t={id:r,observer:new IntersectionObserver(e=>{e.forEach(e=>{let t=i.get(e.target),r=e.isIntersecting||e.intersectionRatio>0;t&&r&&t(r)})},e),elements:i},s.push(r),a.set(r,t),t}(r);return o.set(e,t),i.observe(e),function(){if(o.delete(e),i.unobserve(e),0===o.size){i.disconnect(),a.delete(n);let e=s.findIndex(e=>e.root===n.root&&e.margin===n.margin);e>-1&&s.splice(e,1)}}}(e,e=>e&&d(e),{root:null==t?void 0:t.current,rootMargin:r})}else if(!c){let e=(0,i.requestIdleCallback)(()=>d(!0));return()=>(0,i.cancelIdleCallback)(e)}},[u,r,t,c,f.current]),[h,c,(0,n.useCallback)(()=>{d(!1)},[])]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},25633:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ACTION_SUFFIX:function(){return l},APP_DIR_ALIAS:function(){return T},CACHE_ONE_YEAR:function(){return x},DOT_NEXT_ALIAS:function(){return _},ESLINT_DEFAULT_DIRS:function(){return $},GSP_NO_RETURNED_VALUE:function(){return U},GSSP_COMPONENT_MEMBER_ERROR:function(){return W},GSSP_NO_RETURNED_VALUE:function(){return V},INSTRUMENTATION_HOOK_FILENAME:function(){return E},MIDDLEWARE_FILENAME:function(){return w},MIDDLEWARE_LOCATION_REGEXP:function(){return P},NEXT_BODY_SUFFIX:function(){return d},NEXT_CACHE_IMPLICIT_TAG_ID:function(){return b},NEXT_CACHE_REVALIDATED_TAGS_HEADER:function(){return p},NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER:function(){return m},NEXT_CACHE_SOFT_TAGS_HEADER:function(){return h},NEXT_CACHE_SOFT_TAG_MAX_LENGTH:function(){return v},NEXT_CACHE_TAGS_HEADER:function(){return f},NEXT_CACHE_TAG_MAX_ITEMS:function(){return y},NEXT_CACHE_TAG_MAX_LENGTH:function(){return g},NEXT_DATA_SUFFIX:function(){return u},NEXT_INTERCEPTION_MARKER_PREFIX:function(){return n},NEXT_META_SUFFIX:function(){return c},NEXT_QUERY_PARAM_PREFIX:function(){return r},NON_STANDARD_NODE_ENV:function(){return H},PAGES_DIR_ALIAS:function(){return R},PRERENDER_REVALIDATE_HEADER:function(){return i},PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER:function(){return o},PUBLIC_DIR_MIDDLEWARE_CONFLICT:function(){return D},ROOT_DIR_ALIAS:function(){return S},RSC_ACTION_CLIENT_WRAPPER_ALIAS:function(){return A},RSC_ACTION_ENCRYPTION_ALIAS:function(){return M},RSC_ACTION_PROXY_ALIAS:function(){return C},RSC_ACTION_VALIDATE_ALIAS:function(){return O},RSC_MOD_REF_PROXY_ALIAS:function(){return j},RSC_PREFETCH_SUFFIX:function(){return a},RSC_SUFFIX:function(){return s},SERVER_PROPS_EXPORT_ERROR:function(){return F},SERVER_PROPS_GET_INIT_PROPS_CONFLICT:function(){return N},SERVER_PROPS_SSG_CONFLICT:function(){return L},SERVER_RUNTIME:function(){return X},SSG_FALLBACK_EXPORT_ERROR:function(){return z},SSG_GET_INITIAL_PROPS_CONFLICT:function(){return k},STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR:function(){return I},UNSTABLE_REVALIDATE_RENAME_ERROR:function(){return B},WEBPACK_LAYERS:function(){return K},WEBPACK_RESOURCE_QUERIES:function(){return G}});let r="nxtP",n="nxtI",i="x-prerender-revalidate",o="x-prerender-revalidate-if-generated",a=".prefetch.rsc",s=".rsc",l=".action",u=".json",c=".meta",d=".body",f="x-next-cache-tags",h="x-next-cache-soft-tags",p="x-next-revalidated-tags",m="x-next-revalidate-tag-token",y=128,g=256,v=1024,b="_N_T_",x=31536e3,w="middleware",P=`(?:src/)?${w}`,E="instrumentation",R="private-next-pages",_="private-dot-next",S="private-next-root-dir",T="private-next-app-dir",j="private-next-rsc-mod-ref-proxy",O="private-next-rsc-action-validate",C="private-next-rsc-server-reference",M="private-next-rsc-action-encryption",A="private-next-rsc-action-client-wrapper",D="You can not have a '_next' folder inside of your public folder. This conflicts with the internal '/_next' route. https://nextjs.org/docs/messages/public-next-folder-conflict",k="You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps",N="You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.",L="You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps",I="can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props",F="pages with `getServerSideProps` can not be exported. See more info here: https://nextjs.org/docs/messages/gssp-export",U="Your `getStaticProps` function did not return an object. Did you forget to add a `return`?",V="Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?",B="The `unstable_revalidate` property is available for general use.\nPlease use `revalidate` instead.",W="can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member",H='You are using a non-standard "NODE_ENV" value in your environment. This creates inconsistencies in the project and is strongly advised against. Read more: https://nextjs.org/docs/messages/non-standard-node-env',z="Pages with `fallback` enabled in `getStaticPaths` can not be exported. See more info here: https://nextjs.org/docs/messages/ssg-fallback-true-export",$=["app","pages","components","lib","src"],X={edge:"edge",experimentalEdge:"experimental-edge",nodejs:"nodejs"},q={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",api:"api",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",appMetadataRoute:"app-metadata-route",appRouteHandler:"app-route-handler"},K={...q,GROUP:{serverOnly:[q.reactServerComponents,q.actionBrowser,q.appMetadataRoute,q.appRouteHandler,q.instrument],clientOnly:[q.serverSideRendering,q.appPagesBrowser],nonClientServerTarget:[q.middleware,q.api],app:[q.reactServerComponents,q.actionBrowser,q.appMetadataRoute,q.appRouteHandler,q.serverSideRendering,q.appPagesBrowser,q.shared,q.instrument]}},G={edgeSSREntry:"__next_edge_ssr_entry__",metadata:"__next_metadata__",metadataRoute:"__next_metadata_route__",metadataImageMeta:"__next_metadata_image_meta__"}},56401:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getPathname:function(){return n},isFullStringUrl:function(){return i},parseUrl:function(){return o}});let r="http://n";function n(e){return new URL(e,r).pathname}function i(e){return/https?:\/\//.test(e)}function o(e){let t;try{t=new URL(e,r)}catch{}return t}},52846:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{Postpone:function(){return d},createPostponedAbortSignal:function(){return g},createPrerenderState:function(){return l},formatDynamicAPIAccesses:function(){return m},markCurrentScopeAsDynamic:function(){return u},trackDynamicDataAccessed:function(){return c},trackDynamicFetch:function(){return f},usedDynamicAPIs:function(){return p}});let n=function(e){return e&&e.__esModule?e:{default:e}}(r(17577)),i=r(70442),o=r(86488),a=r(56401),s="function"==typeof n.default.unstable_postpone;function l(e){return{isDebugSkeleton:e,dynamicAccesses:[]}}function u(e,t){let r=(0,a.getPathname)(e.urlPathname);if(!e.isUnstableCacheCallback){if(e.dynamicShouldError)throw new o.StaticGenBailoutError(`Route ${r} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${t}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`);if(e.prerenderState)h(e.prerenderState,t,r);else if(e.revalidate=0,e.isStaticGeneration){let n=new i.DynamicServerError(`Route ${r} couldn't be rendered statically because it used ${t}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`);throw e.dynamicUsageDescription=t,e.dynamicUsageStack=n.stack,n}}}function c(e,t){let r=(0,a.getPathname)(e.urlPathname);if(e.isUnstableCacheCallback)throw Error(`Route ${r} used "${t}" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "${t}" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`);if(e.dynamicShouldError)throw new o.StaticGenBailoutError(`Route ${r} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${t}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`);if(e.prerenderState)h(e.prerenderState,t,r);else if(e.revalidate=0,e.isStaticGeneration){let n=new i.DynamicServerError(`Route ${r} couldn't be rendered statically because it used \`${t}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`);throw e.dynamicUsageDescription=t,e.dynamicUsageStack=n.stack,n}}function d({reason:e,prerenderState:t,pathname:r}){h(t,e,r)}function f(e,t){e.prerenderState&&h(e.prerenderState,t,e.urlPathname)}function h(e,t,r){y();let i=`Route ${r} needs to bail out of prerendering at this point because it used ${t}. React throws this special object to indicate where. It should not be caught by your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`;e.dynamicAccesses.push({stack:e.isDebugSkeleton?Error().stack:void 0,expression:t}),n.default.unstable_postpone(i)}function p(e){return e.dynamicAccesses.length>0}function m(e){return e.dynamicAccesses.filter(e=>"string"==typeof e.stack&&e.stack.length>0).map(({expression:e,stack:t})=>(t=t.split("\n").slice(4).filter(e=>!(e.includes("node_modules/next/")||e.includes(" (<anonymous>)")||e.includes(" (node:"))).join("\n"),`Dynamic API Usage Debug - ${e}:
${t}`))}function y(){if(!s)throw Error("Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js")}function g(e){y();let t=new AbortController;try{n.default.unstable_postpone(e)}catch(e){t.abort(e)}return t.signal}},92357:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getSegmentParam",{enumerable:!0,get:function(){return i}});let n=r(87356);function i(e){let t=n.INTERCEPTION_ROUTE_MARKERS.find(t=>e.startsWith(t));return(t&&(e=e.slice(t.length)),e.startsWith("[[...")&&e.endsWith("]]"))?{type:"optional-catchall",param:e.slice(5,-2)}:e.startsWith("[...")&&e.endsWith("]")?{type:t?"catchall-intercepted":"catchall",param:e.slice(4,-1)}:e.startsWith("[")&&e.endsWith("]")?{type:t?"dynamic-intercepted":"dynamic",param:e.slice(1,-1)}:null}},87356:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return i},extractInterceptionRouteInformation:function(){return a},isInterceptionRouteAppPath:function(){return o}});let n=r(72862),i=["(..)(..)","(.)","(..)","(...)"];function o(e){return void 0!==e.split("/").find(e=>i.find(t=>e.startsWith(t)))}function a(e){let t,r,o;for(let n of e.split("/"))if(r=i.find(e=>n.startsWith(e))){[t,o]=e.split(r,2);break}if(!t||!r||!o)throw Error(`Invalid interception route: ${e}. Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>`);switch(t=(0,n.normalizeAppPath)(t),r){case"(.)":o="/"===t?`/${o}`:t+"/"+o;break;case"(..)":if("/"===t)throw Error(`Invalid interception route: ${e}. Cannot use (..) marker at the root level, use (.) instead.`);o=t.split("/").slice(0,-1).concat(o).join("/");break;case"(...)":o="/"+o;break;case"(..)(..)":let a=t.split("/");if(a.length<=2)throw Error(`Invalid interception route: ${e}. Cannot use (..)(..) marker at the root level or one level up.`);o=a.slice(0,-2).concat(o).join("/");break;default:throw Error("Invariant: unexpected marker")}return{interceptingRoute:t,interceptedRoute:o}}},81616:(e,t,r)=>{"use strict";e.exports=r(20399)},52413:(e,t,r)=>{"use strict";e.exports=r(81616).vendored.contexts.AppRouterContext},97008:(e,t,r)=>{"use strict";e.exports=r(81616).vendored.contexts.HooksClientContext},50131:(e,t,r)=>{"use strict";e.exports=r(81616).vendored.contexts.RouterContext},93347:(e,t,r)=>{"use strict";e.exports=r(81616).vendored.contexts.ServerInsertedHtml},60962:(e,t,r)=>{"use strict";e.exports=r(81616).vendored["react-ssr"].ReactDOM},10326:(e,t,r)=>{"use strict";e.exports=r(81616).vendored["react-ssr"].ReactJsxRuntime},56493:(e,t,r)=>{"use strict";e.exports=r(81616).vendored["react-ssr"].ReactServerDOMWebpackClientEdge},17577:(e,t,r)=>{"use strict";e.exports=r(81616).vendored["react-ssr"].React},22255:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ReflectAdapter",{enumerable:!0,get:function(){return r}});class r{static get(e,t,r){let n=Reflect.get(e,t,r);return"function"==typeof n?n.bind(e):n}static set(e,t,r,n){return Reflect.set(e,t,r,n)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},2451:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return i}});let r=/[|\\{}()[\]^$+*?.-]/,n=/[|\\{}()[\]^$+*?.-]/g;function i(e){return r.test(e)?e.replace(n,"\\$&"):e}},92165:(e,t)=>{"use strict";function r(e){let t=5381;for(let r=0;r<e.length;r++)t=(t<<5)+t+e.charCodeAt(r)&4294967295;return t>>>0}function n(e){return r(e).toString(36).slice(0,5)}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{djb2Hash:function(){return r},hexHash:function(){return n}})},94129:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{BailoutToCSRError:function(){return n},isBailoutToCSRError:function(){return i}});let r="BAILOUT_TO_CLIENT_SIDE_RENDERING";class n extends Error{constructor(e){super("Bail out to client-side rendering: "+e),this.reason=e,this.digest=r}}function i(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}},36058:(e,t)=>{"use strict";function r(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return r}})},33879:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ActionQueueContext:function(){return s},createMutableActionQueue:function(){return c}});let n=r(31667),i=r(57767),o=r(83860),a=n._(r(17577)),s=a.default.createContext(null);function l(e,t){null!==e.pending&&(e.pending=e.pending.next,null!==e.pending?u({actionQueue:e,action:e.pending,setState:t}):e.needsRefresh&&(e.needsRefresh=!1,e.dispatch({type:i.ACTION_REFRESH,origin:window.location.origin},t)))}async function u(e){let{actionQueue:t,action:r,setState:n}=e,o=t.state;if(!o)throw Error("Invariant: Router state not initialized");t.pending=r;let a=r.payload,s=t.action(o,a);function u(e){r.discarded||(t.state=e,t.devToolsInstance&&t.devToolsInstance.send(a,e),l(t,n),r.resolve(e))}(0,i.isThenable)(s)?s.then(u,e=>{l(t,n),r.reject(e)}):u(s)}function c(){let e={state:null,dispatch:(t,r)=>(function(e,t,r){let n={resolve:r,reject:()=>{}};if(t.type!==i.ACTION_RESTORE){let e=new Promise((e,t)=>{n={resolve:e,reject:t}});(0,a.startTransition)(()=>{r(e)})}let o={payload:t,next:null,resolve:n.resolve,reject:n.reject};null===e.pending?(e.last=o,u({actionQueue:e,action:o,setState:r})):t.type===i.ACTION_NAVIGATE||t.type===i.ACTION_RESTORE?(e.pending.discarded=!0,e.last=o,e.pending.payload.type===i.ACTION_SERVER_ACTION&&(e.needsRefresh=!0),u({actionQueue:e,action:o,setState:r})):(null!==e.last&&(e.last.next=o),e.last=o)})(e,t,r),action:async(e,t)=>{if(null===e)throw Error("Invariant: Router state not initialized");return(0,o.reducer)(e,t)},pending:null,last:null};return e}},8974:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return i}});let n=r(93067);function i(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:i,hash:o}=(0,n.parsePath)(e);return""+t+r+i+o}},72862:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{normalizeAppPath:function(){return o},normalizeRscURL:function(){return a}});let n=r(36058),i=r(68071);function o(e){return(0,n.ensureLeadingSlash)(e.split("/").reduce((e,t,r,n)=>!t||(0,i.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&r===n.length-1?e:e+"/"+t,""))}function a(e){return e.replace(/\.rsc($|\?)/,"$1")}},43071:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{formatUrl:function(){return o},formatWithValidation:function(){return s},urlObjectKeys:function(){return a}});let n=r(31667)._(r(72149)),i=/https?|ftp|gopher|file/;function o(e){let{auth:t,hostname:r}=e,o=e.protocol||"",a=e.pathname||"",s=e.hash||"",l=e.query||"",u=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?u=t+e.host:r&&(u=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(u+=":"+e.port)),l&&"object"==typeof l&&(l=String(n.urlQueryToSearchParams(l)));let c=e.search||l&&"?"+l||"";return o&&!o.endsWith(":")&&(o+=":"),e.slashes||(!o||i.test(o))&&!1!==u?(u="//"+(u||""),a&&"/"!==a[0]&&(a="/"+a)):u||(u=""),s&&"#"!==s[0]&&(s="#"+s),c&&"?"!==c[0]&&(c="?"+c),""+o+u+(a=a.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+s}let a=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function s(e){return o(e)}},79976:(e,t)=>{"use strict";function r(e,t){if(void 0===t&&(t={}),t.onlyHashChange){e();return}let r=document.documentElement,n=r.style.scrollBehavior;r.style.scrollBehavior="auto",t.dontForceLayout||r.getClientRects(),e(),r.style.scrollBehavior=n}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleSmoothScroll",{enumerable:!0,get:function(){return r}})},94903:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getSortedRoutes:function(){return n.getSortedRoutes},isDynamicRoute:function(){return i.isDynamicRoute}});let n=r(44712),i=r(45541)},81394:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"interpolateAs",{enumerable:!0,get:function(){return o}});let n=r(9966),i=r(37249);function o(e,t,r){let o="",a=(0,i.getRouteRegex)(e),s=a.groups,l=(t!==e?(0,n.getRouteMatcher)(a)(t):"")||r;o=e;let u=Object.keys(s);return u.every(e=>{let t=l[e]||"",{repeat:r,optional:n}=s[e],i="["+(r?"...":"")+e+"]";return n&&(i=(t?"":"/")+"["+i+"]"),r&&!Array.isArray(t)&&(t=[t]),(n||e in l)&&(o=o.replace(i,r?t.map(e=>encodeURIComponent(e)).join("/"):encodeURIComponent(t))||"/")})||(o=""),{params:u,result:o}}},32148:(e,t)=>{"use strict";function r(e){return/Googlebot|Mediapartners-Google|AdsBot-Google|googleweblight|Storebot-Google|Google-PageRenderer|Bingbot|BingPreview|Slurp|DuckDuckBot|baiduspider|yandex|sogou|LinkedInBot|bitlybot|tumblr|vkShare|quora link preview|facebookexternalhit|facebookcatalog|Twitterbot|applebot|redditbot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|ia_archiver/i.test(e)}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isBot",{enumerable:!0,get:function(){return r}})},45541:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isDynamicRoute",{enumerable:!0,get:function(){return o}});let n=r(87356),i=/\/\[[^/]+?\](?=\/|$)/;function o(e){return(0,n.isInterceptionRouteAppPath)(e)&&(e=(0,n.extractInterceptionRouteInformation)(e).interceptedRoute),i.test(e)}},60944:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return o}});let n=r(51348),i=r(37929);function o(e){if(!(0,n.isAbsoluteUrl)(e))return!0;try{let t=(0,n.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,i.hasBasePath)(r.pathname)}catch(e){return!1}}},20757:(e,t)=>{"use strict";function r(e,t){let r={};return Object.keys(e).forEach(n=>{t.includes(n)||(r[n]=e[n])}),r}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"omit",{enumerable:!0,get:function(){return r}})},93067:(e,t)=>{"use strict";function r(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return r}})},34655:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"pathHasPrefix",{enumerable:!0,get:function(){return i}});let n=r(93067);function i(e,t){if("string"!=typeof e)return!1;let{pathname:r}=(0,n.parsePath)(e);return r===t||r.startsWith(t+"/")}},72149:(e,t)=>{"use strict";function r(e){let t={};return e.forEach((e,r)=>{void 0===t[r]?t[r]=e:Array.isArray(t[r])?t[r].push(e):t[r]=[t[r],e]}),t}function n(e){return"string"!=typeof e&&("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function i(e){let t=new URLSearchParams;return Object.entries(e).forEach(e=>{let[r,i]=e;Array.isArray(i)?i.forEach(e=>t.append(r,n(e))):t.set(r,n(i))}),t}function o(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return r.forEach(t=>{Array.from(t.keys()).forEach(t=>e.delete(t)),t.forEach((t,r)=>e.append(r,t))}),e}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return o},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return i}})},83236:(e,t)=>{"use strict";function r(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return r}})},9966:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return i}});let n=r(51348);function i(e){let{re:t,groups:r}=e;return e=>{let i=t.exec(e);if(!i)return!1;let o=e=>{try{return decodeURIComponent(e)}catch(e){throw new n.DecodeError("failed to decode param")}},a={};return Object.keys(r).forEach(e=>{let t=r[e],n=i[t.pos];void 0!==n&&(a[e]=~n.indexOf("/")?n.split("/").map(e=>o(e)):t.repeat?[o(n)]:o(n))}),a}}},37249:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getNamedMiddlewareRegex:function(){return h},getNamedRouteRegex:function(){return f},getRouteRegex:function(){return u},parseParameter:function(){return s}});let n=r(25633),i=r(87356),o=r(2451),a=r(83236);function s(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let r=e.startsWith("...");return r&&(e=e.slice(3)),{key:e,repeat:r,optional:t}}function l(e){let t=(0,a.removeTrailingSlash)(e).slice(1).split("/"),r={},n=1;return{parameterizedRoute:t.map(e=>{let t=i.INTERCEPTION_ROUTE_MARKERS.find(t=>e.startsWith(t)),a=e.match(/\[((?:\[.*\])|.+)\]/);if(t&&a){let{key:e,optional:i,repeat:l}=s(a[1]);return r[e]={pos:n++,repeat:l,optional:i},"/"+(0,o.escapeStringRegexp)(t)+"([^/]+?)"}if(!a)return"/"+(0,o.escapeStringRegexp)(e);{let{key:e,repeat:t,optional:i}=s(a[1]);return r[e]={pos:n++,repeat:t,optional:i},t?i?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)"}}).join(""),groups:r}}function u(e){let{parameterizedRoute:t,groups:r}=l(e);return{re:RegExp("^"+t+"(?:/)?$"),groups:r}}function c(e){let{interceptionMarker:t,getSafeRouteKey:r,segment:n,routeKeys:i,keyPrefix:a}=e,{key:l,optional:u,repeat:c}=s(n),d=l.replace(/\W/g,"");a&&(d=""+a+d);let f=!1;(0===d.length||d.length>30)&&(f=!0),isNaN(parseInt(d.slice(0,1)))||(f=!0),f&&(d=r()),a?i[d]=""+a+l:i[d]=l;let h=t?(0,o.escapeStringRegexp)(t):"";return c?u?"(?:/"+h+"(?<"+d+">.+?))?":"/"+h+"(?<"+d+">.+?)":"/"+h+"(?<"+d+">[^/]+?)"}function d(e,t){let r;let s=(0,a.removeTrailingSlash)(e).slice(1).split("/"),l=(r=0,()=>{let e="",t=++r;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),u={};return{namedParameterizedRoute:s.map(e=>{let r=i.INTERCEPTION_ROUTE_MARKERS.some(t=>e.startsWith(t)),a=e.match(/\[((?:\[.*\])|.+)\]/);if(r&&a){let[r]=e.split(a[0]);return c({getSafeRouteKey:l,interceptionMarker:r,segment:a[1],routeKeys:u,keyPrefix:t?n.NEXT_INTERCEPTION_MARKER_PREFIX:void 0})}return a?c({getSafeRouteKey:l,segment:a[1],routeKeys:u,keyPrefix:t?n.NEXT_QUERY_PARAM_PREFIX:void 0}):"/"+(0,o.escapeStringRegexp)(e)}).join(""),routeKeys:u}}function f(e,t){let r=d(e,t);return{...u(e),namedRegex:"^"+r.namedParameterizedRoute+"(?:/)?$",routeKeys:r.routeKeys}}function h(e,t){let{parameterizedRoute:r}=l(e),{catchAll:n=!0}=t;if("/"===r)return{namedRegex:"^/"+(n?".*":"")+"$"};let{namedParameterizedRoute:i}=d(e,!1);return{namedRegex:"^"+i+(n?"(?:(/.*)?)":"")+"$"}}},44712:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getSortedRoutes",{enumerable:!0,get:function(){return n}});class r{insert(e){this._insert(e.split("/").filter(Boolean),[],!1)}smoosh(){return this._smoosh()}_smoosh(e){void 0===e&&(e="/");let t=[...this.children.keys()].sort();null!==this.slugName&&t.splice(t.indexOf("[]"),1),null!==this.restSlugName&&t.splice(t.indexOf("[...]"),1),null!==this.optionalRestSlugName&&t.splice(t.indexOf("[[...]]"),1);let r=t.map(t=>this.children.get(t)._smoosh(""+e+t+"/")).reduce((e,t)=>[...e,...t],[]);if(null!==this.slugName&&r.push(...this.children.get("[]")._smoosh(e+"["+this.slugName+"]/")),!this.placeholder){let t="/"===e?"/":e.slice(0,-1);if(null!=this.optionalRestSlugName)throw Error('You cannot define a route with the same specificity as a optional catch-all route ("'+t+'" and "'+t+"[[..."+this.optionalRestSlugName+']]").');r.unshift(t)}return null!==this.restSlugName&&r.push(...this.children.get("[...]")._smoosh(e+"[..."+this.restSlugName+"]/")),null!==this.optionalRestSlugName&&r.push(...this.children.get("[[...]]")._smoosh(e+"[[..."+this.optionalRestSlugName+"]]/")),r}_insert(e,t,n){if(0===e.length){this.placeholder=!1;return}if(n)throw Error("Catch-all must be the last part of the URL.");let i=e[0];if(i.startsWith("[")&&i.endsWith("]")){let r=i.slice(1,-1),a=!1;if(r.startsWith("[")&&r.endsWith("]")&&(r=r.slice(1,-1),a=!0),r.startsWith("...")&&(r=r.substring(3),n=!0),r.startsWith("[")||r.endsWith("]"))throw Error("Segment names may not start or end with extra brackets ('"+r+"').");if(r.startsWith("."))throw Error("Segment names may not start with erroneous periods ('"+r+"').");function o(e,r){if(null!==e&&e!==r)throw Error("You cannot use different slug names for the same dynamic path ('"+e+"' !== '"+r+"').");t.forEach(e=>{if(e===r)throw Error('You cannot have the same slug name "'+r+'" repeat within a single dynamic path');if(e.replace(/\W/g,"")===i.replace(/\W/g,""))throw Error('You cannot have the slug names "'+e+'" and "'+r+'" differ only by non-word symbols within a single dynamic path')}),t.push(r)}if(n){if(a){if(null!=this.restSlugName)throw Error('You cannot use both an required and optional catch-all route at the same level ("[...'+this.restSlugName+']" and "'+e[0]+'" ).');o(this.optionalRestSlugName,r),this.optionalRestSlugName=r,i="[[...]]"}else{if(null!=this.optionalRestSlugName)throw Error('You cannot use both an optional and required catch-all route at the same level ("[[...'+this.optionalRestSlugName+']]" and "'+e[0]+'").');o(this.restSlugName,r),this.restSlugName=r,i="[...]"}}else{if(a)throw Error('Optional route parameters are not yet supported ("'+e[0]+'").');o(this.slugName,r),this.slugName=r,i="[]"}}this.children.has(i)||this.children.set(i,new r),this.children.get(i)._insert(e.slice(1),t,n)}constructor(){this.placeholder=!0,this.children=new Map,this.slugName=null,this.restSlugName=null,this.optionalRestSlugName=null}}function n(e){let t=new r;return e.forEach(e=>t.insert(e)),t.smoosh()}},68071:(e,t)=>{"use strict";function r(e){return"("===e[0]&&e.endsWith(")")}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DEFAULT_SEGMENT_KEY:function(){return i},PAGE_SEGMENT_KEY:function(){return n},isGroupSegment:function(){return r}});let n="__PAGE__",i="__DEFAULT__"},51348:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return p},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return g},NormalizeError:function(){return m},PageNotFoundError:function(){return y},SP:function(){return f},ST:function(){return h},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return l},getLocationOrigin:function(){return a},getURL:function(){return s},isAbsoluteUrl:function(){return o},isResSent:function(){return u},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return b}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,i=Array(n),o=0;o<n;o++)i[o]=arguments[o];return r||(r=!0,t=e(...i)),t}}let i=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,o=e=>i.test(e);function a(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function s(){let{href:e}=window.location,t=a();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function u(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&u(r))return n;if(!n)throw Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.');return n}let f="undefined"!=typeof performance,h=f&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class p extends Error{}class m extends Error{}class y extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class g extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}},576:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},68570:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createProxy",{enumerable:!0,get:function(){return n}});let n=r(51749).createClientModuleProxy},59943:(e,t,r)=>{"use strict";let{createProxy:n}=r(68570);e.exports=n("/Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/next/dist/client/components/app-router.js")},53144:(e,t,r)=>{"use strict";let{createProxy:n}=r(68570);e.exports=n("/Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/next/dist/client/components/client-page.js")},37922:(e,t,r)=>{"use strict";let{createProxy:n}=r(68570);e.exports=n("/Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/next/dist/client/components/error-boundary.js")},95106:(e,t,r)=>{"use strict";let{createProxy:n}=r(68570);e.exports=n("/Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/next/dist/client/components/layout-router.js")},60525:(e,t,r)=>{"use strict";let{createProxy:n}=r(68570);e.exports=n("/Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/next/dist/client/components/not-found-boundary.js")},84892:(e,t,r)=>{"use strict";let{createProxy:n}=r(68570);e.exports=n("/Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/next/dist/client/components/render-from-template-context.js")},79181:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createDynamicallyTrackedSearchParams:function(){return s},createUntrackedSearchParams:function(){return a}});let n=r(45869),i=r(6278),o=r(38238);function a(e){let t=n.staticGenerationAsyncStorage.getStore();return t&&t.forceStatic?{}:e}function s(e){let t=n.staticGenerationAsyncStorage.getStore();return t?t.forceStatic?{}:t.isStaticGeneration||t.dynamicShouldError?new Proxy({},{get:(e,r,n)=>("string"==typeof r&&(0,i.trackDynamicDataAccessed)(t,"searchParams."+r),o.ReflectAdapter.get(e,r,n)),has:(e,r)=>("string"==typeof r&&(0,i.trackDynamicDataAccessed)(t,"searchParams."+r),Reflect.has(e,r)),ownKeys:e=>((0,i.trackDynamicDataAccessed)(t,"searchParams"),Reflect.ownKeys(e))}):e:e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},95231:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{AppRouter:function(){return i.default},ClientPageRoot:function(){return c.ClientPageRoot},LayoutRouter:function(){return o.default},NotFoundBoundary:function(){return h.NotFoundBoundary},Postpone:function(){return y.Postpone},RenderFromTemplateContext:function(){return a.default},actionAsyncStorage:function(){return u.actionAsyncStorage},createDynamicallyTrackedSearchParams:function(){return d.createDynamicallyTrackedSearchParams},createUntrackedSearchParams:function(){return d.createUntrackedSearchParams},decodeAction:function(){return n.decodeAction},decodeFormState:function(){return n.decodeFormState},decodeReply:function(){return n.decodeReply},patchFetch:function(){return x},preconnect:function(){return m.preconnect},preloadFont:function(){return m.preloadFont},preloadStyle:function(){return m.preloadStyle},renderToReadableStream:function(){return n.renderToReadableStream},requestAsyncStorage:function(){return l.requestAsyncStorage},serverHooks:function(){return f},staticGenerationAsyncStorage:function(){return s.staticGenerationAsyncStorage},taintObjectReference:function(){return g.taintObjectReference}});let n=r(51749),i=v(r(59943)),o=v(r(95106)),a=v(r(84892)),s=r(45869),l=r(54580),u=r(72934),c=r(53144),d=r(79181),f=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=b(void 0);if(r&&r.has(e))return r.get(e);var n={__proto__:null},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var a=i?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(n,o,a):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(44789)),h=r(60525),p=r(60670);r(37922);let m=r(20135),y=r(49257),g=r(526);function v(e){return e&&e.__esModule?e:{default:e}}function b(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(b=function(e){return e?r:t})(e)}function x(){return(0,p.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:s.staticGenerationAsyncStorage})}},49257:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Postpone",{enumerable:!0,get:function(){return n.Postpone}});let n=r(6278)},20135:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{preconnect:function(){return a},preloadFont:function(){return o},preloadStyle:function(){return i}});let n=function(e){return e&&e.__esModule?e:{default:e}}(r(97049));function i(e,t){let r={as:"style"};"string"==typeof t&&(r.crossOrigin=t),n.default.preload(e,r)}function o(e,t,r){let i={as:"font",type:t};"string"==typeof r&&(i.crossOrigin=r),n.default.preload(e,i)}function a(e,t){n.default.preconnect(e,"string"==typeof t?{crossOrigin:t}:void 0)}},526:(e,t,r)=>{"use strict";function n(){throw Error("Taint can only be used with the taint flag.")}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{taintObjectReference:function(){return i},taintUniqueValue:function(){return o}}),r(71159);let i=n,o=n},97049:(e,t,r)=>{"use strict";e.exports=r(23191).vendored["react-rsc"].ReactDOM},19510:(e,t,r)=>{"use strict";e.exports=r(23191).vendored["react-rsc"].ReactJsxRuntime},51749:(e,t,r)=>{"use strict";e.exports=r(23191).vendored["react-rsc"].ReactServerDOMWebpackServerEdge},82561:(e,t,r)=>{"use strict";function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}r.d(t,{M:()=>n})},70545:(e,t,r)=>{"use strict";r.d(t,{B:()=>l});var n=r(17577),i=r(93095),o=r(48051),a=r(34214),s=r(10326);function l(e){let t=e+"CollectionProvider",[r,l]=(0,i.b)(t),[u,c]=r(t,{collectionRef:{current:null},itemMap:new Map}),d=e=>{let{scope:t,children:r}=e,i=n.useRef(null),o=n.useRef(new Map).current;return(0,s.jsx)(u,{scope:t,itemMap:o,collectionRef:i,children:r})};d.displayName=t;let f=e+"CollectionSlot",h=(0,a.Z8)(f),p=n.forwardRef((e,t)=>{let{scope:r,children:n}=e,i=c(f,r),a=(0,o.e)(t,i.collectionRef);return(0,s.jsx)(h,{ref:a,children:n})});p.displayName=f;let m=e+"CollectionItemSlot",y="data-radix-collection-item",g=(0,a.Z8)(m),v=n.forwardRef((e,t)=>{let{scope:r,children:i,...a}=e,l=n.useRef(null),u=(0,o.e)(t,l),d=c(m,r);return n.useEffect(()=>(d.itemMap.set(l,{ref:l,...a}),()=>void d.itemMap.delete(l))),(0,s.jsx)(g,{[y]:"",ref:u,children:i})});return v.displayName=m,[{Provider:d,Slot:p,ItemSlot:v},function(t){let r=c(e+"CollectionConsumer",t);return n.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${y}]`));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},l]}},48051:(e,t,r)=>{"use strict";r.d(t,{F:()=>o,e:()=>a});var n=r(17577);function i(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function o(...e){return t=>{let r=!1,n=e.map(e=>{let n=i(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():i(e[t],null)}}}}function a(...e){return n.useCallback(o(...e),e)}},93095:(e,t,r)=>{"use strict";r.d(t,{b:()=>a,k:()=>o});var n=r(17577),i=r(10326);function o(e,t){let r=n.createContext(t),o=e=>{let{children:t,...o}=e,a=n.useMemo(()=>o,Object.values(o));return(0,i.jsx)(r.Provider,{value:a,children:t})};return o.displayName=e+"Provider",[o,function(i){let o=n.useContext(r);if(o)return o;if(void 0!==t)return t;throw Error(`\`${i}\` must be used within \`${e}\``)}]}function a(e,t=[]){let r=[],o=()=>{let t=r.map(e=>n.createContext(e));return function(r){let i=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:i}}),[r,i])}};return o.scopeName=e,[function(t,o){let a=n.createContext(o),s=r.length;r=[...r,o];let l=t=>{let{scope:r,children:o,...l}=t,u=r?.[e]?.[s]||a,c=n.useMemo(()=>l,Object.values(l));return(0,i.jsx)(u.Provider,{value:c,children:o})};return l.displayName=t+"Provider",[l,function(r,i){let l=i?.[e]?.[s]||a,u=n.useContext(l);if(u)return u;if(void 0!==o)return o;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let i=r.reduce((t,{useScope:r,scopeName:n})=>{let i=r(e)[`__scope${n}`];return{...t,...i}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return r.scopeName=t.scopeName,r}(o,...t)]}},825:(e,t,r)=>{"use strict";r.d(t,{I0:()=>g,XB:()=>f,fC:()=>y});var n,i=r(17577),o=r(82561),a=r(45226),s=r(48051),l=r(55049),u=r(10326),c="dismissableLayer.update",d=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=i.forwardRef((e,t)=>{let{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:f,onPointerDownOutside:h,onFocusOutside:y,onInteractOutside:g,onDismiss:v,...b}=e,x=i.useContext(d),[w,P]=i.useState(null),E=w?.ownerDocument??globalThis?.document,[,R]=i.useState({}),_=(0,s.e)(t,e=>P(e)),S=Array.from(x.layers),[T]=[...x.layersWithOutsidePointerEventsDisabled].slice(-1),j=S.indexOf(T),O=w?S.indexOf(w):-1,C=x.layersWithOutsidePointerEventsDisabled.size>0,M=O>=j,A=function(e,t=globalThis?.document){let r=(0,l.W)(e),n=i.useRef(!1),o=i.useRef(()=>{});return i.useEffect(()=>{let e=e=>{if(e.target&&!n.current){let n=function(){m("dismissableLayer.pointerDownOutside",r,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",o.current),o.current=n,t.addEventListener("click",o.current,{once:!0})):n()}else t.removeEventListener("click",o.current);n.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",e),t.removeEventListener("click",o.current)}},[t,r]),{onPointerDownCapture:()=>n.current=!0}}(e=>{let t=e.target,r=[...x.branches].some(e=>e.contains(t));!M||r||(h?.(e),g?.(e),e.defaultPrevented||v?.())},E),D=function(e,t=globalThis?.document){let r=(0,l.W)(e),n=i.useRef(!1);return i.useEffect(()=>{let e=e=>{e.target&&!n.current&&m("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,r]),{onFocusCapture:()=>n.current=!0,onBlurCapture:()=>n.current=!1}}(e=>{let t=e.target;[...x.branches].some(e=>e.contains(t))||(y?.(e),g?.(e),e.defaultPrevented||v?.())},E);return function(e,t=globalThis?.document){let r=(0,l.W)(e);i.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{O!==x.layers.size-1||(f?.(e),!e.defaultPrevented&&v&&(e.preventDefault(),v()))},E),i.useEffect(()=>{if(w)return r&&(0===x.layersWithOutsidePointerEventsDisabled.size&&(n=E.body.style.pointerEvents,E.body.style.pointerEvents="none"),x.layersWithOutsidePointerEventsDisabled.add(w)),x.layers.add(w),p(),()=>{r&&1===x.layersWithOutsidePointerEventsDisabled.size&&(E.body.style.pointerEvents=n)}},[w,E,r,x]),i.useEffect(()=>()=>{w&&(x.layers.delete(w),x.layersWithOutsidePointerEventsDisabled.delete(w),p())},[w,x]),i.useEffect(()=>{let e=()=>R({});return document.addEventListener(c,e),()=>document.removeEventListener(c,e)},[]),(0,u.jsx)(a.WV.div,{...b,ref:_,style:{pointerEvents:C?M?"auto":"none":void 0,...e.style},onFocusCapture:(0,o.M)(e.onFocusCapture,D.onFocusCapture),onBlurCapture:(0,o.M)(e.onBlurCapture,D.onBlurCapture),onPointerDownCapture:(0,o.M)(e.onPointerDownCapture,A.onPointerDownCapture)})});f.displayName="DismissableLayer";var h=i.forwardRef((e,t)=>{let r=i.useContext(d),n=i.useRef(null),o=(0,s.e)(t,n);return i.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,u.jsx)(a.WV.div,{...e,ref:o})});function p(){let e=new CustomEvent(c);document.dispatchEvent(e)}function m(e,t,r,{discrete:n}){let i=r.originalEvent.target,o=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&i.addEventListener(e,t,{once:!0}),n?(0,a.jH)(i,o):i.dispatchEvent(o)}h.displayName="DismissableLayerBranch";var y=f,g=h},88957:(e,t,r)=>{"use strict";r.d(t,{M:()=>l});var n,i=r(17577),o=r(65819),a=(n||(n=r.t(i,2)))[" useId ".trim().toString()]||(()=>void 0),s=0;function l(e){let[t,r]=i.useState(a());return(0,o.b)(()=>{e||r(e=>e??String(s++))},[e]),e||(t?`radix-${t}`:"")}},83078:(e,t,r)=>{"use strict";r.d(t,{h:()=>l});var n=r(17577),i=r(60962),o=r(45226),a=r(65819),s=r(10326),l=n.forwardRef((e,t)=>{let{container:r,...l}=e,[u,c]=n.useState(!1);(0,a.b)(()=>c(!0),[]);let d=r||u&&globalThis?.document?.body;return d?i.createPortal((0,s.jsx)(o.WV.div,{...l,ref:t}),d):null});l.displayName="Portal"},9815:(e,t,r)=>{"use strict";r.d(t,{z:()=>a});var n=r(17577),i=r(48051),o=r(65819),a=e=>{let{present:t,children:r}=e,a=function(e){var t,r;let[i,a]=n.useState(),l=n.useRef(null),u=n.useRef(e),c=n.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>r[e][t]??e,t));return n.useEffect(()=>{let e=s(l.current);c.current="mounted"===d?e:"none"},[d]),(0,o.b)(()=>{let t=l.current,r=u.current;if(r!==e){let n=c.current,i=s(t);e?f("MOUNT"):"none"===i||t?.display==="none"?f("UNMOUNT"):r&&n!==i?f("ANIMATION_OUT"):f("UNMOUNT"),u.current=e}},[e,f]),(0,o.b)(()=>{if(i){let e;let t=i.ownerDocument.defaultView??window,r=r=>{let n=s(l.current).includes(r.animationName);if(r.target===i&&n&&(f("ANIMATION_END"),!u.current)){let r=i.style.animationFillMode;i.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===i.style.animationFillMode&&(i.style.animationFillMode=r)})}},n=e=>{e.target===i&&(c.current=s(l.current))};return i.addEventListener("animationstart",n),i.addEventListener("animationcancel",r),i.addEventListener("animationend",r),()=>{t.clearTimeout(e),i.removeEventListener("animationstart",n),i.removeEventListener("animationcancel",r),i.removeEventListener("animationend",r)}}f("ANIMATION_END")},[i,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:n.useCallback(e=>{l.current=e?getComputedStyle(e):null,a(e)},[])}}(t),l="function"==typeof r?r({present:a.isPresent}):n.Children.only(r),u=(0,i.e)(a.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof r||a.isPresent?n.cloneElement(l,{ref:u}):null};function s(e){return e?.animationName||"none"}a.displayName="Presence"},45226:(e,t,r)=>{"use strict";r.d(t,{WV:()=>s,jH:()=>l});var n=r(17577),i=r(60962),o=r(34214),a=r(10326),s=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,o.Z8)(`Primitive.${t}`),i=n.forwardRef((e,n)=>{let{asChild:i,...o}=e,s=i?r:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(s,{...o,ref:n})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{});function l(e,t){e&&i.flushSync(()=>e.dispatchEvent(t))}},34214:(e,t,r)=>{"use strict";r.d(t,{Z8:()=>a,g7:()=>s,sA:()=>u});var n=r(17577),i=r(48051),o=r(10326);function a(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...o}=e;if(n.isValidElement(r)){let e,a;let s=(e=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.ref:(e=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.props.ref:r.props.ref||r.ref,l=function(e,t){let r={...t};for(let n in t){let i=e[n],o=t[n];/^on[A-Z]/.test(n)?i&&o?r[n]=(...e)=>{let t=o(...e);return i(...e),t}:i&&(r[n]=i):"style"===n?r[n]={...i,...o}:"className"===n&&(r[n]=[i,o].filter(Boolean).join(" "))}return{...e,...r}}(o,r.props);return r.type!==n.Fragment&&(l.ref=t?(0,i.F)(t,s):s),n.cloneElement(r,l)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:i,...a}=e,s=n.Children.toArray(i),l=s.find(c);if(l){let e=l.props.children,i=s.map(t=>t!==l?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,o.jsx)(t,{...a,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,i):null})}return(0,o.jsx)(t,{...a,ref:r,children:i})});return r.displayName=`${e}.Slot`,r}var s=a("Slot"),l=Symbol("radix.slottable");function u(e){let t=({children:e})=>(0,o.jsx)(o.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=l,t}function c(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}},10321:(e,t,r)=>{"use strict";r.d(t,{Dx:()=>J,aU:()=>et,dk:()=>ee,fC:()=>Z,l_:()=>Q,x8:()=>er,zt:()=>Y});var n=r(17577),i=r(60962),o=r(82561),a=r(48051),s=r(70545),l=r(93095),u=r(825),c=r(83078),d=r(9815),f=r(45226),h=r(55049),p=r(52067),m=r(65819),y=r(6009),g=r(10326),v="ToastProvider",[b,x,w]=(0,s.B)("Toast"),[P,E]=(0,l.b)("Toast",[w]),[R,_]=P(v),S=e=>{let{__scopeToast:t,label:r="Notification",duration:i=5e3,swipeDirection:o="right",swipeThreshold:a=50,children:s}=e,[l,u]=n.useState(null),[c,d]=n.useState(0),f=n.useRef(!1),h=n.useRef(!1);return r.trim()||console.error(`Invalid prop \`label\` supplied to \`${v}\`. Expected non-empty \`string\`.`),(0,g.jsx)(b.Provider,{scope:t,children:(0,g.jsx)(R,{scope:t,label:r,duration:i,swipeDirection:o,swipeThreshold:a,toastCount:c,viewport:l,onViewportChange:u,onToastAdd:n.useCallback(()=>d(e=>e+1),[]),onToastRemove:n.useCallback(()=>d(e=>e-1),[]),isFocusedToastEscapeKeyDownRef:f,isClosePausedRef:h,children:s})})};S.displayName=v;var T="ToastViewport",j=["F8"],O="toast.viewportPause",C="toast.viewportResume",M=n.forwardRef((e,t)=>{let{__scopeToast:r,hotkey:i=j,label:o="Notifications ({hotkey})",...s}=e,l=_(T,r),c=x(r),d=n.useRef(null),h=n.useRef(null),p=n.useRef(null),m=n.useRef(null),y=(0,a.e)(t,m,l.onViewportChange),v=i.join("+").replace(/Key/g,"").replace(/Digit/g,""),w=l.toastCount>0;n.useEffect(()=>{let e=e=>{0!==i.length&&i.every(t=>e[t]||e.code===t)&&m.current?.focus()};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[i]),n.useEffect(()=>{let e=d.current,t=m.current;if(w&&e&&t){let r=()=>{if(!l.isClosePausedRef.current){let e=new CustomEvent(O);t.dispatchEvent(e),l.isClosePausedRef.current=!0}},n=()=>{if(l.isClosePausedRef.current){let e=new CustomEvent(C);t.dispatchEvent(e),l.isClosePausedRef.current=!1}},i=t=>{e.contains(t.relatedTarget)||n()},o=()=>{e.contains(document.activeElement)||n()};return e.addEventListener("focusin",r),e.addEventListener("focusout",i),e.addEventListener("pointermove",r),e.addEventListener("pointerleave",o),window.addEventListener("blur",r),window.addEventListener("focus",n),()=>{e.removeEventListener("focusin",r),e.removeEventListener("focusout",i),e.removeEventListener("pointermove",r),e.removeEventListener("pointerleave",o),window.removeEventListener("blur",r),window.removeEventListener("focus",n)}}},[w,l.isClosePausedRef]);let P=n.useCallback(({tabbingDirection:e})=>{let t=c().map(t=>{let r=t.ref.current,n=[r,...function(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}(r)];return"forwards"===e?n:n.reverse()});return("forwards"===e?t.reverse():t).flat()},[c]);return n.useEffect(()=>{let e=m.current;if(e){let t=t=>{let r=t.altKey||t.ctrlKey||t.metaKey;if("Tab"===t.key&&!r){let r=document.activeElement,n=t.shiftKey;if(t.target===e&&n){h.current?.focus();return}let i=P({tabbingDirection:n?"backwards":"forwards"}),o=i.findIndex(e=>e===r);G(i.slice(o+1))?t.preventDefault():n?h.current?.focus():p.current?.focus()}};return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)}},[c,P]),(0,g.jsxs)(u.I0,{ref:d,role:"region","aria-label":o.replace("{hotkey}",v),tabIndex:-1,style:{pointerEvents:w?void 0:"none"},children:[w&&(0,g.jsx)(D,{ref:h,onFocusFromOutsideViewport:()=>{G(P({tabbingDirection:"forwards"}))}}),(0,g.jsx)(b.Slot,{scope:r,children:(0,g.jsx)(f.WV.ol,{tabIndex:-1,...s,ref:y})}),w&&(0,g.jsx)(D,{ref:p,onFocusFromOutsideViewport:()=>{G(P({tabbingDirection:"backwards"}))}})]})});M.displayName=T;var A="ToastFocusProxy",D=n.forwardRef((e,t)=>{let{__scopeToast:r,onFocusFromOutsideViewport:n,...i}=e,o=_(A,r);return(0,g.jsx)(y.TX,{"aria-hidden":!0,tabIndex:0,...i,ref:t,style:{position:"fixed"},onFocus:e=>{let t=e.relatedTarget;o.viewport?.contains(t)||n()}})});D.displayName=A;var k="Toast",N=n.forwardRef((e,t)=>{let{forceMount:r,open:n,defaultOpen:i,onOpenChange:a,...s}=e,[l,u]=(0,p.T)({prop:n,defaultProp:i??!0,onChange:a,caller:k});return(0,g.jsx)(d.z,{present:r||l,children:(0,g.jsx)(F,{open:l,...s,ref:t,onClose:()=>u(!1),onPause:(0,h.W)(e.onPause),onResume:(0,h.W)(e.onResume),onSwipeStart:(0,o.M)(e.onSwipeStart,e=>{e.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:(0,o.M)(e.onSwipeMove,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${t}px`),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${r}px`)}),onSwipeCancel:(0,o.M)(e.onSwipeCancel,e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:(0,o.M)(e.onSwipeEnd,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${t}px`),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${r}px`),u(!1)})})})});N.displayName=k;var[L,I]=P(k,{onClose(){}}),F=n.forwardRef((e,t)=>{let{__scopeToast:r,type:s="foreground",duration:l,open:c,onClose:d,onEscapeKeyDown:p,onPause:m,onResume:y,onSwipeStart:v,onSwipeMove:x,onSwipeCancel:w,onSwipeEnd:P,...E}=e,R=_(k,r),[S,T]=n.useState(null),j=(0,a.e)(t,e=>T(e)),M=n.useRef(null),A=n.useRef(null),D=l||R.duration,N=n.useRef(0),I=n.useRef(D),F=n.useRef(0),{onToastAdd:V,onToastRemove:B}=R,W=(0,h.W)(()=>{S?.contains(document.activeElement)&&R.viewport?.focus(),d()}),H=n.useCallback(e=>{e&&e!==1/0&&(window.clearTimeout(F.current),N.current=new Date().getTime(),F.current=window.setTimeout(W,e))},[W]);n.useEffect(()=>{let e=R.viewport;if(e){let t=()=>{H(I.current),y?.()},r=()=>{let e=new Date().getTime()-N.current;I.current=I.current-e,window.clearTimeout(F.current),m?.()};return e.addEventListener(O,r),e.addEventListener(C,t),()=>{e.removeEventListener(O,r),e.removeEventListener(C,t)}}},[R.viewport,D,m,y,H]),n.useEffect(()=>{c&&!R.isClosePausedRef.current&&H(D)},[c,D,R.isClosePausedRef,H]),n.useEffect(()=>(V(),()=>B()),[V,B]);let z=n.useMemo(()=>S?function e(t){let r=[];return Array.from(t.childNodes).forEach(t=>{if(t.nodeType===t.TEXT_NODE&&t.textContent&&r.push(t.textContent),t.nodeType===t.ELEMENT_NODE){let n=t.ariaHidden||t.hidden||"none"===t.style.display,i=""===t.dataset.radixToastAnnounceExclude;if(!n){if(i){let e=t.dataset.radixToastAnnounceAlt;e&&r.push(e)}else r.push(...e(t))}}}),r}(S):null,[S]);return R.viewport?(0,g.jsxs)(g.Fragment,{children:[z&&(0,g.jsx)(U,{__scopeToast:r,role:"status","aria-live":"foreground"===s?"assertive":"polite","aria-atomic":!0,children:z}),(0,g.jsx)(L,{scope:r,onClose:W,children:i.createPortal((0,g.jsx)(b.ItemSlot,{scope:r,children:(0,g.jsx)(u.fC,{asChild:!0,onEscapeKeyDown:(0,o.M)(p,()=>{R.isFocusedToastEscapeKeyDownRef.current||W(),R.isFocusedToastEscapeKeyDownRef.current=!1}),children:(0,g.jsx)(f.WV.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":c?"open":"closed","data-swipe-direction":R.swipeDirection,...E,ref:j,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:(0,o.M)(e.onKeyDown,e=>{"Escape"!==e.key||(p?.(e.nativeEvent),e.nativeEvent.defaultPrevented||(R.isFocusedToastEscapeKeyDownRef.current=!0,W()))}),onPointerDown:(0,o.M)(e.onPointerDown,e=>{0===e.button&&(M.current={x:e.clientX,y:e.clientY})}),onPointerMove:(0,o.M)(e.onPointerMove,e=>{if(!M.current)return;let t=e.clientX-M.current.x,r=e.clientY-M.current.y,n=!!A.current,i=["left","right"].includes(R.swipeDirection),o=["left","up"].includes(R.swipeDirection)?Math.min:Math.max,a=i?o(0,t):0,s=i?0:o(0,r),l="touch"===e.pointerType?10:2,u={x:a,y:s},c={originalEvent:e,delta:u};n?(A.current=u,q("toast.swipeMove",x,c,{discrete:!1})):K(u,R.swipeDirection,l)?(A.current=u,q("toast.swipeStart",v,c,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>l||Math.abs(r)>l)&&(M.current=null)}),onPointerUp:(0,o.M)(e.onPointerUp,e=>{let t=A.current,r=e.target;if(r.hasPointerCapture(e.pointerId)&&r.releasePointerCapture(e.pointerId),A.current=null,M.current=null,t){let r=e.currentTarget,n={originalEvent:e,delta:t};K(t,R.swipeDirection,R.swipeThreshold)?q("toast.swipeEnd",P,n,{discrete:!0}):q("toast.swipeCancel",w,n,{discrete:!0}),r.addEventListener("click",e=>e.preventDefault(),{once:!0})}})})})}),R.viewport)})]}):null}),U=e=>{let{__scopeToast:t,children:r,...i}=e,o=_(k,t),[a,s]=n.useState(!1),[l,u]=n.useState(!1);return function(e=()=>{}){let t=(0,h.W)(e);(0,m.b)(()=>{let e=0,r=0;return e=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(r)}},[t])}(()=>s(!0)),n.useEffect(()=>{let e=window.setTimeout(()=>u(!0),1e3);return()=>window.clearTimeout(e)},[]),l?null:(0,g.jsx)(c.h,{asChild:!0,children:(0,g.jsx)(y.TX,{...i,children:a&&(0,g.jsxs)(g.Fragment,{children:[o.label," ",r]})})})},V=n.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e;return(0,g.jsx)(f.WV.div,{...n,ref:t})});V.displayName="ToastTitle";var B=n.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e;return(0,g.jsx)(f.WV.div,{...n,ref:t})});B.displayName="ToastDescription";var W="ToastAction",H=n.forwardRef((e,t)=>{let{altText:r,...n}=e;return r.trim()?(0,g.jsx)(X,{altText:r,asChild:!0,children:(0,g.jsx)($,{...n,ref:t})}):(console.error(`Invalid prop \`altText\` supplied to \`${W}\`. Expected non-empty \`string\`.`),null)});H.displayName=W;var z="ToastClose",$=n.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e,i=I(z,r);return(0,g.jsx)(X,{asChild:!0,children:(0,g.jsx)(f.WV.button,{type:"button",...n,ref:t,onClick:(0,o.M)(e.onClick,i.onClose)})})});$.displayName=z;var X=n.forwardRef((e,t)=>{let{__scopeToast:r,altText:n,...i}=e;return(0,g.jsx)(f.WV.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":n||void 0,...i,ref:t})});function q(e,t,r,{discrete:n}){let i=r.originalEvent.currentTarget,o=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:r});t&&i.addEventListener(e,t,{once:!0}),n?(0,f.jH)(i,o):i.dispatchEvent(o)}var K=(e,t,r=0)=>{let n=Math.abs(e.x),i=Math.abs(e.y),o=n>i;return"left"===t||"right"===t?o&&n>r:!o&&i>r};function G(e){let t=document.activeElement;return e.some(e=>e===t||(e.focus(),document.activeElement!==t))}var Y=S,Q=M,Z=N,J=V,ee=B,et=H,er=$},25813:(e,t,r)=>{"use strict";r.d(t,{VY:()=>tv,zt:()=>tm,fC:()=>ty,xz:()=>tg});var n=r(17577),i=r(82561),o=r(48051),a=r(93095),s=r(825),l=r(88957);let u=["top","right","bottom","left"],c=Math.min,d=Math.max,f=Math.round,h=Math.floor,p=e=>({x:e,y:e}),m={left:"right",right:"left",bottom:"top",top:"bottom"},y={start:"end",end:"start"};function g(e,t){return"function"==typeof e?e(t):e}function v(e){return e.split("-")[0]}function b(e){return e.split("-")[1]}function x(e){return"x"===e?"y":"x"}function w(e){return"y"===e?"height":"width"}function P(e){return["top","bottom"].includes(v(e))?"y":"x"}function E(e){return e.replace(/start|end/g,e=>y[e])}function R(e){return e.replace(/left|right|bottom|top/g,e=>m[e])}function _(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function S(e){let{x:t,y:r,width:n,height:i}=e;return{width:n,height:i,top:r,left:t,right:t+n,bottom:r+i,x:t,y:r}}function T(e,t,r){let n,{reference:i,floating:o}=e,a=P(t),s=x(P(t)),l=w(s),u=v(t),c="y"===a,d=i.x+i.width/2-o.width/2,f=i.y+i.height/2-o.height/2,h=i[l]/2-o[l]/2;switch(u){case"top":n={x:d,y:i.y-o.height};break;case"bottom":n={x:d,y:i.y+i.height};break;case"right":n={x:i.x+i.width,y:f};break;case"left":n={x:i.x-o.width,y:f};break;default:n={x:i.x,y:i.y}}switch(b(t)){case"start":n[s]-=h*(r&&c?-1:1);break;case"end":n[s]+=h*(r&&c?-1:1)}return n}let j=async(e,t,r)=>{let{placement:n="bottom",strategy:i="absolute",middleware:o=[],platform:a}=r,s=o.filter(Boolean),l=await (null==a.isRTL?void 0:a.isRTL(t)),u=await a.getElementRects({reference:e,floating:t,strategy:i}),{x:c,y:d}=T(u,n,l),f=n,h={},p=0;for(let r=0;r<s.length;r++){let{name:o,fn:m}=s[r],{x:y,y:g,data:v,reset:b}=await m({x:c,y:d,initialPlacement:n,placement:f,strategy:i,middlewareData:h,rects:u,platform:a,elements:{reference:e,floating:t}});c=null!=y?y:c,d=null!=g?g:d,h={...h,[o]:{...h[o],...v}},b&&p<=50&&(p++,"object"==typeof b&&(b.placement&&(f=b.placement),b.rects&&(u=!0===b.rects?await a.getElementRects({reference:e,floating:t,strategy:i}):b.rects),{x:c,y:d}=T(u,f,l)),r=-1)}return{x:c,y:d,placement:f,strategy:i,middlewareData:h}};async function O(e,t){var r;void 0===t&&(t={});let{x:n,y:i,platform:o,rects:a,elements:s,strategy:l}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:d="floating",altBoundary:f=!1,padding:h=0}=g(t,e),p=_(h),m=s[f?"floating"===d?"reference":"floating":d],y=S(await o.getClippingRect({element:null==(r=await (null==o.isElement?void 0:o.isElement(m)))||r?m:m.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(s.floating)),boundary:u,rootBoundary:c,strategy:l})),v="floating"===d?{x:n,y:i,width:a.floating.width,height:a.floating.height}:a.reference,b=await (null==o.getOffsetParent?void 0:o.getOffsetParent(s.floating)),x=await (null==o.isElement?void 0:o.isElement(b))&&await (null==o.getScale?void 0:o.getScale(b))||{x:1,y:1},w=S(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:s,rect:v,offsetParent:b,strategy:l}):v);return{top:(y.top-w.top+p.top)/x.y,bottom:(w.bottom-y.bottom+p.bottom)/x.y,left:(y.left-w.left+p.left)/x.x,right:(w.right-y.right+p.right)/x.x}}function C(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function M(e){return u.some(t=>e[t]>=0)}async function A(e,t){let{placement:r,platform:n,elements:i}=e,o=await (null==n.isRTL?void 0:n.isRTL(i.floating)),a=v(r),s=b(r),l="y"===P(r),u=["left","top"].includes(a)?-1:1,c=o&&l?-1:1,d=g(t,e),{mainAxis:f,crossAxis:h,alignmentAxis:p}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return s&&"number"==typeof p&&(h="end"===s?-1*p:p),l?{x:h*c,y:f*u}:{x:f*u,y:h*c}}function D(){return"undefined"!=typeof window}function k(e){return I(e)?(e.nodeName||"").toLowerCase():"#document"}function N(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function L(e){var t;return null==(t=(I(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function I(e){return!!D()&&(e instanceof Node||e instanceof N(e).Node)}function F(e){return!!D()&&(e instanceof Element||e instanceof N(e).Element)}function U(e){return!!D()&&(e instanceof HTMLElement||e instanceof N(e).HTMLElement)}function V(e){return!!D()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof N(e).ShadowRoot)}function B(e){let{overflow:t,overflowX:r,overflowY:n,display:i}=X(e);return/auto|scroll|overlay|hidden|clip/.test(t+n+r)&&!["inline","contents"].includes(i)}function W(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function H(e){let t=z(),r=F(e)?X(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!r[e]&&"none"!==r[e])||!!r.containerType&&"normal"!==r.containerType||!t&&!!r.backdropFilter&&"none"!==r.backdropFilter||!t&&!!r.filter&&"none"!==r.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(r.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(r.contain||"").includes(e))}function z(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function $(e){return["html","body","#document"].includes(k(e))}function X(e){return N(e).getComputedStyle(e)}function q(e){return F(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function K(e){if("html"===k(e))return e;let t=e.assignedSlot||e.parentNode||V(e)&&e.host||L(e);return V(t)?t.host:t}function G(e,t,r){var n;void 0===t&&(t=[]),void 0===r&&(r=!0);let i=function e(t){let r=K(t);return $(r)?t.ownerDocument?t.ownerDocument.body:t.body:U(r)&&B(r)?r:e(r)}(e),o=i===(null==(n=e.ownerDocument)?void 0:n.body),a=N(i);if(o){let e=Y(a);return t.concat(a,a.visualViewport||[],B(i)?i:[],e&&r?G(e):[])}return t.concat(i,G(i,[],r))}function Y(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Q(e){let t=X(e),r=parseFloat(t.width)||0,n=parseFloat(t.height)||0,i=U(e),o=i?e.offsetWidth:r,a=i?e.offsetHeight:n,s=f(r)!==o||f(n)!==a;return s&&(r=o,n=a),{width:r,height:n,$:s}}function Z(e){return F(e)?e:e.contextElement}function J(e){let t=Z(e);if(!U(t))return p(1);let r=t.getBoundingClientRect(),{width:n,height:i,$:o}=Q(t),a=(o?f(r.width):r.width)/n,s=(o?f(r.height):r.height)/i;return a&&Number.isFinite(a)||(a=1),s&&Number.isFinite(s)||(s=1),{x:a,y:s}}let ee=p(0);function et(e){let t=N(e);return z()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:ee}function er(e,t,r,n){var i;void 0===t&&(t=!1),void 0===r&&(r=!1);let o=e.getBoundingClientRect(),a=Z(e),s=p(1);t&&(n?F(n)&&(s=J(n)):s=J(e));let l=(void 0===(i=r)&&(i=!1),n&&(!i||n===N(a))&&i)?et(a):p(0),u=(o.left+l.x)/s.x,c=(o.top+l.y)/s.y,d=o.width/s.x,f=o.height/s.y;if(a){let e=N(a),t=n&&F(n)?N(n):n,r=e,i=Y(r);for(;i&&n&&t!==r;){let e=J(i),t=i.getBoundingClientRect(),n=X(i),o=t.left+(i.clientLeft+parseFloat(n.paddingLeft))*e.x,a=t.top+(i.clientTop+parseFloat(n.paddingTop))*e.y;u*=e.x,c*=e.y,d*=e.x,f*=e.y,u+=o,c+=a,i=Y(r=N(i))}}return S({width:d,height:f,x:u,y:c})}function en(e,t){let r=q(e).scrollLeft;return t?t.left+r:er(L(e)).left+r}function ei(e,t,r){void 0===r&&(r=!1);let n=e.getBoundingClientRect();return{x:n.left+t.scrollLeft-(r?0:en(e,n)),y:n.top+t.scrollTop}}function eo(e,t,r){let n;if("viewport"===t)n=function(e,t){let r=N(e),n=L(e),i=r.visualViewport,o=n.clientWidth,a=n.clientHeight,s=0,l=0;if(i){o=i.width,a=i.height;let e=z();(!e||e&&"fixed"===t)&&(s=i.offsetLeft,l=i.offsetTop)}return{width:o,height:a,x:s,y:l}}(e,r);else if("document"===t)n=function(e){let t=L(e),r=q(e),n=e.ownerDocument.body,i=d(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),o=d(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight),a=-r.scrollLeft+en(e),s=-r.scrollTop;return"rtl"===X(n).direction&&(a+=d(t.clientWidth,n.clientWidth)-i),{width:i,height:o,x:a,y:s}}(L(e));else if(F(t))n=function(e,t){let r=er(e,!0,"fixed"===t),n=r.top+e.clientTop,i=r.left+e.clientLeft,o=U(e)?J(e):p(1),a=e.clientWidth*o.x;return{width:a,height:e.clientHeight*o.y,x:i*o.x,y:n*o.y}}(t,r);else{let r=et(e);n={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return S(n)}function ea(e){return"static"===X(e).position}function es(e,t){if(!U(e)||"fixed"===X(e).position)return null;if(t)return t(e);let r=e.offsetParent;return L(e)===r&&(r=r.ownerDocument.body),r}function el(e,t){let r=N(e);if(W(e))return r;if(!U(e)){let t=K(e);for(;t&&!$(t);){if(F(t)&&!ea(t))return t;t=K(t)}return r}let n=es(e,t);for(;n&&["table","td","th"].includes(k(n))&&ea(n);)n=es(n,t);return n&&$(n)&&ea(n)&&!H(n)?r:n||function(e){let t=K(e);for(;U(t)&&!$(t);){if(H(t))return t;if(W(t))break;t=K(t)}return null}(e)||r}let eu=async function(e){let t=this.getOffsetParent||el,r=this.getDimensions,n=await r(e.floating);return{reference:function(e,t,r){let n=U(t),i=L(t),o="fixed"===r,a=er(e,!0,o,t),s={scrollLeft:0,scrollTop:0},l=p(0);if(n||!n&&!o){if(("body"!==k(t)||B(i))&&(s=q(t)),n){let e=er(t,!0,o,t);l.x=e.x+t.clientLeft,l.y=e.y+t.clientTop}else i&&(l.x=en(i))}o&&!n&&i&&(l.x=en(i));let u=!i||n||o?p(0):ei(i,s);return{x:a.left+s.scrollLeft-l.x-u.x,y:a.top+s.scrollTop-l.y-u.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}},ec={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:r,offsetParent:n,strategy:i}=e,o="fixed"===i,a=L(n),s=!!t&&W(t.floating);if(n===a||s&&o)return r;let l={scrollLeft:0,scrollTop:0},u=p(1),c=p(0),d=U(n);if((d||!d&&!o)&&(("body"!==k(n)||B(a))&&(l=q(n)),U(n))){let e=er(n);u=J(n),c.x=e.x+n.clientLeft,c.y=e.y+n.clientTop}let f=!a||d||o?p(0):ei(a,l,!0);return{width:r.width*u.x,height:r.height*u.y,x:r.x*u.x-l.scrollLeft*u.x+c.x+f.x,y:r.y*u.y-l.scrollTop*u.y+c.y+f.y}},getDocumentElement:L,getClippingRect:function(e){let{element:t,boundary:r,rootBoundary:n,strategy:i}=e,o=[..."clippingAncestors"===r?W(t)?[]:function(e,t){let r=t.get(e);if(r)return r;let n=G(e,[],!1).filter(e=>F(e)&&"body"!==k(e)),i=null,o="fixed"===X(e).position,a=o?K(e):e;for(;F(a)&&!$(a);){let t=X(a),r=H(a);r||"fixed"!==t.position||(i=null),(o?!r&&!i:!r&&"static"===t.position&&!!i&&["absolute","fixed"].includes(i.position)||B(a)&&!r&&function e(t,r){let n=K(t);return!(n===r||!F(n)||$(n))&&("fixed"===X(n).position||e(n,r))}(e,a))?n=n.filter(e=>e!==a):i=t,a=K(a)}return t.set(e,n),n}(t,this._c):[].concat(r),n],a=o[0],s=o.reduce((e,r)=>{let n=eo(t,r,i);return e.top=d(n.top,e.top),e.right=c(n.right,e.right),e.bottom=c(n.bottom,e.bottom),e.left=d(n.left,e.left),e},eo(t,a,i));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}},getOffsetParent:el,getElementRects:eu,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:r}=Q(e);return{width:t,height:r}},getScale:J,isElement:F,isRTL:function(e){return"rtl"===X(e).direction}};function ed(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ef=e=>({name:"arrow",options:e,async fn(t){let{x:r,y:n,placement:i,rects:o,platform:a,elements:s,middlewareData:l}=t,{element:u,padding:f=0}=g(e,t)||{};if(null==u)return{};let h=_(f),p={x:r,y:n},m=x(P(i)),y=w(m),v=await a.getDimensions(u),E="y"===m,R=E?"clientHeight":"clientWidth",S=o.reference[y]+o.reference[m]-p[m]-o.floating[y],T=p[m]-o.reference[m],j=await (null==a.getOffsetParent?void 0:a.getOffsetParent(u)),O=j?j[R]:0;O&&await (null==a.isElement?void 0:a.isElement(j))||(O=s.floating[R]||o.floating[y]);let C=O/2-v[y]/2-1,M=c(h[E?"top":"left"],C),A=c(h[E?"bottom":"right"],C),D=O-v[y]-A,k=O/2-v[y]/2+(S/2-T/2),N=d(M,c(k,D)),L=!l.arrow&&null!=b(i)&&k!==N&&o.reference[y]/2-(k<M?M:A)-v[y]/2<0,I=L?k<M?k-M:k-D:0;return{[m]:p[m]+I,data:{[m]:N,centerOffset:k-N-I,...L&&{alignmentOffset:I}},reset:L}}}),eh=(e,t,r)=>{let n=new Map,i={platform:ec,...r},o={...i.platform,_c:n};return j(e,t,{...i,platform:o})};var ep=r(60962),em="undefined"!=typeof document?n.useLayoutEffect:n.useEffect;function ey(e,t){let r,n,i;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((r=e.length)!==t.length)return!1;for(n=r;0!=n--;)if(!ey(e[n],t[n]))return!1;return!0}if((r=(i=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(n=r;0!=n--;)if(!({}).hasOwnProperty.call(t,i[n]))return!1;for(n=r;0!=n--;){let r=i[n];if(("_owner"!==r||!e.$$typeof)&&!ey(e[r],t[r]))return!1}return!0}return e!=e&&t!=t}function eg(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ev(e,t){let r=eg(e);return Math.round(t*r)/r}function eb(e){let t=n.useRef(e);return em(()=>{t.current=e}),t}let ex=e=>({name:"arrow",options:e,fn(t){let{element:r,padding:n}="function"==typeof e?e(t):e;return r&&({}).hasOwnProperty.call(r,"current")?null!=r.current?ef({element:r.current,padding:n}).fn(t):{}:r?ef({element:r,padding:n}).fn(t):{}}}),ew=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var r,n;let{x:i,y:o,placement:a,middlewareData:s}=t,l=await A(t,e);return a===(null==(r=s.offset)?void 0:r.placement)&&null!=(n=s.arrow)&&n.alignmentOffset?{}:{x:i+l.x,y:o+l.y,data:{...l,placement:a}}}}}(e),options:[e,t]}),eP=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:r,y:n,placement:i}=t,{mainAxis:o=!0,crossAxis:a=!1,limiter:s={fn:e=>{let{x:t,y:r}=e;return{x:t,y:r}}},...l}=g(e,t),u={x:r,y:n},f=await O(t,l),h=P(v(i)),p=x(h),m=u[p],y=u[h];if(o){let e="y"===p?"top":"left",t="y"===p?"bottom":"right",r=m+f[e],n=m-f[t];m=d(r,c(m,n))}if(a){let e="y"===h?"top":"left",t="y"===h?"bottom":"right",r=y+f[e],n=y-f[t];y=d(r,c(y,n))}let b=s.fn({...t,[p]:m,[h]:y});return{...b,data:{x:b.x-r,y:b.y-n,enabled:{[p]:o,[h]:a}}}}}}(e),options:[e,t]}),eE=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:r,y:n,placement:i,rects:o,middlewareData:a}=t,{offset:s=0,mainAxis:l=!0,crossAxis:u=!0}=g(e,t),c={x:r,y:n},d=P(i),f=x(d),h=c[f],p=c[d],m=g(s,t),y="number"==typeof m?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(l){let e="y"===f?"height":"width",t=o.reference[f]-o.floating[e]+y.mainAxis,r=o.reference[f]+o.reference[e]-y.mainAxis;h<t?h=t:h>r&&(h=r)}if(u){var b,w;let e="y"===f?"width":"height",t=["top","left"].includes(v(i)),r=o.reference[d]-o.floating[e]+(t&&(null==(b=a.offset)?void 0:b[d])||0)+(t?0:y.crossAxis),n=o.reference[d]+o.reference[e]+(t?0:(null==(w=a.offset)?void 0:w[d])||0)-(t?y.crossAxis:0);p<r?p=r:p>n&&(p=n)}return{[f]:h,[d]:p}}}}(e),options:[e,t]}),eR=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var r,n,i,o,a,s;let{placement:l,middlewareData:u,rects:c,initialPlacement:d,platform:f,elements:h}=t,{mainAxis:p=!0,crossAxis:m=!0,fallbackPlacements:y,fallbackStrategy:_="bestFit",fallbackAxisSideDirection:S="none",flipAlignment:T=!0,...j}=g(e,t);if(null!=(r=u.arrow)&&r.alignmentOffset)return{};let C=v(l),M=P(d),A=v(d)===d,D=await (null==f.isRTL?void 0:f.isRTL(h.floating)),k=y||(A||!T?[R(d)]:function(e){let t=R(e);return[E(e),t,E(t)]}(d)),N="none"!==S;!y&&N&&k.push(...function(e,t,r,n){let i=b(e),o=function(e,t,r){let n=["left","right"],i=["right","left"];switch(e){case"top":case"bottom":if(r)return t?i:n;return t?n:i;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(v(e),"start"===r,n);return i&&(o=o.map(e=>e+"-"+i),t&&(o=o.concat(o.map(E)))),o}(d,T,S,D));let L=[d,...k],I=await O(t,j),F=[],U=(null==(n=u.flip)?void 0:n.overflows)||[];if(p&&F.push(I[C]),m){let e=function(e,t,r){void 0===r&&(r=!1);let n=b(e),i=x(P(e)),o=w(i),a="x"===i?n===(r?"end":"start")?"right":"left":"start"===n?"bottom":"top";return t.reference[o]>t.floating[o]&&(a=R(a)),[a,R(a)]}(l,c,D);F.push(I[e[0]],I[e[1]])}if(U=[...U,{placement:l,overflows:F}],!F.every(e=>e<=0)){let e=((null==(i=u.flip)?void 0:i.index)||0)+1,t=L[e];if(t){let r="alignment"===m&&M!==P(t),n=(null==(a=U[0])?void 0:a.overflows[0])>0;if(!r||n)return{data:{index:e,overflows:U},reset:{placement:t}}}let r=null==(o=U.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:o.placement;if(!r)switch(_){case"bestFit":{let e=null==(s=U.filter(e=>{if(N){let t=P(e.placement);return t===M||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:s[0];e&&(r=e);break}case"initialPlacement":r=d}if(l!==r)return{reset:{placement:r}}}return{}}}}(e),options:[e,t]}),e_=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var r,n;let i,o;let{placement:a,rects:s,platform:l,elements:u}=t,{apply:f=()=>{},...h}=g(e,t),p=await O(t,h),m=v(a),y=b(a),x="y"===P(a),{width:w,height:E}=s.floating;"top"===m||"bottom"===m?(i=m,o=y===(await (null==l.isRTL?void 0:l.isRTL(u.floating))?"start":"end")?"left":"right"):(o=m,i="end"===y?"top":"bottom");let R=E-p.top-p.bottom,_=w-p.left-p.right,S=c(E-p[i],R),T=c(w-p[o],_),j=!t.middlewareData.shift,C=S,M=T;if(null!=(r=t.middlewareData.shift)&&r.enabled.x&&(M=_),null!=(n=t.middlewareData.shift)&&n.enabled.y&&(C=R),j&&!y){let e=d(p.left,0),t=d(p.right,0),r=d(p.top,0),n=d(p.bottom,0);x?M=w-2*(0!==e||0!==t?e+t:d(p.left,p.right)):C=E-2*(0!==r||0!==n?r+n:d(p.top,p.bottom))}await f({...t,availableWidth:M,availableHeight:C});let A=await l.getDimensions(u.floating);return w!==A.width||E!==A.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eS=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:r}=t,{strategy:n="referenceHidden",...i}=g(e,t);switch(n){case"referenceHidden":{let e=C(await O(t,{...i,elementContext:"reference"}),r.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:M(e)}}}case"escaped":{let e=C(await O(t,{...i,altBoundary:!0}),r.floating);return{data:{escapedOffsets:e,escaped:M(e)}}}default:return{}}}}}(e),options:[e,t]}),eT=(e,t)=>({...ex(e),options:[e,t]});var ej=r(45226),eO=r(10326),eC=n.forwardRef((e,t)=>{let{children:r,width:n=10,height:i=5,...o}=e;return(0,eO.jsx)(ej.WV.svg,{...o,ref:t,width:n,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,eO.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eC.displayName="Arrow";var eM=r(55049),eA=r(65819),eD="Popper",[ek,eN]=(0,a.b)(eD),[eL,eI]=ek(eD),eF=e=>{let{__scopePopper:t,children:r}=e,[i,o]=n.useState(null);return(0,eO.jsx)(eL,{scope:t,anchor:i,onAnchorChange:o,children:r})};eF.displayName=eD;var eU="PopperAnchor",eV=n.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:i,...a}=e,s=eI(eU,r),l=n.useRef(null),u=(0,o.e)(t,l);return n.useEffect(()=>{s.onAnchorChange(i?.current||l.current)}),i?null:(0,eO.jsx)(ej.WV.div,{...a,ref:u})});eV.displayName=eU;var eB="PopperContent",[eW,eH]=ek(eB),ez=n.forwardRef((e,t)=>{let{__scopePopper:r,side:i="bottom",sideOffset:a=0,align:s="center",alignOffset:l=0,arrowPadding:u=0,avoidCollisions:f=!0,collisionBoundary:p=[],collisionPadding:m=0,sticky:y="partial",hideWhenDetached:g=!1,updatePositionStrategy:v="optimized",onPlaced:b,...x}=e,w=eI(eB,r),[P,E]=n.useState(null),R=(0,o.e)(t,e=>E(e)),[_,S]=n.useState(null),T=function(e){let[t,r]=n.useState(void 0);return(0,eA.b)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,i;if(!Array.isArray(t)||!t.length)return;let o=t[0];if("borderBoxSize"in o){let e=o.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,i=t.blockSize}else n=e.offsetWidth,i=e.offsetHeight;r({width:n,height:i})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}(_),j=T?.width??0,O=T?.height??0,C="number"==typeof m?m:{top:0,right:0,bottom:0,left:0,...m},M=Array.isArray(p)?p:[p],A=M.length>0,D={padding:C,boundary:M.filter(eK),altBoundary:A},{refs:k,floatingStyles:N,placement:I,isPositioned:F,middlewareData:U}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:r="absolute",middleware:i=[],platform:o,elements:{reference:a,floating:s}={},transform:l=!0,whileElementsMounted:u,open:c}=e,[d,f]=n.useState({x:0,y:0,strategy:r,placement:t,middlewareData:{},isPositioned:!1}),[h,p]=n.useState(i);ey(h,i)||p(i);let[m,y]=n.useState(null),[g,v]=n.useState(null),b=n.useCallback(e=>{e!==E.current&&(E.current=e,y(e))},[]),x=n.useCallback(e=>{e!==R.current&&(R.current=e,v(e))},[]),w=a||m,P=s||g,E=n.useRef(null),R=n.useRef(null),_=n.useRef(d),S=null!=u,T=eb(u),j=eb(o),O=eb(c),C=n.useCallback(()=>{if(!E.current||!R.current)return;let e={placement:t,strategy:r,middleware:h};j.current&&(e.platform=j.current),eh(E.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==O.current};M.current&&!ey(_.current,t)&&(_.current=t,ep.flushSync(()=>{f(t)}))})},[h,t,r,j,O]);em(()=>{!1===c&&_.current.isPositioned&&(_.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[c]);let M=n.useRef(!1);em(()=>(M.current=!0,()=>{M.current=!1}),[]),em(()=>{if(w&&(E.current=w),P&&(R.current=P),w&&P){if(T.current)return T.current(w,P,C);C()}},[w,P,C,T,S]);let A=n.useMemo(()=>({reference:E,floating:R,setReference:b,setFloating:x}),[b,x]),D=n.useMemo(()=>({reference:w,floating:P}),[w,P]),k=n.useMemo(()=>{let e={position:r,left:0,top:0};if(!D.floating)return e;let t=ev(D.floating,d.x),n=ev(D.floating,d.y);return l?{...e,transform:"translate("+t+"px, "+n+"px)",...eg(D.floating)>=1.5&&{willChange:"transform"}}:{position:r,left:t,top:n}},[r,l,D.floating,d.x,d.y]);return n.useMemo(()=>({...d,update:C,refs:A,elements:D,floatingStyles:k}),[d,C,A,D,k])}({strategy:"fixed",placement:i+("center"!==s?"-"+s:""),whileElementsMounted:(...e)=>(function(e,t,r,n){let i;void 0===n&&(n={});let{ancestorScroll:o=!0,ancestorResize:a=!0,elementResize:s="function"==typeof ResizeObserver,layoutShift:l="function"==typeof IntersectionObserver,animationFrame:u=!1}=n,f=Z(e),p=o||a?[...f?G(f):[],...G(t)]:[];p.forEach(e=>{o&&e.addEventListener("scroll",r,{passive:!0}),a&&e.addEventListener("resize",r)});let m=f&&l?function(e,t){let r,n=null,i=L(e);function o(){var e;clearTimeout(r),null==(e=n)||e.disconnect(),n=null}return function a(s,l){void 0===s&&(s=!1),void 0===l&&(l=1),o();let u=e.getBoundingClientRect(),{left:f,top:p,width:m,height:y}=u;if(s||t(),!m||!y)return;let g=h(p),v=h(i.clientWidth-(f+m)),b={rootMargin:-g+"px "+-v+"px "+-h(i.clientHeight-(p+y))+"px "+-h(f)+"px",threshold:d(0,c(1,l))||1},x=!0;function w(t){let n=t[0].intersectionRatio;if(n!==l){if(!x)return a();n?a(!1,n):r=setTimeout(()=>{a(!1,1e-7)},1e3)}1!==n||ed(u,e.getBoundingClientRect())||a(),x=!1}try{n=new IntersectionObserver(w,{...b,root:i.ownerDocument})}catch(e){n=new IntersectionObserver(w,b)}n.observe(e)}(!0),o}(f,r):null,y=-1,g=null;s&&(g=new ResizeObserver(e=>{let[n]=e;n&&n.target===f&&g&&(g.unobserve(t),cancelAnimationFrame(y),y=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),r()}),f&&!u&&g.observe(f),g.observe(t));let v=u?er(e):null;return u&&function t(){let n=er(e);v&&!ed(v,n)&&r(),v=n,i=requestAnimationFrame(t)}(),r(),()=>{var e;p.forEach(e=>{o&&e.removeEventListener("scroll",r),a&&e.removeEventListener("resize",r)}),null==m||m(),null==(e=g)||e.disconnect(),g=null,u&&cancelAnimationFrame(i)}})(...e,{animationFrame:"always"===v}),elements:{reference:w.anchor},middleware:[ew({mainAxis:a+O,alignmentAxis:l}),f&&eP({mainAxis:!0,crossAxis:!1,limiter:"partial"===y?eE():void 0,...D}),f&&eR({...D}),e_({...D,apply:({elements:e,rects:t,availableWidth:r,availableHeight:n})=>{let{width:i,height:o}=t.reference,a=e.floating.style;a.setProperty("--radix-popper-available-width",`${r}px`),a.setProperty("--radix-popper-available-height",`${n}px`),a.setProperty("--radix-popper-anchor-width",`${i}px`),a.setProperty("--radix-popper-anchor-height",`${o}px`)}}),_&&eT({element:_,padding:u}),eG({arrowWidth:j,arrowHeight:O}),g&&eS({strategy:"referenceHidden",...D})]}),[V,B]=eY(I),W=(0,eM.W)(b);(0,eA.b)(()=>{F&&W?.()},[F,W]);let H=U.arrow?.x,z=U.arrow?.y,$=U.arrow?.centerOffset!==0,[X,q]=n.useState();return(0,eA.b)(()=>{P&&q(window.getComputedStyle(P).zIndex)},[P]),(0,eO.jsx)("div",{ref:k.setFloating,"data-radix-popper-content-wrapper":"",style:{...N,transform:F?N.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:X,"--radix-popper-transform-origin":[U.transformOrigin?.x,U.transformOrigin?.y].join(" "),...U.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eO.jsx)(eW,{scope:r,placedSide:V,onArrowChange:S,arrowX:H,arrowY:z,shouldHideArrow:$,children:(0,eO.jsx)(ej.WV.div,{"data-side":V,"data-align":B,...x,ref:R,style:{...x.style,animation:F?void 0:"none"}})})})});ez.displayName=eB;var e$="PopperArrow",eX={top:"bottom",right:"left",bottom:"top",left:"right"},eq=n.forwardRef(function(e,t){let{__scopePopper:r,...n}=e,i=eH(e$,r),o=eX[i.placedSide];return(0,eO.jsx)("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:(0,eO.jsx)(eC,{...n,ref:t,style:{...n.style,display:"block"}})})});function eK(e){return null!==e}eq.displayName=e$;var eG=e=>({name:"transformOrigin",options:e,fn(t){let{placement:r,rects:n,middlewareData:i}=t,o=i.arrow?.centerOffset!==0,a=o?0:e.arrowWidth,s=o?0:e.arrowHeight,[l,u]=eY(r),c={start:"0%",center:"50%",end:"100%"}[u],d=(i.arrow?.x??0)+a/2,f=(i.arrow?.y??0)+s/2,h="",p="";return"bottom"===l?(h=o?c:`${d}px`,p=`${-s}px`):"top"===l?(h=o?c:`${d}px`,p=`${n.floating.height+s}px`):"right"===l?(h=`${-s}px`,p=o?c:`${f}px`):"left"===l&&(h=`${n.floating.width+s}px`,p=o?c:`${f}px`),{data:{x:h,y:p}}}});function eY(e){let[t,r="center"]=e.split("-");return[t,r]}r(83078);var eQ=r(9815),eZ=r(34214),eJ=r(52067),e0=r(6009),[e1,e2]=(0,a.b)("Tooltip",[eN]),e5=eN(),e7="TooltipProvider",e3="tooltip.open",[e9,e4]=e1(e7),e6=e=>{let{__scopeTooltip:t,delayDuration:r=700,skipDelayDuration:i=300,disableHoverableContent:o=!1,children:a}=e,s=n.useRef(!0),l=n.useRef(!1),u=n.useRef(0);return n.useEffect(()=>{let e=u.current;return()=>window.clearTimeout(e)},[]),(0,eO.jsx)(e9,{scope:t,isOpenDelayedRef:s,delayDuration:r,onOpen:n.useCallback(()=>{window.clearTimeout(u.current),s.current=!1},[]),onClose:n.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>s.current=!0,i)},[i]),isPointerInTransitRef:l,onPointerInTransitChange:n.useCallback(e=>{l.current=e},[]),disableHoverableContent:o,children:a})};e6.displayName=e7;var e8="Tooltip",[te,tt]=e1(e8),tr=e=>{let{__scopeTooltip:t,children:r,open:i,defaultOpen:o,onOpenChange:a,disableHoverableContent:s,delayDuration:u}=e,c=e4(e8,e.__scopeTooltip),d=e5(t),[f,h]=n.useState(null),p=(0,l.M)(),m=n.useRef(0),y=s??c.disableHoverableContent,g=u??c.delayDuration,v=n.useRef(!1),[b,x]=(0,eJ.T)({prop:i,defaultProp:o??!1,onChange:e=>{e?(c.onOpen(),document.dispatchEvent(new CustomEvent(e3))):c.onClose(),a?.(e)},caller:e8}),w=n.useMemo(()=>b?v.current?"delayed-open":"instant-open":"closed",[b]),P=n.useCallback(()=>{window.clearTimeout(m.current),m.current=0,v.current=!1,x(!0)},[x]),E=n.useCallback(()=>{window.clearTimeout(m.current),m.current=0,x(!1)},[x]),R=n.useCallback(()=>{window.clearTimeout(m.current),m.current=window.setTimeout(()=>{v.current=!0,x(!0),m.current=0},g)},[g,x]);return n.useEffect(()=>()=>{m.current&&(window.clearTimeout(m.current),m.current=0)},[]),(0,eO.jsx)(eF,{...d,children:(0,eO.jsx)(te,{scope:t,contentId:p,open:b,stateAttribute:w,trigger:f,onTriggerChange:h,onTriggerEnter:n.useCallback(()=>{c.isOpenDelayedRef.current?R():P()},[c.isOpenDelayedRef,R,P]),onTriggerLeave:n.useCallback(()=>{y?E():(window.clearTimeout(m.current),m.current=0)},[E,y]),onOpen:P,onClose:E,disableHoverableContent:y,children:r})})};tr.displayName=e8;var tn="TooltipTrigger",ti=n.forwardRef((e,t)=>{let{__scopeTooltip:r,...a}=e,s=tt(tn,r),l=e4(tn,r),u=e5(r),c=n.useRef(null),d=(0,o.e)(t,c,s.onTriggerChange),f=n.useRef(!1),h=n.useRef(!1),p=n.useCallback(()=>f.current=!1,[]);return n.useEffect(()=>()=>document.removeEventListener("pointerup",p),[p]),(0,eO.jsx)(eV,{asChild:!0,...u,children:(0,eO.jsx)(ej.WV.button,{"aria-describedby":s.open?s.contentId:void 0,"data-state":s.stateAttribute,...a,ref:d,onPointerMove:(0,i.M)(e.onPointerMove,e=>{"touch"===e.pointerType||h.current||l.isPointerInTransitRef.current||(s.onTriggerEnter(),h.current=!0)}),onPointerLeave:(0,i.M)(e.onPointerLeave,()=>{s.onTriggerLeave(),h.current=!1}),onPointerDown:(0,i.M)(e.onPointerDown,()=>{s.open&&s.onClose(),f.current=!0,document.addEventListener("pointerup",p,{once:!0})}),onFocus:(0,i.M)(e.onFocus,()=>{f.current||s.onOpen()}),onBlur:(0,i.M)(e.onBlur,s.onClose),onClick:(0,i.M)(e.onClick,s.onClose)})})});ti.displayName=tn;var[to,ta]=e1("TooltipPortal",{forceMount:void 0}),ts="TooltipContent",tl=n.forwardRef((e,t)=>{let r=ta(ts,e.__scopeTooltip),{forceMount:n=r.forceMount,side:i="top",...o}=e,a=tt(ts,e.__scopeTooltip);return(0,eO.jsx)(eQ.z,{present:n||a.open,children:a.disableHoverableContent?(0,eO.jsx)(th,{side:i,...o,ref:t}):(0,eO.jsx)(tu,{side:i,...o,ref:t})})}),tu=n.forwardRef((e,t)=>{let r=tt(ts,e.__scopeTooltip),i=e4(ts,e.__scopeTooltip),a=n.useRef(null),s=(0,o.e)(t,a),[l,u]=n.useState(null),{trigger:c,onClose:d}=r,f=a.current,{onPointerInTransitChange:h}=i,p=n.useCallback(()=>{u(null),h(!1)},[h]),m=n.useCallback((e,t)=>{let r=e.currentTarget,n={x:e.clientX,y:e.clientY},i=function(e,t){let r=Math.abs(t.top-e.y),n=Math.abs(t.bottom-e.y),i=Math.abs(t.right-e.x),o=Math.abs(t.left-e.x);switch(Math.min(r,n,i,o)){case o:return"left";case i:return"right";case r:return"top";case n:return"bottom";default:throw Error("unreachable")}}(n,r.getBoundingClientRect());u(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:e.y>t.y?1:0),function(e){if(e.length<=1)return e.slice();let t=[];for(let r=0;r<e.length;r++){let n=e[r];for(;t.length>=2;){let e=t[t.length-1],r=t[t.length-2];if((e.x-r.x)*(n.y-r.y)>=(e.y-r.y)*(n.x-r.x))t.pop();else break}t.push(n)}t.pop();let r=[];for(let t=e.length-1;t>=0;t--){let n=e[t];for(;r.length>=2;){let e=r[r.length-1],t=r[r.length-2];if((e.x-t.x)*(n.y-t.y)>=(e.y-t.y)*(n.x-t.x))r.pop();else break}r.push(n)}return(r.pop(),1===t.length&&1===r.length&&t[0].x===r[0].x&&t[0].y===r[0].y)?t:t.concat(r)}(t)}([...function(e,t,r=5){let n=[];switch(t){case"top":n.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":n.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":n.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":n.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r})}return n}(n,i),...function(e){let{top:t,right:r,bottom:n,left:i}=e;return[{x:i,y:t},{x:r,y:t},{x:r,y:n},{x:i,y:n}]}(t.getBoundingClientRect())])),h(!0)},[h]);return n.useEffect(()=>()=>p(),[p]),n.useEffect(()=>{if(c&&f){let e=e=>m(e,f),t=e=>m(e,c);return c.addEventListener("pointerleave",e),f.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),f.removeEventListener("pointerleave",t)}}},[c,f,m,p]),n.useEffect(()=>{if(l){let e=e=>{let t=e.target,r={x:e.clientX,y:e.clientY},n=c?.contains(t)||f?.contains(t),i=!function(e,t){let{x:r,y:n}=e,i=!1;for(let e=0,o=t.length-1;e<t.length;o=e++){let a=t[e],s=t[o],l=a.x,u=a.y,c=s.x,d=s.y;u>n!=d>n&&r<(c-l)*(n-u)/(d-u)+l&&(i=!i)}return i}(r,l);n?p():i&&(p(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,f,l,d,p]),(0,eO.jsx)(th,{...e,ref:s})}),[tc,td]=e1(e8,{isInside:!1}),tf=(0,eZ.sA)("TooltipContent"),th=n.forwardRef((e,t)=>{let{__scopeTooltip:r,children:i,"aria-label":o,onEscapeKeyDown:a,onPointerDownOutside:l,...u}=e,c=tt(ts,r),d=e5(r),{onClose:f}=c;return n.useEffect(()=>(document.addEventListener(e3,f),()=>document.removeEventListener(e3,f)),[f]),n.useEffect(()=>{if(c.trigger){let e=e=>{let t=e.target;t?.contains(c.trigger)&&f()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[c.trigger,f]),(0,eO.jsx)(s.XB,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:a,onPointerDownOutside:l,onFocusOutside:e=>e.preventDefault(),onDismiss:f,children:(0,eO.jsxs)(ez,{"data-state":c.stateAttribute,...d,...u,ref:t,style:{...u.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,eO.jsx)(tf,{children:i}),(0,eO.jsx)(tc,{scope:r,isInside:!0,children:(0,eO.jsx)(e0.fC,{id:c.contentId,role:"tooltip",children:o||i})})]})})});tl.displayName=ts;var tp="TooltipArrow";n.forwardRef((e,t)=>{let{__scopeTooltip:r,...n}=e,i=e5(r);return td(tp,r).isInside?null:(0,eO.jsx)(eq,{...i,...n,ref:t})}).displayName=tp;var tm=e6,ty=tr,tg=ti,tv=tl},55049:(e,t,r)=>{"use strict";r.d(t,{W:()=>i});var n=r(17577);function i(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},52067:(e,t,r)=>{"use strict";r.d(t,{T:()=>s});var n,i=r(17577),o=r(65819),a=(n||(n=r.t(i,2)))[" useInsertionEffect ".trim().toString()]||o.b;function s({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[o,s,l]=function({defaultProp:e,onChange:t}){let[r,n]=i.useState(e),o=i.useRef(r),s=i.useRef(t);return a(()=>{s.current=t},[t]),i.useEffect(()=>{o.current!==r&&(s.current?.(r),o.current=r)},[r,o]),[r,n,s]}({defaultProp:t,onChange:r}),u=void 0!==e,c=u?e:o;{let t=i.useRef(void 0!==e);i.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${n} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,n])}return[c,i.useCallback(t=>{if(u){let r="function"==typeof t?t(e):t;r!==e&&l.current?.(r)}else s(t)},[u,e,s,l])]}Symbol("RADIX:SYNC_STATE")},65819:(e,t,r)=>{"use strict";r.d(t,{b:()=>i});var n=r(17577),i=globalThis?.document?n.useLayoutEffect:()=>{}},6009:(e,t,r)=>{"use strict";r.d(t,{TX:()=>s,fC:()=>l});var n=r(17577),i=r(45226),o=r(10326),a=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),s=n.forwardRef((e,t)=>(0,o.jsx)(i.WV.span,{...e,ref:t,style:{...a,...e.style}}));s.displayName="VisuallyHidden";var l=s},2659:(e,t,r)=>{"use strict";r.d(t,{S:()=>L});var n="undefined"==typeof window||"Deno"in globalThis;function i(){}function o(e,t){return"function"==typeof e?e(t):e}function a(e,t){let{type:r="all",exact:n,fetchStatus:i,predicate:o,queryKey:a,stale:s}=e;if(a){if(n){if(t.queryHash!==l(a,t.options))return!1}else if(!c(t.queryKey,a))return!1}if("all"!==r){let e=t.isActive();if("active"===r&&!e||"inactive"===r&&e)return!1}return("boolean"!=typeof s||t.isStale()===s)&&(!i||i===t.state.fetchStatus)&&(!o||!!o(t))}function s(e,t){let{exact:r,status:n,predicate:i,mutationKey:o}=e;if(o){if(!t.options.mutationKey)return!1;if(r){if(u(t.options.mutationKey)!==u(o))return!1}else if(!c(t.options.mutationKey,o))return!1}return(!n||t.state.status===n)&&(!i||!!i(t))}function l(e,t){return(t?.queryKeyHashFn||u)(e)}function u(e){return JSON.stringify(e,(e,t)=>f(t)?Object.keys(t).sort().reduce((e,r)=>(e[r]=t[r],e),{}):t)}function c(e,t){return e===t||typeof e==typeof t&&!!e&&!!t&&"object"==typeof e&&"object"==typeof t&&Object.keys(t).every(r=>c(e[r],t[r]))}function d(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function f(e){if(!h(e))return!1;let t=e.constructor;if(void 0===t)return!0;let r=t.prototype;return!!(h(r)&&r.hasOwnProperty("isPrototypeOf"))&&Object.getPrototypeOf(e)===Object.prototype}function h(e){return"[object Object]"===Object.prototype.toString.call(e)}function p(e,t,r=0){let n=[...e,t];return r&&n.length>r?n.slice(1):n}function m(e,t,r=0){let n=[t,...e];return r&&n.length>r?n.slice(0,-1):n}var y=Symbol();function g(e,t){return!e.queryFn&&t?.initialPromise?()=>t.initialPromise:e.queryFn&&e.queryFn!==y?e.queryFn:()=>Promise.reject(Error(`Missing queryFn: '${e.queryHash}'`))}var v=e=>setTimeout(e,0),b=function(){let e=[],t=0,r=e=>{e()},n=e=>{e()},i=v,o=n=>{t?e.push(n):i(()=>{r(n)})},a=()=>{let t=e;e=[],t.length&&i(()=>{n(()=>{t.forEach(e=>{r(e)})})})};return{batch:e=>{let r;t++;try{r=e()}finally{--t||a()}return r},batchCalls:e=>(...t)=>{o(()=>{e(...t)})},schedule:o,setNotifyFunction:e=>{r=e},setBatchNotifyFunction:e=>{n=e},setScheduler:e=>{i=e}}}(),x=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},w=new class extends x{#e;#t;#r;constructor(){super(),this.#r=e=>{if(!n&&window.addEventListener){let t=()=>e();return window.addEventListener("visibilitychange",t,!1),()=>{window.removeEventListener("visibilitychange",t)}}}}onSubscribe(){this.#t||this.setEventListener(this.#r)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#r=e,this.#t?.(),this.#t=e(e=>{"boolean"==typeof e?this.setFocused(e):this.onFocus()})}setFocused(e){this.#e!==e&&(this.#e=e,this.onFocus())}onFocus(){let e=this.isFocused();this.listeners.forEach(t=>{t(e)})}isFocused(){return"boolean"==typeof this.#e?this.#e:globalThis.document?.visibilityState!=="hidden"}},P=new class extends x{#n=!0;#t;#r;constructor(){super(),this.#r=e=>{if(!n&&window.addEventListener){let t=()=>e(!0),r=()=>e(!1);return window.addEventListener("online",t,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",t),window.removeEventListener("offline",r)}}}}onSubscribe(){this.#t||this.setEventListener(this.#r)}onUnsubscribe(){this.hasListeners()||(this.#t?.(),this.#t=void 0)}setEventListener(e){this.#r=e,this.#t?.(),this.#t=e(this.setOnline.bind(this))}setOnline(e){this.#n!==e&&(this.#n=e,this.listeners.forEach(t=>{t(e)}))}isOnline(){return this.#n}};function E(e){return Math.min(1e3*2**e,3e4)}function R(e){return(e??"online")!=="online"||P.isOnline()}var _=class extends Error{constructor(e){super("CancelledError"),this.revert=e?.revert,this.silent=e?.silent}};function S(e){return e instanceof _}function T(e){let t,r=!1,i=0,o=!1,a=function(){let e,t;let r=new Promise((r,n)=>{e=r,t=n});function n(e){Object.assign(r,e),delete r.resolve,delete r.reject}return r.status="pending",r.catch(()=>{}),r.resolve=t=>{n({status:"fulfilled",value:t}),e(t)},r.reject=e=>{n({status:"rejected",reason:e}),t(e)},r}(),s=()=>w.isFocused()&&("always"===e.networkMode||P.isOnline())&&e.canRun(),l=()=>R(e.networkMode)&&e.canRun(),u=r=>{o||(o=!0,e.onSuccess?.(r),t?.(),a.resolve(r))},c=r=>{o||(o=!0,e.onError?.(r),t?.(),a.reject(r))},d=()=>new Promise(r=>{t=e=>{(o||s())&&r(e)},e.onPause?.()}).then(()=>{t=void 0,o||e.onContinue?.()}),f=()=>{let t;if(o)return;let a=0===i?e.initialPromise:void 0;try{t=a??e.fn()}catch(e){t=Promise.reject(e)}Promise.resolve(t).then(u).catch(t=>{if(o)return;let a=e.retry??(n?0:3),l=e.retryDelay??E,u="function"==typeof l?l(i,t):l,h=!0===a||"number"==typeof a&&i<a||"function"==typeof a&&a(i,t);if(r||!h){c(t);return}i++,e.onFail?.(i,t),new Promise(e=>{setTimeout(e,u)}).then(()=>s()?void 0:d()).then(()=>{r?c(t):f()})})};return{promise:a,cancel:t=>{o||(c(new _(t)),e.abort?.())},continue:()=>(t?.(),a),cancelRetry:()=>{r=!0},continueRetry:()=>{r=!1},canStart:l,start:()=>(l()?f():d().then(f),a)}}var j=class{#i;destroy(){this.clearGcTimeout()}scheduleGc(){var e;this.clearGcTimeout(),"number"==typeof(e=this.gcTime)&&e>=0&&e!==1/0&&(this.#i=setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(n?1/0:3e5))}clearGcTimeout(){this.#i&&(clearTimeout(this.#i),this.#i=void 0)}},O=class extends j{#o;#a;#s;#l;#u;#c;#d;constructor(e){super(),this.#d=!1,this.#c=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.#l=e.client,this.#s=this.#l.getQueryCache(),this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.#o=function(e){let t="function"==typeof e.initialData?e.initialData():e.initialData,r=void 0!==t,n=r?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:r?n??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:r?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=e.state??this.#o,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#u?.promise}setOptions(e){this.options={...this.#c,...e},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#s.remove(this)}setData(e,t){var r,n;let i=(r=this.state.data,"function"==typeof(n=this.options).structuralSharing?n.structuralSharing(r,e):!1!==n.structuralSharing?function e(t,r){if(t===r)return t;let n=d(t)&&d(r);if(n||f(t)&&f(r)){let i=n?t:Object.keys(t),o=i.length,a=n?r:Object.keys(r),s=a.length,l=n?[]:{},u=0;for(let o=0;o<s;o++){let s=n?o:a[o];(!n&&i.includes(s)||n)&&void 0===t[s]&&void 0===r[s]?(l[s]=void 0,u++):(l[s]=e(t[s],r[s]),l[s]===t[s]&&void 0!==t[s]&&u++)}return o===s&&u===o?t:l}return r}(r,e):e);return this.#f({data:i,type:"success",dataUpdatedAt:t?.updatedAt,manual:t?.manual}),i}setState(e,t){this.#f({type:"setState",state:e,setStateOptions:t})}cancel(e){let t=this.#u?.promise;return this.#u?.cancel(e),t?t.then(i).catch(i):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#o)}isActive(){return this.observers.some(e=>{var t;return!1!==("function"==typeof(t=e.options.enabled)?t(this):t)})}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===y||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return!!this.state.isInvalidated||(this.getObserversCount()>0?this.observers.some(e=>e.getCurrentResult().isStale):void 0===this.state.data)}isStaleByTime(e=0){return this.state.isInvalidated||void 0===this.state.data||!Math.max(this.state.dataUpdatedAt+(e||0)-Date.now(),0)}onFocus(){let e=this.observers.find(e=>e.shouldFetchOnWindowFocus());e?.refetch({cancelRefetch:!1}),this.#u?.continue()}onOnline(){let e=this.observers.find(e=>e.shouldFetchOnReconnect());e?.refetch({cancelRefetch:!1}),this.#u?.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.#s.notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter(t=>t!==e),this.observers.length||(this.#u&&(this.#d?this.#u.cancel({revert:!0}):this.#u.cancelRetry()),this.scheduleGc()),this.#s.notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#f({type:"invalidate"})}fetch(e,t){if("idle"!==this.state.fetchStatus){if(void 0!==this.state.data&&t?.cancelRefetch)this.cancel({silent:!0});else if(this.#u)return this.#u.continueRetry(),this.#u.promise}if(e&&this.setOptions(e),!this.options.queryFn){let e=this.observers.find(e=>e.options.queryFn);e&&this.setOptions(e.options)}let r=new AbortController,n=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(this.#d=!0,r.signal)})},i={fetchOptions:t,options:this.options,queryKey:this.queryKey,client:this.#l,state:this.state,fetchFn:()=>{let e=g(this.options,t),r={client:this.#l,queryKey:this.queryKey,meta:this.meta};return(n(r),this.#d=!1,this.options.persister)?this.options.persister(e,r,this):e(r)}};n(i),this.options.behavior?.onFetch(i,this),this.#a=this.state,("idle"===this.state.fetchStatus||this.state.fetchMeta!==i.fetchOptions?.meta)&&this.#f({type:"fetch",meta:i.fetchOptions?.meta});let o=e=>{S(e)&&e.silent||this.#f({type:"error",error:e}),S(e)||(this.#s.config.onError?.(e,this),this.#s.config.onSettled?.(this.state.data,e,this)),this.scheduleGc()};return this.#u=T({initialPromise:t?.initialPromise,fn:i.fetchFn,abort:r.abort.bind(r),onSuccess:e=>{if(void 0===e){o(Error(`${this.queryHash} data is undefined`));return}try{this.setData(e)}catch(e){o(e);return}this.#s.config.onSuccess?.(e,this),this.#s.config.onSettled?.(e,this.state.error,this),this.scheduleGc()},onError:o,onFail:(e,t)=>{this.#f({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#f({type:"pause"})},onContinue:()=>{this.#f({type:"continue"})},retry:i.options.retry,retryDelay:i.options.retryDelay,networkMode:i.options.networkMode,canRun:()=>!0}),this.#u.start()}#f(e){this.state=(t=>{switch(e.type){case"failed":return{...t,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...t,fetchStatus:"paused"};case"continue":return{...t,fetchStatus:"fetching"};case"fetch":var r;return{...t,...(r=t.data,{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:R(this.options.networkMode)?"fetching":"paused",...void 0===r&&{error:null,status:"pending"}}),fetchMeta:e.meta??null};case"success":return{...t,data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:e.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":let n=e.error;if(S(n)&&n.revert&&this.#a)return{...this.#a,fetchStatus:"idle"};return{...t,error:n,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,fetchFailureReason:n,fetchStatus:"idle",status:"error"};case"invalidate":return{...t,isInvalidated:!0};case"setState":return{...t,...e.state}}})(this.state),b.batch(()=>{this.observers.forEach(e=>{e.onQueryUpdate()}),this.#s.notify({query:this,type:"updated",action:e})})}},C=class extends x{constructor(e={}){super(),this.config=e,this.#h=new Map}#h;build(e,t,r){let n=t.queryKey,i=t.queryHash??l(n,t),o=this.get(i);return o||(o=new O({client:e,queryKey:n,queryHash:i,options:e.defaultQueryOptions(t),state:r,defaultOptions:e.getQueryDefaults(n)}),this.add(o)),o}add(e){this.#h.has(e.queryHash)||(this.#h.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){let t=this.#h.get(e.queryHash);t&&(e.destroy(),t===e&&this.#h.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){b.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}get(e){return this.#h.get(e)}getAll(){return[...this.#h.values()]}find(e){let t={exact:!0,...e};return this.getAll().find(e=>a(t,e))}findAll(e={}){let t=this.getAll();return Object.keys(e).length>0?t.filter(t=>a(e,t)):t}notify(e){b.batch(()=>{this.listeners.forEach(t=>{t(e)})})}onFocus(){b.batch(()=>{this.getAll().forEach(e=>{e.onFocus()})})}onOnline(){b.batch(()=>{this.getAll().forEach(e=>{e.onOnline()})})}},M=class extends j{#p;#m;#u;constructor(e){super(),this.mutationId=e.mutationId,this.#m=e.mutationCache,this.#p=[],this.state=e.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#p.includes(e)||(this.#p.push(e),this.clearGcTimeout(),this.#m.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#p=this.#p.filter(t=>t!==e),this.scheduleGc(),this.#m.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#p.length||("pending"===this.state.status?this.scheduleGc():this.#m.remove(this))}continue(){return this.#u?.continue()??this.execute(this.state.variables)}async execute(e){let t=()=>{this.#f({type:"continue"})};this.#u=T({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(Error("No mutationFn found")),onFail:(e,t)=>{this.#f({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#f({type:"pause"})},onContinue:t,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#m.canRun(this)});let r="pending"===this.state.status,n=!this.#u.canStart();try{if(r)t();else{this.#f({type:"pending",variables:e,isPaused:n}),await this.#m.config.onMutate?.(e,this);let t=await this.options.onMutate?.(e);t!==this.state.context&&this.#f({type:"pending",context:t,variables:e,isPaused:n})}let i=await this.#u.start();return await this.#m.config.onSuccess?.(i,e,this.state.context,this),await this.options.onSuccess?.(i,e,this.state.context),await this.#m.config.onSettled?.(i,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(i,null,e,this.state.context),this.#f({type:"success",data:i}),i}catch(t){try{throw await this.#m.config.onError?.(t,e,this.state.context,this),await this.options.onError?.(t,e,this.state.context),await this.#m.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,t,e,this.state.context),t}finally{this.#f({type:"error",error:t})}}finally{this.#m.runNext(this)}}#f(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),b.batch(()=>{this.#p.forEach(t=>{t.onMutationUpdate(e)}),this.#m.notify({mutation:this,type:"updated",action:e})})}},A=class extends x{constructor(e={}){super(),this.config=e,this.#y=new Set,this.#g=new Map,this.#v=0}#y;#g;#v;build(e,t,r){let n=new M({mutationCache:this,mutationId:++this.#v,options:e.defaultMutationOptions(t),state:r});return this.add(n),n}add(e){this.#y.add(e);let t=D(e);if("string"==typeof t){let r=this.#g.get(t);r?r.push(e):this.#g.set(t,[e])}this.notify({type:"added",mutation:e})}remove(e){if(this.#y.delete(e)){let t=D(e);if("string"==typeof t){let r=this.#g.get(t);if(r){if(r.length>1){let t=r.indexOf(e);-1!==t&&r.splice(t,1)}else r[0]===e&&this.#g.delete(t)}}}this.notify({type:"removed",mutation:e})}canRun(e){let t=D(e);if("string"!=typeof t)return!0;{let r=this.#g.get(t),n=r?.find(e=>"pending"===e.state.status);return!n||n===e}}runNext(e){let t=D(e);if("string"!=typeof t)return Promise.resolve();{let r=this.#g.get(t)?.find(t=>t!==e&&t.state.isPaused);return r?.continue()??Promise.resolve()}}clear(){b.batch(()=>{this.#y.forEach(e=>{this.notify({type:"removed",mutation:e})}),this.#y.clear(),this.#g.clear()})}getAll(){return Array.from(this.#y)}find(e){let t={exact:!0,...e};return this.getAll().find(e=>s(t,e))}findAll(e={}){return this.getAll().filter(t=>s(e,t))}notify(e){b.batch(()=>{this.listeners.forEach(t=>{t(e)})})}resumePausedMutations(){let e=this.getAll().filter(e=>e.state.isPaused);return b.batch(()=>Promise.all(e.map(e=>e.continue().catch(i))))}};function D(e){return e.options.scope?.id}function k(e){return{onFetch:(t,r)=>{let n=t.options,i=t.fetchOptions?.meta?.fetchMore?.direction,o=t.state.data?.pages||[],a=t.state.data?.pageParams||[],s={pages:[],pageParams:[]},l=0,u=async()=>{let r=!1,u=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(t.signal.aborted?r=!0:t.signal.addEventListener("abort",()=>{r=!0}),t.signal)})},c=g(t.options,t.fetchOptions),d=async(e,n,i)=>{if(r)return Promise.reject();if(null==n&&e.pages.length)return Promise.resolve(e);let o={client:t.client,queryKey:t.queryKey,pageParam:n,direction:i?"backward":"forward",meta:t.options.meta};u(o);let a=await c(o),{maxPages:s}=t.options,l=i?m:p;return{pages:l(e.pages,a,s),pageParams:l(e.pageParams,n,s)}};if(i&&o.length){let e="backward"===i,t={pages:o,pageParams:a},r=(e?function(e,{pages:t,pageParams:r}){return t.length>0?e.getPreviousPageParam?.(t[0],t,r[0],r):void 0}:N)(n,t);s=await d(t,r,e)}else{let t=e??o.length;do{let e=0===l?a[0]??n.initialPageParam:N(n,s);if(l>0&&null==e)break;s=await d(s,e),l++}while(l<t)}return s};t.options.persister?t.fetchFn=()=>t.options.persister?.(u,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},r):t.fetchFn=u}}}function N(e,{pages:t,pageParams:r}){let n=t.length-1;return t.length>0?e.getNextPageParam(t[n],t,r[n],r):void 0}var L=class{#b;#m;#c;#x;#w;#P;#E;#R;constructor(e={}){this.#b=e.queryCache||new C,this.#m=e.mutationCache||new A,this.#c=e.defaultOptions||{},this.#x=new Map,this.#w=new Map,this.#P=0}mount(){this.#P++,1===this.#P&&(this.#E=w.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#b.onFocus())}),this.#R=P.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#b.onOnline())}))}unmount(){this.#P--,0===this.#P&&(this.#E?.(),this.#E=void 0,this.#R?.(),this.#R=void 0)}isFetching(e){return this.#b.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#m.findAll({...e,status:"pending"}).length}getQueryData(e){let t=this.defaultQueryOptions({queryKey:e});return this.#b.get(t.queryHash)?.state.data}ensureQueryData(e){let t=this.defaultQueryOptions(e),r=this.#b.build(this,t),n=r.state.data;return void 0===n?this.fetchQuery(e):(e.revalidateIfStale&&r.isStaleByTime(o(t.staleTime,r))&&this.prefetchQuery(t),Promise.resolve(n))}getQueriesData(e){return this.#b.findAll(e).map(({queryKey:e,state:t})=>[e,t.data])}setQueryData(e,t,r){let n=this.defaultQueryOptions({queryKey:e}),i=this.#b.get(n.queryHash),o=i?.state.data,a="function"==typeof t?t(o):t;if(void 0!==a)return this.#b.build(this,n).setData(a,{...r,manual:!0})}setQueriesData(e,t,r){return b.batch(()=>this.#b.findAll(e).map(({queryKey:e})=>[e,this.setQueryData(e,t,r)]))}getQueryState(e){let t=this.defaultQueryOptions({queryKey:e});return this.#b.get(t.queryHash)?.state}removeQueries(e){let t=this.#b;b.batch(()=>{t.findAll(e).forEach(e=>{t.remove(e)})})}resetQueries(e,t){let r=this.#b;return b.batch(()=>(r.findAll(e).forEach(e=>{e.reset()}),this.refetchQueries({type:"active",...e},t)))}cancelQueries(e,t={}){let r={revert:!0,...t};return Promise.all(b.batch(()=>this.#b.findAll(e).map(e=>e.cancel(r)))).then(i).catch(i)}invalidateQueries(e,t={}){return b.batch(()=>(this.#b.findAll(e).forEach(e=>{e.invalidate()}),e?.refetchType==="none")?Promise.resolve():this.refetchQueries({...e,type:e?.refetchType??e?.type??"active"},t))}refetchQueries(e,t={}){let r={...t,cancelRefetch:t.cancelRefetch??!0};return Promise.all(b.batch(()=>this.#b.findAll(e).filter(e=>!e.isDisabled()).map(e=>{let t=e.fetch(void 0,r);return r.throwOnError||(t=t.catch(i)),"paused"===e.state.fetchStatus?Promise.resolve():t}))).then(i)}fetchQuery(e){let t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);let r=this.#b.build(this,t);return r.isStaleByTime(o(t.staleTime,r))?r.fetch(t):Promise.resolve(r.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(i).catch(i)}fetchInfiniteQuery(e){return e.behavior=k(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(i).catch(i)}ensureInfiniteQueryData(e){return e.behavior=k(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return P.isOnline()?this.#m.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#b}getMutationCache(){return this.#m}getDefaultOptions(){return this.#c}setDefaultOptions(e){this.#c=e}setQueryDefaults(e,t){this.#x.set(u(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){let t=[...this.#x.values()],r={};return t.forEach(t=>{c(e,t.queryKey)&&Object.assign(r,t.defaultOptions)}),r}setMutationDefaults(e,t){this.#w.set(u(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){let t=[...this.#w.values()],r={};return t.forEach(t=>{c(e,t.mutationKey)&&Object.assign(r,t.defaultOptions)}),r}defaultQueryOptions(e){if(e._defaulted)return e;let t={...this.#c.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=l(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===y&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#c.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#b.clear(),this.#m.clear()}}},44976:(e,t,r)=>{"use strict";r.d(t,{aH:()=>a});var n=r(17577),i=r(10326),o=n.createContext(void 0),a=({client:e,children:t})=>(n.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),(0,i.jsx)(o.Provider,{value:e,children:t}))},79360:(e,t,r)=>{"use strict";r.d(t,{j:()=>a});var n=r(41135);let i=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,o=n.W,a=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return o(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:a,defaultVariants:s}=t,l=Object.keys(a).map(e=>{let t=null==r?void 0:r[e],n=null==s?void 0:s[e];if(null===t)return null;let o=i(t)||i(n);return a[e][o]}),u=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return o(e,l,null==t?void 0:null===(n=t.compoundVariants)||void 0===n?void 0:n.reduce((e,t)=>{let{class:r,className:n,...i}=t;return Object.entries(i).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...s,...u}[t]):({...s,...u})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},41135:(e,t,r)=>{"use strict";function n(){for(var e,t,r=0,n="",i=arguments.length;r<i;r++)(e=arguments[r])&&(t=function e(t){var r,n,i="";if("string"==typeof t||"number"==typeof t)i+=t;else if("object"==typeof t){if(Array.isArray(t)){var o=t.length;for(r=0;r<o;r++)t[r]&&(n=e(t[r]))&&(i&&(i+=" "),i+=n)}else for(n in t)t[n]&&(i&&(i+=" "),i+=n)}return i}(e))&&(n&&(n+=" "),n+=t);return n}r.d(t,{W:()=>n,Z:()=>i});let i=n},88129:(e,t,r)=>{"use strict";r.d(t,{M:()=>v});var n=r(10326),i=r(17577),o=r(40339),a=r(74749),s=r(42482),l=r(40295),u=r(69539),c=r(73965);class d extends i.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,r=(0,u.R)(e)&&e.offsetWidth||0,n=this.props.sizeRef.current;n.height=t.offsetHeight||0,n.width=t.offsetWidth||0,n.top=t.offsetTop,n.left=t.offsetLeft,n.right=r-n.width-n.left}return null}componentDidUpdate(){}render(){return this.props.children}}function f({children:e,isPresent:t,anchorX:r}){let o=(0,i.useId)(),a=(0,i.useRef)(null),s=(0,i.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:l}=(0,i.useContext)(c._);return(0,i.useInsertionEffect)(()=>{let{width:e,height:n,top:i,left:u,right:c}=s.current;if(t||!a.current||!e||!n)return;let d="left"===r?`left: ${u}`:`right: ${c}`;a.current.dataset.motionPopId=o;let f=document.createElement("style");return l&&(f.nonce=l),document.head.appendChild(f),f.sheet&&f.sheet.insertRule(`
          [data-motion-pop-id="${o}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${n}px !important;
            ${d}px !important;
            top: ${i}px !important;
          }
        `),()=>{document.head.contains(f)&&document.head.removeChild(f)}},[t]),(0,n.jsx)(d,{isPresent:t,childRef:a,sizeRef:s,children:i.cloneElement(e,{ref:a})})}let h=({children:e,initial:t,isPresent:r,onExitComplete:o,custom:s,presenceAffectsLayout:u,mode:c,anchorX:d})=>{let h=(0,a.h)(p),m=(0,i.useId)(),y=!0,g=(0,i.useMemo)(()=>(y=!1,{id:m,initial:t,isPresent:r,custom:s,onExitComplete:e=>{for(let t of(h.set(e,!0),h.values()))if(!t)return;o&&o()},register:e=>(h.set(e,!1),()=>h.delete(e))}),[r,h,o]);return u&&y&&(g={...g}),(0,i.useMemo)(()=>{h.forEach((e,t)=>h.set(t,!1))},[r]),i.useEffect(()=>{r||h.size||!o||o()},[r]),"popLayout"===c&&(e=(0,n.jsx)(f,{isPresent:r,anchorX:d,children:e})),(0,n.jsx)(l.O.Provider,{value:g,children:e})};function p(){return new Map}var m=r(56933);let y=e=>e.key||"";function g(e){let t=[];return i.Children.forEach(e,e=>{(0,i.isValidElement)(e)&&t.push(e)}),t}let v=({children:e,custom:t,initial:r=!0,onExitComplete:l,presenceAffectsLayout:u=!0,mode:c="sync",propagate:d=!1,anchorX:f="left"})=>{let[p,v]=(0,m.oO)(d),b=(0,i.useMemo)(()=>g(e),[e]),x=d&&!p?[]:b.map(y),w=(0,i.useRef)(!0),P=(0,i.useRef)(b),E=(0,a.h)(()=>new Map),[R,_]=(0,i.useState)(b),[S,T]=(0,i.useState)(b);(0,s.L)(()=>{w.current=!1,P.current=b;for(let e=0;e<S.length;e++){let t=y(S[e]);x.includes(t)?E.delete(t):!0!==E.get(t)&&E.set(t,!1)}},[S,x.length,x.join("-")]);let j=[];if(b!==R){let e=[...b];for(let t=0;t<S.length;t++){let r=S[t],n=y(r);x.includes(n)||(e.splice(t,0,r),j.push(r))}return"wait"===c&&j.length&&(e=j),T(g(e)),_(b),null}let{forceRender:O}=(0,i.useContext)(o.p);return(0,n.jsx)(n.Fragment,{children:S.map(e=>{let i=y(e),o=(!d||!!p)&&(b===S||x.includes(i));return(0,n.jsx)(h,{isPresent:o,initial:(!w.current||!!r)&&void 0,custom:t,presenceAffectsLayout:u,mode:c,onExitComplete:o?void 0:()=>{if(!E.has(i))return;E.set(i,!0);let e=!0;E.forEach(t=>{t||(e=!1)}),e&&(O?.(),T(P.current),d&&v?.(),l&&l())},anchorX:f,children:e},i)})})}},56933:(e,t,r)=>{"use strict";r.d(t,{oO:()=>o});var n=r(17577),i=r(40295);function o(e=!0){let t=(0,n.useContext)(i.O);if(null===t)return[!0,null];let{isPresent:r,onExitComplete:o,register:a}=t,s=(0,n.useId)();(0,n.useEffect)(()=>{if(e)return a(s)},[e]);let l=(0,n.useCallback)(()=>e&&o&&o(s),[s,o,e]);return!r&&o?[!1,l]:[!0]}},40339:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});let n=(0,r(17577).createContext)({})},73965:(e,t,r)=>{"use strict";r.d(t,{_:()=>n});let n=(0,r(17577).createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"})},40295:(e,t,r)=>{"use strict";r.d(t,{O:()=>n});let n=(0,r(17577).createContext)(null)},48430:(e,t,r)=>{"use strict";function n(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}function i(e){let t=[{},{}];return e?.values.forEach((e,r)=>{t[0][r]=e.get(),t[1][r]=e.getVelocity()}),t}function o(e,t,r,n){if("function"==typeof t){let[o,a]=i(n);t=t(void 0!==r?r:e.custom,o,a)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){let[o,a]=i(n);t=t(void 0!==r?r:e.custom,o,a)}return t}function a(e,t,r){let n=e.getProps();return o(n,t,void 0!==r?r:n.custom,e)}function s(e,t){return e?.[t]??e?.default??e}r.d(t,{E:()=>ia});var l,u,c=r(71470);let d=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],f=new Set(d),h=new Set(["width","height","top","left","right","bottom",...d]);var p=r(19073);let m=e=>Array.isArray(e);var y=r(93712),g=r(45290);function v(e,t){let r=e.getValue("willChange");if((0,g.i)(r)&&r.add)return r.add(t);if(!r&&y.c.WillChange){let r=new y.c.WillChange("auto");e.addValue("willChange",r),r.add(t)}}let b=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),x="data-"+b("framerAppearId");var w=r(45307),P=r(39911),E=r(84289),R=r(48077);let _=e=>180*e/Math.PI,S=e=>j(_(Math.atan2(e[1],e[0]))),T={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:e=>(Math.abs(e[0])+Math.abs(e[3]))/2,rotate:S,rotateZ:S,skewX:e=>_(Math.atan(e[1])),skewY:e=>_(Math.atan(e[2])),skew:e=>(Math.abs(e[1])+Math.abs(e[2]))/2},j=e=>((e%=360)<0&&(e+=360),e),O=e=>Math.sqrt(e[0]*e[0]+e[1]*e[1]),C=e=>Math.sqrt(e[4]*e[4]+e[5]*e[5]),M={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:O,scaleY:C,scale:e=>(O(e)+C(e))/2,rotateX:e=>j(_(Math.atan2(e[6],e[5]))),rotateY:e=>j(_(Math.atan2(-e[2],e[0]))),rotateZ:S,rotate:S,skewX:e=>_(Math.atan(e[4])),skewY:e=>_(Math.atan(e[1])),skew:e=>(Math.abs(e[1])+Math.abs(e[4]))/2};function A(e){return e.includes("scale")?1:0}function D(e,t){let r,n;if(!e||"none"===e)return A(t);let i=e.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(i)r=M,n=i;else{let t=e.match(/^matrix\(([-\d.e\s,]+)\)$/u);r=T,n=t}if(!n)return A(t);let o=r[t],a=n[1].split(",").map(N);return"function"==typeof o?o(a):a[o]}let k=(e,t)=>{let{transform:r="none"}=getComputedStyle(e);return D(r,t)};function N(e){return parseFloat(e.trim())}var L=r(74334),I=r(48663);let F=e=>e===L.Rx||e===I.px,U=new Set(["x","y","z"]),V=d.filter(e=>!U.has(e)),B={width:({x:e},{paddingLeft:t="0",paddingRight:r="0"})=>e.max-e.min-parseFloat(t)-parseFloat(r),height:({y:e},{paddingTop:t="0",paddingBottom:r="0"})=>e.max-e.min-parseFloat(t)-parseFloat(r),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:(e,{transform:t})=>D(t,"x"),y:(e,{transform:t})=>D(t,"y")};B.translateX=B.x,B.translateY=B.y;let W=new Set,H=!1,z=!1,$=!1;function X(){if(z){let e=Array.from(W).filter(e=>e.needsMeasurement),t=new Set(e.map(e=>e.element)),r=new Map;t.forEach(e=>{let t=function(e){let t=[];return V.forEach(r=>{let n=e.getValue(r);void 0!==n&&(t.push([r,n.get()]),n.set(r.startsWith("scale")?1:0))}),t}(e);t.length&&(r.set(e,t),e.render())}),e.forEach(e=>e.measureInitialState()),t.forEach(e=>{e.render();let t=r.get(e);t&&t.forEach(([t,r])=>{e.getValue(t)?.set(r)})}),e.forEach(e=>e.measureEndState()),e.forEach(e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)})}z=!1,H=!1,W.forEach(e=>e.complete($)),W.clear()}function q(){W.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(z=!0)})}class K{constructor(e,t,r,n,i,o=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=r,this.motionValue=n,this.element=i,this.isAsync=o}scheduleResolve(){this.state="scheduled",this.isAsync?(W.add(this),H||(H=!0,c.Wi.read(q),c.Wi.resolveKeyframes(X))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:e,name:t,element:r,motionValue:n}=this;if(null===e[0]){let i=n?.get(),o=e[e.length-1];if(void 0!==i)e[0]=i;else if(r&&t){let n=r.readValue(t,o);null!=n&&(e[0]=n)}void 0===e[0]&&(e[0]=o),n&&void 0===i&&n.set(e[0])}!function(e){for(let t=1;t<e.length;t++)e[t]??(e[t]=e[t-1])}(e)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(e=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),W.delete(this)}cancel(){"scheduled"===this.state&&(W.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}var G=r(51612),Y=r(17941);let Q=e=>e.startsWith("--");function Z(e){let t;return()=>(void 0===t&&(t=e()),t)}let J=Z(()=>void 0!==window.ScrollTimeline);var ee=r(42036),et=r(7626),er=r(87167),en=r(71683);let ei={},eo=function(e,t){let r=Z(e);return()=>ei[t]??r()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0},"linearEasing");var ea=r(56720);let es=([e,t,r,n])=>`cubic-bezier(${e}, ${t}, ${r}, ${n})`,el={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:es([0,.65,.55,1]),circOut:es([.55,0,1,.45]),backIn:es([.31,.01,.66,-.59]),backOut:es([.33,1.53,.69,.99])};function eu(e){return"function"==typeof e&&"applyToOptions"in e}class ec extends ee.T{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;let{element:t,name:r,keyframes:n,pseudoElement:i,allowFlatten:o=!1,finalKeyframe:a,onComplete:s}=e;this.isPseudoElement=!!i,this.allowFlatten=o,this.options=e,(0,Y.k)("string"!=typeof e.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:e,...t}){return eu(e)&&eo()?e.applyToOptions(t):(t.duration??(t.duration=300),t.ease??(t.ease="easeOut"),t)}(e);this.animation=function(e,t,r,{delay:n=0,duration:i=300,repeat:o=0,repeatType:a="loop",ease:s="easeOut",times:l}={},u){let c={[t]:r};l&&(c.offset=l);let d=function e(t,r){if(t)return"function"==typeof t?eo()?(0,ea.w)(t,r):"ease-out":(0,en.q)(t)?es(t):Array.isArray(t)?t.map(t=>e(t,r)||el.easeOut):el[t]}(s,i);Array.isArray(d)&&(c.easing=d),er.f.value&&et.P.waapi++;let f={delay:n,duration:i,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:o+1,direction:"reverse"===a?"alternate":"normal"};u&&(f.pseudoElement=u);let h=e.animate(c,f);return er.f.value&&h.finished.finally(()=>{et.P.waapi--}),h}(t,r,n,l,i),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!i){let e=(0,R.$)(n,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(e):function(e,t,r){Q(t)?e.style.setProperty(t,r):e.style[t]=r}(t,r,e),this.animation.cancel()}s?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(e){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:e}=this;"idle"!==e&&"finished"!==e&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){let e=this.animation.effect?.getComputedTiming?.().duration||0;return(0,G.X)(Number(e))}get time(){return(0,G.X)(Number(this.animation.currentTime)||0)}set time(e){this.finishedTime=null,this.animation.currentTime=(0,G.w)(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline({timeline:e,observe:t}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,e&&J())?(this.animation.timeline=e,P.Z):t(this)}}var ed=r(10108),ef=r(48219),eh=r(27597),ep=r(34946);let em={anticipate:ef.L,backInOut:eh.XL,circInOut:ep.X7};class ey extends ec{constructor(e){(function(e){"string"==typeof e.ease&&e.ease in em&&(e.ease=em[e.ease])})(e),(0,ed.f)(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){let{motionValue:t,onUpdate:r,onComplete:n,element:i,...o}=this.options;if(!t)return;if(void 0!==e){t.set(e);return}let a=new w.L({...o,autoplay:!1}),s=(0,G.w)(this.finishedTime??this.time);t.setWithVelocity(a.sample(s-10).value,a.sample(s).value,10),a.stop()}}var eg=r(4789);let ev=(e,t)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(eg.P.test(e)||"0"===e)&&!e.startsWith("url("));var eb=r(69539);let ex=new Set(["opacity","clipPath","filter","transform"]),ew=Z(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class eP extends ee.T{constructor({autoplay:e=!0,delay:t=0,type:r="keyframes",repeat:n=0,repeatDelay:i=0,repeatType:o="loop",keyframes:a,name:s,motionValue:l,element:u,...c}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=E.X.now();let d={autoplay:e,delay:t,type:r,repeat:n,repeatDelay:i,repeatType:o,name:s,motionValue:l,element:u,...c},f=u?.KeyframeResolver||K;this.keyframeResolver=new f(a,(e,t,r)=>this.onKeyframesResolved(e,t,d,!r),s,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(e,t,r,n){this.keyframeResolver=void 0;let{name:i,type:o,velocity:a,delay:s,isHandoff:l,onUpdate:u}=r;this.resolvedAt=E.X.now(),!function(e,t,r,n){let i=e[0];if(null===i)return!1;if("display"===t||"visibility"===t)return!0;let o=e[e.length-1],a=ev(i,t),s=ev(o,t);return(0,Y.K)(a===s,`You are trying to animate ${t} from "${i}" to "${o}". ${i} is not an animatable value - to enable this animation set ${i} to a value animatable to ${o} via the \`style\` property.`),!!a&&!!s&&(function(e){let t=e[0];if(1===e.length)return!0;for(let r=0;r<e.length;r++)if(e[r]!==t)return!0}(e)||("spring"===r||eu(r))&&n)}(e,i,o,a)&&((y.c.instantAnimations||!s)&&u?.(R.$(e,r,t)),e[0]=e[e.length-1],r.duration=0,r.repeat=0);let c={startTime:n?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:t,...r,keyframes:e},d=!l&&function(e){let{motionValue:t,name:r,repeatDelay:n,repeatType:i,damping:o,type:a}=e;if(!(0,eb.R)(t?.owner?.current))return!1;let{onUpdate:s,transformTemplate:l}=t.owner.getProps();return ew()&&r&&ex.has(r)&&("transform"!==r||!l)&&!s&&!n&&"mirror"!==i&&0!==o&&"inertia"!==a}(c)?new ey({...c,element:c.motionValue.owner.current}):new w.L(c);d.finished.then(()=>this.notifyFinished()).catch(P.Z),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}get finished(){return this._animation?this.animation.finished:this._finished}then(e,t){return this.finished.finally(e).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),$=!0,q(),X(),$=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let eE=e=>null!==e,eR={type:"spring",stiffness:500,damping:25,restSpeed:10},e_=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),eS={type:"keyframes",duration:.8},eT={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},ej=(e,{keyframes:t})=>t.length>2?eS:f.has(e)?e.startsWith("scale")?e_(t[1]):eR:eT,eO=(e,t,r,n={},i,o)=>a=>{let l=s(n,e)||{},u=l.delay||n.delay||0,{elapsed:d=0}=n;d-=(0,G.w)(u);let f={keyframes:Array.isArray(r)?r:[null,r],ease:"easeOut",velocity:t.getVelocity(),...l,delay:-d,onUpdate:e=>{t.set(e),l.onUpdate&&l.onUpdate(e)},onComplete:()=>{a(),l.onComplete&&l.onComplete()},name:e,motionValue:t,element:o?void 0:i};!function({when:e,delay:t,delayChildren:r,staggerChildren:n,staggerDirection:i,repeat:o,repeatType:a,repeatDelay:s,from:l,elapsed:u,...c}){return!!Object.keys(c).length}(l)&&Object.assign(f,ej(e,f)),f.duration&&(f.duration=(0,G.w)(f.duration)),f.repeatDelay&&(f.repeatDelay=(0,G.w)(f.repeatDelay)),void 0!==f.from&&(f.keyframes[0]=f.from);let h=!1;if(!1!==f.type&&(0!==f.duration||f.repeatDelay)||(f.duration=0,0!==f.delay||(h=!0)),(y.c.instantAnimations||y.c.skipAnimations)&&(h=!0,f.duration=0,f.delay=0),f.allowFlatten=!l.type&&!l.ease,h&&!o&&void 0!==t.get()){let e=function(e,{repeat:t,repeatType:r="loop"},n){let i=e.filter(eE),o=t&&"loop"!==r&&t%2==1?0:i.length-1;return i[o]}(f.keyframes,l);if(void 0!==e){c.Wi.update(()=>{f.onUpdate(e),f.onComplete()});return}}return l.isSync?new w.L(f):new eP(f)};function eC(e,t,{delay:r=0,transitionOverride:n,type:i}={}){let{transition:o=e.getDefaultTransition(),transitionEnd:l,...u}=t;n&&(o=n);let d=[],f=i&&e.animationState&&e.animationState.getState()[i];for(let t in u){let n=e.getValue(t,e.latestValues[t]??null),i=u[t];if(void 0===i||f&&function({protectedKeys:e,needsAnimating:t},r){let n=e.hasOwnProperty(r)&&!0!==t[r];return t[r]=!1,n}(f,t))continue;let a={delay:r,...s(o||{},t)},l=n.get();if(void 0!==l&&!n.isAnimating&&!Array.isArray(i)&&i===l&&!a.velocity)continue;let p=!1;if(window.MotionHandoffAnimation){let r=e.props[x];if(r){let e=window.MotionHandoffAnimation(r,t,c.Wi);null!==e&&(a.startTime=e,p=!0)}}v(e,t),n.start(eO(t,n,i,e.shouldReduceMotion&&h.has(t)?{type:!1}:a,e,p));let m=n.animation;m&&d.push(m)}return l&&Promise.all(d).then(()=>{c.Wi.update(()=>{l&&function(e,t){let{transitionEnd:r={},transition:n={},...i}=a(e,t)||{};for(let t in i={...i,...r}){var o;let r=m(o=i[t])?o[o.length-1]||0:o;e.hasValue(t)?e.getValue(t).set(r):e.addValue(t,(0,p.BX)(r))}}(e,l)})}),d}function eM(e,t,r={}){let n=a(e,t,"exit"===r.type?e.presenceContext?.custom:void 0),{transition:i=e.getDefaultTransition()||{}}=n||{};r.transitionOverride&&(i=r.transitionOverride);let o=n?()=>Promise.all(eC(e,n,r)):()=>Promise.resolve(),s=e.variantChildren&&e.variantChildren.size?(n=0)=>{let{delayChildren:o=0,staggerChildren:a,staggerDirection:s}=i;return function(e,t,r=0,n=0,i=1,o){let a=[],s=(e.variantChildren.size-1)*n,l=1===i?(e=0)=>e*n:(e=0)=>s-e*n;return Array.from(e.variantChildren).sort(eA).forEach((e,n)=>{e.notify("AnimationStart",t),a.push(eM(e,t,{...o,delay:r+l(n)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(a)}(e,t,o+n,a,s,r)}:()=>Promise.resolve(),{when:l}=i;if(!l)return Promise.all([o(),s(r.delay)]);{let[e,t]="beforeChildren"===l?[o,s]:[s,o];return e().then(()=>t())}}function eA(e,t){return e.sortNodePosition(t)}function eD(e,t){if(!Array.isArray(t))return!1;let r=t.length;if(r!==e.length)return!1;for(let n=0;n<r;n++)if(t[n]!==e[n])return!1;return!0}function ek(e){return"string"==typeof e||Array.isArray(e)}let eN=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],eL=["initial",...eN],eI=eL.length,eF=[...eN].reverse(),eU=eN.length;function eV(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function eB(){return{animate:eV(!0),whileInView:eV(),whileHover:eV(),whileTap:eV(),whileDrag:eV(),whileFocus:eV(),exit:eV()}}class eW{constructor(e){this.isMounted=!1,this.node=e}update(){}}class eH extends eW{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:r})=>(function(e,t,r={}){let n;if(e.notify("AnimationStart",t),Array.isArray(t))n=Promise.all(t.map(t=>eM(e,t,r)));else if("string"==typeof t)n=eM(e,t,r);else{let i="function"==typeof t?a(e,t,r.custom):t;n=Promise.all(eC(e,i,r))}return n.then(()=>{e.notify("AnimationComplete",t)})})(e,t,r))),r=eB(),i=!0,o=t=>(r,n)=>{let i=a(e,n,"exit"===t?e.presenceContext?.custom:void 0);if(i){let{transition:e,transitionEnd:t,...n}=i;r={...r,...n,...t}}return r};function s(s){let{props:l}=e,u=function e(t){if(!t)return;if(!t.isControllingVariants){let r=t.parent&&e(t.parent)||{};return void 0!==t.props.initial&&(r.initial=t.props.initial),r}let r={};for(let e=0;e<eI;e++){let n=eL[e],i=t.props[n];(ek(i)||!1===i)&&(r[n]=i)}return r}(e.parent)||{},c=[],d=new Set,f={},h=1/0;for(let t=0;t<eU;t++){var p;let a=eF[t],y=r[a],g=void 0!==l[a]?l[a]:u[a],v=ek(g),b=a===s?y.isActive:null;!1===b&&(h=t);let x=g===u[a]&&g!==l[a]&&v;if(x&&i&&e.manuallyAnimateOnMount&&(x=!1),y.protectedKeys={...f},!y.isActive&&null===b||!g&&!y.prevProp||n(g)||"boolean"==typeof g)continue;let w=(p=y.prevProp,"string"==typeof g?g!==p:!!Array.isArray(g)&&!eD(g,p)),P=w||a===s&&y.isActive&&!x&&v||t>h&&v,E=!1,R=Array.isArray(g)?g:[g],_=R.reduce(o(a),{});!1===b&&(_={});let{prevResolvedValues:S={}}=y,T={...S,..._},j=t=>{P=!0,d.has(t)&&(E=!0,d.delete(t)),y.needsAnimating[t]=!0;let r=e.getValue(t);r&&(r.liveStyle=!1)};for(let e in T){let t=_[e],r=S[e];if(!f.hasOwnProperty(e))(m(t)&&m(r)?eD(t,r):t===r)?void 0!==t&&d.has(e)?j(e):y.protectedKeys[e]=!0:null!=t?j(e):d.add(e)}y.prevProp=g,y.prevResolvedValues=_,y.isActive&&(f={...f,..._}),i&&e.blockInitialAnimation&&(P=!1);let O=!(x&&w)||E;P&&O&&c.push(...R.map(e=>({animation:e,options:{type:a}})))}if(d.size){let t={};if("boolean"!=typeof l.initial){let r=a(e,Array.isArray(l.initial)?l.initial[0]:l.initial);r&&r.transition&&(t.transition=r.transition)}d.forEach(r=>{let n=e.getBaseTarget(r),i=e.getValue(r);i&&(i.liveStyle=!0),t[r]=n??null}),c.push({animation:t})}let y=!!c.length;return i&&(!1===l.initial||l.initial===l.animate)&&!e.manuallyAnimateOnMount&&(y=!1),i=!1,y?t(c):Promise.resolve()}return{animateChanges:s,setActive:function(t,n){if(r[t].isActive===n)return Promise.resolve();e.variantChildren?.forEach(e=>e.animationState?.setActive(t,n)),r[t].isActive=n;let i=s(t);for(let e in r)r[e].protectedKeys={};return i},setAnimateFunction:function(r){t=r(e)},getState:()=>r,reset:()=>{r=eB(),i=!0}}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();n(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let ez=0;class e$ extends eW{constructor(){super(...arguments),this.id=ez++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===r)return;let n=this.node.animationState.setActive("exit",!e);t&&!e&&n.then(()=>{t(this.id)})}mount(){let{register:e,onExitComplete:t}=this.node.presenceContext||{};t&&t(this.id),e&&(this.unmount=e(this.id))}unmount(){}}let eX={x:!1,y:!1};var eq=r(92844);function eK(e,t,r,n={passive:!0}){return e.addEventListener(t,r,n),()=>e.removeEventListener(t,r)}let eG=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary;function eY(e){return{point:{x:e.pageX,y:e.pageY}}}let eQ=e=>t=>eG(t)&&e(t,eY(t));function eZ(e,t,r,n){return eK(e,t,eQ(r),n)}function eJ({top:e,left:t,right:r,bottom:n}){return{x:{min:t,max:r},y:{min:e,max:n}}}function e0(e){return e.max-e.min}function e1(e,t,r,n=.5){e.origin=n,e.originPoint=(0,eq.t)(t.min,t.max,e.origin),e.scale=e0(r)/e0(t),e.translate=(0,eq.t)(r.min,r.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function e2(e,t,r,n){e1(e.x,t.x,r.x,n?n.originX:void 0),e1(e.y,t.y,r.y,n?n.originY:void 0)}function e5(e,t,r){e.min=r.min+t.min,e.max=e.min+e0(t)}function e7(e,t,r){e.min=t.min-r.min,e.max=e.min+e0(t)}function e3(e,t,r){e7(e.x,t.x,r.x),e7(e.y,t.y,r.y)}let e9=()=>({translate:0,scale:1,origin:0,originPoint:0}),e4=()=>({x:e9(),y:e9()}),e6=()=>({min:0,max:0}),e8=()=>({x:e6(),y:e6()});function te(e){return[e("x"),e("y")]}function tt(e){return void 0===e||1===e}function tr({scale:e,scaleX:t,scaleY:r}){return!tt(e)||!tt(t)||!tt(r)}function tn(e){return tr(e)||ti(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function ti(e){var t,r;return(t=e.x)&&"0%"!==t||(r=e.y)&&"0%"!==r}function to(e,t,r,n,i){return void 0!==i&&(e=n+i*(e-n)),n+r*(e-n)+t}function ta(e,t=0,r=1,n,i){e.min=to(e.min,t,r,n,i),e.max=to(e.max,t,r,n,i)}function ts(e,{x:t,y:r}){ta(e.x,t.translate,t.scale,t.originPoint),ta(e.y,r.translate,r.scale,r.originPoint)}function tl(e,t){e.min=e.min+t,e.max=e.max+t}function tu(e,t,r,n,i=.5){let o=(0,eq.t)(e.min,e.max,i);ta(e,t,r,o,n)}function tc(e,t){tu(e.x,t.x,t.scaleX,t.scale,t.originX),tu(e.y,t.y,t.scaleY,t.scale,t.originY)}function td(e,t){return eJ(function(e,t){if(!t)return e;let r=t({x:e.left,y:e.top}),n=t({x:e.right,y:e.bottom});return{top:r.y,left:r.x,bottom:n.y,right:n.x}}(e.getBoundingClientRect(),t))}let tf=({current:e})=>e?e.ownerDocument.defaultView:null;function th(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}var tp=r(85294);let tm=(e,t)=>Math.abs(e-t);class ty{constructor(e,t,{transformPagePoint:r,contextWindow:n,dragSnapToOrigin:i=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=tb(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,r=function(e,t){return Math.sqrt(tm(e.x,t.x)**2+tm(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!r)return;let{point:n}=e,{timestamp:i}=c.frameData;this.history.push({...n,timestamp:i});let{onStart:o,onMove:a}=this.handlers;t||(o&&o(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),a&&a(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=tg(t,this.transformPagePoint),c.Wi.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:r,onSessionEnd:n,resumeAnimation:i}=this.handlers;if(this.dragSnapToOrigin&&i&&i(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let o=tb("pointercancel"===e.type?this.lastMoveEventInfo:tg(t,this.transformPagePoint),this.history);this.startEvent&&r&&r(e,o),n&&n(e,o)},!eG(e))return;this.dragSnapToOrigin=i,this.handlers=t,this.transformPagePoint=r,this.contextWindow=n||window;let o=tg(eY(e),this.transformPagePoint),{point:a}=o,{timestamp:s}=c.frameData;this.history=[{...a,timestamp:s}];let{onSessionStart:l}=t;l&&l(e,tb(o,this.history)),this.removeListeners=(0,tp.z)(eZ(this.contextWindow,"pointermove",this.handlePointerMove),eZ(this.contextWindow,"pointerup",this.handlePointerUp),eZ(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),(0,c.Pn)(this.updatePoint)}}function tg(e,t){return t?{point:t(e.point)}:e}function tv(e,t){return{x:e.x-t.x,y:e.y-t.y}}function tb({point:e},t){return{point:e,delta:tv(e,tx(t)),offset:tv(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let r=e.length-1,n=null,i=tx(e);for(;r>=0&&(n=e[r],!(i.timestamp-n.timestamp>(0,G.w)(.1)));)r--;if(!n)return{x:0,y:0};let o=(0,G.X)(i.timestamp-n.timestamp);if(0===o)return{x:0,y:0};let a={x:(i.x-n.x)/o,y:(i.y-n.y)/o};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(t,0)}}function tx(e){return e[e.length-1]}var tw=r(19069),tP=r(68132);function tE(e,t,r){return{min:void 0!==t?e.min+t:void 0,max:void 0!==r?e.max+r-(e.max-e.min):void 0}}function tR(e,t){let r=t.min-e.min,n=t.max-e.max;return t.max-t.min<e.max-e.min&&([r,n]=[n,r]),{min:r,max:n}}function t_(e,t,r){return{min:tS(e,t),max:tS(e,r)}}function tS(e,t){return"number"==typeof e?e:e[t]||0}let tT=new WeakMap;class tj{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=e8(),this.visualElement=e}start(e,{snapToCursor:t=!1}={}){let{presenceContext:r}=this.visualElement;if(r&&!1===r.isPresent)return;let{dragSnapToOrigin:n}=this.getProps();this.panSession=new ty(e,{onSessionStart:e=>{let{dragSnapToOrigin:r}=this.getProps();r?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(eY(e).point)},onStart:(e,t)=>{let{drag:r,dragPropagation:n,onDragStart:i}=this.getProps();if(r&&!n&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===r||"y"===r?eX[r]?null:(eX[r]=!0,()=>{eX[r]=!1}):eX.x||eX.y?null:(eX.x=eX.y=!0,()=>{eX.x=eX.y=!1}),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),te(e=>{let t=this.getAxisMotionValue(e).get()||0;if(I.aQ.test(t)){let{projection:r}=this.visualElement;if(r&&r.layout){let n=r.layout.layoutBox[e];if(n){let e=e0(n);t=parseFloat(t)/100*e}}}this.originPoint[e]=t}),i&&c.Wi.postRender(()=>i(e,t)),v(this.visualElement,"transform");let{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},onMove:(e,t)=>{let{dragPropagation:r,dragDirectionLock:n,onDirectionLock:i,onDrag:o}=this.getProps();if(!r&&!this.openDragLock)return;let{offset:a}=t;if(n&&null===this.currentDirection){this.currentDirection=function(e,t=10){let r=null;return Math.abs(e.y)>t?r="y":Math.abs(e.x)>t&&(r="x"),r}(a),null!==this.currentDirection&&i&&i(this.currentDirection);return}this.updateAxis("x",t.point,a),this.updateAxis("y",t.point,a),this.visualElement.render(),o&&o(e,t)},onSessionEnd:(e,t)=>this.stop(e,t),resumeAnimation:()=>te(e=>"paused"===this.getAnimationState(e)&&this.getAxisMotionValue(e).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:n,contextWindow:tf(this.visualElement)})}stop(e,t){let r=this.isDragging;if(this.cancel(),!r)return;let{velocity:n}=t;this.startAnimation(n);let{onDragEnd:i}=this.getProps();i&&c.Wi.postRender(()=>i(e,t))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:r}=this.getProps();!r&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,r){let{drag:n}=this.getProps();if(!r||!tO(e,n,this.currentDirection))return;let i=this.getAxisMotionValue(e),o=this.originPoint[e]+r[e];this.constraints&&this.constraints[e]&&(o=function(e,{min:t,max:r},n){return void 0!==t&&e<t?e=n?(0,eq.t)(t,e,n.min):Math.max(e,t):void 0!==r&&e>r&&(e=n?(0,eq.t)(r,e,n.max):Math.min(e,r)),e}(o,this.constraints[e],this.elastic[e])),i.set(o)}resolveConstraints(){let{dragConstraints:e,dragElastic:t}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,n=this.constraints;e&&th(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&r?this.constraints=function(e,{top:t,left:r,bottom:n,right:i}){return{x:tE(e.x,r,i),y:tE(e.y,t,n)}}(r.layoutBox,e):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:t_(e,"left","right"),y:t_(e,"top","bottom")}}(t),n!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&te(e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let r={};return void 0!==t.min&&(r.min=t.min-e.min),void 0!==t.max&&(r.max=t.max-e.min),r}(r.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:r}=this.getProps();if(!t||!th(t))return!1;let n=t.current;(0,Y.k)(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:i}=this.visualElement;if(!i||!i.layout)return!1;let o=function(e,t,r){let n=td(e,r),{scroll:i}=t;return i&&(tl(n.x,i.offset.x),tl(n.y,i.offset.y)),n}(n,i.root,this.visualElement.getTransformPagePoint()),a={x:tR((e=i.layout.layoutBox).x,o.x),y:tR(e.y,o.y)};if(r){let e=r(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(a));this.hasMutatedConstraints=!!e,e&&(a=eJ(e))}return a}startAnimation(e){let{drag:t,dragMomentum:r,dragElastic:n,dragTransition:i,dragSnapToOrigin:o,onDragTransitionEnd:a}=this.getProps(),s=this.constraints||{};return Promise.all(te(a=>{if(!tO(a,t,this.currentDirection))return;let l=s&&s[a]||{};o&&(l={min:0,max:0});let u={type:"inertia",velocity:r?e[a]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...i,...l};return this.startAxisValueAnimation(a,u)})).then(a)}startAxisValueAnimation(e,t){let r=this.getAxisMotionValue(e);return v(this.visualElement,e),r.start(eO(e,r,0,t,this.visualElement,!1))}stopAnimation(){te(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){te(e=>this.getAxisMotionValue(e).animation?.pause())}getAnimationState(e){return this.getAxisMotionValue(e).animation?.state}getAxisMotionValue(e){let t=`_drag${e.toUpperCase()}`,r=this.visualElement.getProps();return r[t]||this.visualElement.getValue(e,(r.initial?r.initial[e]:void 0)||0)}snapToCursor(e){te(t=>{let{drag:r}=this.getProps();if(!tO(t,r,this.currentDirection))return;let{projection:n}=this.visualElement,i=this.getAxisMotionValue(t);if(n&&n.layout){let{min:r,max:o}=n.layout.layoutBox[t];i.set(e[t]-(0,eq.t)(r,o,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:r}=this.visualElement;if(!th(t)||!r||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};te(e=>{let t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){let r=t.get();n[e]=function(e,t){let r=.5,n=e0(e),i=e0(t);return i>n?r=(0,tw.Y)(t.min,t.max-n,e.min):n>i&&(r=(0,tw.Y)(e.min,e.max-i,t.min)),(0,tP.u)(0,1,r)}({min:r,max:r},this.constraints[e])}});let{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),te(t=>{if(!tO(t,e,null))return;let r=this.getAxisMotionValue(t),{min:i,max:o}=this.constraints[t];r.set((0,eq.t)(i,o,n[t]))})}addListeners(){if(!this.visualElement.current)return;tT.set(this.visualElement,this);let e=eZ(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:r=!0}=this.getProps();t&&r&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();th(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:r}=this.visualElement,n=r.addEventListener("measure",t);r&&!r.layout&&(r.root&&r.root.updateScroll(),r.updateLayout()),c.Wi.read(t);let i=eK(window,"resize",()=>this.scalePositionWithinConstraints()),o=r.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(te(t=>{let r=this.getAxisMotionValue(t);r&&(this.originPoint[t]+=e[t].translate,r.set(r.get()+e[t].translate))}),this.visualElement.render())});return()=>{i(),e(),n(),o&&o()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:r=!1,dragPropagation:n=!1,dragConstraints:i=!1,dragElastic:o=.35,dragMomentum:a=!0}=e;return{...e,drag:t,dragDirectionLock:r,dragPropagation:n,dragConstraints:i,dragElastic:o,dragMomentum:a}}}function tO(e,t,r){return(!0===t||t===e)&&(null===r||r===e)}class tC extends eW{constructor(e){super(e),this.removeGroupControls=P.Z,this.removeListeners=P.Z,this.controls=new tj(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||P.Z}unmount(){this.removeGroupControls(),this.removeListeners()}}let tM=e=>(t,r)=>{e&&c.Wi.postRender(()=>e(t,r))};class tA extends eW{constructor(){super(...arguments),this.removePointerDownListener=P.Z}onPointerDown(e){this.session=new ty(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:tf(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:r,onPanEnd:n}=this.node.getProps();return{onSessionStart:tM(e),onStart:tM(t),onMove:r,onEnd:(e,t)=>{delete this.session,n&&c.Wi.postRender(()=>n(e,t))}}}mount(){this.removePointerDownListener=eZ(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var tD=r(10326);let{schedule:tk,cancel:tN}=(0,r(81282).Z)(queueMicrotask,!1);var tL=r(17577),tI=r(56933),tF=r(40339);let tU=(0,tL.createContext)({}),tV={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function tB(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let tW={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e){if(!I.px.test(e))return e;e=parseFloat(e)}let r=tB(e,t.target.x),n=tB(e,t.target.y);return`${r}% ${n}%`}};var tH=r(97125);let tz={};class t$ extends tL.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:r,layoutId:n}=this.props,{projection:i}=e;(function(e){for(let t in e)tz[t]=e[t],(0,tH.f)(t)&&(tz[t].isCSSVariable=!0)})(tq),i&&(t.group&&t.group.add(i),r&&r.register&&n&&r.register(i),i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),tV.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:r,drag:n,isPresent:i}=this.props,{projection:o}=r;return o&&(o.isPresent=i,n||e.layoutDependency!==t||void 0===t||e.isPresent!==i?o.willUpdate():this.safeToRemove(),e.isPresent===i||(i?o.promote():o.relegate()||c.Wi.postRender(()=>{let e=o.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),tk.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:r}=this.props,{projection:n}=e;n&&(n.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(n),r&&r.deregister&&r.deregister(n))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function tX(e){let[t,r]=(0,tI.oO)(),n=(0,tL.useContext)(tF.p);return(0,tD.jsx)(t$,{...e,layoutGroup:n,switchLayoutGroup:(0,tL.useContext)(tU),isPresent:t,safeToRemove:r})}let tq={borderRadius:{...tW,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:tW,borderTopRightRadius:tW,borderBottomLeftRadius:tW,borderBottomRightRadius:tW,boxShadow:{correct:(e,{treeScale:t,projectionDelta:r})=>{let n=eg.P.parse(e);if(n.length>5)return e;let i=eg.P.createTransformer(e),o="number"!=typeof n[0]?1:0,a=r.x.scale*t.x,s=r.y.scale*t.y;n[0+o]/=a,n[1+o]/=s;let l=(0,eq.t)(a,s,.5);return"number"==typeof n[2+o]&&(n[2+o]/=l),"number"==typeof n[3+o]&&(n[3+o]/=l),i(n)}}};var tK=r(17640);function tG(e){return(0,tK.K)(e)&&"ownerSVGElement"in e}var tY=r(19691),tQ=r(65518);let tZ=(e,t)=>e.depth-t.depth;class tJ{constructor(){this.children=[],this.isDirty=!1}add(e){(0,tQ.y4)(this.children,e),this.isDirty=!0}remove(e){(0,tQ.cl)(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(tZ),this.isDirty=!1,this.children.forEach(e)}}function t0(e){return(0,g.i)(e)?e.get():e}let t1=["TopLeft","TopRight","BottomLeft","BottomRight"],t2=t1.length,t5=e=>"string"==typeof e?parseFloat(e):e,t7=e=>"number"==typeof e||I.px.test(e);function t3(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let t9=t6(0,.5,ep.Bn),t4=t6(.5,.95,P.Z);function t6(e,t,r){return n=>n<e?0:n>t?1:r((0,tw.Y)(e,t,n))}function t8(e,t){e.min=t.min,e.max=t.max}function re(e,t){t8(e.x,t.x),t8(e.y,t.y)}function rt(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function rr(e,t,r,n,i){return e-=t,e=n+1/r*(e-n),void 0!==i&&(e=n+1/i*(e-n)),e}function rn(e,t,[r,n,i],o,a){!function(e,t=0,r=1,n=.5,i,o=e,a=e){if(I.aQ.test(t)&&(t=parseFloat(t),t=(0,eq.t)(a.min,a.max,t/100)-a.min),"number"!=typeof t)return;let s=(0,eq.t)(o.min,o.max,n);e===o&&(s-=t),e.min=rr(e.min,t,r,s,i),e.max=rr(e.max,t,r,s,i)}(e,t[r],t[n],t[i],t.scale,o,a)}let ri=["x","scaleX","originX"],ro=["y","scaleY","originY"];function ra(e,t,r,n){rn(e.x,t,ri,r?r.x:void 0,n?n.x:void 0),rn(e.y,t,ro,r?r.y:void 0,n?n.y:void 0)}function rs(e){return 0===e.translate&&1===e.scale}function rl(e){return rs(e.x)&&rs(e.y)}function ru(e,t){return e.min===t.min&&e.max===t.max}function rc(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function rd(e,t){return rc(e.x,t.x)&&rc(e.y,t.y)}function rf(e){return e0(e.x)/e0(e.y)}function rh(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class rp{constructor(){this.members=[]}add(e){(0,tQ.y4)(this.members,e),e.scheduleRender()}remove(e){if((0,tQ.cl)(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t;let r=this.members.findIndex(t=>e===t);if(0===r)return!1;for(let e=r;e>=0;e--){let r=this.members[e];if(!1!==r.isPresent){t=r;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let r=this.lead;if(e!==r&&(this.prevLead=r,this.lead=e,e.show(),r)){r.instance&&r.scheduleRender(),e.scheduleRender(),e.resumeFrom=r,t&&(e.resumeFrom.preserveOpacity=!0),r.snapshot&&(e.snapshot=r.snapshot,e.snapshot.latestValues=r.animationValues||r.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:n}=e.options;!1===n&&r.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:r}=e;t.onExitComplete&&t.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let rm={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},ry=["","X","Y","Z"],rg={visibility:"hidden"},rv=0;function rb(e,t,r,n){let{latestValues:i}=t;i[e]&&(r[e]=i[e],t.setStaticValue(e,0),n&&(n[e]=0))}function rx({attachResizeListener:e,defaultParent:t,measureScroll:r,checkIsScrollRoot:n,resetTransform:i}){return class{constructor(e={},r=t?.()){this.id=rv++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,er.f.value&&(rm.nodes=rm.calculatedTargetDeltas=rm.calculatedProjections=0),this.nodes.forEach(rE),this.nodes.forEach(rC),this.nodes.forEach(rM),this.nodes.forEach(rR),er.f.addProjectionMetrics&&er.f.addProjectionMetrics(rm)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=r?r.root||r:this,this.path=r?[...r.path,r]:[],this.parent=r,this.depth=r?r.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new tJ)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new tY.L),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let r=this.eventHandlers.get(e);r&&r.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t){if(this.instance)return;this.isSVG=tG(t)&&!(tG(t)&&"svg"===t.tagName),this.instance=t;let{layoutId:r,layout:n,visualElement:i}=this.options;if(i&&!i.current&&i.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(n||r)&&(this.isLayoutDirty=!0),e){let r;let n=()=>this.root.updateBlockedByResize=!1;e(t,()=>{this.root.updateBlockedByResize=!0,r&&r(),r=function(e,t){let r=E.X.now(),n=({timestamp:t})=>{let i=t-r;i>=250&&((0,c.Pn)(n),e(i-250))};return c.Wi.setup(n,!0),()=>(0,c.Pn)(n)}(n,250),tV.hasAnimatedSinceResize&&(tV.hasAnimatedSinceResize=!1,this.nodes.forEach(rO))})}r&&this.root.registerSharedNode(r,this),!1!==this.options.animate&&i&&(r||n)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeLayoutChanged:r,layout:n})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let o=this.options.transition||i.getDefaultTransition()||rI,{onLayoutAnimationStart:a,onLayoutAnimationComplete:l}=i.getProps(),u=!this.targetLayout||!rd(this.targetLayout,n),c=!t&&r;if(this.options.layoutRoot||this.resumeFrom||c||t&&(u||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(e,c);let t={...s(o,"layout"),onPlay:a,onComplete:l};(i.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t)}else t||rO(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),(0,c.Pn)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(rA),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function e(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;let{visualElement:r}=t.options;if(!r)return;let n=r.props[x];if(window.MotionHasOptimisedAnimation(n,"transform")){let{layout:e,layoutId:r}=t.options;window.MotionCancelOptimisedAnimation(n,"transform",c.Wi,!(e||r))}let{parent:i}=t;i&&!i.hasCheckedOptimisedAppear&&e(i)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:r}=this.options;if(void 0===t&&!r)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(rS);return}this.isUpdating||this.nodes.forEach(rT),this.isUpdating=!1,this.nodes.forEach(rj),this.nodes.forEach(rw),this.nodes.forEach(rP),this.clearAllSnapshots();let e=E.X.now();c.frameData.delta=(0,tP.u)(0,1e3/60,e-c.frameData.timestamp),c.frameData.timestamp=e,c.frameData.isProcessing=!0,c.yL.update.process(c.frameData),c.yL.preRender.process(c.frameData),c.yL.render.process(c.frameData),c.frameData.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,tk.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(r_),this.sharedNodes.forEach(rD)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,c.Wi.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){c.Wi.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),!this.snapshot||e0(this.snapshot.measuredBox.x)||e0(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=e8(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t&&this.instance){let t=n(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:r(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!i)return;let e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!rl(this.projectionDelta),r=this.getTransformTemplate(),n=r?r(this.latestValues,""):void 0,o=n!==this.prevTransformTemplateValue;e&&this.instance&&(t||tn(this.latestValues)||o)&&(i(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let r=this.measurePageBox(),n=this.removeElementScroll(r);return e&&(n=this.removeTransform(n)),rV((t=n).x),rV(t.y),{animationId:this.root.animationId,measuredBox:r,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:e}=this.options;if(!e)return e8();let t=e.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(rW))){let{scroll:e}=this.root;e&&(tl(t.x,e.offset.x),tl(t.y,e.offset.y))}return t}removeElementScroll(e){let t=e8();if(re(t,e),this.scroll?.wasRoot)return t;for(let r=0;r<this.path.length;r++){let n=this.path[r],{scroll:i,options:o}=n;n!==this.root&&i&&o.layoutScroll&&(i.wasRoot&&re(t,e),tl(t.x,i.offset.x),tl(t.y,i.offset.y))}return t}applyTransform(e,t=!1){let r=e8();re(r,e);for(let e=0;e<this.path.length;e++){let n=this.path[e];!t&&n.options.layoutScroll&&n.scroll&&n!==n.root&&tc(r,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),tn(n.latestValues)&&tc(r,n.latestValues)}return tn(this.latestValues)&&tc(r,this.latestValues),r}removeTransform(e){let t=e8();re(t,e);for(let e=0;e<this.path.length;e++){let r=this.path[e];if(!r.instance||!tn(r.latestValues))continue;tr(r.latestValues)&&r.updateSnapshot();let n=e8();re(n,r.measurePageBox()),ra(t,r.latestValues,r.snapshot?r.snapshot.layoutBox:void 0,n)}return tn(this.latestValues)&&ra(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==c.frameData.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){let t=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=t.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=t.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=t.isSharedProjectionDirty);let r=!!this.resumingFrom||this!==t;if(!(e||r&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:n,layoutId:i}=this.options;if(this.layout&&(n||i)){if(this.resolvedRelativeTargetAt=c.frameData.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=e8(),this.relativeTargetOrigin=e8(),e3(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),re(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=e8(),this.targetWithTransforms=e8()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var o,a,s;this.forceRelativeParentToResolveTarget(),o=this.target,a=this.relativeTarget,s=this.relativeParent.target,e5(o.x,a.x,s.x),e5(o.y,a.y,s.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):re(this.target,this.layout.layoutBox),ts(this.target,this.targetDelta)):re(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=e8(),this.relativeTargetOrigin=e8(),e3(this.relativeTargetOrigin,this.target,e.target),re(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}er.f.value&&rm.calculatedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||tr(this.parent.latestValues)||ti(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let e=this.getLead(),t=!!this.resumingFrom||this!==e,r=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(r=!1),t&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(r=!1),this.resolvedRelativeTargetAt===c.frameData.timestamp&&(r=!1),r)return;let{layout:n,layoutId:i}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||i))return;re(this.layoutCorrected,this.layout.layoutBox);let o=this.treeScale.x,a=this.treeScale.y;(function(e,t,r,n=!1){let i,o;let a=r.length;if(a){t.x=t.y=1;for(let s=0;s<a;s++){o=(i=r[s]).projectionDelta;let{visualElement:a}=i.options;(!a||!a.props.style||"contents"!==a.props.style.display)&&(n&&i.options.layoutScroll&&i.scroll&&i!==i.root&&tc(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),o&&(t.x*=o.x.scale,t.y*=o.y.scale,ts(e,o)),n&&tn(i.latestValues)&&tc(e,i.latestValues))}t.x<1.0000000000001&&t.x>.999999999999&&(t.x=1),t.y<1.0000000000001&&t.y>.999999999999&&(t.y=1)}})(this.layoutCorrected,this.treeScale,this.path,t),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=e8());let{target:s}=e;if(!s){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(rt(this.prevProjectionDelta.x,this.projectionDelta.x),rt(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),e2(this.projectionDelta,this.layoutCorrected,s,this.latestValues),this.treeScale.x===o&&this.treeScale.y===a&&rh(this.projectionDelta.x,this.prevProjectionDelta.x)&&rh(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",s)),er.f.value&&rm.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){if(this.options.visualElement?.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=e4(),this.projectionDelta=e4(),this.projectionDeltaWithTransform=e4()}setAnimationOrigin(e,t=!1){let r;let n=this.snapshot,i=n?n.latestValues:{},o={...this.latestValues},a=e4();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let s=e8(),l=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),c=!u||u.members.length<=1,d=!!(l&&!c&&!0===this.options.crossfade&&!this.path.some(rL));this.animationProgress=0,this.mixTargetDelta=t=>{let n=t/1e3;if(rk(a.x,e.x,n),rk(a.y,e.y,n),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,f,h,p;e3(s,this.layout.layoutBox,this.relativeParent.layout.layoutBox),h=this.relativeTarget,p=this.relativeTargetOrigin,rN(h.x,p.x,s.x,n),rN(h.y,p.y,s.y,n),r&&(u=this.relativeTarget,f=r,ru(u.x,f.x)&&ru(u.y,f.y))&&(this.isProjectionDirty=!1),r||(r=e8()),re(r,this.relativeTarget)}l&&(this.animationValues=o,function(e,t,r,n,i,o){i?(e.opacity=(0,eq.t)(0,r.opacity??1,t9(n)),e.opacityExit=(0,eq.t)(t.opacity??1,0,t4(n))):o&&(e.opacity=(0,eq.t)(t.opacity??1,r.opacity??1,n));for(let i=0;i<t2;i++){let o=`border${t1[i]}Radius`,a=t3(t,o),s=t3(r,o);(void 0!==a||void 0!==s)&&(a||(a=0),s||(s=0),0===a||0===s||t7(a)===t7(s)?(e[o]=Math.max((0,eq.t)(t5(a),t5(s),n),0),(I.aQ.test(s)||I.aQ.test(a))&&(e[o]+="%")):e[o]=s)}(t.rotate||r.rotate)&&(e.rotate=(0,eq.t)(t.rotate||0,r.rotate||0,n))}(o,i,this.latestValues,n,d,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation?.stop(!1),this.resumingFrom?.currentAnimation?.stop(!1),this.pendingAnimation&&((0,c.Pn)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=c.Wi.update(()=>{tV.hasAnimatedSinceResize=!0,et.P.layout++,this.motionValue||(this.motionValue=(0,p.BX)(0)),this.currentAnimation=function(e,t,r){let n=(0,g.i)(e)?e:(0,p.BX)(e);return n.start(eO("",n,t,r)),n.animation}(this.motionValue,[0,1e3],{...e,isSync:!0,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onStop:()=>{et.P.layout--},onComplete:()=>{et.P.layout--,e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop(!1)),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:r,layout:n,latestValues:i}=e;if(t&&r&&n){if(this!==e&&this.layout&&n&&rB(this.options.animationType,this.layout.layoutBox,n.layoutBox)){r=this.target||e8();let t=e0(this.layout.layoutBox.x);r.x.min=e.target.x.min,r.x.max=r.x.min+t;let n=e0(this.layout.layoutBox.y);r.y.min=e.target.y.min,r.y.max=r.y.min+n}re(t,r),tc(t,i),e2(this.projectionDeltaWithTransform,this.layoutCorrected,t,i)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new rp),this.sharedNodes.get(e).add(t);let r=t.options.initialPromotionConfig;t.promote({transition:r?r.transition:void 0,preserveFollowOpacity:r&&r.shouldPreserveFollowOpacity?r.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){let{layoutId:e}=this.options;return e&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:e}=this.options;return e?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:r}={}){let n=this.getStack();n&&n.promote(this,r),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:r}=e;if((r.z||r.rotate||r.rotateX||r.rotateY||r.rotateZ||r.skewX||r.skewY)&&(t=!0),!t)return;let n={};r.z&&rb("z",e,n,this.animationValues);for(let t=0;t<ry.length;t++)rb(`rotate${ry[t]}`,e,n,this.animationValues),rb(`skew${ry[t]}`,e,n,this.animationValues);for(let t in e.render(),n)e.setStaticValue(t,n[t]),this.animationValues&&(this.animationValues[t]=n[t]);e.scheduleRender()}getProjectionStyles(e){if(!this.instance||this.isSVG)return;if(!this.isVisible)return rg;let t={visibility:""},r=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,t.opacity="",t.pointerEvents=t0(e?.pointerEvents)||"",t.transform=r?r(this.latestValues,""):"none",t;let n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target){let t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=t0(e?.pointerEvents)||""),this.hasProjected&&!tn(this.latestValues)&&(t.transform=r?r({},""):"none",this.hasProjected=!1),t}let i=n.animationValues||n.latestValues;this.applyTransformsToTarget(),t.transform=function(e,t,r){let n="",i=e.x.translate/t.x,o=e.y.translate/t.y,a=r?.z||0;if((i||o||a)&&(n=`translate3d(${i}px, ${o}px, ${a}px) `),(1!==t.x||1!==t.y)&&(n+=`scale(${1/t.x}, ${1/t.y}) `),r){let{transformPerspective:e,rotate:t,rotateX:i,rotateY:o,skewX:a,skewY:s}=r;e&&(n=`perspective(${e}px) ${n}`),t&&(n+=`rotate(${t}deg) `),i&&(n+=`rotateX(${i}deg) `),o&&(n+=`rotateY(${o}deg) `),a&&(n+=`skewX(${a}deg) `),s&&(n+=`skewY(${s}deg) `)}let s=e.x.scale*t.x,l=e.y.scale*t.y;return(1!==s||1!==l)&&(n+=`scale(${s}, ${l})`),n||"none"}(this.projectionDeltaWithTransform,this.treeScale,i),r&&(t.transform=r(i,t.transform));let{x:o,y:a}=this.projectionDelta;for(let e in t.transformOrigin=`${100*o.origin}% ${100*a.origin}% 0`,n.animationValues?t.opacity=n===this?i.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:i.opacityExit:t.opacity=n===this?void 0!==i.opacity?i.opacity:"":void 0!==i.opacityExit?i.opacityExit:0,tz){if(void 0===i[e])continue;let{correct:r,applyTo:o,isCSSVariable:a}=tz[e],s="none"===t.transform?i[e]:r(i[e],n);if(o){let e=o.length;for(let r=0;r<e;r++)t[o[r]]=s}else a?this.options.visualElement.renderState.vars[e]=s:t[e]=s}return this.options.layoutId&&(t.pointerEvents=n===this?t0(e?.pointerEvents)||"":"none"),t}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>e.currentAnimation?.stop(!1)),this.root.nodes.forEach(rS),this.root.sharedNodes.clear()}}}function rw(e){e.updateLayout()}function rP(e){let t=e.resumeFrom?.snapshot||e.snapshot;if(e.isLead()&&e.layout&&t&&e.hasListeners("didUpdate")){let{layoutBox:r,measuredBox:n}=e.layout,{animationType:i}=e.options,o=t.source!==e.layout.source;"size"===i?te(e=>{let n=o?t.measuredBox[e]:t.layoutBox[e],i=e0(n);n.min=r[e].min,n.max=n.min+i}):rB(i,t.layoutBox,r)&&te(n=>{let i=o?t.measuredBox[n]:t.layoutBox[n],a=e0(r[n]);i.max=i.min+a,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[n].max=e.relativeTarget[n].min+a)});let a=e4();e2(a,r,t.layoutBox);let s=e4();o?e2(s,e.applyTransform(n,!0),t.measuredBox):e2(s,r,t.layoutBox);let l=!rl(a),u=!1;if(!e.resumeFrom){let n=e.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:i,layout:o}=n;if(i&&o){let a=e8();e3(a,t.layoutBox,i.layoutBox);let s=e8();e3(s,r,o.layoutBox),rd(a,s)||(u=!0),n.options.layoutRoot&&(e.relativeTarget=s,e.relativeTargetOrigin=a,e.relativeParent=n)}}}e.notifyListeners("didUpdate",{layout:r,snapshot:t,delta:s,layoutDelta:a,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function rE(e){er.f.value&&rm.nodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function rR(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function r_(e){e.clearSnapshot()}function rS(e){e.clearMeasurements()}function rT(e){e.isLayoutDirty=!1}function rj(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function rO(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function rC(e){e.resolveTargetDelta()}function rM(e){e.calcProjection()}function rA(e){e.resetSkewAndRotation()}function rD(e){e.removeLeadSnapshot()}function rk(e,t,r){e.translate=(0,eq.t)(t.translate,0,r),e.scale=(0,eq.t)(t.scale,1,r),e.origin=t.origin,e.originPoint=t.originPoint}function rN(e,t,r,n){e.min=(0,eq.t)(t.min,r.min,n),e.max=(0,eq.t)(t.max,r.max,n)}function rL(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let rI={duration:.45,ease:[.4,0,.1,1]},rF=e=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),rU=rF("applewebkit/")&&!rF("chrome/")?Math.round:P.Z;function rV(e){e.min=rU(e.min),e.max=rU(e.max)}function rB(e,t,r){return"position"===e||"preserve-aspect"===e&&!(.2>=Math.abs(rf(t)-rf(r)))}function rW(e){return e!==e.root&&e.scroll?.wasRoot}let rH=rx({attachResizeListener:(e,t)=>eK(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),rz={current:void 0},r$=rx({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!rz.current){let e=new rH({});e.mount(window),e.setOptions({layoutScroll:!0}),rz.current=e}return rz.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position});function rX(e,t){let r=function(e,t,r){if(e instanceof EventTarget)return[e];if("string"==typeof e){let t=document,r=(void 0)??t.querySelectorAll(e);return r?Array.from(r):[]}return Array.from(e)}(e),n=new AbortController;return[r,{passive:!0,...t,signal:n.signal},()=>n.abort()]}function rq(e){return!("touch"===e.pointerType||eX.x||eX.y)}function rK(e,t,r){let{props:n}=e;e.animationState&&n.whileHover&&e.animationState.setActive("whileHover","Start"===r);let i=n["onHover"+r];i&&c.Wi.postRender(()=>i(t,eY(t)))}class rG extends eW{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,r={}){let[n,i,o]=rX(e,r),a=e=>{if(!rq(e))return;let{target:r}=e,n=t(r,e);if("function"!=typeof n||!r)return;let o=e=>{rq(e)&&(n(e),r.removeEventListener("pointerleave",o))};r.addEventListener("pointerleave",o,i)};return n.forEach(e=>{e.addEventListener("pointerenter",a,i)}),o}(e,(e,t)=>(rK(this.node,t,"Start"),e=>rK(this.node,e,"End"))))}unmount(){}}class rY extends eW{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=(0,tp.z)(eK(this.node.current,"focus",()=>this.onFocus()),eK(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let rQ=(e,t)=>!!t&&(e===t||rQ(e,t.parentElement)),rZ=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),rJ=new WeakSet;function r0(e){return t=>{"Enter"===t.key&&e(t)}}function r1(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}let r2=(e,t)=>{let r=e.currentTarget;if(!r)return;let n=r0(()=>{if(rJ.has(r))return;r1(r,"down");let e=r0(()=>{r1(r,"up")});r.addEventListener("keyup",e,t),r.addEventListener("blur",()=>r1(r,"cancel"),t)});r.addEventListener("keydown",n,t),r.addEventListener("blur",()=>r.removeEventListener("keydown",n),t)};function r5(e){return eG(e)&&!(eX.x||eX.y)}function r7(e,t,r){let{props:n}=e;if(e.current instanceof HTMLButtonElement&&e.current.disabled)return;e.animationState&&n.whileTap&&e.animationState.setActive("whileTap","Start"===r);let i=n["onTap"+("End"===r?"":r)];i&&c.Wi.postRender(()=>i(t,eY(t)))}class r3 extends eW{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,r={}){let[n,i,o]=rX(e,r),a=e=>{let n=e.currentTarget;if(!r5(e))return;rJ.add(n);let o=t(n,e),a=(e,t)=>{window.removeEventListener("pointerup",s),window.removeEventListener("pointercancel",l),rJ.has(n)&&rJ.delete(n),r5(e)&&"function"==typeof o&&o(e,{success:t})},s=e=>{a(e,n===window||n===document||r.useGlobalTarget||rQ(n,e.target))},l=e=>{a(e,!1)};window.addEventListener("pointerup",s,i),window.addEventListener("pointercancel",l,i)};return n.forEach(e=>{(r.useGlobalTarget?window:e).addEventListener("pointerdown",a,i),(0,eb.R)(e)&&(e.addEventListener("focus",e=>r2(e,i)),rZ.has(e.tagName)||-1!==e.tabIndex||e.hasAttribute("tabindex")||(e.tabIndex=0))}),o}(e,(e,t)=>(r7(this.node,t,"Start"),(e,{success:t})=>r7(this.node,e,t?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let r9=new WeakMap,r4=new WeakMap,r6=e=>{let t=r9.get(e.target);t&&t(e)},r8=e=>{e.forEach(r6)},ne={some:0,all:1};class nt extends eW{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:r,amount:n="some",once:i}=e,o={root:t?t.current:void 0,rootMargin:r,threshold:"number"==typeof n?n:ne[n]};return function(e,t,r){let n=function({root:e,...t}){let r=e||document;r4.has(r)||r4.set(r,{});let n=r4.get(r),i=JSON.stringify(t);return n[i]||(n[i]=new IntersectionObserver(r8,{root:e,...t})),n[i]}(t);return r9.set(e,r),n.observe(e),()=>{r9.delete(e),n.unobserve(e)}}(this.node.current,o,e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,i&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:r,onViewportLeave:n}=this.node.getProps(),o=t?r:n;o&&o(e)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return r=>e[r]!==t[r]}(e,t))&&this.startObserver()}unmount(){}}let nr=(0,tL.createContext)({strict:!1});var nn=r(73965);let ni=(0,tL.createContext)({});function no(e){return n(e.animate)||eL.some(t=>ek(e[t]))}function na(e){return!!(no(e)||e.variants)}function ns(e){return Array.isArray(e)?e.join(" "):e}var nl=r(8263);let nu={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},nc={};for(let e in nu)nc[e]={isEnabled:t=>nu[e].some(e=>!!t[e])};let nd=Symbol.for("motionComponentSymbol");var nf=r(40295),nh=r(42482);function np(e,{layout:t,layoutId:r}){return f.has(e)||e.startsWith("origin")||(t||void 0!==r)&&(!!tz[e]||"opacity"===e)}let nm=(e,t)=>t&&"number"==typeof e?t.transform(e):e,ny={...L.Rx,transform:Math.round},ng={rotate:I.RW,rotateX:I.RW,rotateY:I.RW,rotateZ:I.RW,scale:L.bA,scaleX:L.bA,scaleY:L.bA,scaleZ:L.bA,skew:I.RW,skewX:I.RW,skewY:I.RW,distance:I.px,translateX:I.px,translateY:I.px,translateZ:I.px,x:I.px,y:I.px,z:I.px,perspective:I.px,transformPerspective:I.px,opacity:L.Fq,originX:I.$C,originY:I.$C,originZ:I.px},nv={borderWidth:I.px,borderTopWidth:I.px,borderRightWidth:I.px,borderBottomWidth:I.px,borderLeftWidth:I.px,borderRadius:I.px,radius:I.px,borderTopLeftRadius:I.px,borderTopRightRadius:I.px,borderBottomRightRadius:I.px,borderBottomLeftRadius:I.px,width:I.px,maxWidth:I.px,height:I.px,maxHeight:I.px,top:I.px,right:I.px,bottom:I.px,left:I.px,padding:I.px,paddingTop:I.px,paddingRight:I.px,paddingBottom:I.px,paddingLeft:I.px,margin:I.px,marginTop:I.px,marginRight:I.px,marginBottom:I.px,marginLeft:I.px,backgroundPositionX:I.px,backgroundPositionY:I.px,...ng,zIndex:ny,fillOpacity:L.Fq,strokeOpacity:L.Fq,numOctaves:ny},nb={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},nx=d.length;function nw(e,t,r){let{style:n,vars:i,transformOrigin:o}=e,a=!1,s=!1;for(let e in t){let r=t[e];if(f.has(e)){a=!0;continue}if((0,tH.f)(e)){i[e]=r;continue}{let t=nm(r,nv[e]);e.startsWith("origin")?(s=!0,o[e]=t):n[e]=t}}if(!t.transform&&(a||r?n.transform=function(e,t,r){let n="",i=!0;for(let o=0;o<nx;o++){let a=d[o],s=e[a];if(void 0===s)continue;let l=!0;if(!(l="number"==typeof s?s===(a.startsWith("scale")?1:0):0===parseFloat(s))||r){let e=nm(s,nv[a]);if(!l){i=!1;let t=nb[a]||a;n+=`${t}(${e}) `}r&&(t[a]=e)}}return n=n.trim(),r?n=r(t,i?"":n):i&&(n="none"),n}(t,e.transform,r):n.transform&&(n.transform="none")),s){let{originX:e="50%",originY:t="50%",originZ:r=0}=o;n.transformOrigin=`${e} ${t} ${r}`}}let nP=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function nE(e,t,r){for(let n in t)(0,g.i)(t[n])||np(n,r)||(e[n]=t[n])}let nR={offset:"stroke-dashoffset",array:"stroke-dasharray"},n_={offset:"strokeDashoffset",array:"strokeDasharray"};function nS(e,{attrX:t,attrY:r,attrScale:n,pathLength:i,pathSpacing:o=1,pathOffset:a=0,...s},l,u,c){if(nw(e,s,u),l){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:d,style:f}=e;d.transform&&(f.transform=d.transform,delete d.transform),(f.transform||d.transformOrigin)&&(f.transformOrigin=d.transformOrigin??"50% 50%",delete d.transformOrigin),f.transform&&(f.transformBox=c?.transformBox??"fill-box",delete d.transformBox),void 0!==t&&(d.x=t),void 0!==r&&(d.y=r),void 0!==n&&(d.scale=n),void 0!==i&&function(e,t,r=1,n=0,i=!0){e.pathLength=1;let o=i?nR:n_;e[o.offset]=I.px.transform(-n);let a=I.px.transform(t),s=I.px.transform(r);e[o.array]=`${a} ${s}`}(d,i,o,a,!1)}let nT=()=>({...nP(),attrs:{}}),nj=e=>"string"==typeof e&&"svg"===e.toLowerCase(),nO=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function nC(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||nO.has(e)}let nM=e=>!nC(e);try{!function(e){e&&(nM=t=>t.startsWith("on")?!nC(t):e(t))}(require("@emotion/is-prop-valid").default)}catch{}let nA=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function nD(e){if("string"!=typeof e||e.includes("-"));else if(nA.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}var nk=r(74749);let nN=e=>(t,r)=>{let i=(0,tL.useContext)(ni),a=(0,tL.useContext)(nf.O),s=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t},r,i,a){return{latestValues:function(e,t,r,i){let a={},s=i(e,{});for(let e in s)a[e]=t0(s[e]);let{initial:l,animate:u}=e,c=no(e),d=na(e);t&&d&&!c&&!1!==e.inherit&&(void 0===l&&(l=t.initial),void 0===u&&(u=t.animate));let f=!!r&&!1===r.initial,h=(f=f||!1===l)?u:l;if(h&&"boolean"!=typeof h&&!n(h)){let t=Array.isArray(h)?h:[h];for(let r=0;r<t.length;r++){let n=o(e,t[r]);if(n){let{transitionEnd:e,transition:t,...r}=n;for(let e in r){let t=r[e];if(Array.isArray(t)){let e=f?t.length-1:0;t=t[e]}null!==t&&(a[e]=t)}for(let t in e)a[t]=e[t]}}}return a}(r,i,a,e),renderState:t()}})(e,t,i,a);return r?s():(0,nk.h)(s)};function nL(e,t,r){let{style:n}=e,i={};for(let o in n)((0,g.i)(n[o])||t.style&&(0,g.i)(t.style[o])||np(o,e)||r?.getValue(o)?.liveStyle!==void 0)&&(i[o]=n[o]);return i}let nI={useVisualState:nN({scrapeMotionValuesFromProps:nL,createRenderState:nP})};function nF(e,t,r){let n=nL(e,t,r);for(let r in e)((0,g.i)(e[r])||(0,g.i)(t[r]))&&(n[-1!==d.indexOf(r)?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r]=e[r]);return n}let nU={useVisualState:nN({scrapeMotionValuesFromProps:nF,createRenderState:nT})},nV=e=>t=>t.test(e),nB=[L.Rx,I.px,I.aQ,I.RW,I.vw,I.vh,{test:e=>"auto"===e,parse:e=>e}],nW=e=>nB.find(nV(e)),nH=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),nz=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,n$=e=>/^0[^.\s]+$/u.test(e);var nX=r(1849);let nq=new Set(["brightness","contrast","saturate","opacity"]);function nK(e){let[t,r]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[n]=r.match(nX.K)||[];if(!n)return e;let i=r.replace(n,""),o=nq.has(t)?1:0;return n!==r&&(o*=100),t+"("+o+i+")"}let nG=/\b([a-z-]*)\(.*?\)/gu,nY={...eg.P,getAnimatableNone:e=>{let t=e.match(nG);return t?t.map(nK).join(" "):e}};var nQ=r(31068);let nZ={...nv,color:nQ.$,backgroundColor:nQ.$,outlineColor:nQ.$,fill:nQ.$,stroke:nQ.$,borderColor:nQ.$,borderTopColor:nQ.$,borderRightColor:nQ.$,borderBottomColor:nQ.$,borderLeftColor:nQ.$,filter:nY,WebkitFilter:nY},nJ=e=>nZ[e];function n0(e,t){let r=nJ(e);return r!==nY&&(r=eg.P),r.getAnimatableNone?r.getAnimatableNone(t):void 0}let n1=new Set(["auto","none","0"]);class n2 extends K{constructor(e,t,r,n,i){super(e,t,r,n,i,!0)}readKeyframes(){let{unresolvedKeyframes:e,element:t,name:r}=this;if(!t||!t.current)return;super.readKeyframes();for(let r=0;r<e.length;r++){let n=e[r];if("string"==typeof n&&(n=n.trim(),(0,tH.t)(n))){let i=function e(t,r,n=1){(0,Y.k)(n<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`);let[i,o]=function(e){let t=nz.exec(e);if(!t)return[,];let[,r,n,i]=t;return[`--${r??n}`,i]}(t);if(!i)return;let a=window.getComputedStyle(r).getPropertyValue(i);if(a){let e=a.trim();return nH(e)?parseFloat(e):e}return(0,tH.t)(o)?e(o,r,n+1):o}(n,t.current);void 0!==i&&(e[r]=i),r===e.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!h.has(r)||2!==e.length)return;let[n,i]=e,o=nW(n),a=nW(i);if(o!==a){if(F(o)&&F(a))for(let t=0;t<e.length;t++){let r=e[t];"string"==typeof r&&(e[t]=parseFloat(r))}else B[r]&&(this.needsMeasurement=!0)}}resolveNoneKeyframes(){let{unresolvedKeyframes:e,name:t}=this,r=[];for(let t=0;t<e.length;t++){var n;(null===e[t]||("number"==typeof(n=e[t])?0===n:null===n||"none"===n||"0"===n||n$(n)))&&r.push(t)}r.length&&function(e,t,r){let n,i=0;for(;i<e.length&&!n;){let t=e[i];"string"==typeof t&&!n1.has(t)&&(0,eg.V)(t).values.length&&(n=e[i]),i++}if(n&&r)for(let i of t)e[i]=n0(r,n)}(e,r,t)}measureInitialState(){let{element:e,unresolvedKeyframes:t,name:r}=this;if(!e||!e.current)return;"height"===r&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=B[r](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;let n=t[t.length-1];void 0!==n&&e.getValue(r,n).jump(n,!1)}measureEndState(){let{element:e,name:t,unresolvedKeyframes:r}=this;if(!e||!e.current)return;let n=e.getValue(t);n&&n.jump(this.measuredOrigin,!1);let i=r.length-1,o=r[i];r[i]=B[t](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==o&&void 0===this.finalKeyframe&&(this.finalKeyframe=o),this.removedTransforms?.length&&this.removedTransforms.forEach(([t,r])=>{e.getValue(t).set(r)}),this.resolveNoneKeyframes()}}let n5=[...nB,nQ.$,eg.P],n7=e=>n5.find(nV(e)),n3={current:null},n9={current:!1},n4=new WeakMap,n6=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class n8{scrapeMotionValuesFromProps(e,t,r){return{}}constructor({parent:e,props:t,presenceContext:r,reducedMotionConfig:n,blockInitialAnimation:i,visualState:o},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=K,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let e=E.X.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,c.Wi.render(this.render,!1,!0))};let{latestValues:s,renderState:l}=o;this.latestValues=s,this.baseTarget={...s},this.initialValues=t.initial?{...s}:{},this.renderState=l,this.parent=e,this.props=t,this.presenceContext=r,this.depth=e?e.depth+1:0,this.reducedMotionConfig=n,this.options=a,this.blockInitialAnimation=!!i,this.isControllingVariants=no(t),this.isVariantNode=na(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:u,...d}=this.scrapeMotionValuesFromProps(t,{},this);for(let e in d){let t=d[e];void 0!==s[e]&&(0,g.i)(t)&&t.set(s[e],!1)}}mount(e){this.current=e,n4.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),n9.current||function(){if(n9.current=!0,nl.j){if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>n3.current=e.matches;e.addListener(t),t()}else n3.current=!1}}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||n3.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in this.projection&&this.projection.unmount(),(0,c.Pn)(this.notifyUpdate),(0,c.Pn)(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(let e in this.features){let t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}bindToMotionValue(e,t){let r;this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();let n=f.has(e);n&&this.onBindTransform&&this.onBindTransform();let i=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&c.Wi.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),o=t.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(r=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,()=>{i(),o(),r&&r(),t.owner&&t.stop()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in nc){let t=nc[e];if(!t)continue;let{isEnabled:r,Feature:n}=t;if(!this.features[e]&&n&&r(this.props)&&(this.features[e]=new n(this)),this.features[e]){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):e8()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<n6.length;t++){let r=n6[t];this.propEventSubscriptions[r]&&(this.propEventSubscriptions[r](),delete this.propEventSubscriptions[r]);let n=e["on"+r];n&&(this.propEventSubscriptions[r]=this.on(r,n))}this.prevMotionValues=function(e,t,r){for(let n in t){let i=t[n],o=r[n];if((0,g.i)(i))e.addValue(n,i);else if((0,g.i)(o))e.addValue(n,(0,p.BX)(i,{owner:e}));else if(o!==i){if(e.hasValue(n)){let t=e.getValue(n);!0===t.liveStyle?t.jump(i):t.hasAnimated||t.set(i)}else{let t=e.getStaticValue(n);e.addValue(n,(0,p.BX)(void 0!==t?t:i,{owner:e}))}}}for(let n in r)void 0===t[n]&&e.removeValue(n);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){let r=this.values.get(e);t!==r&&(r&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let r=this.values.get(e);return void 0===r&&void 0!==t&&(r=(0,p.BX)(null===t?void 0:t,{owner:this}),this.addValue(e,r)),r}readValue(e,t){let r=void 0===this.latestValues[e]&&this.current?this.getBaseTargetFromProps(this.props,e)??this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];return null!=r&&("string"==typeof r&&(nH(r)||n$(r))?r=parseFloat(r):!n7(r)&&eg.P.test(t)&&(r=n0(e,t)),this.setBaseTarget(e,(0,g.i)(r)?r.get():r)),(0,g.i)(r)?r.get():r}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){let t;let{initial:r}=this.props;if("string"==typeof r||"object"==typeof r){let n=o(this.props,r,this.presenceContext?.custom);n&&(t=n[e])}if(r&&void 0!==t)return t;let n=this.getBaseTargetFromProps(this.props,e);return void 0===n||(0,g.i)(n)?void 0!==this.initialValues[e]&&void 0===t?void 0:this.baseTarget[e]:n}on(e,t){return this.events[e]||(this.events[e]=new tY.L),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}class ie extends n8{constructor(){super(...arguments),this.KeyframeResolver=n2}sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:r}){delete t[e],delete r[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;(0,g.i)(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}}function it(e,{style:t,vars:r},n,i){for(let o in Object.assign(e.style,t,i&&i.getProjectionStyles(n)),r)e.style.setProperty(o,r[o])}class ir extends ie{constructor(){super(...arguments),this.type="html",this.renderInstance=it}readValueFromInstance(e,t){if(f.has(t))return this.projection?.isProjecting?A(t):k(e,t);{let r=window.getComputedStyle(e),n=((0,tH.f)(t)?r.getPropertyValue(t):r[t])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(e,{transformPagePoint:t}){return td(e,t)}build(e,t,r){nw(e,t,r.transformTemplate)}scrapeMotionValuesFromProps(e,t,r){return nL(e,t,r)}}let ii=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class io extends ie{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=e8}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(f.has(t)){let e=nJ(t);return e&&e.default||0}return t=ii.has(t)?t:b(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,r){return nF(e,t,r)}build(e,t,r){nS(e,t,this.isSVGTag,r.transformTemplate,r.style)}renderInstance(e,t,r,n){!function(e,t,r,n){for(let r in it(e,t,void 0,n),t.attrs)e.setAttribute(ii.has(r)?r:b(r),t.attrs[r])}(e,t,0,n)}mount(e){this.isSVGTag=nj(e.tagName),super.mount(e)}}let ia=function(e){if("undefined"==typeof Proxy)return e;let t=new Map;return new Proxy((...t)=>e(...t),{get:(r,n)=>"create"===n?e:(t.has(n)||t.set(n,e(n)),t.get(n))})}((l={animation:{Feature:eH},exit:{Feature:e$},inView:{Feature:nt},tap:{Feature:r3},focus:{Feature:rY},hover:{Feature:rG},pan:{Feature:tA},drag:{Feature:tC,ProjectionNode:r$,MeasureLayout:tX},layout:{ProjectionNode:r$,MeasureLayout:tX}},u=(e,t)=>nD(e)?new io(t):new ir(t,{allowProjection:e!==tL.Fragment}),function(e,{forwardMotionProps:t}={forwardMotionProps:!1}){return function({preloadedFeatures:e,createVisualElement:t,useRender:r,useVisualState:n,Component:i}){function o(e,o){var a;let s;let l={...(0,tL.useContext)(nn._),...e,layoutId:function({layoutId:e}){let t=(0,tL.useContext)(tF.p).id;return t&&void 0!==e?t+"-"+e:e}(e)},{isStatic:u}=l,c=function(e){let{initial:t,animate:r}=function(e,t){if(no(e)){let{initial:t,animate:r}=e;return{initial:!1===t||ek(t)?t:void 0,animate:ek(r)?r:void 0}}return!1!==e.inherit?t:{}}(e,(0,tL.useContext)(ni));return(0,tL.useMemo)(()=>({initial:t,animate:r}),[ns(t),ns(r)])}(e),d=n(e,u);if(!u&&nl.j){(0,tL.useContext)(nr).strict;let e=function(e){let{drag:t,layout:r}=nc;if(!t&&!r)return{};let n={...t,...r};return{MeasureLayout:t?.isEnabled(e)||r?.isEnabled(e)?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(l);s=e.MeasureLayout,c.visualElement=function(e,t,r,n,i){let{visualElement:o}=(0,tL.useContext)(ni),a=(0,tL.useContext)(nr),s=(0,tL.useContext)(nf.O),l=(0,tL.useContext)(nn._).reducedMotion,u=(0,tL.useRef)(null);n=n||a.renderer,!u.current&&n&&(u.current=n(e,{visualState:t,parent:o,props:r,presenceContext:s,blockInitialAnimation:!!s&&!1===s.initial,reducedMotionConfig:l}));let c=u.current,d=(0,tL.useContext)(tU);c&&!c.projection&&i&&("html"===c.type||"svg"===c.type)&&function(e,t,r,n){let{layoutId:i,layout:o,drag:a,dragConstraints:s,layoutScroll:l,layoutRoot:u,layoutCrossfade:c}=t;e.projection=new r(e.latestValues,t["data-framer-portal-id"]?void 0:function e(t){if(t)return!1!==t.options.allowProjection?t.projection:e(t.parent)}(e.parent)),e.projection.setOptions({layoutId:i,layout:o,alwaysMeasureLayout:!!a||s&&th(s),visualElement:e,animationType:"string"==typeof o?o:"both",initialPromotionConfig:n,crossfade:c,layoutScroll:l,layoutRoot:u})}(u.current,r,i,d);let f=(0,tL.useRef)(!1);(0,tL.useInsertionEffect)(()=>{c&&f.current&&c.update(r,s)});let h=r[x],p=(0,tL.useRef)(!!h&&!window.MotionHandoffIsComplete?.(h)&&window.MotionHasOptimisedAnimation?.(h));return(0,nh.L)(()=>{c&&(f.current=!0,window.MotionIsMounted=!0,c.updateFeatures(),tk.render(c.render),p.current&&c.animationState&&c.animationState.animateChanges())}),(0,tL.useEffect)(()=>{c&&(!p.current&&c.animationState&&c.animationState.animateChanges(),p.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(h)}),p.current=!1))}),c}(i,d,l,t,e.ProjectionNode)}return(0,tD.jsxs)(ni.Provider,{value:c,children:[s&&c.visualElement?(0,tD.jsx)(s,{visualElement:c.visualElement,...l}):null,r(i,e,(a=c.visualElement,(0,tL.useCallback)(e=>{e&&d.onMount&&d.onMount(e),a&&(e?a.mount(e):a.unmount()),o&&("function"==typeof o?o(e):th(o)&&(o.current=e))},[a])),d,u,c.visualElement)]})}e&&function(e){for(let t in e)nc[t]={...nc[t],...e[t]}}(e),o.displayName=`motion.${"string"==typeof i?i:`create(${i.displayName??i.name??""})`}`;let a=(0,tL.forwardRef)(o);return a[nd]=i,a}({...nD(e)?nU:nI,preloadedFeatures:l,useRender:function(e=!1){return(t,r,n,{latestValues:i},o)=>{let a=(nD(t)?function(e,t,r,n){let i=(0,tL.useMemo)(()=>{let r=nT();return nS(r,t,nj(n),e.transformTemplate,e.style),{...r.attrs,style:{...r.style}}},[t]);if(e.style){let t={};nE(t,e.style,e),i.style={...t,...i.style}}return i}:function(e,t){let r={},n=function(e,t){let r=e.style||{},n={};return nE(n,r,e),Object.assign(n,function({transformTemplate:e},t){return(0,tL.useMemo)(()=>{let r=nP();return nw(r,t,e),Object.assign({},r.vars,r.style)},[t])}(e,t)),n}(e,t);return e.drag&&!1!==e.dragListener&&(r.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(r.tabIndex=0),r.style=n,r})(r,i,o,t),s=function(e,t,r){let n={};for(let i in e)("values"!==i||"object"!=typeof e.values)&&(nM(i)||!0===r&&nC(i)||!t&&!nC(i)||e.draggable&&i.startsWith("onDrag"))&&(n[i]=e[i]);return n}(r,"string"==typeof t,e),l=t!==tL.Fragment?{...s,...a,ref:n}:{},{children:u}=r,c=(0,tL.useMemo)(()=>(0,g.i)(u)?u.get():u,[u]);return(0,tL.createElement)(t,{...l,children:c})}}(t),createVisualElement:u,Component:e})}))},8263:(e,t,r)=>{"use strict";r.d(t,{j:()=>n});let n="undefined"!=typeof window},74749:(e,t,r)=>{"use strict";r.d(t,{h:()=>i});var n=r(17577);function i(e){let t=(0,n.useRef)(null);return null===t.current&&(t.current=e()),t.current}},42482:(e,t,r)=>{"use strict";r.d(t,{L:()=>i});var n=r(17577);let i=r(8263).j?n.useLayoutEffect:n.useEffect},33519:(e,t,r)=>{"use strict";r.d(t,{c:()=>s});var n=r(19073),i=r(17577),o=r(73965),a=r(74749);function s(e){let t=(0,a.h)(()=>(0,n.BX)(e)),{isStatic:r}=(0,i.useContext)(o._);if(r){let[,r]=(0,i.useState)(e);(0,i.useEffect)(()=>t.on("change",r),[])}return t}},55139:(e,t,r)=>{"use strict";r.d(t,{q:()=>d});var n=r(45290),i=r(45307),o=r(71470);function a(e){return"number"==typeof e?e:parseFloat(e)}var s=r(17577),l=r(73965),u=r(33519),c=r(42734);function d(e,t={}){let{isStatic:r}=(0,s.useContext)(l._),d=()=>(0,n.i)(e)?e.get():e;if(r)return(0,c.H)(d);let f=(0,u.c)(d());return(0,s.useInsertionEffect)(()=>(function(e,t,r){let s,l;let u=e.get(),c=null,d=u,f="string"==typeof u?u.replace(/[\d.-]/g,""):void 0,h=()=>{c&&(c.stop(),c=null)},p=()=>{h(),c=new i.L({keyframes:[a(e.get()),a(d)],velocity:e.getVelocity(),type:"spring",restDelta:.001,restSpeed:.01,...r,onUpdate:s})};return e.attach((t,r)=>(d=t,s=e=>r(f?e+f:e),o.Wi.postRender(p),e.get()),h),(0,n.i)(t)&&(l=t.on("change",t=>e.set(f?t+f:t)),e.on("destroy",l)),l})(f,e,t),[f,JSON.stringify(t)]),f}},42734:(e,t,r)=>{"use strict";r.d(t,{H:()=>c});var n=r(50522),i=r(74749),o=r(71470),a=r(42482),s=r(33519);function l(e,t){let r=(0,s.c)(t()),n=()=>r.set(t());return n(),(0,a.L)(()=>{let t=()=>o.Wi.preRender(n,!1,!0),r=e.map(e=>e.on("change",t));return()=>{r.forEach(e=>e()),(0,o.Pn)(n)}}),r}var u=r(19073);function c(e,t,r,i){if("function"==typeof e)return function(e){u.S1.current=[],e();let t=l(u.S1.current,e);return u.S1.current=void 0,t}(e);let o="function"==typeof t?t:function(...e){let t=!Array.isArray(e[0]),r=t?0:-1,i=e[0+r],o=e[1+r],a=e[2+r],s=e[3+r],l=(0,n.s)(o,a,s);return t?l(i):l}(t,r,i);return Array.isArray(e)?d(e,o):d([e],([e])=>o(e))}function d(e,t){let r=(0,i.h)(()=>[]);return l(e,()=>{r.length=0;let n=e.length;for(let t=0;t<n;t++)r[t]=e[t].get();return t(r)})}},45307:(e,t,r)=>{"use strict";r.d(t,{L:()=>v});var n=r(85294),i=r(68132),o=r(51612),a=r(84289),s=r(7626),l=r(52416),u=r(71470);let c=e=>{let t=({timestamp:t})=>e(t);return{start:(e=!0)=>u.Wi.update(t,e),stop:()=>(0,u.Pn)(t),now:()=>u.frameData.isProcessing?u.frameData.timestamp:a.X.now()}};var d=r(67510),f=r(90811),h=r(81076),p=r(48077),m=r(10108),y=r(42036);let g=e=>e/100;class v extends y.T{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=(e=!0)=>{if(e){let{motionValue:e}=this.options;e&&e.updatedAt!==a.X.now()&&this.tick(a.X.now())}this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},s.P.mainThread++,this.options=e,this.initAnimation(),this.play(),!1===e.autoplay&&this.pause()}initAnimation(){let{options:e}=this;(0,m.f)(e);let{type:t=f.F,repeat:r=0,repeatDelay:i=0,repeatType:o,velocity:a=0}=e,{keyframes:s}=e,u=t||f.F;u!==f.F&&"number"!=typeof s[0]&&(this.mixKeyframes=(0,n.z)(g,(0,l.C)(s[0],s[1])),s=[0,100]);let c=u({...e,keyframes:s});"mirror"===o&&(this.mirroredGenerator=u({...e,keyframes:[...s].reverse(),velocity:-a})),null===c.calculatedDuration&&(c.calculatedDuration=(0,h.i)(c));let{calculatedDuration:d}=c;this.calculatedDuration=d,this.resolvedDuration=d+i,this.totalDuration=this.resolvedDuration*(r+1)-i,this.generator=c}updateTime(e){let t=Math.round(e-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=t}tick(e,t=!1){let{generator:r,totalDuration:n,mixKeyframes:o,mirroredGenerator:a,resolvedDuration:s,calculatedDuration:l}=this;if(null===this.startTime)return r.next(0);let{delay:u=0,keyframes:c,repeat:f,repeatType:h,repeatDelay:m,type:y,onUpdate:g,finalKeyframe:v}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-n/this.speed,this.startTime)),t?this.currentTime=e:this.updateTime(e);let b=this.currentTime-u*(this.playbackSpeed>=0?1:-1),x=this.playbackSpeed>=0?b<0:b>n;this.currentTime=Math.max(b,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=n);let w=this.currentTime,P=r;if(f){let e=Math.min(this.currentTime,n)/s,t=Math.floor(e),r=e%1;!r&&e>=1&&(r=1),1===r&&t--,(t=Math.min(t,f+1))%2&&("reverse"===h?(r=1-r,m&&(r-=m/s)):"mirror"===h&&(P=a)),w=(0,i.u)(0,1,r)*s}let E=x?{done:!1,value:c[0]}:P.next(w);o&&(E.value=o(E.value));let{done:R}=E;x||null===l||(R=this.playbackSpeed>=0?this.currentTime>=n:this.currentTime<=0);let _=null===this.holdTime&&("finished"===this.state||"running"===this.state&&R);return _&&y!==d.I&&(E.value=(0,p.$)(c,this.options,v,this.speed)),g&&g(E.value),_&&this.finish(),E}then(e,t){return this.finished.then(e,t)}get duration(){return(0,o.X)(this.calculatedDuration)}get time(){return(0,o.X)(this.currentTime)}set time(e){e=(0,o.w)(e),this.currentTime=e,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(a.X.now());let t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=(0,o.X)(this.currentTime))}play(){if(this.isStopped)return;let{driver:e=c,startTime:t}=this.options;this.driver||(this.driver=e(e=>this.tick(e))),this.options.onPlay?.();let r=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=r):null!==this.holdTime?this.startTime=r-this.holdTime:this.startTime||(this.startTime=t??r),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(a.X.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,s.P.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),e.observe(this)}}},67510:(e,t,r)=>{"use strict";r.d(t,{I:()=>o});var n=r(13836),i=r(97197);function o({keyframes:e,velocity:t=0,power:r=.8,timeConstant:o=325,bounceDamping:a=10,bounceStiffness:s=500,modifyTarget:l,min:u,max:c,restDelta:d=.5,restSpeed:f}){let h,p;let m=e[0],y={done:!1,value:m},g=e=>void 0!==u&&e<u||void 0!==c&&e>c,v=e=>void 0===u?c:void 0===c?u:Math.abs(u-e)<Math.abs(c-e)?u:c,b=r*t,x=m+b,w=void 0===l?x:l(x);w!==x&&(b=w-m);let P=e=>-b*Math.exp(-e/o),E=e=>w+P(e),R=e=>{let t=P(e),r=E(e);y.done=Math.abs(t)<=d,y.value=y.done?w:r},_=e=>{g(y.value)&&(h=e,p=(0,n.S)({keyframes:[y.value,v(y.value)],velocity:(0,i.P)(E,e,y.value),damping:a,stiffness:s,restDelta:d,restSpeed:f}))};return _(0),{calculatedDuration:null,next:e=>{let t=!1;return(p||void 0!==h||(t=!0,R(e),_(e)),void 0!==h&&e>=h)?p.next(e-h):(t||R(e),y)}}}},90811:(e,t,r)=>{"use strict";r.d(t,{F:()=>x});var n=r(75631);let i=(0,n._)(.42,0,1,1),o=(0,n._)(0,0,.58,1),a=(0,n._)(.42,0,.58,1),s=e=>Array.isArray(e)&&"number"!=typeof e[0];var l=r(17941),u=r(39911),c=r(48219),d=r(27597),f=r(34946),h=r(71683);let p={linear:u.Z,easeIn:i,easeInOut:a,easeOut:o,circIn:f.Z7,circInOut:f.X7,circOut:f.Bn,backIn:d.G2,backInOut:d.XL,backOut:d.CG,anticipate:c.L},m=e=>"string"==typeof e,y=e=>{if((0,h.q)(e)){(0,l.k)(4===e.length,"Cubic bezier arrays must contain four numerical values.");let[t,r,i,o]=e;return(0,n._)(t,r,i,o)}return m(e)?((0,l.k)(void 0!==p[e],`Invalid easing type '${e}'`),p[e]):e};var g=r(50522),v=r(19069),b=r(92844);function x({duration:e=300,keyframes:t,times:r,ease:n="easeInOut"}){let i=s(n)?n.map(y):y(n),o={done:!1,value:t[0]},l=(r&&r.length===t.length?r:function(e){let t=[0];return function(e,t){let r=e[e.length-1];for(let n=1;n<=t;n++){let i=(0,v.Y)(0,t,n);e.push((0,b.t)(r,1,i))}}(t,e.length-1),t}(t)).map(t=>t*e),u=(0,g.s)(l,t,{ease:Array.isArray(i)?i:t.map(()=>i||a).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(o.value=u(t),o.done=t>=e,o)}}},13836:(e,t,r)=>{"use strict";r.d(t,{S:()=>p});var n=r(68132),i=r(51612),o=r(56720),a=r(81076),s=r(97197);let l={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};var u=r(17941);function c(e,t){return e*Math.sqrt(1-t*t)}let d=["duration","bounce"],f=["stiffness","damping","mass"];function h(e,t){return t.some(t=>void 0!==e[t])}function p(e=l.visualDuration,t=l.bounce){let r;let p="object"!=typeof e?{visualDuration:e,keyframes:[0,1],bounce:t}:e,{restSpeed:m,restDelta:y}=p,g=p.keyframes[0],v=p.keyframes[p.keyframes.length-1],b={done:!1,value:g},{stiffness:x,damping:w,mass:P,duration:E,velocity:R,isResolvedFromDuration:_}=function(e){let t={velocity:l.velocity,stiffness:l.stiffness,damping:l.damping,mass:l.mass,isResolvedFromDuration:!1,...e};if(!h(e,f)&&h(e,d)){if(e.visualDuration){let r=2*Math.PI/(1.2*e.visualDuration),i=r*r,o=2*(0,n.u)(.05,1,1-(e.bounce||0))*Math.sqrt(i);t={...t,mass:l.mass,stiffness:i,damping:o}}else{let r=function({duration:e=l.duration,bounce:t=l.bounce,velocity:r=l.velocity,mass:o=l.mass}){let a,s;(0,u.K)(e<=(0,i.w)(l.maxDuration),"Spring duration must be 10 seconds or less");let d=1-t;d=(0,n.u)(l.minDamping,l.maxDamping,d),e=(0,n.u)(l.minDuration,l.maxDuration,(0,i.X)(e)),d<1?(a=t=>{let n=t*d,i=n*e;return .001-(n-r)/c(t,d)*Math.exp(-i)},s=t=>{let n=t*d*e,i=Math.pow(d,2)*Math.pow(t,2)*e,o=c(Math.pow(t,2),d);return(n*r+r-i)*Math.exp(-n)*(-a(t)+.001>0?-1:1)/o}):(a=t=>-.001+Math.exp(-t*e)*((t-r)*e+1),s=t=>e*e*(r-t)*Math.exp(-t*e));let f=function(e,t,r){let n=r;for(let r=1;r<12;r++)n-=e(n)/t(n);return n}(a,s,5/e);if(e=(0,i.w)(e),isNaN(f))return{stiffness:l.stiffness,damping:l.damping,duration:e};{let t=Math.pow(f,2)*o;return{stiffness:t,damping:2*d*Math.sqrt(o*t),duration:e}}}(e);(t={...t,...r,mass:l.mass}).isResolvedFromDuration=!0}}return t}({...p,velocity:-(0,i.X)(p.velocity||0)}),S=R||0,T=w/(2*Math.sqrt(x*P)),j=v-g,O=(0,i.X)(Math.sqrt(x/P)),C=5>Math.abs(j);if(m||(m=C?l.restSpeed.granular:l.restSpeed.default),y||(y=C?l.restDelta.granular:l.restDelta.default),T<1){let e=c(O,T);r=t=>v-Math.exp(-T*O*t)*((S+T*O*j)/e*Math.sin(e*t)+j*Math.cos(e*t))}else if(1===T)r=e=>v-Math.exp(-O*e)*(j+(S+O*j)*e);else{let e=O*Math.sqrt(T*T-1);r=t=>{let r=Math.exp(-T*O*t),n=Math.min(e*t,300);return v-r*((S+T*O*j)*Math.sinh(n)+e*j*Math.cosh(n))/e}}let M={calculatedDuration:_&&E||null,next:e=>{let t=r(e);if(_)b.done=e>=E;else{let n=0===e?S:0;T<1&&(n=0===e?(0,i.w)(S):(0,s.P)(r,e,t));let o=Math.abs(n)<=m,a=Math.abs(v-t)<=y;b.done=o&&a}return b.value=b.done?v:t,b},toString:()=>{let e=Math.min((0,a.i)(M),a.E),t=(0,o.w)(t=>M.next(e*t).value,e,30);return e+"ms "+t},toTransition:()=>{}};return M}p.applyToOptions=e=>{let t=function(e,t=100,r){let n=r({...e,keyframes:[0,t]}),o=Math.min((0,a.i)(n),a.E);return{type:"keyframes",ease:e=>n.next(o*e).value/t,duration:(0,i.X)(o)}}(e,100,p);return e.ease=t.ease,e.duration=(0,i.w)(t.duration),e.type="keyframes",e}},81076:(e,t,r)=>{"use strict";r.d(t,{E:()=>n,i:()=>i});let n=2e4;function i(e){let t=0,r=e.next(t);for(;!r.done&&t<n;)t+=50,r=e.next(t);return t>=n?1/0:t}},97197:(e,t,r)=>{"use strict";r.d(t,{P:()=>i});var n=r(46390);function i(e,t,r){let i=Math.max(t-5,0);return(0,n.R)(r-e(i),t-i)}},48077:(e,t,r)=>{"use strict";r.d(t,{$:()=>i});let n=e=>null!==e;function i(e,{repeat:t,repeatType:r="loop"},i,o=1){let a=e.filter(n),s=o<0||t&&"loop"!==r&&t%2==1?0:a.length-1;return s&&void 0!==i?i:a[s]}},42036:(e,t,r)=>{"use strict";r.d(t,{T:()=>n});class n{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,t){return this.finished.then(e,t)}}},97125:(e,t,r)=>{"use strict";r.d(t,{f:()=>i,t:()=>a});let n=e=>t=>"string"==typeof t&&t.startsWith(e),i=n("--"),o=n("var(--"),a=e=>!!o(e)&&s.test(e.split("/*")[0].trim()),s=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu},10108:(e,t,r)=>{"use strict";r.d(t,{f:()=>s});var n=r(67510),i=r(90811),o=r(13836);let a={decay:n.I,inertia:n.I,tween:i.F,keyframes:i.F,spring:o.S};function s(e){"string"==typeof e.type&&(e.type=a[e.type])}},56720:(e,t,r)=>{"use strict";r.d(t,{w:()=>n});let n=(e,t,r=10)=>{let n="",i=Math.max(Math.round(t/r),2);for(let t=0;t<i;t++)n+=e(t/(i-1))+", ";return`linear(${n.substring(0,n.length-2)})`}},81282:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});var n=r(93712);let i=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"];var o=r(87167);function a(e,t){let r=!1,a=!0,s={delta:0,timestamp:0,isProcessing:!1},l=()=>r=!0,u=i.reduce((e,r)=>(e[r]=function(e,t){let r=new Set,n=new Set,i=!1,a=!1,s=new WeakSet,l={delta:0,timestamp:0,isProcessing:!1},u=0;function c(t){s.has(t)&&(d.schedule(t),e()),u++,t(l)}let d={schedule:(e,t=!1,o=!1)=>{let a=o&&i?r:n;return t&&s.add(e),a.has(e)||a.add(e),e},cancel:e=>{n.delete(e),s.delete(e)},process:e=>{if(l=e,i){a=!0;return}i=!0,[r,n]=[n,r],r.forEach(c),t&&o.f.value&&o.f.value.frameloop[t].push(u),u=0,r.clear(),i=!1,a&&(a=!1,d.process(e))}};return d}(l,t?r:void 0),e),{}),{setup:c,read:d,resolveKeyframes:f,preUpdate:h,update:p,preRender:m,render:y,postRender:g}=u,v=()=>{let i=n.c.useManualTiming?s.timestamp:performance.now();r=!1,n.c.useManualTiming||(s.delta=a?1e3/60:Math.max(Math.min(i-s.timestamp,40),1)),s.timestamp=i,s.isProcessing=!0,c.process(s),d.process(s),f.process(s),h.process(s),p.process(s),m.process(s),y.process(s),g.process(s),s.isProcessing=!1,r&&t&&(a=!1,e(v))},b=()=>{r=!0,a=!0,s.isProcessing||e(v)};return{schedule:i.reduce((e,t)=>{let n=u[t];return e[t]=(e,t=!1,i=!1)=>(r||b(),n.schedule(e,t,i)),e},{}),cancel:e=>{for(let t=0;t<i.length;t++)u[i[t]].cancel(e)},state:s,steps:u}}},71470:(e,t,r)=>{"use strict";r.d(t,{Pn:()=>o,Wi:()=>i,frameData:()=>a,yL:()=>s});var n=r(39911);let{schedule:i,cancel:o,state:a,steps:s}=(0,r(81282).Z)("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:n.Z,!0)},84289:(e,t,r)=>{"use strict";let n;r.d(t,{X:()=>s});var i=r(93712),o=r(71470);function a(){n=void 0}let s={now:()=>(void 0===n&&s.set(o.frameData.isProcessing||i.c.useManualTiming?o.frameData.timestamp:performance.now()),n),set:e=>{n=e,queueMicrotask(a)}}},7626:(e,t,r)=>{"use strict";r.d(t,{P:()=>n});let n={layout:0,mainThread:0,waapi:0}},87167:(e,t,r)=>{"use strict";r.d(t,{f:()=>n});let n={value:null,addProjectionMetrics:null}},50522:(e,t,r)=>{"use strict";r.d(t,{s:()=>c});var n=r(93712),i=r(39911),o=r(85294),a=r(17941),s=r(19069),l=r(68132),u=r(52416);function c(e,t,{clamp:r=!0,ease:c,mixer:d}={}){let f=e.length;if((0,a.k)(f===t.length,"Both input and output ranges must be the same length"),1===f)return()=>t[0];if(2===f&&t[0]===t[1])return()=>t[1];let h=e[0]===e[1];e[0]>e[f-1]&&(e=[...e].reverse(),t=[...t].reverse());let p=function(e,t,r){let a=[],s=r||n.c.mix||u.C,l=e.length-1;for(let r=0;r<l;r++){let n=s(e[r],e[r+1]);if(t){let e=Array.isArray(t)?t[r]||i.Z:t;n=(0,o.z)(e,n)}a.push(n)}return a}(t,c,d),m=p.length,y=r=>{if(h&&r<e[0])return t[0];let n=0;if(m>1)for(;n<e.length-2&&!(r<e[n+1]);n++);let i=(0,s.Y)(e[n],e[n+1],r);return p[n](i)};return r?t=>y((0,l.u)(e[0],e[f-1],t)):y}},69539:(e,t,r)=>{"use strict";r.d(t,{R:()=>i});var n=r(17640);function i(e){return(0,n.K)(e)&&"offsetHeight"in e}},52416:(e,t,r)=>{"use strict";r.d(t,{C:()=>_});var n=r(85294),i=r(17941),o=r(97125),a=r(31068),s=r(4789),l=r(42103),u=r(85963);function c(e,t,r){return(r<0&&(r+=1),r>1&&(r-=1),r<1/6)?e+(t-e)*6*r:r<.5?t:r<2/3?e+(t-e)*(2/3-r)*6:e}var d=r(25588);function f(e,t){return r=>r>0?t:e}var h=r(92844);let p=(e,t,r)=>{let n=e*e,i=r*(t*t-n)+n;return i<0?0:Math.sqrt(i)},m=[l.$,d.m,u.J],y=e=>m.find(t=>t.test(e));function g(e){let t=y(e);if((0,i.K)(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`),!t)return!1;let r=t.parse(e);return t===u.J&&(r=function({hue:e,saturation:t,lightness:r,alpha:n}){e/=360,r/=100;let i=0,o=0,a=0;if(t/=100){let n=r<.5?r*(1+t):r+t-r*t,s=2*r-n;i=c(s,n,e+1/3),o=c(s,n,e),a=c(s,n,e-1/3)}else i=o=a=r;return{red:Math.round(255*i),green:Math.round(255*o),blue:Math.round(255*a),alpha:n}}(r)),r}let v=(e,t)=>{let r=g(e),n=g(t);if(!r||!n)return f(e,t);let i={...r};return e=>(i.red=p(r.red,n.red,e),i.green=p(r.green,n.green,e),i.blue=p(r.blue,n.blue,e),i.alpha=(0,h.t)(r.alpha,n.alpha,e),d.m.transform(i))},b=new Set(["none","hidden"]);function x(e,t){return r=>(0,h.t)(e,t,r)}function w(e){return"number"==typeof e?x:"string"==typeof e?(0,o.t)(e)?f:a.$.test(e)?v:R:Array.isArray(e)?P:"object"==typeof e?a.$.test(e)?v:E:f}function P(e,t){let r=[...e],n=r.length,i=e.map((e,r)=>w(e)(e,t[r]));return e=>{for(let t=0;t<n;t++)r[t]=i[t](e);return r}}function E(e,t){let r={...e,...t},n={};for(let i in r)void 0!==e[i]&&void 0!==t[i]&&(n[i]=w(e[i])(e[i],t[i]));return e=>{for(let t in n)r[t]=n[t](e);return r}}let R=(e,t)=>{let r=s.P.createTransformer(t),o=(0,s.V)(e),a=(0,s.V)(t);return o.indexes.var.length===a.indexes.var.length&&o.indexes.color.length===a.indexes.color.length&&o.indexes.number.length>=a.indexes.number.length?b.has(e)&&!a.values.length||b.has(t)&&!o.values.length?function(e,t){return b.has(e)?r=>r<=0?e:t:r=>r>=1?t:e}(e,t):(0,n.z)(P(function(e,t){let r=[],n={color:0,var:0,number:0};for(let i=0;i<t.values.length;i++){let o=t.types[i],a=e.indexes[o][n[o]],s=e.values[a]??0;r[i]=s,n[o]++}return r}(o,a),a.values),r):((0,i.K)(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),f(e,t))};function _(e,t,r){return"number"==typeof e&&"number"==typeof t&&"number"==typeof r?(0,h.t)(e,t,r):w(e)(e,t)}},92844:(e,t,r)=>{"use strict";r.d(t,{t:()=>n});let n=(e,t,r)=>e+(t-e)*r},19073:(e,t,r)=>{"use strict";r.d(t,{BX:()=>c,S1:()=>l});var n=r(19691),i=r(46390),o=r(84289),a=r(71470);let s=e=>!isNaN(parseFloat(e)),l={current:void 0};class u{constructor(e,t={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(e,t=!0)=>{let r=o.X.now();if(this.updatedAt!==r&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let e of this.dependents)e.dirty();t&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}setCurrent(e){this.current=e,this.updatedAt=o.X.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=s(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new n.L);let r=this.events[e].add(t);return"change"===e?()=>{r(),a.Wi.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,r){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-r}jump(e,t=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return l.current&&l.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){let e=o.X.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let t=Math.min(this.updatedAt-this.prevUpdatedAt,30);return(0,i.R)(parseFloat(this.current)-parseFloat(this.prevFrameValue),t)}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function c(e,t){return new u(e,t)}},42103:(e,t,r)=>{"use strict";r.d(t,{$:()=>i});var n=r(25588);let i={test:(0,r(84551).i)("#"),parse:function(e){let t="",r="",n="",i="";return e.length>5?(t=e.substring(1,3),r=e.substring(3,5),n=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),r=e.substring(2,3),n=e.substring(3,4),i=e.substring(4,5),t+=t,r+=r,n+=n,i+=i),{red:parseInt(t,16),green:parseInt(r,16),blue:parseInt(n,16),alpha:i?parseInt(i,16)/255:1}},transform:n.m.transform}},85963:(e,t,r)=>{"use strict";r.d(t,{J:()=>s});var n=r(74334),i=r(48663),o=r(30539),a=r(84551);let s={test:(0,a.i)("hsl","hue"),parse:(0,a.d)("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:r,alpha:a=1})=>"hsla("+Math.round(e)+", "+i.aQ.transform((0,o.N)(t))+", "+i.aQ.transform((0,o.N)(r))+", "+(0,o.N)(n.Fq.transform(a))+")"}},31068:(e,t,r)=>{"use strict";r.d(t,{$:()=>a});var n=r(42103),i=r(85963),o=r(25588);let a={test:e=>o.m.test(e)||n.$.test(e)||i.J.test(e),parse:e=>o.m.test(e)?o.m.parse(e):i.J.test(e)?i.J.parse(e):n.$.parse(e),transform:e=>"string"==typeof e?e:e.hasOwnProperty("red")?o.m.transform(e):i.J.transform(e)}},25588:(e,t,r)=>{"use strict";r.d(t,{m:()=>u});var n=r(68132),i=r(74334),o=r(30539),a=r(84551);let s=e=>(0,n.u)(0,255,e),l={...i.Rx,transform:e=>Math.round(s(e))},u={test:(0,a.i)("rgb","red"),parse:(0,a.d)("red","green","blue"),transform:({red:e,green:t,blue:r,alpha:n=1})=>"rgba("+l.transform(e)+", "+l.transform(t)+", "+l.transform(r)+", "+(0,o.N)(i.Fq.transform(n))+")"}},84551:(e,t,r)=>{"use strict";r.d(t,{i:()=>o,d:()=>a});var n=r(1849);let i=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,o=(e,t)=>r=>!!("string"==typeof r&&i.test(r)&&r.startsWith(e)||t&&null!=r&&Object.prototype.hasOwnProperty.call(r,t)),a=(e,t,r)=>i=>{if("string"!=typeof i)return i;let[o,a,s,l]=i.match(n.K);return{[e]:parseFloat(o),[t]:parseFloat(a),[r]:parseFloat(s),alpha:void 0!==l?parseFloat(l):1}}},4789:(e,t,r)=>{"use strict";r.d(t,{V:()=>c,P:()=>p});var n=r(31068);let i=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;var o=r(1849),a=r(30539);let s="number",l="color",u=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function c(e){let t=e.toString(),r=[],i={color:[],number:[],var:[]},o=[],a=0,c=t.replace(u,e=>(n.$.test(e)?(i.color.push(a),o.push(l),r.push(n.$.parse(e))):e.startsWith("var(")?(i.var.push(a),o.push("var"),r.push(e)):(i.number.push(a),o.push(s),r.push(parseFloat(e))),++a,"${}")).split("${}");return{values:r,split:c,indexes:i,types:o}}function d(e){return c(e).values}function f(e){let{split:t,types:r}=c(e),i=t.length;return e=>{let o="";for(let u=0;u<i;u++)if(o+=t[u],void 0!==e[u]){let t=r[u];t===s?o+=(0,a.N)(e[u]):t===l?o+=n.$.transform(e[u]):o+=e[u]}return o}}let h=e=>"number"==typeof e?0:e,p={test:function(e){return isNaN(e)&&"string"==typeof e&&(e.match(o.K)?.length||0)+(e.match(i)?.length||0)>0},parse:d,createTransformer:f,getAnimatableNone:function(e){let t=d(e);return f(e)(t.map(h))}}},74334:(e,t,r)=>{"use strict";r.d(t,{Fq:()=>o,Rx:()=>i,bA:()=>a});var n=r(68132);let i={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},o={...i,transform:e=>(0,n.u)(0,1,e)},a={...i,default:1}},48663:(e,t,r)=>{"use strict";r.d(t,{$C:()=>u,RW:()=>i,aQ:()=>o,px:()=>a,vh:()=>s,vw:()=>l});let n=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),i=n("deg"),o=n("%"),a=n("px"),s=n("vh"),l=n("vw"),u={...o,parse:e=>o.parse(e)/100,transform:e=>o.transform(100*e)}},1849:(e,t,r)=>{"use strict";r.d(t,{K:()=>n});let n=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu},30539:(e,t,r)=>{"use strict";r.d(t,{N:()=>n});let n=e=>Math.round(1e5*e)/1e5},45290:(e,t,r)=>{"use strict";r.d(t,{i:()=>n});let n=e=>!!(e&&e.getVelocity)},65518:(e,t,r)=>{"use strict";function n(e,t){-1===e.indexOf(t)&&e.push(t)}function i(e,t){let r=e.indexOf(t);r>-1&&e.splice(r,1)}r.d(t,{cl:()=>i,y4:()=>n})},68132:(e,t,r)=>{"use strict";r.d(t,{u:()=>n});let n=(e,t,r)=>r>t?t:r<e?e:r},48219:(e,t,r)=>{"use strict";r.d(t,{L:()=>i});var n=r(27597);let i=e=>(e*=2)<1?.5*(0,n.G2)(e):.5*(2-Math.pow(2,-10*(e-1)))},27597:(e,t,r)=>{"use strict";r.d(t,{CG:()=>a,G2:()=>s,XL:()=>l});var n=r(75631),i=r(53301),o=r(61553);let a=(0,n._)(.33,1.53,.69,.99),s=(0,o.M)(a),l=(0,i.o)(s)},34946:(e,t,r)=>{"use strict";r.d(t,{Bn:()=>a,X7:()=>s,Z7:()=>o});var n=r(53301),i=r(61553);let o=e=>1-Math.sin(Math.acos(e)),a=(0,i.M)(o),s=(0,n.o)(o)},75631:(e,t,r)=>{"use strict";r.d(t,{_:()=>o});var n=r(39911);let i=(e,t,r)=>(((1-3*r+3*t)*e+(3*r-6*t))*e+3*t)*e;function o(e,t,r,o){if(e===t&&r===o)return n.Z;let a=t=>(function(e,t,r,n,o){let a,s;let l=0;do(a=i(s=t+(r-t)/2,n,o)-e)>0?r=s:t=s;while(Math.abs(a)>1e-7&&++l<12);return s})(t,0,1,e,r);return e=>0===e||1===e?e:i(a(e),t,o)}},53301:(e,t,r)=>{"use strict";r.d(t,{o:()=>n});let n=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2},61553:(e,t,r)=>{"use strict";r.d(t,{M:()=>n});let n=e=>t=>1-e(1-t)},71683:(e,t,r)=>{"use strict";r.d(t,{q:()=>n});let n=e=>Array.isArray(e)&&"number"==typeof e[0]},17941:(e,t,r)=>{"use strict";r.d(t,{K:()=>n,k:()=>i});let n=()=>{},i=()=>{}},93712:(e,t,r)=>{"use strict";r.d(t,{c:()=>n});let n={}},17640:(e,t,r)=>{"use strict";function n(e){return"object"==typeof e&&null!==e}r.d(t,{K:()=>n})},39911:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=e=>e},85294:(e,t,r)=>{"use strict";r.d(t,{z:()=>i});let n=(e,t)=>r=>t(e(r)),i=(...e)=>e.reduce(n)},19069:(e,t,r)=>{"use strict";r.d(t,{Y:()=>n});let n=(e,t,r)=>{let n=t-e;return 0===n?1:(r-e)/n}},19691:(e,t,r)=>{"use strict";r.d(t,{L:()=>i});var n=r(65518);class i{constructor(){this.subscriptions=[]}add(e){return(0,n.y4)(this.subscriptions,e),()=>(0,n.cl)(this.subscriptions,e)}notify(e,t,r){let n=this.subscriptions.length;if(n){if(1===n)this.subscriptions[0](e,t,r);else for(let i=0;i<n;i++){let n=this.subscriptions[i];n&&n(e,t,r)}}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}},51612:(e,t,r)=>{"use strict";r.d(t,{X:()=>i,w:()=>n});let n=e=>1e3*e,i=e=>e/1e3},46390:(e,t,r)=>{"use strict";function n(e,t){return t?1e3/t*e:0}r.d(t,{R:()=>n})},14831:(e,t,r)=>{"use strict";r.d(t,{F:()=>a});var n=r(17577),i=n.createContext(void 0),o={setTheme:e=>{},themes:[]},a=()=>{var e;return null!=(e=n.useContext(i))?e:o}},11726:(e,t,r)=>{"use strict";function n(e,t){if(!Object.prototype.hasOwnProperty.call(e,t))throw TypeError("attempted to use private field on non-instance");return e}r.r(t),r.d(t,{_:()=>n,_class_private_field_loose_base:()=>n})},48610:(e,t,r)=>{"use strict";r.r(t),r.d(t,{_:()=>i,_class_private_field_loose_key:()=>i});var n=0;function i(e){return"__private_"+n+++"_"+e}},98052:(e,t,r)=>{"use strict";function n(e){return e&&e.__esModule?e:{default:e}}r.r(t),r.d(t,{_:()=>n,_interop_require_default:()=>n})},31667:(e,t,r)=>{"use strict";function n(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(n=function(e){return e?r:t})(e)}function i(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=n(t);if(r&&r.has(e))return r.get(e);var i={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var s=o?Object.getOwnPropertyDescriptor(e,a):null;s&&(s.get||s.set)?Object.defineProperty(i,a,s):i[a]=e[a]}return i.default=e,r&&r.set(e,i),i}r.r(t),r.d(t,{_:()=>i,_interop_require_wildcard:()=>i})},85999:(e,t,r)=>{"use strict";r.d(t,{Am:()=>g,x7:()=>w});var n=r(17577),i=r(60962),o=e=>{switch(e){case"success":return l;case"info":return c;case"warning":return u;case"error":return d;default:return null}},a=Array(12).fill(0),s=({visible:e,className:t})=>n.createElement("div",{className:["sonner-loading-wrapper",t].filter(Boolean).join(" "),"data-visible":e},n.createElement("div",{className:"sonner-spinner"},a.map((e,t)=>n.createElement("div",{className:"sonner-loading-bar",key:`spinner-bar-${t}`})))),l=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},n.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),u=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},n.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),c=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},n.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),d=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},n.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),f=n.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},n.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),n.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"})),h=()=>{let[e,t]=n.useState(document.hidden);return n.useEffect(()=>{let e=()=>{t(document.hidden)};return document.addEventListener("visibilitychange",e),()=>window.removeEventListener("visibilitychange",e)},[]),e},p=1,m=new class{constructor(){this.subscribe=e=>(this.subscribers.push(e),()=>{let t=this.subscribers.indexOf(e);this.subscribers.splice(t,1)}),this.publish=e=>{this.subscribers.forEach(t=>t(e))},this.addToast=e=>{this.publish(e),this.toasts=[...this.toasts,e]},this.create=e=>{var t;let{message:r,...n}=e,i="number"==typeof(null==e?void 0:e.id)||(null==(t=e.id)?void 0:t.length)>0?e.id:p++,o=this.toasts.find(e=>e.id===i),a=void 0===e.dismissible||e.dismissible;return this.dismissedToasts.has(i)&&this.dismissedToasts.delete(i),o?this.toasts=this.toasts.map(t=>t.id===i?(this.publish({...t,...e,id:i,title:r}),{...t,...e,id:i,dismissible:a,title:r}):t):this.addToast({title:r,...n,dismissible:a,id:i}),i},this.dismiss=e=>(this.dismissedToasts.add(e),e||this.toasts.forEach(e=>{this.subscribers.forEach(t=>t({id:e.id,dismiss:!0}))}),this.subscribers.forEach(t=>t({id:e,dismiss:!0})),e),this.message=(e,t)=>this.create({...t,message:e}),this.error=(e,t)=>this.create({...t,message:e,type:"error"}),this.success=(e,t)=>this.create({...t,type:"success",message:e}),this.info=(e,t)=>this.create({...t,type:"info",message:e}),this.warning=(e,t)=>this.create({...t,type:"warning",message:e}),this.loading=(e,t)=>this.create({...t,type:"loading",message:e}),this.promise=(e,t)=>{let r;if(!t)return;void 0!==t.loading&&(r=this.create({...t,promise:e,type:"loading",message:t.loading,description:"function"!=typeof t.description?t.description:void 0}));let i=e instanceof Promise?e:e(),o=void 0!==r,a,s=i.then(async e=>{if(a=["resolve",e],n.isValidElement(e))o=!1,this.create({id:r,type:"default",message:e});else if(y(e)&&!e.ok){o=!1;let n="function"==typeof t.error?await t.error(`HTTP error! status: ${e.status}`):t.error,i="function"==typeof t.description?await t.description(`HTTP error! status: ${e.status}`):t.description;this.create({id:r,type:"error",message:n,description:i})}else if(void 0!==t.success){o=!1;let n="function"==typeof t.success?await t.success(e):t.success,i="function"==typeof t.description?await t.description(e):t.description;this.create({id:r,type:"success",message:n,description:i})}}).catch(async e=>{if(a=["reject",e],void 0!==t.error){o=!1;let n="function"==typeof t.error?await t.error(e):t.error,i="function"==typeof t.description?await t.description(e):t.description;this.create({id:r,type:"error",message:n,description:i})}}).finally(()=>{var e;o&&(this.dismiss(r),r=void 0),null==(e=t.finally)||e.call(t)}),l=()=>new Promise((e,t)=>s.then(()=>"reject"===a[0]?t(a[1]):e(a[1])).catch(t));return"string"!=typeof r&&"number"!=typeof r?{unwrap:l}:Object.assign(r,{unwrap:l})},this.custom=(e,t)=>{let r=(null==t?void 0:t.id)||p++;return this.create({jsx:e(r),id:r,...t}),r},this.getActiveToasts=()=>this.toasts.filter(e=>!this.dismissedToasts.has(e.id)),this.subscribers=[],this.toasts=[],this.dismissedToasts=new Set}},y=e=>e&&"object"==typeof e&&"ok"in e&&"boolean"==typeof e.ok&&"status"in e&&"number"==typeof e.status,g=Object.assign((e,t)=>{let r=(null==t?void 0:t.id)||p++;return m.addToast({title:e,...t,id:r}),r},{success:m.success,info:m.info,warning:m.warning,error:m.error,custom:m.custom,message:m.message,promise:m.promise,dismiss:m.dismiss,loading:m.loading},{getHistory:()=>m.toasts,getToasts:()=>m.getActiveToasts()});function v(e){return void 0!==e.label}function b(...e){return e.filter(Boolean).join(" ")}!function(e,{insertAt:t}={}){if(!e||"undefined"==typeof document)return;let r=document.head||document.getElementsByTagName("head")[0],n=document.createElement("style");n.type="text/css","top"===t&&r.firstChild?r.insertBefore(n,r.firstChild):r.appendChild(n),n.styleSheet?n.styleSheet.cssText=e:n.appendChild(document.createTextNode(e))}(`:where(html[dir="ltr"]),:where([data-sonner-toaster][dir="ltr"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir="rtl"]),:where([data-sonner-toaster][dir="rtl"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999;transition:transform .4s ease}:where([data-sonner-toaster][data-lifted="true"]){transform:translateY(-10px)}@media (hover: none) and (pointer: coarse){:where([data-sonner-toaster][data-lifted="true"]){transform:none}}:where([data-sonner-toaster][data-x-position="right"]){right:var(--offset-right)}:where([data-sonner-toaster][data-x-position="left"]){left:var(--offset-left)}:where([data-sonner-toaster][data-x-position="center"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position="top"]){top:var(--offset-top)}:where([data-sonner-toaster][data-y-position="bottom"]){bottom:var(--offset-bottom)}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled="true"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position="top"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position="bottom"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise="true"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme="dark"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast] [data-close-button]{background:var(--gray1)}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled="true"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping="true"]):before{content:"";position:absolute;left:-50%;right:-50%;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position="top"][data-swiping="true"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position="bottom"][data-swiping="true"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping="false"][data-removed="true"]):before{content:"";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:"";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted="true"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded="false"][data-front="false"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded="false"][data-front="false"][data-styled="true"])>*{opacity:0}:where([data-sonner-toast][data-visible="false"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted="true"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed="true"][data-front="true"][data-swipe-out="false"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="false"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed="true"][data-front="false"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y, 0px)) translate(var(--swipe-amount-x, 0px));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{0%{transform:var(--y) translate(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translate(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{0%{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-bg-hover: hsl(0, 0%, 12%);--normal-border: hsl(0, 0%, 20%);--normal-border-hover: hsl(0, 0%, 25%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}
`);var x=e=>{var t,r,i,a,l,u,c,d,p,m,y;let{invert:g,toast:x,unstyled:w,interacting:P,setHeights:E,visibleToasts:R,heights:_,index:S,toasts:T,expanded:j,removeToast:O,defaultRichColors:C,closeButton:M,style:A,cancelButtonStyle:D,actionButtonStyle:k,className:N="",descriptionClassName:L="",duration:I,position:F,gap:U,loadingIcon:V,expandByDefault:B,classNames:W,icons:H,closeButtonAriaLabel:z="Close toast",pauseWhenPageIsHidden:$}=e,[X,q]=n.useState(null),[K,G]=n.useState(null),[Y,Q]=n.useState(!1),[Z,J]=n.useState(!1),[ee,et]=n.useState(!1),[er,en]=n.useState(!1),[ei,eo]=n.useState(!1),[ea,es]=n.useState(0),[el,eu]=n.useState(0),ec=n.useRef(x.duration||I||4e3),ed=n.useRef(null),ef=n.useRef(null),eh=0===S,ep=S+1<=R,em=x.type,ey=!1!==x.dismissible,eg=x.className||"",ev=x.descriptionClassName||"",eb=n.useMemo(()=>_.findIndex(e=>e.toastId===x.id)||0,[_,x.id]),ex=n.useMemo(()=>{var e;return null!=(e=x.closeButton)?e:M},[x.closeButton,M]),ew=n.useMemo(()=>x.duration||I||4e3,[x.duration,I]),eP=n.useRef(0),eE=n.useRef(0),eR=n.useRef(0),e_=n.useRef(null),[eS,eT]=F.split("-"),ej=n.useMemo(()=>_.reduce((e,t,r)=>r>=eb?e:e+t.height,0),[_,eb]),eO=h(),eC=x.invert||g,eM="loading"===em;eE.current=n.useMemo(()=>eb*U+ej,[eb,ej]),n.useEffect(()=>{ec.current=ew},[ew]),n.useEffect(()=>{Q(!0)},[]),n.useEffect(()=>{let e=ef.current;if(e){let t=e.getBoundingClientRect().height;return eu(t),E(e=>[{toastId:x.id,height:t,position:x.position},...e]),()=>E(e=>e.filter(e=>e.toastId!==x.id))}},[E,x.id]),n.useLayoutEffect(()=>{if(!Y)return;let e=ef.current,t=e.style.height;e.style.height="auto";let r=e.getBoundingClientRect().height;e.style.height=t,eu(r),E(e=>e.find(e=>e.toastId===x.id)?e.map(e=>e.toastId===x.id?{...e,height:r}:e):[{toastId:x.id,height:r,position:x.position},...e])},[Y,x.title,x.description,E,x.id]);let eA=n.useCallback(()=>{J(!0),es(eE.current),E(e=>e.filter(e=>e.toastId!==x.id)),setTimeout(()=>{O(x)},200)},[x,O,E,eE]);return n.useEffect(()=>{let e;if((!x.promise||"loading"!==em)&&x.duration!==1/0&&"loading"!==x.type)return j||P||$&&eO?(()=>{if(eR.current<eP.current){let e=new Date().getTime()-eP.current;ec.current=ec.current-e}eR.current=new Date().getTime()})():ec.current!==1/0&&(eP.current=new Date().getTime(),e=setTimeout(()=>{var e;null==(e=x.onAutoClose)||e.call(x,x),eA()},ec.current)),()=>clearTimeout(e)},[j,P,x,em,$,eO,eA]),n.useEffect(()=>{x.delete&&eA()},[eA,x.delete]),n.createElement("li",{tabIndex:0,ref:ef,className:b(N,eg,null==W?void 0:W.toast,null==(t=null==x?void 0:x.classNames)?void 0:t.toast,null==W?void 0:W.default,null==W?void 0:W[em],null==(r=null==x?void 0:x.classNames)?void 0:r[em]),"data-sonner-toast":"","data-rich-colors":null!=(i=x.richColors)?i:C,"data-styled":!(x.jsx||x.unstyled||w),"data-mounted":Y,"data-promise":!!x.promise,"data-swiped":ei,"data-removed":Z,"data-visible":ep,"data-y-position":eS,"data-x-position":eT,"data-index":S,"data-front":eh,"data-swiping":ee,"data-dismissible":ey,"data-type":em,"data-invert":eC,"data-swipe-out":er,"data-swipe-direction":K,"data-expanded":!!(j||B&&Y),style:{"--index":S,"--toasts-before":S,"--z-index":T.length-S,"--offset":`${Z?ea:eE.current}px`,"--initial-height":B?"auto":`${el}px`,...A,...x.style},onDragEnd:()=>{et(!1),q(null),e_.current=null},onPointerDown:e=>{eM||!ey||(ed.current=new Date,es(eE.current),e.target.setPointerCapture(e.pointerId),"BUTTON"!==e.target.tagName&&(et(!0),e_.current={x:e.clientX,y:e.clientY}))},onPointerUp:()=>{var e,t,r,n;if(er||!ey)return;e_.current=null;let i=Number((null==(e=ef.current)?void 0:e.style.getPropertyValue("--swipe-amount-x").replace("px",""))||0),o=Number((null==(t=ef.current)?void 0:t.style.getPropertyValue("--swipe-amount-y").replace("px",""))||0),a=new Date().getTime()-(null==(r=ed.current)?void 0:r.getTime()),s="x"===X?i:o;if(Math.abs(s)>=20||Math.abs(s)/a>.11){es(eE.current),null==(n=x.onDismiss)||n.call(x,x),G("x"===X?i>0?"right":"left":o>0?"down":"up"),eA(),en(!0),eo(!1);return}et(!1),q(null)},onPointerMove:t=>{var r,n,i,o;if(!e_.current||!ey||(null==(r=window.getSelection())?void 0:r.toString().length)>0)return;let a=t.clientY-e_.current.y,s=t.clientX-e_.current.x,l=null!=(n=e.swipeDirections)?n:function(e){let[t,r]=e.split("-"),n=[];return t&&n.push(t),r&&n.push(r),n}(F);!X&&(Math.abs(s)>1||Math.abs(a)>1)&&q(Math.abs(s)>Math.abs(a)?"x":"y");let u={x:0,y:0};"y"===X?(l.includes("top")||l.includes("bottom"))&&(l.includes("top")&&a<0||l.includes("bottom")&&a>0)&&(u.y=a):"x"===X&&(l.includes("left")||l.includes("right"))&&(l.includes("left")&&s<0||l.includes("right")&&s>0)&&(u.x=s),(Math.abs(u.x)>0||Math.abs(u.y)>0)&&eo(!0),null==(i=ef.current)||i.style.setProperty("--swipe-amount-x",`${u.x}px`),null==(o=ef.current)||o.style.setProperty("--swipe-amount-y",`${u.y}px`)}},ex&&!x.jsx?n.createElement("button",{"aria-label":z,"data-disabled":eM,"data-close-button":!0,onClick:eM||!ey?()=>{}:()=>{var e;eA(),null==(e=x.onDismiss)||e.call(x,x)},className:b(null==W?void 0:W.closeButton,null==(a=null==x?void 0:x.classNames)?void 0:a.closeButton)},null!=(l=null==H?void 0:H.close)?l:f):null,x.jsx||(0,n.isValidElement)(x.title)?x.jsx?x.jsx:"function"==typeof x.title?x.title():x.title:n.createElement(n.Fragment,null,em||x.icon||x.promise?n.createElement("div",{"data-icon":"",className:b(null==W?void 0:W.icon,null==(u=null==x?void 0:x.classNames)?void 0:u.icon)},x.promise||"loading"===x.type&&!x.icon?x.icon||function(){var e,t,r;return null!=H&&H.loading?n.createElement("div",{className:b(null==W?void 0:W.loader,null==(e=null==x?void 0:x.classNames)?void 0:e.loader,"sonner-loader"),"data-visible":"loading"===em},H.loading):V?n.createElement("div",{className:b(null==W?void 0:W.loader,null==(t=null==x?void 0:x.classNames)?void 0:t.loader,"sonner-loader"),"data-visible":"loading"===em},V):n.createElement(s,{className:b(null==W?void 0:W.loader,null==(r=null==x?void 0:x.classNames)?void 0:r.loader),visible:"loading"===em})}():null,"loading"!==x.type?x.icon||(null==H?void 0:H[em])||o(em):null):null,n.createElement("div",{"data-content":"",className:b(null==W?void 0:W.content,null==(c=null==x?void 0:x.classNames)?void 0:c.content)},n.createElement("div",{"data-title":"",className:b(null==W?void 0:W.title,null==(d=null==x?void 0:x.classNames)?void 0:d.title)},"function"==typeof x.title?x.title():x.title),x.description?n.createElement("div",{"data-description":"",className:b(L,ev,null==W?void 0:W.description,null==(p=null==x?void 0:x.classNames)?void 0:p.description)},"function"==typeof x.description?x.description():x.description):null),(0,n.isValidElement)(x.cancel)?x.cancel:x.cancel&&v(x.cancel)?n.createElement("button",{"data-button":!0,"data-cancel":!0,style:x.cancelButtonStyle||D,onClick:e=>{var t,r;v(x.cancel)&&ey&&(null==(r=(t=x.cancel).onClick)||r.call(t,e),eA())},className:b(null==W?void 0:W.cancelButton,null==(m=null==x?void 0:x.classNames)?void 0:m.cancelButton)},x.cancel.label):null,(0,n.isValidElement)(x.action)?x.action:x.action&&v(x.action)?n.createElement("button",{"data-button":!0,"data-action":!0,style:x.actionButtonStyle||k,onClick:e=>{var t,r;v(x.action)&&(null==(r=(t=x.action).onClick)||r.call(t,e),e.defaultPrevented||eA())},className:b(null==W?void 0:W.actionButton,null==(y=null==x?void 0:x.classNames)?void 0:y.actionButton)},x.action.label):null))},w=(0,n.forwardRef)(function(e,t){let{invert:r,position:o="bottom-right",hotkey:a=["altKey","KeyT"],expand:s,closeButton:l,className:u,offset:c,mobileOffset:d,theme:f="light",richColors:h,duration:p,style:y,visibleToasts:g=3,toastOptions:v,dir:b="ltr",gap:w=14,loadingIcon:P,icons:E,containerAriaLabel:R="Notifications",pauseWhenPageIsHidden:_}=e,[S,T]=n.useState([]),j=n.useMemo(()=>Array.from(new Set([o].concat(S.filter(e=>e.position).map(e=>e.position)))),[S,o]),[O,C]=n.useState([]),[M,A]=n.useState(!1),[D,k]=n.useState(!1),[N,L]=n.useState("system"!==f?f:"light"),I=n.useRef(null),F=a.join("+").replace(/Key/g,"").replace(/Digit/g,""),U=n.useRef(null),V=n.useRef(!1),B=n.useCallback(e=>{T(t=>{var r;return null!=(r=t.find(t=>t.id===e.id))&&r.delete||m.dismiss(e.id),t.filter(({id:t})=>t!==e.id)})},[]);return n.useEffect(()=>m.subscribe(e=>{if(e.dismiss){T(t=>t.map(t=>t.id===e.id?{...t,delete:!0}:t));return}setTimeout(()=>{i.flushSync(()=>{T(t=>{let r=t.findIndex(t=>t.id===e.id);return -1!==r?[...t.slice(0,r),{...t[r],...e},...t.slice(r+1)]:[e,...t]})})})}),[]),n.useEffect(()=>{if("system"!==f){L(f);return}"system"===f&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?L("dark"):L("light"))},[f]),n.useEffect(()=>{S.length<=1&&A(!1)},[S]),n.useEffect(()=>{let e=e=>{var t,r;a.every(t=>e[t]||e.code===t)&&(A(!0),null==(t=I.current)||t.focus()),"Escape"===e.code&&(document.activeElement===I.current||null!=(r=I.current)&&r.contains(document.activeElement))&&A(!1)};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[a]),n.useEffect(()=>{if(I.current)return()=>{U.current&&(U.current.focus({preventScroll:!0}),U.current=null,V.current=!1)}},[I.current]),n.createElement("section",{ref:t,"aria-label":`${R} ${F}`,tabIndex:-1,"aria-live":"polite","aria-relevant":"additions text","aria-atomic":"false",suppressHydrationWarning:!0},j.map((t,i)=>{var o;let a;let[f,m]=t.split("-");return S.length?n.createElement("ol",{key:t,dir:"auto"===b?"ltr":b,tabIndex:-1,ref:I,className:u,"data-sonner-toaster":!0,"data-theme":N,"data-y-position":f,"data-lifted":M&&S.length>1&&!s,"data-x-position":m,style:{"--front-toast-height":`${(null==(o=O[0])?void 0:o.height)||0}px`,"--width":"356px","--gap":`${w}px`,...y,...(a={},[c,d].forEach((e,t)=>{let r=1===t,n=r?"--mobile-offset":"--offset",i=r?"16px":"32px";function o(e){["top","right","bottom","left"].forEach(t=>{a[`${n}-${t}`]="number"==typeof e?`${e}px`:e})}"number"==typeof e||"string"==typeof e?o(e):"object"==typeof e?["top","right","bottom","left"].forEach(t=>{void 0===e[t]?a[`${n}-${t}`]=i:a[`${n}-${t}`]="number"==typeof e[t]?`${e[t]}px`:e[t]}):o(i)}),a)},onBlur:e=>{V.current&&!e.currentTarget.contains(e.relatedTarget)&&(V.current=!1,U.current&&(U.current.focus({preventScroll:!0}),U.current=null))},onFocus:e=>{e.target instanceof HTMLElement&&"false"===e.target.dataset.dismissible||V.current||(V.current=!0,U.current=e.relatedTarget)},onMouseEnter:()=>A(!0),onMouseMove:()=>A(!0),onMouseLeave:()=>{D||A(!1)},onDragEnd:()=>A(!1),onPointerDown:e=>{e.target instanceof HTMLElement&&"false"===e.target.dataset.dismissible||k(!0)},onPointerUp:()=>k(!1)},S.filter(e=>!e.position&&0===i||e.position===t).map((i,o)=>{var a,u;return n.createElement(x,{key:i.id,icons:E,index:o,toast:i,defaultRichColors:h,duration:null!=(a=null==v?void 0:v.duration)?a:p,className:null==v?void 0:v.className,descriptionClassName:null==v?void 0:v.descriptionClassName,invert:r,visibleToasts:g,closeButton:null!=(u=null==v?void 0:v.closeButton)?u:l,interacting:D,position:t,style:null==v?void 0:v.style,unstyled:null==v?void 0:v.unstyled,classNames:null==v?void 0:v.classNames,cancelButtonStyle:null==v?void 0:v.cancelButtonStyle,actionButtonStyle:null==v?void 0:v.actionButtonStyle,removeToast:B,toasts:S.filter(e=>e.position==i.position),heights:O.filter(e=>e.position==i.position),setHeights:C,expandByDefault:s,gap:w,loadingIcon:P,expanded:M,pauseWhenPageIsHidden:_,swipeDirections:e.swipeDirections})})):null}))})},31009:(e,t,r)=>{"use strict";r.d(t,{m6:()=>G});let n=e=>{let t=s(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),i(r,t)||a(e)},getConflictingClassGroupIds:(e,t)=>{let i=r[e]||[];return t&&n[e]?[...i,...n[e]]:i}}},i=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],n=t.nextPart.get(r),o=n?i(e.slice(1),n):void 0;if(o)return o;if(0===t.validators.length)return;let a=e.join("-");return t.validators.find(({validator:e})=>e(a))?.classGroupId},o=/^\[(.+)\]$/,a=e=>{if(o.test(e)){let t=o.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},s=e=>{let{theme:t,prefix:r}=e,n={nextPart:new Map,validators:[]};return d(Object.entries(e.classGroups),r).forEach(([e,r])=>{l(r,n,e,t)}),n},l=(e,t,r,n)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:u(t,e)).classGroupId=r;return}if("function"==typeof e){if(c(e)){l(e(n),t,r,n);return}t.validators.push({validator:e,classGroupId:r});return}Object.entries(e).forEach(([e,i])=>{l(i,u(t,e),r,n)})})},u=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},c=e=>e.isThemeGetter,d=(e,t)=>t?e.map(([e,r])=>[e,r.map(e=>"string"==typeof e?t+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map(([e,r])=>[t+e,r])):e)]):e,f=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,n=new Map,i=(i,o)=>{r.set(i,o),++t>e&&(t=0,n=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=n.get(e))?(i(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):i(e,t)}}},h=e=>{let{separator:t,experimentalParseClassName:r}=e,n=1===t.length,i=t[0],o=t.length,a=e=>{let r;let a=[],s=0,l=0;for(let u=0;u<e.length;u++){let c=e[u];if(0===s){if(c===i&&(n||e.slice(u,u+o)===t)){a.push(e.slice(l,u)),l=u+o;continue}if("/"===c){r=u;continue}}"["===c?s++:"]"===c&&s--}let u=0===a.length?e:e.substring(l),c=u.startsWith("!"),d=c?u.substring(1):u;return{modifiers:a,hasImportantModifier:c,baseClassName:d,maybePostfixModifierPosition:r&&r>l?r-l:void 0}};return r?e=>r({className:e,parseClassName:a}):a},p=e=>{if(e.length<=1)return e;let t=[],r=[];return e.forEach(e=>{"["===e[0]?(t.push(...r.sort(),e),r=[]):r.push(e)}),t.push(...r.sort()),t},m=e=>({cache:f(e.cacheSize),parseClassName:h(e),...n(e)}),y=/\s+/,g=(e,t)=>{let{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:i}=t,o=[],a=e.trim().split(y),s="";for(let e=a.length-1;e>=0;e-=1){let t=a[e],{modifiers:l,hasImportantModifier:u,baseClassName:c,maybePostfixModifierPosition:d}=r(t),f=!!d,h=n(f?c.substring(0,d):c);if(!h){if(!f||!(h=n(c))){s=t+(s.length>0?" "+s:s);continue}f=!1}let m=p(l).join(":"),y=u?m+"!":m,g=y+h;if(o.includes(g))continue;o.push(g);let v=i(h,f);for(let e=0;e<v.length;++e){let t=v[e];o.push(y+t)}s=t+(s.length>0?" "+s:s)}return s};function v(){let e,t,r=0,n="";for(;r<arguments.length;)(e=arguments[r++])&&(t=b(e))&&(n&&(n+=" "),n+=t);return n}let b=e=>{let t;if("string"==typeof e)return e;let r="";for(let n=0;n<e.length;n++)e[n]&&(t=b(e[n]))&&(r&&(r+=" "),r+=t);return r},x=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},w=/^\[(?:([a-z-]+):)?(.+)\]$/i,P=/^\d+\/\d+$/,E=new Set(["px","full","screen"]),R=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,_=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,S=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,T=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,j=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,O=e=>M(e)||E.has(e)||P.test(e),C=e=>z(e,"length",$),M=e=>!!e&&!Number.isNaN(Number(e)),A=e=>z(e,"number",M),D=e=>!!e&&Number.isInteger(Number(e)),k=e=>e.endsWith("%")&&M(e.slice(0,-1)),N=e=>w.test(e),L=e=>R.test(e),I=new Set(["length","size","percentage"]),F=e=>z(e,I,X),U=e=>z(e,"position",X),V=new Set(["image","url"]),B=e=>z(e,V,K),W=e=>z(e,"",q),H=()=>!0,z=(e,t,r)=>{let n=w.exec(e);return!!n&&(n[1]?"string"==typeof t?n[1]===t:t.has(n[1]):r(n[2]))},$=e=>_.test(e)&&!S.test(e),X=()=>!1,q=e=>T.test(e),K=e=>j.test(e);Symbol.toStringTag;let G=function(e,...t){let r,n,i;let o=function(s){return n=(r=m(t.reduce((e,t)=>t(e),e()))).cache.get,i=r.cache.set,o=a,a(s)};function a(e){let t=n(e);if(t)return t;let o=g(e,r);return i(e,o),o}return function(){return o(v.apply(null,arguments))}}(()=>{let e=x("colors"),t=x("spacing"),r=x("blur"),n=x("brightness"),i=x("borderColor"),o=x("borderRadius"),a=x("borderSpacing"),s=x("borderWidth"),l=x("contrast"),u=x("grayscale"),c=x("hueRotate"),d=x("invert"),f=x("gap"),h=x("gradientColorStops"),p=x("gradientColorStopPositions"),m=x("inset"),y=x("margin"),g=x("opacity"),v=x("padding"),b=x("saturate"),w=x("scale"),P=x("sepia"),E=x("skew"),R=x("space"),_=x("translate"),S=()=>["auto","contain","none"],T=()=>["auto","hidden","clip","visible","scroll"],j=()=>["auto",N,t],I=()=>[N,t],V=()=>["",O,C],z=()=>["auto",M,N],$=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],X=()=>["solid","dashed","dotted","double","none"],q=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],K=()=>["start","end","center","between","around","evenly","stretch"],G=()=>["","0",N],Y=()=>["auto","avoid","all","avoid-page","page","left","right","column"],Q=()=>[M,N];return{cacheSize:500,separator:":",theme:{colors:[H],spacing:[O,C],blur:["none","",L,N],brightness:Q(),borderColor:[e],borderRadius:["none","","full",L,N],borderSpacing:I(),borderWidth:V(),contrast:Q(),grayscale:G(),hueRotate:Q(),invert:G(),gap:I(),gradientColorStops:[e],gradientColorStopPositions:[k,C],inset:j(),margin:j(),opacity:Q(),padding:I(),saturate:Q(),scale:Q(),sepia:G(),skew:Q(),space:I(),translate:I()},classGroups:{aspect:[{aspect:["auto","square","video",N]}],container:["container"],columns:[{columns:[L]}],"break-after":[{"break-after":Y()}],"break-before":[{"break-before":Y()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...$(),N]}],overflow:[{overflow:T()}],"overflow-x":[{"overflow-x":T()}],"overflow-y":[{"overflow-y":T()}],overscroll:[{overscroll:S()}],"overscroll-x":[{"overscroll-x":S()}],"overscroll-y":[{"overscroll-y":S()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[m]}],"inset-x":[{"inset-x":[m]}],"inset-y":[{"inset-y":[m]}],start:[{start:[m]}],end:[{end:[m]}],top:[{top:[m]}],right:[{right:[m]}],bottom:[{bottom:[m]}],left:[{left:[m]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",D,N]}],basis:[{basis:j()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",N]}],grow:[{grow:G()}],shrink:[{shrink:G()}],order:[{order:["first","last","none",D,N]}],"grid-cols":[{"grid-cols":[H]}],"col-start-end":[{col:["auto",{span:["full",D,N]},N]}],"col-start":[{"col-start":z()}],"col-end":[{"col-end":z()}],"grid-rows":[{"grid-rows":[H]}],"row-start-end":[{row:["auto",{span:[D,N]},N]}],"row-start":[{"row-start":z()}],"row-end":[{"row-end":z()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",N]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",N]}],gap:[{gap:[f]}],"gap-x":[{"gap-x":[f]}],"gap-y":[{"gap-y":[f]}],"justify-content":[{justify:["normal",...K()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...K(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...K(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[v]}],px:[{px:[v]}],py:[{py:[v]}],ps:[{ps:[v]}],pe:[{pe:[v]}],pt:[{pt:[v]}],pr:[{pr:[v]}],pb:[{pb:[v]}],pl:[{pl:[v]}],m:[{m:[y]}],mx:[{mx:[y]}],my:[{my:[y]}],ms:[{ms:[y]}],me:[{me:[y]}],mt:[{mt:[y]}],mr:[{mr:[y]}],mb:[{mb:[y]}],ml:[{ml:[y]}],"space-x":[{"space-x":[R]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[R]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",N,t]}],"min-w":[{"min-w":[N,t,"min","max","fit"]}],"max-w":[{"max-w":[N,t,"none","full","min","max","fit","prose",{screen:[L]},L]}],h:[{h:[N,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[N,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[N,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[N,t,"auto","min","max","fit"]}],"font-size":[{text:["base",L,C]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",A]}],"font-family":[{font:[H]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",N]}],"line-clamp":[{"line-clamp":["none",M,A]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",O,N]}],"list-image":[{"list-image":["none",N]}],"list-style-type":[{list:["none","disc","decimal",N]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[g]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[g]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...X(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",O,C]}],"underline-offset":[{"underline-offset":["auto",O,N]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:I()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",N]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",N]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[g]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...$(),U]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",F]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},B]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[p]}],"gradient-via-pos":[{via:[p]}],"gradient-to-pos":[{to:[p]}],"gradient-from":[{from:[h]}],"gradient-via":[{via:[h]}],"gradient-to":[{to:[h]}],rounded:[{rounded:[o]}],"rounded-s":[{"rounded-s":[o]}],"rounded-e":[{"rounded-e":[o]}],"rounded-t":[{"rounded-t":[o]}],"rounded-r":[{"rounded-r":[o]}],"rounded-b":[{"rounded-b":[o]}],"rounded-l":[{"rounded-l":[o]}],"rounded-ss":[{"rounded-ss":[o]}],"rounded-se":[{"rounded-se":[o]}],"rounded-ee":[{"rounded-ee":[o]}],"rounded-es":[{"rounded-es":[o]}],"rounded-tl":[{"rounded-tl":[o]}],"rounded-tr":[{"rounded-tr":[o]}],"rounded-br":[{"rounded-br":[o]}],"rounded-bl":[{"rounded-bl":[o]}],"border-w":[{border:[s]}],"border-w-x":[{"border-x":[s]}],"border-w-y":[{"border-y":[s]}],"border-w-s":[{"border-s":[s]}],"border-w-e":[{"border-e":[s]}],"border-w-t":[{"border-t":[s]}],"border-w-r":[{"border-r":[s]}],"border-w-b":[{"border-b":[s]}],"border-w-l":[{"border-l":[s]}],"border-opacity":[{"border-opacity":[g]}],"border-style":[{border:[...X(),"hidden"]}],"divide-x":[{"divide-x":[s]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[s]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[g]}],"divide-style":[{divide:X()}],"border-color":[{border:[i]}],"border-color-x":[{"border-x":[i]}],"border-color-y":[{"border-y":[i]}],"border-color-s":[{"border-s":[i]}],"border-color-e":[{"border-e":[i]}],"border-color-t":[{"border-t":[i]}],"border-color-r":[{"border-r":[i]}],"border-color-b":[{"border-b":[i]}],"border-color-l":[{"border-l":[i]}],"divide-color":[{divide:[i]}],"outline-style":[{outline:["",...X()]}],"outline-offset":[{"outline-offset":[O,N]}],"outline-w":[{outline:[O,C]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:V()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[g]}],"ring-offset-w":[{"ring-offset":[O,C]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",L,W]}],"shadow-color":[{shadow:[H]}],opacity:[{opacity:[g]}],"mix-blend":[{"mix-blend":[...q(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":q()}],filter:[{filter:["","none"]}],blur:[{blur:[r]}],brightness:[{brightness:[n]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",L,N]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[c]}],invert:[{invert:[d]}],saturate:[{saturate:[b]}],sepia:[{sepia:[P]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[r]}],"backdrop-brightness":[{"backdrop-brightness":[n]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[c]}],"backdrop-invert":[{"backdrop-invert":[d]}],"backdrop-opacity":[{"backdrop-opacity":[g]}],"backdrop-saturate":[{"backdrop-saturate":[b]}],"backdrop-sepia":[{"backdrop-sepia":[P]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[a]}],"border-spacing-x":[{"border-spacing-x":[a]}],"border-spacing-y":[{"border-spacing-y":[a]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",N]}],duration:[{duration:Q()}],ease:[{ease:["linear","in","out","in-out",N]}],delay:[{delay:Q()}],animate:[{animate:["none","spin","ping","pulse","bounce",N]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[w]}],"scale-x":[{"scale-x":[w]}],"scale-y":[{"scale-y":[w]}],rotate:[{rotate:[D,N]}],"translate-x":[{"translate-x":[_]}],"translate-y":[{"translate-y":[_]}],"skew-x":[{"skew-x":[E]}],"skew-y":[{"skew-y":[E]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",N]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",N]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":I()}],"scroll-mx":[{"scroll-mx":I()}],"scroll-my":[{"scroll-my":I()}],"scroll-ms":[{"scroll-ms":I()}],"scroll-me":[{"scroll-me":I()}],"scroll-mt":[{"scroll-mt":I()}],"scroll-mr":[{"scroll-mr":I()}],"scroll-mb":[{"scroll-mb":I()}],"scroll-ml":[{"scroll-ml":I()}],"scroll-p":[{"scroll-p":I()}],"scroll-px":[{"scroll-px":I()}],"scroll-py":[{"scroll-py":I()}],"scroll-ps":[{"scroll-ps":I()}],"scroll-pe":[{"scroll-pe":I()}],"scroll-pt":[{"scroll-pt":I()}],"scroll-pr":[{"scroll-pr":I()}],"scroll-pb":[{"scroll-pb":I()}],"scroll-pl":[{"scroll-pl":I()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",N]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[O,C,A]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}})}};