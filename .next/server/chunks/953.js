exports.id=953,exports.ids=[953],exports.modules={10255:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,12994,23)),Promise.resolve().then(n.t.bind(n,96114,23)),Promise.resolve().then(n.t.bind(n,9727,23)),Promise.resolve().then(n.t.bind(n,79671,23)),Promise.resolve().then(n.t.bind(n,41868,23)),Promise.resolve().then(n.t.bind(n,84759,23))},99577:(e,t,n)=>{Promise.resolve().then(n.bind(n,58641))},97773:(e,t,n)=>{Promise.resolve().then(n.bind(n,60215))},60215:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>x});var s=n(10326),o=n(90434),a=n(48430),i=n(29798),r=n(88307),l=n(86333),d=n(17577),c=n(34214),u=n(79360),m=n(51223);let p=(0,u.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),h=d.forwardRef(({className:e,variant:t,size:n,asChild:o=!1,...a},i)=>{let r=o?c.g7:"button";return s.jsx(r,{className:(0,m.cn)(p({variant:t,size:n,className:e})),ref:i,...a})});function x(){return s.jsx("div",{className:"min-h-screen bg-github-dark text-github-text flex items-center justify-center",children:s.jsx("div",{className:"max-w-md mx-auto text-center px-6",children:(0,s.jsxs)(a.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:[s.jsx(a.E.div,{className:"text-8xl font-bold text-neon-green mb-8",initial:{scale:.5},animate:{scale:1},transition:{duration:.5,type:"spring",stiffness:200},children:"404"}),s.jsx(a.E.h1,{className:"text-3xl font-bold text-white mb-4",initial:{opacity:0},animate:{opacity:1},transition:{delay:.2},children:"Page Not Found"}),s.jsx(a.E.p,{className:"text-github-text mb-8 leading-relaxed",initial:{opacity:0},animate:{opacity:1},transition:{delay:.3},children:"Oops! The page you're looking for doesn't exist. It might have been moved, deleted, or you entered the wrong URL."}),(0,s.jsxs)(a.E.div,{className:"flex flex-col sm:flex-row gap-4 justify-center",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},children:[s.jsx(h,{asChild:!0,className:"bg-neon-green text-black hover:bg-neon-green/90",children:(0,s.jsxs)(o.default,{href:"/",className:"flex items-center gap-2",children:[s.jsx(i.Z,{size:18}),"Go Home"]})}),s.jsx(h,{variant:"outline",asChild:!0,className:"border-github-border text-github-text hover:bg-github-light",children:(0,s.jsxs)(o.default,{href:"/#contact",className:"flex items-center gap-2",children:[s.jsx(r.Z,{size:18}),"Contact Support"]})})]}),s.jsx(a.E.div,{className:"mt-8",initial:{opacity:0},animate:{opacity:1},transition:{delay:.5},children:(0,s.jsxs)(h,{variant:"ghost",onClick:()=>window.history.back(),className:"text-github-text hover:text-white flex items-center gap-2",children:[s.jsx(l.Z,{size:18}),"Go Back"]})}),s.jsx(a.E.div,{className:"absolute top-1/4 left-1/4 w-2 h-2 bg-neon-green rounded-full opacity-50",animate:{scale:[1,1.5,1],opacity:[.5,1,.5]},transition:{duration:2,repeat:1/0,ease:"easeInOut"}}),s.jsx(a.E.div,{className:"absolute top-1/3 right-1/4 w-1 h-1 bg-neon-blue rounded-full opacity-50",animate:{scale:[1,2,1],opacity:[.3,.8,.3]},transition:{duration:3,repeat:1/0,ease:"easeInOut",delay:1}})]})})})}h.displayName="Button"},58641:(e,t,n)=>{"use strict";n.d(t,{Providers:()=>B});var s=n(10326),o=n(2659),a=n(44976),i=n(17577),r=n.n(i),l=n(25813),d=n(51223);let c=l.zt;l.fC,l.xz,i.forwardRef(({className:e,sideOffset:t=4,...n},o)=>s.jsx(l.VY,{ref:o,sideOffset:t,className:(0,d.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...n})).displayName=l.VY.displayName;let u=(0,i.createContext)(void 0),m=({children:e})=>{let[t,n]=(0,i.useState)("dark");return(0,i.useEffect)(()=>{},[]),s.jsx(u.Provider,{value:{theme:t,toggleTheme:()=>{n("dark"===t?"light":"dark")}},children:e})};var p=n(84097),h=n(10321),x=n(79360),g=n(94019);let f=h.zt,v=i.forwardRef(({className:e,...t},n)=>s.jsx(h.l_,{ref:n,className:(0,d.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));v.displayName=h.l_.displayName;let b=(0,x.j)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),y=i.forwardRef(({className:e,variant:t,...n},o)=>s.jsx(h.fC,{ref:o,className:(0,d.cn)(b({variant:t}),e),...n}));y.displayName=h.fC.displayName,i.forwardRef(({className:e,...t},n)=>s.jsx(h.aU,{ref:n,className:(0,d.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t})).displayName=h.aU.displayName;let w=i.forwardRef(({className:e,...t},n)=>s.jsx(h.x8,{ref:n,className:(0,d.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:s.jsx(g.Z,{className:"h-4 w-4"})}));w.displayName=h.x8.displayName;let j=i.forwardRef(({className:e,...t},n)=>s.jsx(h.Dx,{ref:n,className:(0,d.cn)("text-sm font-semibold",e),...t}));j.displayName=h.Dx.displayName;let N=i.forwardRef(({className:e,...t},n)=>s.jsx(h.dk,{ref:n,className:(0,d.cn)("text-sm opacity-90",e),...t}));function E(){let{toasts:e}=(0,p.pm)();return(0,s.jsxs)(f,{children:[e.map(function({id:e,title:t,description:n,action:o,...a}){return(0,s.jsxs)(y,{...a,children:[(0,s.jsxs)("div",{className:"grid gap-1",children:[t&&s.jsx(j,{children:t}),n&&s.jsx(N,{children:n})]}),o,s.jsx(w,{})]},e)}),s.jsx(v,{})]})}N.displayName=h.dk.displayName;var k=n(14831),T=n(85999);let D=({...e})=>{let{theme:t="system"}=(0,k.F)();return s.jsx(T.x7,{theme:t,className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}},...e})};var S=n(48430);let C=()=>{let[e,t]=(0,i.useState)(0),[n,o]=(0,i.useState)("Initializing system..."),[a,r]=(0,i.useState)(!0),[l,d]=(0,i.useState)(!1),c=[{text:"Initializing system...",duration:1200},{text:"Establishing secure connection...",duration:1e3},{text:"Authenticating credentials...",duration:800},{text:"Bypassing security protocols...",duration:1500},{text:"Loading developer assets...",duration:1e3},{text:"Compiling portfolio data...",duration:1200},{text:"Optimizing display modules...",duration:900},{text:"Rendering interface...",duration:1300},{text:"System ready. Welcome to GreenHacker portfolio v2.0",duration:1e3}];return(0,i.useEffect)(()=>{let e=setInterval(()=>{r(e=>!e)},500),n=0,s=setTimeout(function e(){if(n<c.length){let{text:s,duration:a}=c[n];o(s),t(Math.min(100,Math.round((n+1)/c.length*100))),n++,setTimeout(e,a)}else d(!0),setTimeout(()=>{},1e3)},500);return()=>{clearInterval(e),clearTimeout(s)}},[]),(0,s.jsxs)(S.E.div,{className:"fixed inset-0 bg-black flex items-center justify-center z-50",initial:{opacity:1},exit:{opacity:0},transition:{duration:.6,ease:"easeInOut"},children:[(0,s.jsxs)(S.E.div,{className:"w-full max-w-3xl bg-black border border-neon-green p-6 rounded-md shadow-neon-green terminal-window",variants:{hidden:{opacity:0},visible:{opacity:1,transition:{duration:.8}}},initial:"hidden",animate:"visible",children:[(0,s.jsxs)("div",{className:"terminal-header flex items-center justify-between mb-4",children:[s.jsx("div",{className:"text-neon-green font-mono text-sm",children:"~/green-hacker/portfolio"}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[s.jsx("div",{className:"w-3 h-3 rounded-full bg-red-500"}),s.jsx("div",{className:"w-3 h-3 rounded-full bg-yellow-500"}),s.jsx("div",{className:"w-3 h-3 rounded-full bg-green-500"})]})]}),(0,s.jsxs)("div",{className:"terminal-content space-y-2 font-mono text-sm overflow-hidden",children:[(0,s.jsxs)("div",{className:"line",children:[s.jsx("span",{className:"text-neon-blue",children:"$ "}),s.jsx("span",{className:"text-white",children:"load portfolio --env=production --secure"})]}),(0,s.jsxs)(S.E.div,{className:"line text-neon-green",initial:{opacity:0},animate:{opacity:1},transition:{delay:.3},children:[n,a?"▋":" "]}),(0,s.jsxs)(S.E.div,{className:"line",initial:{opacity:0},animate:{opacity:1},transition:{delay:.5},children:[(0,s.jsxs)("div",{className:"text-github-text",children:["Progress: ",e,"%"]}),s.jsx("div",{className:"w-full bg-github-dark rounded-full h-2 mt-1",children:s.jsx(S.E.div,{className:"h-2 rounded-full bg-neon-green",initial:{width:0},animate:{width:`${e}%`},transition:{duration:.5}})})]}),l&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(S.E.div,{className:"line",initial:{opacity:0},animate:{opacity:1},transition:{delay:.2},children:[s.jsx("span",{className:"text-neon-blue",children:"$ "}),s.jsx("span",{className:"text-white",children:"launch --mode=interactive"})]}),s.jsx(S.E.div,{className:"line text-neon-purple",initial:{opacity:0},animate:{opacity:1},transition:{delay:.4},children:"Launching portfolio interface..."})]})]}),s.jsx("div",{className:"ascii-art mt-8 text-neon-green font-mono text-xs whitespace-pre",children:` ██████╗ ██████╗ ███████╗███████╗███╗   ██╗██╗  ██╗ █████╗  ██████╗██╗  ██╗███████╗██████╗
██╔════╝ ██╔══██╗██╔════╝██╔════╝████╗  ██║██║  ██║██╔══██╗██╔════╝██║ ██╔╝██╔════╝██╔══██╗
██║  ███╗██████╔╝█████╗  █████╗  ██╔██╗ ██║███████║███████║██║     █████╔╝ █████╗  ██████╔╝
██║   ██║██╔══██╗██╔══╝  ██╔══╝  ██║╚██╗██║██╔══██║██╔══██║██║     ██╔═██╗ ██╔══╝  ██╔══██╗
╚██████╔╝██║  ██║███████╗███████╗██║ ╚████║██║  ██║██║  ██║╚██████╗██║  ██╗███████╗██║  ██║
 ╚═════╝ ╚═╝  ╚═╝╚══════╝╚══════╝╚═╝  ╚═══╝╚═╝  ╚═╝╚═╝  ╚═╝ ╚═════╝╚═╝  ╚═╝╚══════╝╚═╝  ╚═╝`}),l&&(0,s.jsxs)(S.E.div,{className:"mt-6 text-center",initial:{opacity:0},animate:{opacity:1},transition:{delay:.8},children:[s.jsx("span",{className:"text-github-text text-sm",children:"Press "}),s.jsx("span",{className:"px-2 py-1 bg-github-light rounded text-white text-sm mx-1",children:"ENTER"}),s.jsx("span",{className:"text-github-text text-sm",children:" to continue"})]})]}),s.jsx("style",{dangerouslySetInnerHTML:{__html:`
        .terminal-window {
          box-shadow: 0 0 10px rgba(63, 185, 80, 0.3), 0 0 20px rgba(63, 185, 80, 0.2);
        }

        @keyframes scan {
          from { top: 0; }
          to { top: 100%; }
        }

        .terminal-window::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 3px;
          background-color: rgba(63, 185, 80, 0.5);
          animation: scan 3s linear infinite;
        }
      `}})]})},A=()=>{let[e,t]=(0,i.useState)({x:0,y:0}),[n,o]=(0,i.useState)(!1),[a,r]=(0,i.useState)(!1);return(0,i.useEffect)(()=>{let e=e=>{t({x:e.clientX,y:e.clientY})},n=()=>{o(!0),setTimeout(()=>o(!1),300)},s=()=>{document.body.style.cursor="none"},a=()=>{document.body.style.cursor="auto"},i=e=>{let t=e.target;r(!!("button"===t.tagName.toLowerCase()||"a"===t.tagName.toLowerCase()||t.closest("button")||t.closest("a")))};return document.addEventListener("mousemove",e),document.addEventListener("mousedown",n),document.addEventListener("mouseenter",s),document.addEventListener("mouseleave",a),document.addEventListener("mouseover",i),()=>{document.removeEventListener("mousemove",e),document.removeEventListener("mousedown",n),document.removeEventListener("mouseenter",s),document.removeEventListener("mouseleave",a),document.removeEventListener("mouseover",i),document.body.style.cursor="auto"}},[]),(0,s.jsxs)(s.Fragment,{children:[s.jsx(S.E.div,{className:"fixed top-0 left-0 w-8 h-8 rounded-full border border-neon-green z-[9999] pointer-events-none",animate:{x:e.x-16,y:e.y-16,scale:n?.8:a?1.5:1},transition:{type:"spring",stiffness:300,damping:20,mass:.5}}),s.jsx(S.E.div,{className:"fixed top-0 left-0 w-2 h-2 bg-neon-green rounded-full z-[10000] pointer-events-none",animate:{x:e.x-4,y:e.y-4,opacity:n?.5:1},transition:{type:"spring",stiffness:400,damping:15}})]})};var R=n(33519),I=n(55139);let P=()=>{let e=(0,R.c)(0),t=(0,R.c)(0),n={stiffness:50,damping:50},o=(0,I.q)(e,n),a=(0,I.q)(t,n),r=(0,i.useRef)(null);return(0,i.useEffect)(()=>{let n=n=>{if(!r.current)return;let s=r.current.getBoundingClientRect(),o=s.left+s.width/2,a=s.top+s.height/2,i=(n.clientX-o)/(s.width/2),l=(n.clientY-a)/(s.height/2);e.set(10*i),t.set(10*l)};return window.addEventListener("mousemove",n),()=>{window.removeEventListener("mousemove",n)}},[e,t]),s.jsx("div",{ref:r,className:"fixed inset-0 pointer-events-none z-0 overflow-hidden",children:(0,s.jsxs)(S.E.div,{className:"absolute inset-0 opacity-20",style:{translateX:o,translateY:a},children:[s.jsx("div",{className:"absolute top-0 -left-4 w-[50vw] h-[50vw] bg-neon-purple rounded-full mix-blend-screen filter blur-[100px] opacity-70"}),s.jsx("div",{className:"absolute top-[30%] -right-[10%] w-[40vw] h-[40vw] bg-neon-green rounded-full mix-blend-screen filter blur-[100px] opacity-70"}),s.jsx("div",{className:"absolute -bottom-[20%] left-[20%] w-[60vw] h-[60vw] bg-neon-blue rounded-full mix-blend-screen filter blur-[100px] opacity-40"})]})})};var z=n(88129),L=n(32130),H=n(11692),F=n(70715),G=n(69436);let O={help:["\uD83D\uDCBB Available Commands:","- help: Display this help message","- about: Learn about Green Hacker","- skills: View technical skills","- projects: Show recent projects","- contact: Get contact information","- clear: Clear the terminal","- exit: Close the chatbot","","You can also just chat naturally!"],about:["Hey there! \uD83D\uDC4B I'm Green Hacker, a full-stack developer and ML enthusiast.","When I'm not coding, I'm probably hiking, gaming, or learning something new.","I specialize in creating interactive web experiences and AI-powered applications."],skills:["\uD83D\uDE80 Technical Skills:","- Frontend: React, TypeScript, Tailwind CSS, Framer Motion","- Backend: Node.js, Express, FastAPI, GraphQL","- ML/AI: PyTorch, TensorFlow, Computer Vision","- DevOps: Docker, AWS, CI/CD, Kubernetes","- Other: Three.js, React Three Fiber, WebGL"],projects:["\uD83D\uDCC1 Recent Projects:","1. AI Photo Platform - Face recognition for intelligent photo organization","2. Portfolio Website - You're looking at it right now!","3. ML Research Tool - Natural language processing for scientific papers","4. Real-time Collaboration App - WebRTC and WebSockets for seamless teamwork","",'Type "project [number]" for more details!'],"project 1":["\uD83D\uDCF7 AI Photo Platform","A machine learning application that uses facial recognition to organize and tag photos.","Tech stack: React, TypeScript, PyTorch, AWS S3, Tailwind CSS","Features: Face recognition, automatic tagging, search by person, cloud storage"],"project 2":["\uD83C\uDF10 Portfolio Website","An interactive portfolio showcasing my projects and skills with 3D elements.","Tech stack: React, Three.js, Framer Motion, Tailwind CSS","Features: 3D visualization, interactive components, responsive design"],"project 3":["\uD83D\uDCDA ML Research Tool","An AI-powered tool that helps researchers find relevant papers and extract insights.","Tech stack: Python, TensorFlow, FastAPI, React","Features: Paper recommendation, text summarization, citation network analysis"],"project 4":["\uD83D\uDC65 Real-time Collaboration App","A platform for teams to collaborate with document sharing and real-time editing.","Tech stack: React, Node.js, Socket.io, WebRTC, MongoDB","Features: Live document editing, video chat, project management tools"],contact:["\uD83D\uDCEB Contact Information:","Email: <EMAIL>","GitHub: github.com/greenhacker","LinkedIn: linkedin.com/in/greenhacker","Twitter: @greenhacker"],clear:[""],exit:["\uD83D\uDC4B Goodbye! You can open me again by clicking the terminal icon."]},M=()=>{let[e,t]=(0,i.useState)(!1),[n,o]=(0,i.useState)(!1),[a,l]=(0,i.useState)([{type:"bot",content:["\uD83D\uDC4B Hi there! I'm GREENHACKER's AI assistant.","I can tell you about GREENHACKER, their skills, projects, or how to get in touch.",'Type "help" to see what I can do!']}]),[d,c]=(0,i.useState)(""),[u,m]=(0,i.useState)(!1),p=(0,i.useRef)(null),h=(0,i.useRef)(null);(0,i.useEffect)(()=>{p.current?.scrollIntoView({behavior:"smooth"})},[a]),(0,i.useEffect)(()=>{e&&h.current?.focus()},[e]);let x=()=>{t(!e)},f=e=>{let n=e.toLowerCase().trim();if("exit"===n){l([...a,{type:"user",content:[e]},{type:"bot",content:O.exit}]),setTimeout(()=>t(!1),1e3);return}if("clear"===n){l([]);return}if(O[n]){l([...a,{type:"user",content:[e]},{type:"bot",content:O[n]}]);return}l([...a,{type:"user",content:[e]}]),m(!0),setTimeout(()=>{let t=v(e);l(e=>[...e,{type:"bot",content:t}]),m(!1)},1e3+1e3*Math.random())},v=e=>{let t=e.toLowerCase();return t.includes("hi")||t.includes("hello")||t.includes("hey")?["Hello! How can I help you today? \uD83D\uDE0A",'Type "help" to see what I can do.']:t.includes("thanks")||t.includes("thank you")?["You're welcome! Anything else you'd like to know?"]:t.includes("experience")||t.includes("work")?["GREENHACKER has over 5 years of experience in full-stack development and machine learning projects.","They've worked with startups and enterprise companies on various AI-powered applications."]:t.includes("education")?["GREENHACKER has a Master's degree in Computer Science with a specialization in Artificial Intelligence.","They're also continually learning through courses and self-study."]:t.includes("name")?["My name is GreenBot! I'm GREENHACKER's AI assistant."]:["I'm not sure I understand that query.",'Type "help" to see what commands are available.']};return(0,s.jsxs)(s.Fragment,{children:[s.jsx(S.E.button,{className:"fixed bottom-8 right-8 bg-neon-green text-black h-12 w-12 rounded-full flex items-center justify-center shadow-lg hover:scale-110 transition-transform z-50",whileHover:{scale:1.1},whileTap:{scale:.9},onClick:x,children:s.jsx(L.Z,{size:20})}),s.jsx(z.M,{children:e&&(0,s.jsxs)(S.E.div,{className:`fixed ${n?"inset-4 md:inset-10":"bottom-24 right-8 w-[350px] md:w-[400px] h-[500px]"} bg-black border border-neon-green/50 rounded-lg shadow-lg overflow-hidden z-50 flex flex-col`,initial:{opacity:0,y:50},animate:{opacity:1,y:0},exit:{opacity:0,y:50},transition:{duration:.3},children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-3 border-b border-neon-green/30 bg-black",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx(L.Z,{className:"text-neon-green mr-2",size:18}),s.jsx("h3",{className:"text-neon-green font-mono text-sm",children:"GREENHACKER Terminal"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[s.jsx("button",{className:"text-neon-green hover:text-white transition-colors focus:outline-none",onClick:()=>{o(!n)},children:n?s.jsx(H.Z,{size:16}):s.jsx(F.Z,{size:16})}),s.jsx("button",{className:"text-neon-green hover:text-white transition-colors focus:outline-none",onClick:x,children:s.jsx(g.Z,{size:16})})]})]}),s.jsx("div",{className:"flex-grow overflow-y-auto p-4",style:{backgroundColor:"#0d1117"},children:(0,s.jsxs)("div",{className:"space-y-4",children:[a.map((e,t)=>(0,s.jsxs)("div",{className:`${"user"===e.type?"ml-auto max-w-[80%]":"mr-auto max-w-[80%]"}`,children:[s.jsx("div",{className:`rounded-lg p-3 ${"user"===e.type?"bg-neon-green/20 text-white":"bg-github-light text-neon-green"}`,children:e.content.map((e,t)=>s.jsx(r().Fragment,{children:""===e?s.jsx("br",{}):s.jsx("p",{className:"font-mono text-sm",children:e})},t))}),(0,s.jsxs)("p",{className:"text-xs text-github-text mt-1",children:["user"===e.type?"You":"GREENHACKER Bot"," • ",new Date().toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})]})]},t)),u&&s.jsx("div",{className:"mr-auto",children:s.jsx("div",{className:"bg-github-light rounded-lg p-3 max-w-[80%]",children:(0,s.jsxs)("div",{className:"flex space-x-1",children:[s.jsx("div",{className:"h-2 w-2 bg-neon-green rounded-full animate-bounce"}),s.jsx("div",{className:"h-2 w-2 bg-neon-green rounded-full animate-bounce",style:{animationDelay:"0.2s"}}),s.jsx("div",{className:"h-2 w-2 bg-neon-green rounded-full animate-bounce",style:{animationDelay:"0.4s"}})]})})}),s.jsx("div",{ref:p})]})}),s.jsx("form",{onSubmit:e=>{e.preventDefault(),d.trim()&&(f(d),c(""))},className:"p-3 border-t border-neon-green/30 bg-github-dark",children:(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx("span",{className:"text-neon-green font-mono mr-2",children:"$"}),s.jsx("input",{ref:h,type:"text",value:d,onChange:e=>c(e.target.value),className:"flex-grow bg-transparent border-none text-white font-mono focus:outline-none text-sm",placeholder:"Type a message or command..."}),s.jsx("button",{type:"submit",className:"text-neon-green hover:text-white transition-colors focus:outline-none",children:s.jsx(G.Z,{size:16})})]})})]})})]})},_=new o.S({defaultOptions:{queries:{staleTime:6e4,refetchOnWindowFocus:!1}}});function B({children:e}){let[t,n]=(0,i.useState)(!0),[o,r]=(0,i.useState)(!1);return s.jsx(m,{children:s.jsx(a.aH,{client:_,children:(0,s.jsxs)(c,{children:[s.jsx(E,{}),s.jsx(D,{}),t&&s.jsx(C,{}),s.jsx(P,{}),!o&&s.jsx(A,{}),e,s.jsx(M,{})]})})})}},84097:(e,t,n)=>{"use strict";n.d(t,{pm:()=>m});var s=n(17577);let o=0,a=new Map,i=e=>{if(a.has(e))return;let t=setTimeout(()=>{a.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);a.set(e,t)},r=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:n}=t;return n?i(n):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===n||void 0===n?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],d={toasts:[]};function c(e){d=r(d,e),l.forEach(e=>{e(d)})}function u({...e}){let t=(o=(o+1)%Number.MAX_SAFE_INTEGER).toString(),n=()=>c({type:"DISMISS_TOAST",toastId:t});return c({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||n()}}}),{id:t,dismiss:n,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function m(){let[e,t]=s.useState(d);return s.useEffect(()=>(l.push(t),()=>{let e=l.indexOf(t);e>-1&&l.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},51223:(e,t,n)=>{"use strict";n.d(t,{cn:()=>a});var s=n(41135),o=n(31009);function a(...e){return(0,o.m6)((0,s.W)(e))}},15495:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>l,metadata:()=>r});var s=n(19510),o=n(45317),a=n.n(o);n(5023);let i=(0,n(68570).createProxy)(String.raw`/Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/app/providers.tsx#Providers`),r={title:"GREENHACKER | Developer Portfolio",description:"Full-stack developer portfolio showcasing modern web technologies, AI integration, and innovative projects.",keywords:["developer","portfolio","React","Next.js","TypeScript","AI","machine learning"],authors:[{name:"GreenHacker"}],creator:"GreenHacker",publisher:"GreenHacker",openGraph:{type:"website",locale:"en_US",url:"https://greenhacker.dev",title:"GREENHACKER | Developer Portfolio",description:"Full-stack developer portfolio showcasing modern web technologies, AI integration, and innovative projects.",siteName:"GreenHacker Portfolio"},twitter:{card:"summary_large_image",title:"GREENHACKER | Developer Portfolio",description:"Full-stack developer portfolio showcasing modern web technologies, AI integration, and innovative projects.",creator:"@greenhacker"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},verification:{google:"your-google-verification-code"}};function l({children:e}){return(0,s.jsxs)("html",{lang:"en",suppressHydrationWarning:!0,children:[(0,s.jsxs)("head",{children:[s.jsx("link",{rel:"manifest",href:"/site.webmanifest"}),s.jsx("meta",{name:"theme-color",content:"#0d1117"}),s.jsx("meta",{name:"viewport",content:"width=device-width, initial-scale=1"})]}),s.jsx("body",{className:a().className,suppressHydrationWarning:!0,children:s.jsx(i,{children:e})})]})}},12523:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>s});let s=(0,n(68570).createProxy)(String.raw`/Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/app/not-found.tsx#default`)},5023:()=>{}};