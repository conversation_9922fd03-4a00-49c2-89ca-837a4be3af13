<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="stylesheet" href="/_next/static/css/2d35b5dc3b900f91.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-978303ec57882e3d.js"/><script src="/_next/static/chunks/fd9d1056-aa9b9ca480dc9276.js" async=""></script><script src="/_next/static/chunks/117-11e32dade73bf6e3.js" async=""></script><script src="/_next/static/chunks/main-app-92f312f27fb31731.js" async=""></script><script src="/_next/static/chunks/622-97e2b9d6382830d5.js" async=""></script><script src="/_next/static/chunks/280-638f946c53cd8881.js" async=""></script><script src="/_next/static/chunks/app/not-found-8b982782e4426ea7.js" async=""></script><script src="/_next/static/chunks/467-390f4fce6e80aed8.js" async=""></script><script src="/_next/static/chunks/688-1e2ded085241b8f8.js" async=""></script><script src="/_next/static/chunks/app/layout-eb529cd945e8a721.js" async=""></script><meta name="robots" content="noindex"/><link rel="manifest" href="/site.webmanifest"/><meta name="theme-color" content="#0d1117"/><title>GREENHACKER | Developer Portfolio</title><meta name="description" content="Full-stack developer portfolio showcasing modern web technologies, AI integration, and innovative projects."/><meta name="author" content="GreenHacker"/><meta name="keywords" content="developer,portfolio,React,Next.js,TypeScript,AI,machine learning"/><meta name="creator" content="GreenHacker"/><meta name="publisher" content="GreenHacker"/><meta name="robots" content="index, follow"/><meta name="googlebot" content="index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1"/><meta name="google-site-verification" content="your-google-verification-code"/><meta property="og:title" content="GREENHACKER | Developer Portfolio"/><meta property="og:description" content="Full-stack developer portfolio showcasing modern web technologies, AI integration, and innovative projects."/><meta property="og:url" content="https://greenhacker.dev"/><meta property="og:site_name" content="GreenHacker Portfolio"/><meta property="og:locale" content="en_US"/><meta property="og:type" content="website"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:creator" content="@greenhacker"/><meta name="twitter:title" content="GREENHACKER | Developer Portfolio"/><meta name="twitter:description" content="Full-stack developer portfolio showcasing modern web technologies, AI integration, and innovative projects."/><meta name="next-size-adjust"/><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__className_e8ce0c"><div role="region" aria-label="Notifications (F8)" tabindex="-1" style="pointer-events:none"><ol tabindex="-1" class="fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]"></ol></div><section aria-label="Notifications alt+T" tabindex="-1" aria-live="polite" aria-relevant="additions text" aria-atomic="false"></section><div class="fixed inset-0 bg-black flex items-center justify-center z-50" style="opacity:1"><div class="w-full max-w-3xl bg-black border border-neon-green p-6 rounded-md shadow-neon-green terminal-window" style="opacity:0"><div class="terminal-header flex items-center justify-between mb-4"><div class="text-neon-green font-mono text-sm">~/green-hacker/portfolio</div><div class="flex space-x-2"><div class="w-3 h-3 rounded-full bg-red-500"></div><div class="w-3 h-3 rounded-full bg-yellow-500"></div><div class="w-3 h-3 rounded-full bg-green-500"></div></div></div><div class="terminal-content space-y-2 font-mono text-sm overflow-hidden"><div class="line"><span class="text-neon-blue">$ </span><span class="text-white">load portfolio --env=production --secure</span></div><div class="line text-neon-green" style="opacity:0">Initializing system...<!-- -->▋</div><div class="line" style="opacity:0"><div class="text-github-text">Progress: <!-- -->0<!-- -->%</div><div class="w-full bg-github-dark rounded-full h-2 mt-1"><div class="h-2 rounded-full bg-neon-green" style="width:0px"></div></div></div></div><div class="ascii-art mt-8 text-neon-green font-mono text-xs whitespace-pre"> ██████╗ ██████╗ ███████╗███████╗███╗   ██╗██╗  ██╗ █████╗  ██████╗██╗  ██╗███████╗██████╗
██╔════╝ ██╔══██╗██╔════╝██╔════╝████╗  ██║██║  ██║██╔══██╗██╔════╝██║ ██╔╝██╔════╝██╔══██╗
██║  ███╗██████╔╝█████╗  █████╗  ██╔██╗ ██║███████║███████║██║     █████╔╝ █████╗  ██████╔╝
██║   ██║██╔══██╗██╔══╝  ██╔══╝  ██║╚██╗██║██╔══██║██╔══██║██║     ██╔═██╗ ██╔══╝  ██╔══██╗
╚██████╔╝██║  ██║███████╗███████╗██║ ╚████║██║  ██║██║  ██║╚██████╗██║  ██╗███████╗██║  ██║
 ╚═════╝ ╚═╝  ╚═╝╚══════╝╚══════╝╚═╝  ╚═══╝╚═╝  ╚═╝╚═╝  ╚═╝ ╚═════╝╚═╝  ╚═╝╚══════╝╚═╝  ╚═╝</div></div><style>
        .terminal-window {
          box-shadow: 0 0 10px rgba(63, 185, 80, 0.3), 0 0 20px rgba(63, 185, 80, 0.2);
        }

        @keyframes scan {
          from { top: 0; }
          to { top: 100%; }
        }

        .terminal-window::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 3px;
          background-color: rgba(63, 185, 80, 0.5);
          animation: scan 3s linear infinite;
        }
      </style></div><div class="fixed inset-0 pointer-events-none z-0 overflow-hidden"><div class="absolute inset-0 opacity-20" style="transform:none"><div class="absolute top-0 -left-4 w-[50vw] h-[50vw] bg-neon-purple rounded-full mix-blend-screen filter blur-[100px] opacity-70"></div><div class="absolute top-[30%] -right-[10%] w-[40vw] h-[40vw] bg-neon-green rounded-full mix-blend-screen filter blur-[100px] opacity-70"></div><div class="absolute -bottom-[20%] left-[20%] w-[60vw] h-[60vw] bg-neon-blue rounded-full mix-blend-screen filter blur-[100px] opacity-40"></div></div></div><div class="fixed top-0 left-0 w-8 h-8 rounded-full border border-neon-green z-[9999] pointer-events-none"></div><div class="fixed top-0 left-0 w-2 h-2 bg-neon-green rounded-full z-[10000] pointer-events-none"></div><div class="min-h-screen bg-github-dark text-github-text flex items-center justify-center"><div class="max-w-md mx-auto text-center px-6"><div style="opacity:0;transform:translateY(20px)"><div class="text-8xl font-bold text-neon-green mb-8" style="transform:scale(0.5)">404</div><h1 class="text-3xl font-bold text-white mb-4" style="opacity:0">Page Not Found</h1><p class="text-github-text mb-8 leading-relaxed" style="opacity:0">Oops! The page you&#x27;re looking for doesn&#x27;t exist. It might have been moved, deleted, or you entered the wrong URL.</p><div class="flex flex-col sm:flex-row gap-4 justify-center" style="opacity:0;transform:translateY(20px)"><a class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 h-10 px-4 py-2 bg-neon-green text-black hover:bg-neon-green/90 flex items-center gap-2" href="/"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-house"><path d="M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8"></path><path d="M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path></svg>Go Home</a><a class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 border bg-background hover:text-accent-foreground h-10 px-4 py-2 border-github-border text-github-text hover:bg-github-light flex items-center gap-2" href="/#contact"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-search"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg>Contact Support</a></div><div class="mt-8" style="opacity:0"><button class="justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 hover:bg-accent h-10 px-4 py-2 text-github-text hover:text-white flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-arrow-left"><path d="m12 19-7-7 7-7"></path><path d="M19 12H5"></path></svg>Go Back</button></div><div class="absolute top-1/4 left-1/4 w-2 h-2 bg-neon-green rounded-full opacity-50"></div><div class="absolute top-1/3 right-1/4 w-1 h-1 bg-neon-blue rounded-full opacity-50"></div></div></div></div><button class="fixed bottom-8 right-8 bg-neon-green text-black h-12 w-12 rounded-full flex items-center justify-center shadow-lg hover:scale-110 transition-transform z-50" tabindex="0"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-terminal"><polyline points="4 17 10 11 4 5"></polyline><line x1="12" x2="20" y1="19" y2="19"></line></svg></button><script src="/_next/static/chunks/webpack-978303ec57882e3d.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n2:HL[\"/_next/static/css/2d35b5dc3b900f91.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"3:I[12846,[],\"\"]\n5:I[19107,[],\"ClientPageRoot\"]\n6:I[45086,[\"622\",\"static/chunks/622-97e2b9d6382830d5.js\",\"280\",\"static/chunks/280-638f946c53cd8881.js\",\"160\",\"static/chunks/app/not-found-8b982782e4426ea7.js\"],\"default\",1]\n7:I[4707,[],\"\"]\n8:I[36423,[],\"\"]\n9:I[79329,[\"622\",\"static/chunks/622-97e2b9d6382830d5.js\",\"467\",\"static/chunks/467-390f4fce6e80aed8.js\",\"688\",\"static/chunks/688-1e2ded085241b8f8.js\",\"185\",\"static/chunks/app/layout-eb529cd945e8a721.js\"],\"Providers\"]\na:I[45086,[\"622\",\"static/chunks/622-97e2b9d6382830d5.js\",\"280\",\"static/chunks/280-638f946c53cd8881.js\",\"160\",\"static/chunks/app/not-found-8b982782e4426ea7.js\"],\"default\"]\nc:I[61060,[],\"\"]\nd:[]\n0:[\"$\",\"$L3\",null,{\"buildId\":\"4MYV2pUahTuQm6MmsBSK5\",\"assetPrefix\":\"\",\"urlParts\":[\"\",\"_not-found\"],\"initialTree\":[\"\",{\"children\":[\"/_not-found\",{\"children\":[\"__PAGE__\",{}]}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"/_not-found\",{\"children\":[\"__PAGE__\",{},[[\"$L4\",[\"$\",\"$L5\",null,{\"props\":{\"params\":{},\"searchParams\":{}},\"Component\":\"$6\"}],null],null],null]},[null,[\"$\",\"$L7\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"/_not-found\",\"children\"],\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L8\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"notFoundStyles\":\"$undefined\"}]],null]},[[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/2d35b5dc3b900f91.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"suppressHydrationWarning\":true,\"children\":[[\"$\",\"head\",null,{\"children\":[[\"$\",\"link\",null,{\"rel\":\"manifest\",\"href\":\"/site.webmanifest\"}],[\"$\",\"meta\",null,{\"name\":\"theme-color\",\"content\":\"#0d1117\"}],[\"$\",\"meta\",null,{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]}],[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"suppressHydrationWarning\":true,\"children\":[\"$\",\"$L9\",null,{\"children\":[\"$\",\"$L7\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"error\":\""])</script><script>self.__next_f.push([1,"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L8\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[\"$\",\"$La\",null,{}],\"notFoundStyles\":[]}]}]}]]}]],null],null],\"couldBeIntercepted\":false,\"initialHead\":[[\"$\",\"meta\",null,{\"name\":\"robots\",\"content\":\"noindex\"}],\"$Lb\"],\"globalErrorComponent\":\"$c\",\"missingSlots\":\"$Wd\"}]\n"])</script><script>self.__next_f.push([1,"b:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"GREENHACKER | Developer Portfolio\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"Full-stack developer portfolio showcasing modern web technologies, AI integration, and innovative projects.\"}],[\"$\",\"meta\",\"4\",{\"name\":\"author\",\"content\":\"GreenHacker\"}],[\"$\",\"meta\",\"5\",{\"name\":\"keywords\",\"content\":\"developer,portfolio,React,Next.js,TypeScript,AI,machine learning\"}],[\"$\",\"meta\",\"6\",{\"name\":\"creator\",\"content\":\"GreenHacker\"}],[\"$\",\"meta\",\"7\",{\"name\":\"publisher\",\"content\":\"GreenHacker\"}],[\"$\",\"meta\",\"8\",{\"name\":\"robots\",\"content\":\"index, follow\"}],[\"$\",\"meta\",\"9\",{\"name\":\"googlebot\",\"content\":\"index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1\"}],[\"$\",\"meta\",\"10\",{\"name\":\"google-site-verification\",\"content\":\"your-google-verification-code\"}],[\"$\",\"meta\",\"11\",{\"property\":\"og:title\",\"content\":\"GREENHACKER | Developer Portfolio\"}],[\"$\",\"meta\",\"12\",{\"property\":\"og:description\",\"content\":\"Full-stack developer portfolio showcasing modern web technologies, AI integration, and innovative projects.\"}],[\"$\",\"meta\",\"13\",{\"property\":\"og:url\",\"content\":\"https://greenhacker.dev\"}],[\"$\",\"meta\",\"14\",{\"property\":\"og:site_name\",\"content\":\"GreenHacker Portfolio\"}],[\"$\",\"meta\",\"15\",{\"property\":\"og:locale\",\"content\":\"en_US\"}],[\"$\",\"meta\",\"16\",{\"property\":\"og:type\",\"content\":\"website\"}],[\"$\",\"meta\",\"17\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"18\",{\"name\":\"twitter:creator\",\"content\":\"@greenhacker\"}],[\"$\",\"meta\",\"19\",{\"name\":\"twitter:title\",\"content\":\"GREENHACKER | Developer Portfolio\"}],[\"$\",\"meta\",\"20\",{\"name\":\"twitter:description\",\"content\":\"Full-stack developer portfolio showcasing modern web technologies, AI integration, and innovative projects.\"}],[\"$\",\"meta\",\"21\",{\"name\":\"next-size-adjust\"}]]\n4:null\n"])</script></body></html>