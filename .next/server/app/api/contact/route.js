"use strict";(()=>{var e={};e.id=386,e.ids=[386],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},65475:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>f,patchFetch:()=>g,requestAsyncStorage:()=>l,routeModule:()=>p,serverHooks:()=>m,staticGenerationAsyncStorage:()=>d});var s={};r.r(s),r.d(s,{GET:()=>c,POST:()=>u});var o=r(49303),n=r(88716),a=r(60670),i=r(87070);async function u(e){try{let{name:t,email:r,subject:s,message:o}=await e.json();if(!t||!r||!s||!o)return i.NextResponse.json({error:"All fields are required"},{status:400});if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(r))return i.NextResponse.json({error:"Invalid email format"},{status:400});return console.log("Contact form submission:",{name:t,email:r,subject:s,message:o,timestamp:new Date().toISOString()}),i.NextResponse.json({message:"Message sent successfully!",success:!0},{status:200})}catch(e){return console.error("Contact form error:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function c(){return i.NextResponse.json({message:"Contact API endpoint is working!"},{status:200})}let p=new o.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/contact/route",pathname:"/api/contact",filename:"route",bundlePath:"app/api/contact/route"},resolvedPagePath:"/Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/app/api/contact/route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:l,staticGenerationAsyncStorage:d,serverHooks:m}=p,f="/api/contact/route";function g(){return(0,a.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:d})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[276,972],()=>r(65475));module.exports=s})();