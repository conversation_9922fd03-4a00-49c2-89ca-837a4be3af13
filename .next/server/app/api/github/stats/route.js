"use strict";(()=>{var e={};e.id=174,e.ids=[174],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55181:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>m,patchFetch:()=>h,requestAsyncStorage:()=>l,routeModule:()=>c,serverHooks:()=>g,staticGenerationAsyncStorage:()=>d});var o={};r.r(o),r.d(o,{GET:()=>i,POST:()=>u});var a=r(49303),s=r(88716),n=r(60670),p=r(87070);async function i(){try{let e={user:{login:"GreenHacker420",name:"<PERSON><PERSON><PERSON><PERSON>",bio:"Full-stack developer passionate about AI and open source",public_repos:47,followers:123,following:89,created_at:"2021-01-15T00:00:00Z"},stats:{totalStars:47,totalCommits:430,totalPRs:28,totalIssues:15,contributedRepos:12},languages:[{name:"JavaScript",percentage:38,color:"#f1e05a"},{name:"TypeScript",percentage:24,color:"#3178c6"},{name:"Python",percentage:18,color:"#3572A5"},{name:"HTML",percentage:10,color:"#e34c26"},{name:"CSS",percentage:10,color:"#563d7c"}],recentActivity:[{type:"push",repo:"portfolio-nextjs",message:"Updated portfolio with new projects",date:new Date().toISOString()},{type:"star",repo:"awesome-react-components",message:"Starred repository",date:new Date(Date.now()-864e5).toISOString()}]};return p.NextResponse.json(e,{status:200})}catch(e){return console.error("GitHub API error:",e),p.NextResponse.json({error:"Failed to fetch GitHub stats"},{status:500})}}async function u(){return p.NextResponse.json({error:"Method not allowed"},{status:405})}let c=new a.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/github/stats/route",pathname:"/api/github/stats",filename:"route",bundlePath:"app/api/github/stats/route"},resolvedPagePath:"/Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/app/api/github/stats/route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:l,staticGenerationAsyncStorage:d,serverHooks:g}=c,m="/api/github/stats/route";function h(){return(0,n.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:d})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[276,972],()=>r(55181));module.exports=o})();