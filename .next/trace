[{"name": "generate-buildid", "duration": 94, "timestamp": 798537144269, "id": 4, "parentId": 1, "tags": {}, "startTime": 1748858417001, "traceId": "daee3d50038c5f24"}, {"name": "load-custom-routes", "duration": 736, "timestamp": 798537144406, "id": 5, "parentId": 1, "tags": {}, "startTime": 1748858417001, "traceId": "daee3d50038c5f24"}, {"name": "create-dist-dir", "duration": 138, "timestamp": 798537172237, "id": 6, "parentId": 1, "tags": {}, "startTime": 1748858417029, "traceId": "daee3d50038c5f24"}, {"name": "collect-pages", "duration": 1451, "timestamp": 798537198657, "id": 7, "parentId": 1, "tags": {}, "startTime": 1748858417055, "traceId": "daee3d50038c5f24"}, {"name": "create-pages-mapping", "duration": 83, "timestamp": 798537200652, "id": 8, "parentId": 1, "tags": {}, "startTime": 1748858417057, "traceId": "daee3d50038c5f24"}, {"name": "collect-app-paths", "duration": 1050, "timestamp": 798537200750, "id": 9, "parentId": 1, "tags": {}, "startTime": 1748858417057, "traceId": "daee3d50038c5f24"}, {"name": "create-app-mapping", "duration": 573, "timestamp": 798537201814, "id": 10, "parentId": 1, "tags": {}, "startTime": 1748858417058, "traceId": "daee3d50038c5f24"}, {"name": "public-dir-conflict-check", "duration": 287, "timestamp": 798537202599, "id": 11, "parentId": 1, "tags": {}, "startTime": 1748858417059, "traceId": "daee3d50038c5f24"}, {"name": "generate-routes-manifest", "duration": 1024, "timestamp": 798537202982, "id": 12, "parentId": 1, "tags": {}, "startTime": 1748858417059, "traceId": "daee3d50038c5f24"}, {"name": "create-entrypoints", "duration": 14259, "timestamp": 798537208485, "id": 15, "parentId": 1, "tags": {}, "startTime": 1748858417065, "traceId": "daee3d50038c5f24"}, {"name": "generate-webpack-config", "duration": 180929, "timestamp": 798537222776, "id": 16, "parentId": 14, "tags": {}, "startTime": 1748858417079, "traceId": "daee3d50038c5f24"}, {"name": "next-trace-entrypoint-plugin", "duration": 908, "timestamp": 798537464086, "id": 18, "parentId": 17, "tags": {}, "startTime": 1748858417321, "traceId": "daee3d50038c5f24"}, {"name": "add-entry", "duration": 475138, "timestamp": 798537466192, "id": 25, "parentId": 19, "tags": {"request": "next/dist/pages/_app"}, "startTime": 1748858417323, "traceId": "daee3d50038c5f24"}, {"name": "add-entry", "duration": 478153, "timestamp": 798537466197, "id": 26, "parentId": 19, "tags": {"request": "next-route-loader?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=next%2Fdist%2Fpages%2F_error&absoluteAppPath=next%2Fdist%2Fpages%2F_app&absoluteDocumentPath=next%2Fdist%2Fpages%2F_document&middlewareConfigBase64=e30%3D!"}, "startTime": 1748858417323, "traceId": "daee3d50038c5f24"}, {"name": "add-entry", "duration": 485460, "timestamp": 798537466346, "id": 29, "parentId": 19, "tags": {"request": "next/dist/pages/_document"}, "startTime": 1748858417323, "traceId": "daee3d50038c5f24"}, {"name": "add-entry", "duration": 515657, "timestamp": 798537466203, "id": 27, "parentId": 19, "tags": {"request": "next-app-loader?page=%2F_not-found%2Fpage&name=app%2F_not-found%2Fpage&pagePath=private-next-app-dir%2Fnot-found.tsx&appDir=%2FUsers%2Fgreenhacker%2FDesktop%2Fpersonal%2Fportfolio%2Fbloom-interactive-verse%2Fsrc%2Fapp&appPaths=%2Fnot-found&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1748858417323, "traceId": "daee3d50038c5f24"}, {"name": "add-entry", "duration": 515659, "timestamp": 798537466210, "id": 28, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fpage&name=app%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fgreenhacker%2FDesktop%2Fpersonal%2Fportfolio%2Fbloom-interactive-verse%2Fsrc%2Fapp&appPaths=%2Fpage&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1748858417323, "traceId": "daee3d50038c5f24"}, {"name": "add-entry", "duration": 517840, "timestamp": 798537465969, "id": 20, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fapi%2Fcontact%2Froute&name=app%2Fapi%2Fcontact%2Froute&pagePath=private-next-app-dir%2Fapi%2Fcontact%2Froute.ts&appDir=%2FUsers%2Fgreenhacker%2FDesktop%2Fpersonal%2Fportfolio%2Fbloom-interactive-verse%2Fsrc%2Fapp&appPaths=%2Fapi%2Fcontact%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1748858417322, "traceId": "daee3d50038c5f24"}, {"name": "add-entry", "duration": 517652, "timestamp": 798537466161, "id": 21, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fapi%2Fgithub%2Fcontributions%2Froute&name=app%2Fapi%2Fgithub%2Fcontributions%2Froute&pagePath=private-next-app-dir%2Fapi%2Fgithub%2Fcontributions%2Froute.ts&appDir=%2FUsers%2Fgreenhacker%2FDesktop%2Fpersonal%2Fportfolio%2Fbloom-interactive-verse%2Fsrc%2Fapp&appPaths=%2Fapi%2Fgithub%2Fcontributions%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1748858417323, "traceId": "daee3d50038c5f24"}, {"name": "add-entry", "duration": 517642, "timestamp": 798537466172, "id": 22, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fapi%2Fgithub%2Frepos%2Froute&name=app%2Fapi%2Fgithub%2Frepos%2Froute&pagePath=private-next-app-dir%2Fapi%2Fgithub%2Frepos%2Froute.ts&appDir=%2FUsers%2Fgreenhacker%2FDesktop%2Fpersonal%2Fportfolio%2Fbloom-interactive-verse%2Fsrc%2Fapp&appPaths=%2Fapi%2Fgithub%2Frepos%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1748858417323, "traceId": "daee3d50038c5f24"}, {"name": "add-entry", "duration": 517636, "timestamp": 798537466180, "id": 23, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fapi%2Fgithub%2Fstats%2Froute&name=app%2Fapi%2Fgithub%2Fstats%2Froute&pagePath=private-next-app-dir%2Fapi%2Fgithub%2Fstats%2Froute.ts&appDir=%2FUsers%2Fgreenhacker%2FDesktop%2Fpersonal%2Fportfolio%2Fbloom-interactive-verse%2Fsrc%2Fapp&appPaths=%2Fapi%2Fgithub%2Fstats%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1748858417323, "traceId": "daee3d50038c5f24"}, {"name": "add-entry", "duration": 517636, "timestamp": 798537466186, "id": 24, "parentId": 19, "tags": {"request": "next-app-loader?page=%2Fapi%2Fprojects%2Froute&name=app%2Fapi%2Fprojects%2Froute&pagePath=private-next-app-dir%2Fapi%2Fprojects%2Froute.ts&appDir=%2FUsers%2Fgreenhacker%2FDesktop%2Fpersonal%2Fportfolio%2Fbloom-interactive-verse%2Fsrc%2Fapp&appPaths=%2Fapi%2Fprojects%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&nextConfigExperimentalUseEarlyImport=&preferredRegion=&middlewareConfig=e30%3D!"}, "startTime": 1748858417323, "traceId": "daee3d50038c5f24"}, {"name": "make", "duration": 1314941, "timestamp": 798537465858, "id": 19, "parentId": 17, "tags": {}, "startTime": 1748858417322, "traceId": "daee3d50038c5f24"}, {"name": "get-entries", "duration": 474, "timestamp": 798538781439, "id": 71, "parentId": 70, "tags": {}, "startTime": 1748858418638, "traceId": "daee3d50038c5f24"}, {"name": "node-file-trace-plugin", "duration": 37727, "timestamp": 798538783909, "id": 72, "parentId": 70, "tags": {"traceEntryCount": "18"}, "startTime": 1748858418640, "traceId": "daee3d50038c5f24"}, {"name": "collect-traced-files", "duration": 353, "timestamp": 798538821647, "id": 73, "parentId": 70, "tags": {}, "startTime": 1748858418678, "traceId": "daee3d50038c5f24"}, {"name": "finish-modules", "duration": 40657, "timestamp": 798538781346, "id": 70, "parentId": 18, "tags": {}, "startTime": 1748858418638, "traceId": "daee3d50038c5f24"}, {"name": "chunk-graph", "duration": 18327, "timestamp": 798538922470, "id": 75, "parentId": 74, "tags": {}, "startTime": 1748858418779, "traceId": "daee3d50038c5f24"}, {"name": "optimize-modules", "duration": 10, "timestamp": 798538940857, "id": 77, "parentId": 74, "tags": {}, "startTime": 1748858418797, "traceId": "daee3d50038c5f24"}, {"name": "optimize-chunks", "duration": 11141, "timestamp": 798538940903, "id": 78, "parentId": 74, "tags": {}, "startTime": 1748858418797, "traceId": "daee3d50038c5f24"}, {"name": "optimize-tree", "duration": 15, "timestamp": 798538952090, "id": 79, "parentId": 74, "tags": {}, "startTime": 1748858418809, "traceId": "daee3d50038c5f24"}, {"name": "optimize-chunk-modules", "duration": 60456, "timestamp": 798538952141, "id": 80, "parentId": 74, "tags": {}, "startTime": 1748858418809, "traceId": "daee3d50038c5f24"}, {"name": "optimize", "duration": 71795, "timestamp": 798538940833, "id": 76, "parentId": 74, "tags": {}, "startTime": 1748858418797, "traceId": "daee3d50038c5f24"}, {"name": "module-hash", "duration": 29458, "timestamp": 798539027564, "id": 81, "parentId": 74, "tags": {}, "startTime": 1748858418884, "traceId": "daee3d50038c5f24"}, {"name": "code-generation", "duration": 3031, "timestamp": 798539057070, "id": 82, "parentId": 74, "tags": {}, "startTime": 1748858418914, "traceId": "daee3d50038c5f24"}, {"name": "hash", "duration": 3472, "timestamp": 798539062921, "id": 83, "parentId": 74, "tags": {}, "startTime": 1748858418919, "traceId": "daee3d50038c5f24"}, {"name": "code-generation-jobs", "duration": 90, "timestamp": 798539066392, "id": 84, "parentId": 74, "tags": {}, "startTime": 1748858418923, "traceId": "daee3d50038c5f24"}, {"name": "module-assets", "duration": 386, "timestamp": 798539066465, "id": 85, "parentId": 74, "tags": {}, "startTime": 1748858418923, "traceId": "daee3d50038c5f24"}, {"name": "create-chunk-assets", "duration": 684, "timestamp": 798539066856, "id": 86, "parentId": 74, "tags": {}, "startTime": 1748858418923, "traceId": "daee3d50038c5f24"}, {"name": "minify-js", "duration": 187, "timestamp": 798539072901, "id": 88, "parentId": 87, "tags": {"name": "../app/api/contact/route.js", "cache": "HIT"}, "startTime": 1748858418929, "traceId": "daee3d50038c5f24"}, {"name": "minify-js", "duration": 130, "timestamp": 798539072962, "id": 89, "parentId": 87, "tags": {"name": "../app/api/github/contributions/route.js", "cache": "HIT"}, "startTime": 1748858418929, "traceId": "daee3d50038c5f24"}, {"name": "minify-js", "duration": 126, "timestamp": 798539072966, "id": 90, "parentId": 87, "tags": {"name": "../app/api/github/repos/route.js", "cache": "HIT"}, "startTime": 1748858418929, "traceId": "daee3d50038c5f24"}, {"name": "minify-js", "duration": 124, "timestamp": 798539072969, "id": 91, "parentId": 87, "tags": {"name": "../app/api/github/stats/route.js", "cache": "HIT"}, "startTime": 1748858418929, "traceId": "daee3d50038c5f24"}, {"name": "minify-js", "duration": 122, "timestamp": 798539072972, "id": 92, "parentId": 87, "tags": {"name": "../app/api/projects/route.js", "cache": "HIT"}, "startTime": 1748858418929, "traceId": "daee3d50038c5f24"}, {"name": "minify-js", "duration": 119, "timestamp": 798539072975, "id": 93, "parentId": 87, "tags": {"name": "../pages/_app.js", "cache": "HIT"}, "startTime": 1748858418929, "traceId": "daee3d50038c5f24"}, {"name": "minify-js", "duration": 117, "timestamp": 798539072977, "id": 94, "parentId": 87, "tags": {"name": "../pages/_error.js", "cache": "HIT"}, "startTime": 1748858418929, "traceId": "daee3d50038c5f24"}, {"name": "minify-js", "duration": 115, "timestamp": 798539072980, "id": 95, "parentId": 87, "tags": {"name": "../app/_not-found/page.js", "cache": "HIT"}, "startTime": 1748858418929, "traceId": "daee3d50038c5f24"}, {"name": "minify-js", "duration": 114, "timestamp": 798539072982, "id": 96, "parentId": 87, "tags": {"name": "../app/page.js", "cache": "HIT"}, "startTime": 1748858418929, "traceId": "daee3d50038c5f24"}, {"name": "minify-js", "duration": 28, "timestamp": 798539073068, "id": 97, "parentId": 87, "tags": {"name": "../pages/_document.js", "cache": "HIT"}, "startTime": 1748858418929, "traceId": "daee3d50038c5f24"}, {"name": "minify-js", "duration": 21, "timestamp": 798539073075, "id": 98, "parentId": 87, "tags": {"name": "../webpack-runtime.js", "cache": "HIT"}, "startTime": 1748858418930, "traceId": "daee3d50038c5f24"}, {"name": "minify-js", "duration": 18, "timestamp": 798539073079, "id": 99, "parentId": 87, "tags": {"name": "447.js", "cache": "HIT"}, "startTime": 1748858418930, "traceId": "daee3d50038c5f24"}, {"name": "minify-js", "duration": 16, "timestamp": 798539073081, "id": 100, "parentId": 87, "tags": {"name": "580.js", "cache": "HIT"}, "startTime": 1748858418930, "traceId": "daee3d50038c5f24"}, {"name": "minify-js", "duration": 14, "timestamp": 798539073083, "id": 101, "parentId": 87, "tags": {"name": "973.js", "cache": "HIT"}, "startTime": 1748858418930, "traceId": "daee3d50038c5f24"}, {"name": "minify-js", "duration": 13, "timestamp": 798539073085, "id": 102, "parentId": 87, "tags": {"name": "548.js", "cache": "HIT"}, "startTime": 1748858418930, "traceId": "daee3d50038c5f24"}, {"name": "minify-js", "duration": 12, "timestamp": 798539073087, "id": 103, "parentId": 87, "tags": {"name": "949.js", "cache": "HIT"}, "startTime": 1748858418930, "traceId": "daee3d50038c5f24"}, {"name": "minify-webpack-plugin-optimize", "duration": 4245, "timestamp": 798539068857, "id": 87, "parentId": 17, "tags": {"compilationName": "server", "mangle": "true"}, "startTime": 1748858418925, "traceId": "daee3d50038c5f24"}, {"name": "css-minimizer-plugin", "duration": 60, "timestamp": 798539073148, "id": 104, "parentId": 17, "tags": {}, "startTime": 1748858418930, "traceId": "daee3d50038c5f24"}, {"name": "create-trace-assets", "duration": 1001, "timestamp": 798539073302, "id": 105, "parentId": 18, "tags": {}, "startTime": 1748858418930, "traceId": "daee3d50038c5f24"}, {"name": "seal", "duration": 194455, "timestamp": 798538887989, "id": 74, "parentId": 17, "tags": {}, "startTime": 1748858418744, "traceId": "daee3d50038c5f24"}, {"name": "webpack-compilation", "duration": 1630420, "timestamp": 798537463228, "id": 17, "parentId": 14, "tags": {"name": "server"}, "startTime": 1748858417320, "traceId": "daee3d50038c5f24"}, {"name": "emit", "duration": 4613, "timestamp": 798539093894, "id": 106, "parentId": 14, "tags": {}, "startTime": 1748858418950, "traceId": "daee3d50038c5f24"}, {"name": "webpack-close", "duration": 321, "timestamp": 798539100133, "id": 107, "parentId": 14, "tags": {"name": "server"}, "startTime": 1748858418957, "traceId": "daee3d50038c5f24"}, {"name": "webpack-generate-error-stats", "duration": 1076, "timestamp": 798539100480, "id": 108, "parentId": 107, "tags": {}, "startTime": 1748858418957, "traceId": "daee3d50038c5f24"}, {"name": "make", "duration": 80, "timestamp": 798539105712, "id": 110, "parentId": 109, "tags": {}, "startTime": 1748858418962, "traceId": "daee3d50038c5f24"}, {"name": "chunk-graph", "duration": 15, "timestamp": 798539106077, "id": 112, "parentId": 111, "tags": {}, "startTime": 1748858418963, "traceId": "daee3d50038c5f24"}, {"name": "optimize-modules", "duration": 2, "timestamp": 798539106109, "id": 114, "parentId": 111, "tags": {}, "startTime": 1748858418963, "traceId": "daee3d50038c5f24"}, {"name": "optimize-chunks", "duration": 25, "timestamp": 798539106137, "id": 115, "parentId": 111, "tags": {}, "startTime": 1748858418963, "traceId": "daee3d50038c5f24"}, {"name": "optimize-tree", "duration": 3, "timestamp": 798539106177, "id": 116, "parentId": 111, "tags": {}, "startTime": 1748858418963, "traceId": "daee3d50038c5f24"}, {"name": "optimize-chunk-modules", "duration": 20, "timestamp": 798539106205, "id": 117, "parentId": 111, "tags": {}, "startTime": 1748858418963, "traceId": "daee3d50038c5f24"}, {"name": "optimize", "duration": 138, "timestamp": 798539106099, "id": 113, "parentId": 111, "tags": {}, "startTime": 1748858418963, "traceId": "daee3d50038c5f24"}, {"name": "module-hash", "duration": 6, "timestamp": 798539106318, "id": 118, "parentId": 111, "tags": {}, "startTime": 1748858418963, "traceId": "daee3d50038c5f24"}, {"name": "code-generation", "duration": 6, "timestamp": 798539106338, "id": 119, "parentId": 111, "tags": {}, "startTime": 1748858418963, "traceId": "daee3d50038c5f24"}, {"name": "hash", "duration": 50, "timestamp": 798539106376, "id": 120, "parentId": 111, "tags": {}, "startTime": 1748858418963, "traceId": "daee3d50038c5f24"}, {"name": "code-generation-jobs", "duration": 17, "timestamp": 798539106426, "id": 121, "parentId": 111, "tags": {}, "startTime": 1748858418963, "traceId": "daee3d50038c5f24"}, {"name": "module-assets", "duration": 6, "timestamp": 798539106440, "id": 122, "parentId": 111, "tags": {}, "startTime": 1748858418963, "traceId": "daee3d50038c5f24"}, {"name": "create-chunk-assets", "duration": 6, "timestamp": 798539106449, "id": 123, "parentId": 111, "tags": {}, "startTime": 1748858418963, "traceId": "daee3d50038c5f24"}, {"name": "minify-js", "duration": 13, "timestamp": 798539108013, "id": 125, "parentId": 124, "tags": {"name": "interception-route-rewrite-manifest.js", "cache": "HIT"}, "startTime": 1748858418964, "traceId": "daee3d50038c5f24"}, {"name": "minify-webpack-plugin-optimize", "duration": 348, "timestamp": 798539107682, "id": 124, "parentId": 109, "tags": {"compilationName": "edge-server", "mangle": "true"}, "startTime": 1748858418964, "traceId": "daee3d50038c5f24"}, {"name": "css-minimizer-plugin", "duration": 3, "timestamp": 798539108046, "id": 126, "parentId": 109, "tags": {}, "startTime": 1748858418964, "traceId": "daee3d50038c5f24"}, {"name": "seal", "duration": 2586, "timestamp": 798539106007, "id": 111, "parentId": 109, "tags": {}, "startTime": 1748858418962, "traceId": "daee3d50038c5f24"}, {"name": "webpack-compilation", "duration": 3550, "timestamp": 798539105078, "id": 109, "parentId": 14, "tags": {"name": "edge-server"}, "startTime": 1748858418962, "traceId": "daee3d50038c5f24"}, {"name": "emit", "duration": 256, "timestamp": 798539108649, "id": 127, "parentId": 14, "tags": {}, "startTime": 1748858418965, "traceId": "daee3d50038c5f24"}, {"name": "webpack-close", "duration": 69, "timestamp": 798539108993, "id": 128, "parentId": 14, "tags": {"name": "edge-server"}, "startTime": 1748858418965, "traceId": "daee3d50038c5f24"}, {"name": "webpack-generate-error-stats", "duration": 248, "timestamp": 798539109065, "id": 129, "parentId": 128, "tags": {}, "startTime": 1748858418965, "traceId": "daee3d50038c5f24"}, {"name": "add-entry", "duration": 369906, "timestamp": 798539112832, "id": 138, "parentId": 131, "tags": {"request": "next-flight-client-entry-loader?server=false!"}, "startTime": 1748858418969, "traceId": "daee3d50038c5f24"}, {"name": "add-entry", "duration": 369916, "timestamp": 798539112834, "id": 139, "parentId": 131, "tags": {"request": "next-flight-client-entry-loader?server=false!"}, "startTime": 1748858418969, "traceId": "daee3d50038c5f24"}, {"name": "add-entry", "duration": 369915, "timestamp": 798539112836, "id": 140, "parentId": 131, "tags": {"request": "next-flight-client-entry-loader?server=false!"}, "startTime": 1748858418969, "traceId": "daee3d50038c5f24"}, {"name": "add-entry", "duration": 369912, "timestamp": 798539112840, "id": 141, "parentId": 131, "tags": {"request": "next-flight-client-entry-loader?server=false!"}, "startTime": 1748858418969, "traceId": "daee3d50038c5f24"}, {"name": "add-entry", "duration": 369911, "timestamp": 798539112841, "id": 142, "parentId": 131, "tags": {"request": "next-flight-client-entry-loader?server=false!"}, "startTime": 1748858418969, "traceId": "daee3d50038c5f24"}, {"name": "add-entry", "duration": 369909, "timestamp": 798539112844, "id": 144, "parentId": 131, "tags": {"request": "next-flight-client-entry-loader?server=false!"}, "startTime": 1748858418969, "traceId": "daee3d50038c5f24"}, {"name": "add-entry", "duration": 420832, "timestamp": 798539112828, "id": 135, "parentId": 131, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_app&page=%2F_app!"}, "startTime": 1748858418969, "traceId": "daee3d50038c5f24"}, {"name": "add-entry", "duration": 573368, "timestamp": 798539112831, "id": 137, "parentId": 131, "tags": {"request": "next-client-pages-loader?absolutePagePath=next%2Fdist%2Fpages%2F_error&page=%2F_error!"}, "startTime": 1748858418969, "traceId": "daee3d50038c5f24"}, {"name": "add-entry", "duration": 573397, "timestamp": 798539112826, "id": 134, "parentId": 131, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22%2FUsers%2Fgreenhacker%2FDesktop%2Fpersonal%2Fportfolio%2Fbloom-interactive-verse%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgreenhacker%2FDesktop%2Fpersonal%2Fportfolio%2Fbloom-interactive-verse%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgreenhacker%2FDesktop%2Fpersonal%2Fportfolio%2Fbloom-interactive-verse%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgreenhacker%2FDesktop%2Fpersonal%2Fportfolio%2Fbloom-interactive-verse%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgreenhacker%2FDesktop%2Fpersonal%2Fportfolio%2Fbloom-interactive-verse%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgreenhacker%2FDesktop%2Fpersonal%2Fportfolio%2Fbloom-interactive-verse%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgreenhacker%2FDesktop%2Fpersonal%2Fportfolio%2Fbloom-interactive-verse%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fgreenhacker%2FDesktop%2Fpersonal%2Fportfolio%2Fbloom-interactive-verse%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1748858418969, "traceId": "daee3d50038c5f24"}, {"name": "add-entry", "duration": 585081, "timestamp": 798539112819, "id": 133, "parentId": 131, "tags": {"request": "./node_modules/next/dist/client/app-next.js"}, "startTime": 1748858418969, "traceId": "daee3d50038c5f24"}, {"name": "add-entry", "duration": 615223, "timestamp": 798539112829, "id": 136, "parentId": 131, "tags": {"request": "/Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/next/dist/client/router.js"}, "startTime": 1748858418969, "traceId": "daee3d50038c5f24"}, {"name": "add-entry", "duration": 615259, "timestamp": 798539112802, "id": 132, "parentId": 131, "tags": {"request": "./node_modules/next/dist/client/next.js"}, "startTime": 1748858418969, "traceId": "daee3d50038c5f24"}, {"name": "add-entry", "duration": 638482, "timestamp": 798539112842, "id": 143, "parentId": 131, "tags": {"request": "next-flight-client-entry-loader?modules=%7B%22request%22%3A%22%2FUsers%2Fgreenhacker%2FDesktop%2Fpersonal%2Fportfolio%2Fbloom-interactive-verse%2Fsrc%2Fapp%2Fnot-found.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"}, "startTime": 1748858418969, "traceId": "daee3d50038c5f24"}, {"name": "postcss-process", "duration": 360614, "timestamp": 798540294902, "id": 150, "parentId": 149, "tags": {}, "startTime": 1748858420151, "traceId": "daee3d50038c5f24"}, {"name": "postcss-loader", "duration": 590762, "timestamp": 798540064807, "id": 149, "parentId": 148, "tags": {}, "startTime": 1748858419921, "traceId": "daee3d50038c5f24"}, {"name": "css-loader", "duration": 22305, "timestamp": 798540655700, "id": 151, "parentId": 148, "tags": {"astUsed": "true"}, "startTime": 1748858420512, "traceId": "daee3d50038c5f24"}, {"name": "build-module-css", "duration": 631630, "timestamp": 798540061459, "id": 148, "parentId": 147, "tags": {"name": "/Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/app/globals.css.webpack[javascript/auto]!=!/Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[2]!/Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[13].oneOf[10].use[3]!/Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/app/globals.css", "layer": null}, "startTime": 1748858419918, "traceId": "daee3d50038c5f24"}]