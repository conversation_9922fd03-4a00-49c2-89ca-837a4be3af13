{"version": 4, "routes": {"/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc"}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "699e24e435dd68236ecc32a86039f43d", "previewModeSigningKey": "72e699fb55be6e9de96d464e05b99c5531abff9e9109c9e7c89ded0f73aaa982", "previewModeEncryptionKey": "b451355ae72025786849686c43f7b43356d68198862645dc6eaced4dacbb030e"}}