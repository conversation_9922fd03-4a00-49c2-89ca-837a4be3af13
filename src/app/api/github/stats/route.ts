import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // In a real application, you would fetch from GitHub API
    // const response = await fetch('https://api.github.com/users/GreenHacker420', {
    //   headers: {
    //     'Authorization': `token ${process.env.GITHUB_TOKEN}`,
    //   },
    // });

    // For now, return mock data
    const mockStats = {
      user: {
        login: 'GreenHacker420',
        name: '<PERSON><PERSON>ack<PERSON>',
        bio: 'Full-stack developer passionate about AI and open source',
        public_repos: 47,
        followers: 123,
        following: 89,
        created_at: '2021-01-15T00:00:00Z',
      },
      stats: {
        totalStars: 47,
        totalCommits: 430,
        totalPRs: 28,
        totalIssues: 15,
        contributedRepos: 12,
      },
      languages: [
        { name: 'JavaScript', percentage: 38, color: '#f1e05a' },
        { name: 'TypeScript', percentage: 24, color: '#3178c6' },
        { name: 'Python', percentage: 18, color: '#3572A5' },
        { name: 'HTML', percentage: 10, color: '#e34c26' },
        { name: 'CSS', percentage: 10, color: '#563d7c' },
      ],
      recentActivity: [
        {
          type: 'push',
          repo: 'portfolio-nextjs',
          message: 'Updated portfolio with new projects',
          date: new Date().toISOString(),
        },
        {
          type: 'star',
          repo: 'awesome-react-components',
          message: 'Starred repository',
          date: new Date(Date.now() - 86400000).toISOString(),
        },
      ],
    };

    return NextResponse.json(mockStats, { status: 200 });
  } catch (error) {
    console.error('GitHub API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch GitHub stats' },
      { status: 500 }
    );
  }
}

export async function POST() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
